<!DOCTYPE HTML>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="robots" content="index, follow">
        <title>aciTree usage samples - A treeview control with jQuery</title>
        <meta name="description" content="aciTree demos to show you how it can be used">
        <meta name="keywords" content="aciTree, treeview, control, tree view, javascript, jQuery">
        <link rel="stylesheet" type="text/css" href="../css/demo.css" media="all">
    </head>
    <body>

        <p><a href="using-the-api.html" title="aciTree API">using the API</a> - show you how to use the API</p>
        <p><a href="selected-event.html" title="aciTree selected event">selected event</a> - do something on item selection change</p>
        <p><a href="selected-event-extended.html" title="aciTree selected event">extended selected event</a> - do something on item selection change</p>
        <p><a href="using-links.html" title="aciTree using links">using links</a> - open a page on item click</p>
        <p><a href="using-file-links.html" title="aciTree using links">using links only for tree leaves</a>  - open a page on item click</p>
        <p><a href="using-hash.html" title="aciTree using hash">using hash/anchors</a> - scroll to an anchor on item click</p>
        <p><a href="using-file-hash.html" title="aciTree using hash">using hash/anchors only for tree leaves</a> - scroll to an anchor on item click</p>

    </body>
</html>