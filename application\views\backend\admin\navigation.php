<div class="sidebar-menu">
    <header class="logo-env" >

        <!-- logo -->
        <div class="logo" style="">
            <a href="<?php echo site_url('login'); ?>">
                <img src="<?php echo base_url('uploads/logo.png');?>"  style="max-height:60px;"/>
            </a>
        </div>

        <!-- logo collapse icon -->
        <div class="sidebar-collapse" style="">
            <a href="#" class="sidebar-collapse-icon with-animation">

                <i class="entypo-menu"></i>
            </a>
        </div>

        <!-- open/close menu icon (do not remove if you want to enable menu on mobile devices) -->
        <div class="sidebar-mobile-menu visible-xs">
            <a href="#" class="with-animation">
                <i class="entypo-menu"></i>
            </a>
        </div>
    </header>
    <div class="sidebar-user-info">

        <div class="sui-normal">
            <a href="#" class="user-link">
                <img src="<?php echo $this->crud_model->get_image_url($this->session->userdata('login_type'), $this->session->userdata('login_user_id'));?>" alt="" class="img-circle" style="height:44px;">

                <span><?php echo get_phrase('welcome'); ?>,</span>
                <strong><?php
                    echo $this->db->get_where($this->session->userdata('login_type'), array($this->session->userdata('login_type') . '_id' =>
                        $this->session->userdata('login_user_id')))->row()->name;
                    ?>
                </strong>
            </a>

            <!-- Return to Admin Button (only visible when viewing as user) - COMMENTED OUT
            <?php if ($this->session->userdata('is_impersonating')): ?>
            <div style="
                background: linear-gradient(135deg, #ff6b6b, #ffa500);
                color: white;
                padding: 10px;
                text-align: center;
                margin: 10px 0;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            ">
                <div style="margin-bottom: 8px;">
                    <i class="fa fa-eye"></i> <strong>IMPERSONATION MODE</strong>
                </div>
                <div style="font-size: 12px; margin-bottom: 8px;">
                    Admin <strong><?php echo $this->session->userdata('original_admin_name'); ?></strong> is viewing as <?php echo ucfirst($this->session->userdata('impersonated_user_type')); ?>
                </div>
                <a href="<?php echo site_url('admin/view_as_user/return_to_admin'); ?>"
                   class="btn btn-light btn-sm"
                   style="background: white; color: #333; border: none; margin-right: 5px;">
                    <i class="fa fa-arrow-left"></i> <?php echo get_phrase('return_to_admin'); ?>
                </a>
                <a href="<?php echo site_url('admin/view_as_user/reset_session'); ?>"
                   class="btn btn-light btn-sm"
                   style="background: #ffebee; color: #c62828; border: none;"
                   onclick="return confirm('Reset impersonation session? This will force return to admin mode.')">
                    <i class="fa fa-refresh"></i> Reset
                </a>
            </div>
            <?php endif; ?>
            -->
        </div>

        <div class="sui-hover inline-links animate-in"><!-- You can remove "inline-links" class to make links appear vertically, class "animate-in" will make A elements animateable when click on user profile -->
            <a href="<?php echo site_url('admin/manage_profile');?>">
                <i class="entypo-pencil"></i>
                <?php echo get_phrase('edit_profile'); ?>
            </a>

            <a href="<?php echo site_url('admin/manage_profile');?>">
                <i class="entypo-lock"></i>
                <?php echo get_phrase('change_password'); ?>
            </a>

            <span class="close-sui-popup">×</span><!-- this is mandatory -->
        </div>
    </div>

    <ul id="main-menu" class="">
        <!-- add class "multiple-expanded" to allow multiple submenus to open -->
        <!-- class "auto-inherit-active-class" will automatically add "active" class for parent elements who are marked already with class "active" -->


        <!-- DASHBOARD -->
        <li class="<?php if ($page_name == 'dashboard') echo 'active'; ?> ">
            <a href="<?php echo site_url('admin/dashboard');?>">
                <i class="fa fa-desktop"></i>
                <span><?php echo get_phrase('dashboard'); ?></span>
            </a>
        </li>

        <li class="<?php if ($page_name == 'manage_department' || $page_name == 'department_facilities') echo 'active'; ?> ">
            <a href="<?php echo site_url('admin/department');?>">
                <i class="fa fa-sitemap"></i>
                <span><?php echo get_phrase('department'); ?></span>
            </a>
        </li>

        <li class="<?php if ($page_name == 'manage_doctor' || strpos($page_name, 'doctor_') === 0) echo 'opened active'; ?>">
            <a href="#">
                <i class="fa fa-user-md"></i>
                <span><?php echo get_phrase('doctor'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_doctor') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/doctor');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_doctor'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'doctor_patients') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/doctor_patients');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_patient'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'doctor_appointments') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/doctor_appointments');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_appointment'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'doctor_prescriptions') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/doctor_prescriptions');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_prescription'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'doctor_reports') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/doctor_reports');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_report'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_patient') echo 'active'; ?> ">
            <a href="<?php echo site_url('admin/patient');?>">
                <i class="fa fa-user"></i>
                <span><?php echo get_phrase('patient'); ?></span>
            </a>
        </li>

        <li class="<?php if ($page_name == 'manage_nurse' || strpos($page_name, 'nurse_') === 0) echo 'opened active'; ?> ">
            <a href="#">
                <i class="fa fa-plus-square"></i>
                <span><?php echo get_phrase('nurse'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_nurse') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/nurse');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_nurse'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'nurse_beds') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/nurse_beds');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_bed'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'nurse_bed_allotments') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/nurse_bed_allotments');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_bed_allotment'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_pharmacist' || strpos($page_name, 'pharmacist_') === 0) echo 'opened active'; ?> ">
            <a href="#">
                <i class="fa fa-medkit"></i>
                <span><?php echo get_phrase('pharmacist'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_pharmacist') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/pharmacist');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_pharmacist'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'pharmacist_medicines') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/pharmacist_medicines');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_medicine'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_laboratorist' || strpos($page_name, 'laboratorist_') === 0) echo 'opened active'; ?> ">
            <a href="#">
                <i class="fa fa-user"></i>
                <span><?php echo get_phrase('laboratorist'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_laboratorist') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/laboratorist');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_laboratorist'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'laboratorist_blood_bank') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/laboratorist_blood_bank');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_blood_bank'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'laboratorist_blood_donors') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/laboratorist_blood_donors');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_blood_donor'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_accountant' || strpos($page_name, 'accountant_') === 0) echo 'opened active'; ?> ">
            <a href="#">
                <i class="fa fa-money"></i>
                <span><?php echo get_phrase('accountant'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_accountant') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/accountant');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_accountant'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'accountant_add_invoice') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/accountant_add_invoice');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('add_invoice'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'accountant_invoices') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/accountant_invoices');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_invoice'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_receptionist' || strpos($page_name, 'receptionist_') === 0) echo 'opened active'; ?> ">
            <a href="#">
                <i class="fa fa-plus-square"></i>
                <span><?php echo get_phrase('receptionist'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'manage_receptionist') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/receptionist');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_receptionist'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'receptionist_patients') echo 'active'; ?>">
                    <a href="<?php echo site_url('role_operations/receptionist_patients');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('manage_patient'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'show_payment_history'   || $page_name == 'show_bed_allotment'
                            || $page_name == 'show_blood_bank'      || $page_name == 'show_blood_donor'
                            || $page_name == 'show_medicine'        || $page_name == 'show_operation_report'
                            || $page_name == 'show_birth_report'    || $page_name == 'show_death_report'
                            || $page_name == 'view_as_user'         || $page_name == 'activity_log_dashboard'
                            || $page_name == 'user_search_monitor'  || $page_name == 'role_operations_dashboard'
                            || strpos($page_name, 'doctor_') === 0  || strpos($page_name, 'nurse_') === 0
                            || strpos($page_name, 'pharmacist_') === 0 || strpos($page_name, 'laboratorist_') === 0
                            || strpos($page_name, 'accountant_') === 0 || strpos($page_name, 'receptionist_') === 0)
                        echo 'opened active';?> ">
            <a href="#">
                <i class="fa fa-sun-o"></i>
                <span><?php echo get_phrase('monitor_hospital'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'show_payment_history') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/payment_history');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('payment_history'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_bed_allotment') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/bed_allotment');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('bed_allotment'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_blood_bank') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/blood_bank');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('blood_bank'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_blood_donor') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/blood_donor');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('blood_donor'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_medicine') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/medicine');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('medicine'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_operation_report') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/operation_report');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('operation_report'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_birth_report') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/birth_report');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('birth_report'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'show_death_report') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/death_report');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('death_report'); ?></span>
                    </a>
                </li>
                <!-- VIEW AS USER - COMMENTED OUT
                <li class="<?php if ($page_name == 'view_as_user') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/view_as_user');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('view_as_user'); ?></span>
                    </a>
                </li>
                -->
                <li class="<?php if ($page_name == 'activity_log_dashboard') echo 'active'; ?> ">
                    <a href="<?php echo site_url('activity_log');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('activity_log'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'user_search_monitor') echo 'active'; ?> ">
                    <a href="<?php echo site_url('activity_log/user_search');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('user_search_monitor'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'role_operations_dashboard' || strpos($page_name, 'doctor_') === 0 || strpos($page_name, 'nurse_') === 0 || strpos($page_name, 'pharmacist_') === 0 || strpos($page_name, 'laboratorist_') === 0 || strpos($page_name, 'accountant_') === 0 || strpos($page_name, 'receptionist_') === 0) echo 'active'; ?> ">
                    <a href="<?php echo site_url('role_operations');?>">
                        <i class="entypo-dot"></i>
                        <span><?php echo get_phrase('role_operations'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php
            if ($page_name == 'payroll_add' || $page_name == 'payroll_add_view'
                || $page_name == 'payroll_list')
                echo 'opened active has-sub'; ?>">
            <a href="#">
                <i class="entypo-tag"></i>
                <span><?php echo get_phrase('payroll'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'payroll_add' || $page_name == 'payroll_add_view') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/payroll');?>">
                        <span><i class="entypo-dot"></i> <?php echo get_phrase('create_payroll'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'payroll_list') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/payroll_list');?>">
                        <span><i class="entypo-dot"></i> <?php echo get_phrase('payroll_list'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php if ($page_name == 'manage_notice') echo 'active'; ?> ">
            <a href="<?php echo site_url('admin/notice');?>">
                <i class="entypo-doc-text-inv"></i>
                <span><?php echo get_phrase('noticeboard'); ?></span>
            </a>
        </li>

        <!-- SETTINGS -->
        <li class="<?php if ($page_name == 'system_settings' || $page_name == 'manage_language' ||
                            $page_name == 'sms_settings') echo 'opened active';?> ">
            <a href="#">
                <i class="fa fa-wrench"></i>
                <span><?php echo get_phrase('settings'); ?></span>
            </a>
            <ul>
                <li class="<?php if ($page_name == 'system_settings') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/system_settings');?>">
                        <span><i class="fa fa-h-square"></i> <?php echo get_phrase('system_settings'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'manage_language') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/manage_language');?>">
                        <span><i class="fa fa-globe"></i> <?php echo get_phrase('language_settings'); ?></span>
                    </a>
                </li>
                <li class="<?php if ($page_name == 'sms_settings') echo 'active'; ?> ">
                    <a href="<?php echo site_url('admin/sms_settings');?>">
                        <span><i class="entypo-paper-plane"></i><?php echo get_phrase('sms_settings'); ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- forntend -->
        <li class="<?php if ($page_name == 'frontend') echo 'active'; ?>">
            <a href="<?php echo site_url('admin/frontend');?>">
                <i class="fa fa-laptop"></i>
                <span><?php echo get_phrase('frontend'); ?></span>
            </a>
        </li>

        <!-- contact emails -->
        <li class="<?php if ($page_name == 'contact_email') echo 'active'; ?>">
            <a href="<?php echo site_url('admin/contact_email');?>">
                <i class="fa fa-envelope"></i>
                <span><?php echo get_phrase('contact_emails'); ?></span>
            </a>
        </li>

        <!-- ACCOUNT -->
        <li class="<?php if ($page_name == 'manage_profile') echo 'active'; ?> ">
            <a href="<?php echo site_url('admin/manage_profile');?>">
                <i class="fa fa-user"></i>
                <span><?php echo get_phrase('account'); ?></span>
            </a>
        </li>



    </ul>

</div>
