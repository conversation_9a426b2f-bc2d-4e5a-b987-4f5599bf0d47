body a {
  color: #222222;
}
body .profile-info.dropdown .dropdown-menu {
  background: #222222;
  border-color: #222222;
}
body .profile-info.dropdown .dropdown-menu > li {
  border-bottom-color: transparent;
}
body .profile-info.dropdown .dropdown-menu li a {
  color: #eeeeee;
}
body .profile-info.dropdown .dropdown-menu li a:hover {
  background: #1f1f1f;
}
body .page-container .sidebar-menu {
  background: #222222;
  color: #1f1f1f;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li#search .search-input {
  background-color: #1f1f1f !important;
  border-color: #282828 !important;
}
body .page-container .sidebar-menu #main-menu li#search {
  background-color: #1f1f1f;
  border-color: #282828;
}
body .page-container .sidebar-menu #main-menu li ul {
  border-color: #282828;
}
body .page-container .sidebar-menu #main-menu li ul > li {
  border-color: #282828;
}
body .page-container .sidebar-menu #main-menu li ul > li > a {
  background-color: #222222;
}
body .page-container .sidebar-menu #main-menu li.active > a {
  background: #1f1f1f;
}
body .page-container .sidebar-menu #main-menu li ul > li > a {
  background-color: #1f1f1f;
}
body .page-container .sidebar-menu .logo-env > div.sidebar-collapse a,
body .page-container .sidebar-menu .logo-env > div.sidebar-mobile-menu a {
  border-color: #282828;
}
body .page-container .sidebar-menu .logo-env > div.sidebar-collapse a:hover {
  background: #1f1f1f;
}
body .page-container .sidebar-menu .sidebar-user-info {
  border-color: #282828;
}
body .page-container .sidebar-menu .sidebar-user-info .sui-hover {
  background-color: #222222;
}
body .page-container .sidebar-menu #main-menu li {
  border-color: #282828;
}
body .page-container .sidebar-menu #main-menu li a {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li a:hover {
  background-color: #1f1f1f;
}
body .page-container .sidebar-menu #main-menu li ul > li > a:hover {
  background-color: #141414;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li {
  border-color: #282828;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li > a {
  background-color: #1b1b1b;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li ul > li > a {
  background-color: #1b1b1b;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li ul > li ul > li > a {
  background-color: #1b1b1b;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a > span:not(.badge) {
  background: #222222;
  border-color: #282828;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li ul {
  border-color: #282828;
}
body .profile-info.dropdown .dropdown-menu > .caret {
  border-bottom-color: #222222;
}
body #chat {
  background: #222222;
}
body #chat .chat-header {
  color: #FFF;
  border-bottom: 1px solid #282828;
}
body #chat .chat-group > a:hover,
body #chat .chat-group > a.active {
  background: #282828;
}
body #chat .chat-group > strong {
  color: rgba(255, 255, 255, 0.4);
}
body #chat .chat-conversation {
  background: #1b1b1b;
}
body #chat .chat-conversation .conversation-body > li.odd,
body #chat .chat-conversation .conversation-body > li.even,
body #chat .chat-conversation .conversation-body > li.opponent {
  background: #1f1f1f;
}
body #chat .chat-conversation .conversation-header {
  border-color: #282828;
}
body #chat .chat-conversation .chat-textarea textarea {
  background: #1f1f1f;
  box-shadow: none;
  border-color: #1f1f1f;
}
body #chat .chat-group > a:before {
  border-color: transparent transparent transparent #1b1b1b;
}
body.login-page .login-form .form-group .input-group {
  border-color: #282828;
}
body.login-page {
  background: #1f1f1f;
  color: rgba(255, 255, 255, 0.5);
}
body.login-page .login-form .form-group .input-group .form-control::-webkit-input-placeholder {
  color: #eeeeee;
}
body.login-page .login-form .form-group .input-group .form-control:-moz-placeholder {
  color: #eeeeee;
}
body.login-page .login-form .form-group .input-group .form-control::-moz-placeholder {
  color: #eeeeee;
}
body.login-page .login-form .form-group .input-group .form-control:-ms-input-placeholder {
  color: #eeeeee;
}
body.login-page .login-form .form-group .input-group {
  background: #222222;
  border-color: #282828;
}
body.login-page .login-form .form-group .input-group.focused {
  border-color: #353535;
}
body.login-page .login-form .form-group .input-group .input-group-addon:after {
  background: #282828;
}
body.login-page .login-form .form-group .btn-login {
  background: #1f1f1f;
  border-color: #282828;
}
body.login-page .login-form .form-group .btn-login:hover {
  background: #222222;
}
body .login-container .login-header {
  background-color: #222222;
}
body .login-container .login-header.login-caret:after {
  border-top-color: #222222;
}
body.login-page.logging-in .login-progressbar {
  background: #414141;
  height: 2px;
}
body.login-page.logging-in .login-progressbar div {
  background: #ffba00;
}
body .tile-primary {
  background: #222222;
}
body .tile-primary .tile-entry {
  border-color: #282828;
}
body .tile-primary .title {
  background: #151515;
}
body .tile-white-primary .num,
body .tile-white-primary h3,
body .tile-white-primary p {
  color: #282828;
}
body .btn-primary {
  background: #222222;
  border-color: #222222;
}
body .panel-invert {
  background: #222222;
}
body .navbar-inverse {
  border-color: #222222;
  background: #222222;
}
body .navbar-inverse .navbar-nav > li > a {
  color: #eeeeee;
}
body .navbar-inverse .navbar-nav > .open > a,
body .navbar-inverse .navbar-nav > .open > a:hover,
body .navbar-inverse .navbar-nav > .open > a:focus {
  background: #1f1f1f;
}
body .navbar-inverse .navbar-nav > .active > a,
body .navbar-inverse .navbar-nav > .active > a:hover,
body .navbar-inverse .navbar-nav > .active > a:focus {
  background: #1f1f1f;
}
body .badge.badge-primary,
body .label-primary {
  background-color: #222222;
}
body .badge.badge-secondary,
body .label-secondary {
  background-color: #ffba00;
}
body .pagination > .active > a,
body .pagination > .active > span,
body .pagination > .active > a:hover,
body .pagination > .active > span:hover,
body .pagination > .active > a:focus,
body .pagination > .active > span:focus {
  border-color: #222222;
  background: #222222;
}
body div.datepicker table tr td.active,
body div.datepicker table tr td.active:hover,
body div.datepicker table tr td.active.disabled,
body div.datepicker table tr td.active.disabled:hover {
  background-color: #222222;
}
body.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb img {
  border-color: #1f1f1f;
}
body.login-page .login-content a {
  color: #eeeeee;
}
body .input-group-addon {
  color: #eeeeee;
}
body.page-left-in,
body.page-right-in,
body.page-fade-only,
body.page-fade {
  background: #222222 !important;
}
body .page-container .sidebar-menu #main-menu li#search button i {
  color: #eeeeee;
}
body .btn-primary.btn-icon i {
  background-color: rgba(0, 0, 0, 0.2);
}
body .btn-primary:hover,
body .btn-primary:focus,
body .btn-primary:active,
body .btn-primary.active,
body .open .dropdown-toggle.btn-primary {
  background: #1b1b1b;
  border-color: #1b1b1b;
}
body .tile-block .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #222222;
}
body .page-container.horizontal-menu header.navbar {
  background: #222222;
}
body .page-container.horizontal-menu.with-sidebar header.navbar {
  border-color: #282828;
}
body .page-container.horizontal-menu.with-sidebar .sidebar-user-info {
  border-color: #282828;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li > a {
  border-right-color: #282828;
  color: #eeeeee;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li.active > a {
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar .navbar-nav {
  border-left-color: #282828;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li#search {
  border-right-color: #282828;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li:hover > a {
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul {
  background: #222222;
}
body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open {
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
  border-color: #282828;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li:hover > a {
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li.active > a {
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input,
body .page-container.horizontal-menu header.navbar > ul > li#search .search-input {
  background: #1f1f1f;
  border-color: #282828;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li#search.search-input-collapsed:hover {
  border-color: #282828;
  background: #1f1f1f;
}
body .page-container.horizontal-menu header.navbar ul.nav > li.sep {
  border-color: #282828;
}
body .page-container.horizontal-menu header.navbar ul.nav > li > a,
body .page-container.horizontal-menu header.navbar ul.nav > li > span {
  color: #eeeeee;
}
body .entypo-menu {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li#search .search-input {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li#search .search-input::-webkit-input-placeholder {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li#search .search-input:-moz-placeholder {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li#search .search-input::-moz-placeholder {
  color: #eeeeee;
}
body .page-container .sidebar-menu #main-menu li#search .search-input:-ms-input-placeholder {
  color: #eeeeee;
}
body #chat .chat-group > a {
  color: #eeeeee;
}
body .conversation-body,
body #chat .entypo-cancel,
body #chat .chat-conversation .chat-textarea:after {
  color: #eeeeee;
}
body #chat .chat-conversation .chat-textarea textarea::-webkit-input-placeholder {
  color: #eeeeee;
}
body #chat .chat-conversation .chat-textarea textarea:-moz-placeholder {
  color: #eeeeee;
}
body #chat .chat-conversation .chat-textarea textarea::-moz-placeholder {
  color: #eeeeee;
}
body #chat .chat-conversation .chat-textarea textarea:-ms-input-placeholder {
  color: #eeeeee;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
  color: #eeeeee;
}
body .page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search button i,
body .page-container.horizontal-menu header.navbar > ul > li#search button i {
  color: #eeeeee;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a:hover,
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a:hover {
  color: #eeeeee;
}
body .panel-invert > .panel-heading,
body .modal.invert .modal-dialog .modal-content .modal-header,
body .modal.invert .modal-dialog .modal-content .modal-footer {
  background: #222222;
  border-color: #282828;
}
body .panel-invert > .panel-body,
body .modal.invert .modal-dialog .modal-content {
  background: #222222;
  color: #eeeeee;
}
body .modal.invert .modal-dialog .modal-content {
  border-color: #222222;
}
body .panel-invert {
  border-color: #222222;
}
body .panel-invert > .panel-heading > .panel-options > a.bg,
body .modal.invert .modal-dialog .modal-content .modal-header .close {
  background-color: #1f1f1f;
}
body .panel-invert > .panel-heading > .panel-options > a.bg:hover {
  background-color: #1b1b1b;
}
body a.list-group-item.active,
body a.list-group-item.active:hover,
body a.list-group-item.active:focus {
  background-color: #222222;
  border-color: #222222;
}
body a.list-group-item.active .list-group-item-text,
body a.list-group-item.active:hover .list-group-item-text,
body a.list-group-item.active:focus .list-group-item-text {
  color: #eeeeee;
}
body .popover.popover-primary {
  background-color: #222222;
  border-color: #222222;
}
body .popover.popover-primary .popover-title {
  background-color: #1b1b1b;
  border-color: #1b1b1b;
}
body .popover.popover-primary.top .arrow {
  border-top-color: #222222;
}
body .popover.popover-primary.top .arrow:after {
  border-top-color: #222222;
}
body .popover.popover-primary.right .arrow {
  border-right-color: #222222;
}
body .popover.popover-primary.right .arrow:after {
  border-right-color: #222222;
}
body .popover.popover-primary.bottom .arrow {
  border-bottom-color: #222222;
}
body .popover.popover-primary.bottom .arrow:after {
  border-bottom-color: #222222;
}
body .popover.popover-primary.left .arrow {
  border-left-color: #222222;
}
body .popover.popover-primary.left .arrow:after {
  border-left-color: #222222;
}
body .popover.popover-secondary {
  background-color: #ffba00;
  border-color: #ffba00;
}
body .popover.popover-secondary .popover-title {
  background-color: #ffba00;
  border-color: #ffba00;
}
body .popover.popover-secondary.top .arrow {
  border-top-color: #ffba00;
}
body .popover.popover-secondary.top .arrow:after {
  border-top-color: #ffba00;
}
body .popover.popover-secondary.right .arrow {
  border-right-color: #ffba00;
}
body .popover.popover-secondary.right .arrow:after {
  border-right-color: #ffba00;
}
body .popover.popover-secondary.bottom .arrow {
  border-bottom-color: #ffba00;
}
body .popover.popover-secondary.bottom .arrow:after {
  border-bottom-color: #ffba00;
}
body .popover.popover-secondary.left .arrow {
  border-left-color: #ffba00;
}
body .popover.popover-secondary.left .arrow:after {
  border-left-color: #ffba00;
}
body .tooltip.tooltip-primary .tooltip-inner {
  background-color: #222222;
  color: #eeeeee;
}
body .tooltip.tooltip-primary.top .tooltip-arrow {
  border-top-color: #222222;
}
body .tooltip.tooltip-primary.top-left .tooltip-arrow {
  border-top-color: #222222;
}
body .tooltip.tooltip-primary.top-right .tooltip-arrow {
  border-top-color: #222222;
}
body .tooltip.tooltip-primary.right .tooltip-arrow {
  border-right-color: #222222;
}
body .tooltip.tooltip-primary.left .tooltip-arrow {
  border-left-color: #222222;
}
body .tooltip.tooltip-primary.bottom .tooltip-arrow {
  border-bottom-color: #222222;
}
body .tooltip.tooltip-primary.bottom-left .tooltip-arrow {
  border-bottom-color: #222222;
}
body .tooltip.tooltip-primary.bottom-right .tooltip-arrow {
  border-bottom-color: #222222;
}
body .tooltip.tooltip-secondary .tooltip-inner {
  background-color: #ffba00;
  color: #eeeeee;
}
body .tooltip.tooltip-secondary.top .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.top-left .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.top-right .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.right .tooltip-arrow {
  border-right-color: #ffba00;
}
body .tooltip.tooltip-secondary.left .tooltip-arrow {
  border-left-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom-left .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom-right .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input::-webkit-input-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input::-webkit-input-placeholder {
  color: #eeeeee;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input:-moz-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input:-moz-placeholder {
  color: #eeeeee;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input::-moz-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input::-moz-placeholder {
  color: #eeeeee;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input:-ms-input-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input:-ms-input-placeholder {
  color: #eeeeee;
}
