<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Activity_log extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->library('session');
        $this->load->model('crud_model');
        $this->load->model('activity_log_model');
        /*cache control*/
        $this->output->set_header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
        $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        $this->output->set_header('Pragma: no-cache');
        $this->output->set_header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
    }

    /***default function, redirects to login page if no admin logged in yet***/
    public function index()
    {
        if ($this->session->userdata('admin_login') != 1) {
            $this->session->set_userdata('last_page', current_url());
            redirect(site_url(), 'refresh');
        }
        
        $page_data['page_name']  = 'activity_log_dashboard';
        $page_data['page_title'] = get_phrase('activity_log');
        $this->load->view('backend/index', $page_data);
    }

    // User search and monitoring
    public function user_search()
    {
        if ($this->session->userdata('admin_login') != 1) {
            $this->session->set_userdata('last_page', current_url());
            redirect(site_url(), 'refresh');
        }
        
        $page_data['page_name']  = 'user_search_monitor';
        $page_data['page_title'] = get_phrase('user_search_monitor');
        $this->load->view('backend/index', $page_data);
    }

    // Get user logs via AJAX
    public function get_user_logs()
    {
        if ($this->session->userdata('admin_login') != 1) {
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }

        $user_type = $this->input->post('user_type');
        $user_id = $this->input->post('user_id');
        $date_from = $this->input->post('date_from');
        $date_to = $this->input->post('date_to');
        $activity_type = $this->input->post('activity_type');

        $logs = $this->activity_log_model->get_user_activity_logs($user_type, $user_id, $date_from, $date_to, $activity_type);
        
        echo json_encode($logs);
    }

    // Search users via AJAX
    public function search_users()
    {
        if ($this->session->userdata('admin_login') != 1) {
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }

        $search_term = $this->input->post('search_term');
        $user_type = $this->input->post('user_type');

        $users = $this->activity_log_model->search_users($search_term, $user_type);
        
        echo json_encode($users);
    }

    // Get activity statistics
    public function get_activity_stats()
    {
        if ($this->session->userdata('admin_login') != 1) {
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }

        $stats = $this->activity_log_model->get_activity_statistics();
        
        echo json_encode($stats);
    }

    // Export activity logs
    public function export_logs()
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        $user_type = $this->input->get('user_type');
        $user_id = $this->input->get('user_id');
        $date_from = $this->input->get('date_from');
        $date_to = $this->input->get('date_to');

        $logs = $this->activity_log_model->get_user_activity_logs($user_type, $user_id, $date_from, $date_to);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="activity_logs_' . date('Y-m-d') . '.csv"');

        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, ['Timestamp', 'User Type', 'User Name', 'Activity', 'IP Address', 'User Agent', 'Details']);

        foreach ($logs as $log) {
            fputcsv($output, [
                date('Y-m-d H:i:s', $log['timestamp']),
                ucfirst($log['user_type']),
                $log['user_name'],
                $log['activity'],
                $log['ip_address'],
                $log['user_agent'],
                $log['details']
            ]);
        }

        fclose($output);
    }
}
