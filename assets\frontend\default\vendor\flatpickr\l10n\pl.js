/* Polish locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.pl = {};

flatpickr.l10ns.pl.weekdays = {
	shorthand: ["Nd", "Pn", "Wt", "Śr", "<PERSON><PERSON>", "Pt", "So"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>nied<PERSON>ł<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>wartek", "Piątek", "Sobota"]
};

flatpickr.l10ns.pl.months = {
	shorthand: ["<PERSON>y", "<PERSON>t", "<PERSON>", "<PERSON>wi", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>e", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>s", "<PERSON>ru"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>ń", "<PERSON><PERSON>", "Marzec", "Kwiecień", "Maj", "Czerwiec", "Lipiec", "Sierpień", "Wrzesień", "Październik", "Listopad", "<PERSON><PERSON><PERSON><PERSON>"]
};

flatpickr.l10ns.pl.ordinal = function () {
	return ".";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;