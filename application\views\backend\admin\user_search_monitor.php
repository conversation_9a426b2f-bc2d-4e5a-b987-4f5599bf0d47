<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-search"></i> <?php echo get_phrase('user_search_monitor'); ?></h3>
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Search Section -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><?php echo get_phrase('search_users'); ?></label>
                            <div class="input-group">
                                <input type="text" id="user-search" class="form-control" placeholder="Search by name or email...">
                                <div class="input-group-btn">
                                    <button class="btn btn-primary" onclick="searchUsers()">
                                        <i class="fa fa-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>User Type</label>
                            <select id="search-user-type" class="form-control">
                                <option value="">All Types</option>
                                <option value="admin">Admin</option>
                                <option value="doctor">Doctor</option>
                                <option value="patient">Patient</option>
                                <option value="nurse">Nurse</option>
                                <option value="pharmacist">Pharmacist</option>
                                <option value="laboratorist">Laboratorist</option>
                                <option value="accountant">Accountant</option>
                                <option value="receptionist">Receptionist</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn btn-success btn-block" onclick="clearSearch()">
                                <i class="fa fa-refresh"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search Results -->
                <div id="search-results" style="display: none;">
                    <h4><i class="fa fa-users"></i> Search Results</h4>
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Type</th>
                                    <th>Email</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="search-results-body">
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- User Activity Monitor Modal -->
<div class="modal fade" id="user-activity-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fa fa-user"></i> User Activity Monitor: <span id="modal-user-name"></span>
                </h4>
            </div>
            <div class="modal-body">
                
                <!-- Filters -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Date From</label>
                            <input type="date" id="filter-date-from" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Date To</label>
                            <input type="date" id="filter-date-to" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Activity Type</label>
                            <select id="filter-activity-type" class="form-control">
                                <option value="">All Activities</option>
                                <option value="login_activity">Login</option>
                                <option value="logout_activity">Logout</option>
                                <option value="page_view">Page View</option>
                                <option value="data_create">Data Create</option>
                                <option value="data_update">Data Update</option>
                                <option value="data_delete">Data Delete</option>
                                <option value="file_upload">File Upload</option>
                                <option value="search_performed">Search</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary btn-block" onclick="filterUserActivities()">
                                <i class="fa fa-filter"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Statistics for User -->
                <div class="row" id="user-stats">
                    <div class="col-md-3">
                        <div class="stat-box bg-primary">
                            <div class="stat-number" id="user-total-activities">0</div>
                            <div class="stat-label">Total Activities</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box bg-success">
                            <div class="stat-number" id="user-today-activities">0</div>
                            <div class="stat-label">Today</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box bg-warning">
                            <div class="stat-number" id="user-week-activities">0</div>
                            <div class="stat-label">This Week</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box bg-info">
                            <div class="stat-number" id="user-last-activity">Never</div>
                            <div class="stat-label">Last Activity</div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log Table -->
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Activity</th>
                                <th>Details</th>
                                <th>IP Address</th>
                                <th>User Agent</th>
                            </tr>
                        </thead>
                        <tbody id="user-activities-body">
                            <tr>
                                <td colspan="5" class="text-center">
                                    <i class="fa fa-spinner fa-spin"></i> Loading activities...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="exportUserLogs()">
                    <i class="fa fa-download"></i> Export Logs
                </button>
                <button class="btn btn-warning" onclick="viewAsUser()">
                    <i class="fa fa-eye"></i> View As User
                </button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
var currentUser = null;

$(document).ready(function() {
    // Set default date range (last 7 days)
    var today = new Date();
    var lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#filter-date-from').val(lastWeek.toISOString().split('T')[0]);
    $('#filter-date-to').val(today.toISOString().split('T')[0]);
    
    // Enter key search
    $('#user-search').keypress(function(e) {
        if (e.which == 13) {
            searchUsers();
        }
    });
});

function searchUsers() {
    var searchTerm = $('#user-search').val().trim();
    var userType = $('#search-user-type').val();
    
    if (searchTerm.length < 2) {
        alert('Please enter at least 2 characters to search');
        return;
    }
    
    $.post('<?php echo site_url('activity_log/search_users'); ?>', {
        search_term: searchTerm,
        user_type: userType
    }, function(data) {
        var users = JSON.parse(data);
        var html = '';
        
        if (users && users.length > 0) {
            users.forEach(function(user) {
                html += '<tr>';
                html += '<td><strong>' + user.name + '</strong></td>';
                html += '<td><span class="label label-info">' + user.user_type + '</span></td>';
                html += '<td>' + user.email + '</td>';
                html += '<td>';
                html += '<button class="btn btn-primary btn-sm" onclick="monitorUser(\'' + user.user_type + '\', ' + user.user_id + ', \'' + user.name + '\')">';
                html += '<i class="fa fa-eye"></i> Monitor';
                html += '</button> ';
                html += '<button class="btn btn-warning btn-sm" onclick="viewAsUserDirect(\'' + user.user_type + '\', ' + user.user_id + ', \'' + user.name + '\')">';
                html += '<i class="fa fa-user"></i> View As';
                html += '</button>';
                html += '</td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="4" class="text-center text-muted">No users found</td></tr>';
        }
        
        $('#search-results-body').html(html);
        $('#search-results').show();
    });
}

function clearSearch() {
    $('#user-search').val('');
    $('#search-user-type').val('');
    $('#search-results').hide();
}

function monitorUser(userType, userId, userName) {
    currentUser = {
        type: userType,
        id: userId,
        name: userName
    };
    
    $('#modal-user-name').text(userName + ' (' + userType + ')');
    $('#user-activity-modal').modal('show');
    
    loadUserActivities();
}

function loadUserActivities() {
    if (!currentUser) return;
    
    var dateFrom = $('#filter-date-from').val();
    var dateTo = $('#filter-date-to').val();
    var activityType = $('#filter-activity-type').val();
    
    $.post('<?php echo site_url('activity_log/get_user_logs'); ?>', {
        user_type: currentUser.type,
        user_id: currentUser.id,
        date_from: dateFrom,
        date_to: dateTo,
        activity_type: activityType
    }, function(data) {
        var logs = JSON.parse(data);
        var html = '';
        
        if (logs && logs.length > 0) {
            // Update statistics
            $('#user-total-activities').text(logs.length);
            
            var today = new Date().toDateString();
            var todayCount = logs.filter(log => new Date(log.timestamp * 1000).toDateString() === today).length;
            $('#user-today-activities').text(todayCount);
            
            var weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            var weekCount = logs.filter(log => new Date(log.timestamp * 1000) >= weekAgo).length;
            $('#user-week-activities').text(weekCount);
            
            var lastActivity = logs.length > 0 ? new Date(logs[0].timestamp * 1000).toLocaleString() : 'Never';
            $('#user-last-activity').text(lastActivity);
            
            // Build table
            logs.forEach(function(log) {
                html += '<tr>';
                html += '<td>' + new Date(log.timestamp * 1000).toLocaleString() + '</td>';
                html += '<td><span class="label label-success">' + log.activity + '</span></td>';
                html += '<td>' + (log.details || '-') + '</td>';
                html += '<td>' + log.ip_address + '</td>';
                html += '<td title="' + log.user_agent + '">' + (log.user_agent.substring(0, 50) + '...') + '</td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="5" class="text-center text-muted">No activities found</td></tr>';
            $('#user-total-activities').text('0');
            $('#user-today-activities').text('0');
            $('#user-week-activities').text('0');
            $('#user-last-activity').text('Never');
        }
        
        $('#user-activities-body').html(html);
    });
}

function filterUserActivities() {
    loadUserActivities();
}

function exportUserLogs() {
    if (!currentUser) return;
    
    var dateFrom = $('#filter-date-from').val();
    var dateTo = $('#filter-date-to').val();
    var activityType = $('#filter-activity-type').val();
    
    var url = '<?php echo site_url('activity_log/export_logs'); ?>' + 
              '?user_type=' + currentUser.type +
              '&user_id=' + currentUser.id +
              '&date_from=' + dateFrom +
              '&date_to=' + dateTo +
              '&activity_type=' + activityType;
    
    window.open(url, '_blank');
}

// VIEW AS FUNCTIONS - COMMENTED OUT
/*
function viewAsUser() {
    if (!currentUser) return;

    if (confirm('Are you sure you want to view the system as ' + currentUser.name + '?')) {
        window.location.href = '<?php echo site_url('admin/view_as_user/switch'); ?>/' + currentUser.type + '/' + currentUser.id;
    }
}

function viewAsUserDirect(userType, userId, userName) {
    if (confirm('Are you sure you want to view the system as ' + userName + '?')) {
        window.location.href = '<?php echo site_url('admin/view_as_user/switch'); ?>/' + userType + '/' + userId;
    }
}
*/
</script>

<style>
.stat-box {
    padding: 20px;
    border-radius: 4px;
    color: white;
    text-align: center;
    margin-bottom: 20px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
}

.stat-label {
    font-size: 12px;
    margin-top: 5px;
}

.bg-primary { background-color: #3498db; }
.bg-success { background-color: #27ae60; }
.bg-warning { background-color: #f39c12; }
.bg-info { background-color: #17a2b8; }
</style>
