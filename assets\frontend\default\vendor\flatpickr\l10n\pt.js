/* Portuguese locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.pt = {};

flatpickr.l10ns.pt.weekdays = {
	shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
	longhand: ["<PERSON>", "Segunda-feira", "Terça-feira", "Q<PERSON>rta-feira", "<PERSON><PERSON><PERSON>-feira", "Sexta-feira", "Sábado"]
};

flatpickr.l10ns.pt.months = {
	shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>b<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Set", "Out", "Nov", "Dez"],
	longhand: ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Jun<PERSON>", "Jul<PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
};

flatpickr.l10ns.pt.rangeSeparator = " até ";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;