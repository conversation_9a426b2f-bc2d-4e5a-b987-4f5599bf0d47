/* Estonian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.et = {};

flatpickr.l10ns.et.weekdays = {
	shorthand: ["P", "E", "T", "<PERSON>", "N", "<PERSON>", "<PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]
};

flatpickr.l10ns.et.months = {
	shorthand: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Apr", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Aug", "Sept", "Okt", "Nov", "Dets"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aprill", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"]
};

flatpickr.l10ns.et.firstDayOfWeek = 1;

flatpickr.l10ns.et.ordinal = function () {
	return ".";
};

flatpickr.l10ns.et.weekAbbreviation = "Näd";
flatpickr.l10ns.et.rangeSeparator = " kuni ";
flatpickr.l10ns.et.scrollTitle = "Keri, et suurendada";
flatpickr.l10ns.et.toggleTitle = "Klõpsa, et vahetada";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;