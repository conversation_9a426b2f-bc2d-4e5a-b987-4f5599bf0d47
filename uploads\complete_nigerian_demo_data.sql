-- Complete Nigerian Demo Data for Bayanno Hospital Management System
-- This file contains comprehensive demo data for ALL modules

-- Insert Prescriptions
INSERT IGNORE INTO `prescription` (`prescription_id`, `timestamp`, `doctor_id`, `patient_id`, `case_history`, `medication`, `note`) VALUES
(1, '**********', 1, 1, 'Patient complained of chest pain and shortness of breath. ECG shows irregular rhythm.', 'Amlodipine 5mg - Take once daily\nAspirin 75mg - Take once daily\nAtenolol 50mg - Take twice daily', 'Follow up in 2 weeks. Avoid strenuous activities.'),
(2, '**********', 2, 6, 'Child presented with fever and cough. Physical examination shows throat inflammation.', 'Paracetamol syrup - 5ml every 6 hours\nAmoxicillin suspension - 2.5ml twice daily for 7 days', 'Ensure adequate fluid intake. Return if symptoms worsen.'),
(3, '**********', 3, 3, 'Patient reports knee pain after fall. X-ray shows no fracture but soft tissue injury.', 'Ibuprofen 400mg - Take twice daily with food\nDiclofenac gel - Apply to affected area twice daily', 'Rest and elevate leg. Physiotherapy recommended after 1 week.'),
(4, '**********', 4, 8, 'Routine antenatal visit. Patient is 28 weeks pregnant. All vitals normal.', 'Folic acid 5mg - Take once daily\nIron tablets - Take twice daily\nCalcium supplements - Take once daily', 'Next appointment in 4 weeks. Continue regular exercise.'),
(5, '**********', 5, 5, 'Diabetic patient with elevated blood sugar levels. HbA1c: 8.2%', 'Metformin 500mg - Take twice daily with meals\nGlibenclamide 5mg - Take once daily before breakfast', 'Monitor blood sugar daily. Diet counseling scheduled.'),
(6, '**********', 6, 7, 'Patient presented with severe headache and nausea. Blood pressure elevated.', 'Amlodipine 10mg - Take once daily\nParacetamol 500mg - As needed for headache', 'Emergency follow-up in 48 hours. Monitor blood pressure daily.');

-- Insert Invoices
INSERT IGNORE INTO `invoice` (`invoice_id`, `invoice_number`, `patient_id`, `title`, `invoice_entries`, `creation_timestamp`, `due_timestamp`, `status`, `vat_percentage`, `discount_amount`) VALUES
(1, 'INV-2024-001', 1, 'Cardiology Consultation and Tests', '[{"item":"Consultation Fee","quantity":"1","rate":"15000","total":"15000"},{"item":"ECG Test","quantity":"1","rate":"5000","total":"5000"},{"item":"Blood Pressure Monitoring","quantity":"1","rate":"2000","total":"2000"}]', '**********', '**********', 'paid', '7.5', '1000'),
(2, 'INV-2024-002', 6, 'Pediatric Consultation and Medication', '[{"item":"Pediatric Consultation","quantity":"1","rate":"8000","total":"8000"},{"item":"Throat Examination","quantity":"1","rate":"3000","total":"3000"},{"item":"Medication","quantity":"1","rate":"4500","total":"4500"}]', '**********', '**********', 'paid', '7.5', '500'),
(3, 'INV-2024-003', 3, 'Orthopedic Consultation and X-Ray', '[{"item":"Orthopedic Consultation","quantity":"1","rate":"12000","total":"12000"},{"item":"Knee X-Ray","quantity":"1","rate":"8000","total":"8000"},{"item":"Physiotherapy Session","quantity":"1","rate":"5000","total":"5000"}]', '**********', '1642809600', 'pending', '7.5', '0'),
(4, 'INV-2024-004', 8, 'Antenatal Care Package', '[{"item":"Antenatal Consultation","quantity":"1","rate":"10000","total":"10000"},{"item":"Ultrasound Scan","quantity":"1","rate":"12000","total":"12000"},{"item":"Blood Tests","quantity":"1","rate":"6000","total":"6000"}]', '**********', '1642896000', 'paid', '7.5', '2000'),
(5, 'INV-2024-005', 5, 'Diabetes Management Package', '[{"item":"Endocrinology Consultation","quantity":"1","rate":"15000","total":"15000"},{"item":"HbA1c Test","quantity":"1","rate":"4000","total":"4000"},{"item":"Blood Sugar Monitoring Kit","quantity":"1","rate":"8000","total":"8000"}]', '**********', '1642982400', 'pending', '7.5', '1500'),
(6, 'INV-2024-006', 7, 'Emergency Treatment', '[{"item":"Emergency Consultation","quantity":"1","rate":"20000","total":"20000"},{"item":"Blood Pressure Medication","quantity":"1","rate":"3500","total":"3500"},{"item":"Monitoring Fee","quantity":"1","rate":"5000","total":"5000"}]', '**********', '**********', 'paid', '7.5', '0');

-- Insert Payment Records
INSERT IGNORE INTO `payment` (`payment_id`, `invoice_id`, `amount_paid`, `payment_timestamp`, `payment_method`, `payment_details`) VALUES
(1, 1, 21000, **********, 'cash', 'Cash payment received at reception'),
(2, 2, 15000, **********, 'bank_transfer', 'Transfer from GTBank - Ref: GTB2024001'),
(3, 4, 26000, **********, 'pos', 'POS payment - Verve card ending 1234'),
(4, 6, 28500, **********, 'cash', 'Emergency cash payment');

-- Insert Blood Bank Records
INSERT IGNORE INTO `blood_bank` (`blood_bank_id`, `blood_group`, `status`, `donor_name`, `donor_age`, `donor_sex`, `donor_phone`, `donor_address`, `donation_timestamp`) VALUES
(1, 'O+', 'available', 'Chinedu Okwu', 32, 'male', '+234-************', '15 Surulere, Lagos State', **********),
(2, 'A+', 'available', 'Fatima Bello', 28, 'female', '+234-************', '22 Wuse, Abuja FCT', **********),
(3, 'B+', 'used', 'Emeka Nwankwo', 35, 'male', '+234-************', '8 Coal Camp, Enugu State', **********),
(4, 'AB+', 'available', 'Aisha Mohammed', 29, 'female', '+234-************', '45 Sabon Gari, Kano State', **********),
(5, 'O-', 'available', 'Olumide Adebayo', 31, 'male', '+234-************', '12 Bodija, Ibadan, Oyo State', **********),
(6, 'A-', 'available', 'Blessing Eze', 26, 'female', '+234-************', '33 Trans Ekulu, Enugu State', **********),
(7, 'B-', 'available', 'Usman Garba', 33, 'male', '+234-************', '18 Maitama, Abuja FCT', **********),
(8, 'AB-', 'used', 'Chioma Okafor', 30, 'female', '+234-************', '25 Ikeja GRA, Lagos State', **********);

-- Insert Bed Allotments
INSERT IGNORE INTO `bed_allotment` (`bed_allotment_id`, `bed_id`, `patient_id`, `allotment_timestamp`, `discharge_timestamp`) VALUES
(1, 2, 1, '**********', '**********'),
(2, 4, 8, '**********', NULL),
(3, 6, 10, '**********', '**********'),
(4, 8, 6, '**********', '**********');

-- Insert Reports
INSERT IGNORE INTO `report` (`report_id`, `type`, `description`, `timestamp`, `doctor_id`, `patient_id`, `files`) VALUES
(1, 'lab_report', 'Complete Blood Count and Lipid Profile for cardiac patient', '**********', 1, 1, 'cbc_lipid_profile_pat001.pdf'),
(2, 'radiology', 'Chest X-Ray showing clear lungs, normal heart size', '**********', 2, 6, 'chest_xray_pat002.pdf'),
(3, 'lab_report', 'HbA1c and Fasting Blood Sugar for diabetic monitoring', '**********', 5, 5, 'diabetes_labs_pat005.pdf'),
(4, 'ultrasound', 'Antenatal ultrasound at 28 weeks - normal fetal development', '**********', 4, 8, 'antenatal_scan_pat008.pdf');

-- Insert Pathology Reports
INSERT IGNORE INTO `pathology_report` (`pathology_report_id`, `code`, `patient_id`, `test_name`, `pathology_report`) VALUES
(1, 'LAB001', 1, 'Complete Blood Count', 'cbc_report_pat001.pdf'),
(2, 'LAB002', 1, 'Lipid Profile', 'lipid_profile_pat001.pdf'),
(3, 'LAB003', 5, 'HbA1c Test', 'hba1c_pat005.pdf'),
(4, 'LAB004', 5, 'Fasting Blood Sugar', 'fbs_pat005.pdf'),
(5, 'LAB005', 8, 'Pregnancy Test', 'pregnancy_test_pat008.pdf'),
(6, 'LAB006', 3, 'Inflammatory Markers', 'crp_esr_pat003.pdf');

-- Insert Diagnosis Reports
INSERT IGNORE INTO `diagnosis_report` (`diagnosis_report_id`, `report_type`, `document_type`, `file_name`, `prescription_id`, `description`, `timestamp`, `laboratorist_id`) VALUES
(1, 'blood_test', 'pdf', 'blood_analysis_pat001.pdf', 1, 'Complete blood analysis showing elevated cholesterol levels', **********, 1),
(2, 'urine_test', 'pdf', 'urine_analysis_pat005.pdf', 5, 'Urine analysis for diabetic complications screening', **********, 2),
(3, 'stool_test', 'pdf', 'stool_analysis_pat006.pdf', 6, 'Stool examination for parasitic infections', **********, 3);

-- Insert Payroll Records
INSERT IGNORE INTO `payroll` (`payroll_id`, `payroll_code`, `user_id`, `user_type`, `joining_salary`, `allowances`, `deductions`, `date`, `status`) VALUES
(1, 'PAY-DOC-001', 1, 'doctor', 450000, '[{"type":"housing","amount":"50000"},{"type":"transport","amount":"30000"},{"type":"medical","amount":"20000"}]', '[{"type":"tax","amount":"45000"},{"type":"pension","amount":"22500"}]', '2024-01-31', 1),
(2, 'PAY-DOC-002', 2, 'doctor', 420000, '[{"type":"housing","amount":"45000"},{"type":"transport","amount":"25000"},{"type":"medical","amount":"20000"}]', '[{"type":"tax","amount":"42000"},{"type":"pension","amount":"21000"}]', '2024-01-31', 1),
(3, 'PAY-NUR-001', 1, 'nurse', 180000, '[{"type":"housing","amount":"20000"},{"type":"transport","amount":"15000"},{"type":"medical","amount":"10000"}]', '[{"type":"tax","amount":"18000"},{"type":"pension","amount":"9000"}]', '2024-01-31', 1),
(4, 'PAY-PHA-001', 1, 'pharmacist', 250000, '[{"type":"housing","amount":"30000"},{"type":"transport","amount":"20000"},{"type":"medical","amount":"15000"}]', '[{"type":"tax","amount":"25000"},{"type":"pension","amount":"12500"}]', '2024-01-31', 1),
(5, 'PAY-LAB-001', 1, 'laboratorist', 200000, '[{"type":"housing","amount":"25000"},{"type":"transport","amount":"15000"},{"type":"medical","amount":"12000"}]', '[{"type":"tax","amount":"20000"},{"type":"pension","amount":"10000"}]', '2024-01-31', 1),
(6, 'PAY-ACC-001', 1, 'accountant', 280000, '[{"type":"housing","amount":"35000"},{"type":"transport","amount":"20000"},{"type":"medical","amount":"15000"}]', '[{"type":"tax","amount":"28000"},{"type":"pension","amount":"14000"}]', '2024-01-31', 1);

-- Insert Private Messages
INSERT IGNORE INTO `message` (`message_id`, `message_thread_code`, `message`, `sender`, `timestamp`) VALUES
(1, 'MSG001', 'Please review the lab results for Patient PAT001. The cholesterol levels are concerning.', 'doctor_1', **********),
(2, 'MSG001', 'I have reviewed the results. Recommend dietary changes and follow-up in 2 weeks.', 'doctor_5', **********),
(3, 'MSG002', 'Patient PAT005 needs urgent diabetes counseling. Can we schedule a session?', 'doctor_5', **********),
(4, 'MSG002', 'Diabetes counseling scheduled for tomorrow at 10 AM.', 'nurse_1', **********),
(5, 'MSG003', 'Blood bank inventory is running low on O+ blood type. Need to organize donation drive.', 'laboratorist_1', **********),
(6, 'MSG003', 'I will coordinate with the Nigerian Red Cross for the donation drive next week.', 'admin_1', **********);

-- Insert Message Threads
INSERT IGNORE INTO `message_thread` (`message_thread_id`, `message_thread_code`, `sender`, `receiver`, `timestamp`) VALUES
(1, 'MSG001', 'doctor_1', 'doctor_5', **********),
(2, 'MSG002', 'doctor_5', 'nurse_1', **********),
(3, 'MSG003', 'laboratorist_1', 'admin_1', **********);

-- Insert System Settings Updates
INSERT IGNORE INTO `settings` (`settings_id`, `type`, `description`) VALUES
(18, 'hospital_name', 'Poolot General Hospital'),
(19, 'hospital_address', '123 Independence Avenue, Victoria Island, Lagos State, Nigeria'),
(20, 'hospital_phone', '+234-1-234-5678'),
(21, 'hospital_email', '<EMAIL>'),
(22, 'currency_symbol', '₦'),
(23, 'timezone', 'Africa/Lagos');

-- Insert Frontend Settings
INSERT IGNORE INTO `frontend_settings` (`frontend_settings_id`, `type`, `description`) VALUES
(1, 'hospital_name', 'Poolot General Hospital'),
(2, 'address', '123 Independence Avenue, Victoria Island, Lagos State, Nigeria'),
(3, 'phone', '+234-1-234-5678'),
(4, 'email', '<EMAIL>'),
(5, 'about_us', 'Poolot General Hospital is a leading healthcare institution in Nigeria, providing comprehensive medical services with a focus on quality care and patient satisfaction. Our team of experienced Nigerian medical professionals is committed to delivering world-class healthcare services.'),
(6, 'facebook_url', 'https://facebook.com/poolothospital'),
(7, 'twitter_url', 'https://twitter.com/poolothospital'),
(8, 'linkedin_url', 'https://linkedin.com/company/poolothospital');

-- Insert Additional Language Phrases
INSERT IGNORE INTO `language` (`phrase_id`, `phrase`, `english`, `bengali`, `spanish`, `arabic`, `dutch`, `polish`, `german`, `french`, `italian`, `russian`, `portugese`) VALUES
(299, 'payment_history', 'Payment History', 'পেমেন্ট ইতিহাস', 'Historial de Pagos', 'تاريخ الدفع', 'Betalingsgeschiedenis', 'Historia Płatności', 'Zahlungshistorie', 'Historique des Paiements', 'Cronologia Pagamenti', 'История платежей', 'Histórico de Pagamentos'),
(300, 'blood_bank', 'Blood Bank', 'ব্লাড ব্যাংক', 'Banco de Sangre', 'بنك الدم', 'Bloedbank', 'Bank Krwi', 'Blutbank', 'Banque de Sang', 'Banca del Sangue', 'Банк крови', 'Banco de Sangue'),
(301, 'pathology_report', 'Pathology Report', 'প্যাথলজি রিপোর্ট', 'Informe de Patología', 'تقرير علم الأمراض', 'Pathologie Rapport', 'Raport Patologii', 'Pathologiebericht', 'Rapport de Pathologie', 'Rapporto di Patologia', 'Отчет по патологии', 'Relatório de Patologia'),
(302, 'diagnosis_report', 'Diagnosis Report', 'ডায়াগনোসিস রিপোর্ট', 'Informe de Diagnóstico', 'تقرير التشخيص', 'Diagnose Rapport', 'Raport Diagnozy', 'Diagnosebericht', 'Rapport de Diagnostic', 'Rapporto di Diagnosi', 'Отчет диагностики', 'Relatório de Diagnóstico'),
(303, 'payroll', 'Payroll', 'বেতন তালিকা', 'Nómina', 'كشف الرواتب', 'Salarisadministratie', 'Lista Płac', 'Gehaltsabrechnung', 'Paie', 'Busta Paga', 'Зарплата', 'Folha de Pagamento');

-- Insert Expense Categories
INSERT IGNORE INTO `expense_category` (`expense_category_id`, `name`, `description`) VALUES
(1, 'Medical Equipment', 'Purchase and maintenance of medical equipment'),
(2, 'Utilities', 'Electricity, water, internet and other utility bills'),
(3, 'Staff Training', 'Training and development programs for staff'),
(4, 'Maintenance', 'Building and equipment maintenance costs'),
(5, 'Supplies', 'Medical supplies, office supplies and consumables'),
(6, 'Marketing', 'Hospital marketing and promotional activities');

-- Insert Expenses
INSERT IGNORE INTO `expense` (`expense_id`, `expense_category_id`, `title`, `expense_id_from_expense_category`, `date`, `amount`, `description`, `timestamp`) VALUES
(1, 1, 'ECG Machine Maintenance', 'EQP-001', '2024-01-15', 85000, 'Annual maintenance of ECG machine by certified technician', **********),
(2, 2, 'Electricity Bill - January', 'UTL-001', '2024-01-31', 125000, 'Monthly electricity bill for hospital premises', **********),
(3, 3, 'Nursing Staff Training', 'TRN-001', '2024-01-20', 45000, 'CPR and emergency response training for nursing staff', **********),
(4, 4, 'Generator Repair', 'MNT-001', '2024-01-25', 35000, 'Repair of backup generator - replaced faulty parts', **********),
(5, 5, 'Medical Supplies Purchase', 'SUP-001', '2024-01-10', 150000, 'Monthly purchase of syringes, gloves, masks and other supplies', **********),
(6, 6, 'Health Awareness Campaign', 'MKT-001', '2024-01-28', 25000, 'Community health awareness campaign materials and logistics', **********);

-- Insert Surgery Records
INSERT IGNORE INTO `surgery` (`surgery_id`, `surgery_type`, `patient_id`, `doctor_id`, `surgery_date`, `surgery_time`, `description`, `status`, `notes`) VALUES
(1, 'Appendectomy', 3, 3, '2024-01-20', '14:30:00', 'Emergency appendectomy due to acute appendicitis', 'completed', 'Surgery successful. Patient recovering well. No complications.'),
(2, 'Cesarean Section', 8, 4, '2024-01-25', '09:15:00', 'Elective cesarean section delivery', 'completed', 'Healthy baby delivered. Mother and child in good condition.'),
(3, 'Cataract Surgery', 7, 1, '2024-01-30', '11:00:00', 'Cataract removal from left eye', 'scheduled', 'Pre-operative assessment completed. Surgery scheduled for next week.');

-- Insert Lab Test Categories
INSERT IGNORE INTO `lab_category` (`lab_category_id`, `name`, `description`) VALUES
(1, 'Hematology', 'Blood-related tests and analysis'),
(2, 'Biochemistry', 'Chemical analysis of blood and body fluids'),
(3, 'Microbiology', 'Tests for infections and microorganisms'),
(4, 'Immunology', 'Immune system and antibody tests'),
(5, 'Histopathology', 'Tissue examination and biopsy analysis');

-- Insert Lab Tests
INSERT IGNORE INTO `lab_test` (`lab_test_id`, `lab_category_id`, `test_name`, `normal_range`, `unit`, `price`) VALUES
(1, 1, 'Complete Blood Count (CBC)', '4.5-11.0', '10^9/L', 3500),
(2, 1, 'Hemoglobin Level', '12.0-16.0', 'g/dL', 1500),
(3, 2, 'Fasting Blood Sugar', '70-100', 'mg/dL', 2000),
(4, 2, 'HbA1c', '4.0-5.6', '%', 4000),
(5, 2, 'Lipid Profile', 'Total Cholesterol <200', 'mg/dL', 5000),
(6, 3, 'Malaria Parasite', 'Negative', '', 1500),
(7, 3, 'Typhoid Test', 'Negative', '', 2500),
(8, 4, 'Hepatitis B Surface Antigen', 'Negative', '', 3000),
(9, 4, 'HIV Screening', 'Negative', '', 2000),
(10, 5, 'Pap Smear', 'Normal', '', 6000);

-- Insert Vaccination Records
INSERT IGNORE INTO `vaccination` (`vaccination_id`, `patient_id`, `vaccine_name`, `vaccination_date`, `next_due_date`, `administered_by`, `batch_number`, `notes`) VALUES
(1, 6, 'Hepatitis B', '2024-01-15', '2024-02-15', 'Nurse Chioma Okafor', 'HEP-B-2024-001', 'First dose of Hepatitis B vaccine series'),
(2, 6, 'Polio (OPV)', '2024-01-15', '2024-03-15', 'Nurse Chioma Okafor', 'POLIO-2024-001', 'Oral polio vaccine administered'),
(3, 10, 'Tetanus Toxoid', '2024-01-20', '2025-01-20', 'Nurse Grace Udoh', 'TT-2024-001', 'Tetanus booster shot'),
(4, 12, 'COVID-19 Vaccine', '2024-01-25', '2024-04-25', 'Nurse Kemi Adebayo', 'COVID-2024-001', 'First dose of COVID-19 vaccine');

-- Insert Equipment Records
INSERT IGNORE INTO `equipment` (`equipment_id`, `equipment_name`, `equipment_type`, `purchase_date`, `warranty_expiry`, `maintenance_schedule`, `status`, `location`) VALUES
(1, 'Philips ECG Machine', 'Diagnostic', '2023-06-15', '2026-06-15', 'Monthly', 'active', 'Cardiology Department'),
(2, 'GE Ultrasound Scanner', 'Imaging', '2023-08-20', '2026-08-20', 'Quarterly', 'active', 'Radiology Department'),
(3, 'Mindray Patient Monitor', 'Monitoring', '2023-09-10', '2026-09-10', 'Monthly', 'active', 'ICU Ward'),
(4, 'Olympus Endoscope', 'Diagnostic', '2023-11-05', '2026-11-05', 'After each use', 'active', 'Gastroenterology'),
(5, 'Siemens X-Ray Machine', 'Imaging', '2022-12-15', '2025-12-15', 'Quarterly', 'maintenance', 'Radiology Department'),
(6, 'Medtronic Defibrillator', 'Emergency', '2023-07-30', '2026-07-30', 'Monthly', 'active', 'Emergency Department');
