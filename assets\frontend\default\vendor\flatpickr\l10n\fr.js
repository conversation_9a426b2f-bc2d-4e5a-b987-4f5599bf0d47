/* French locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.fr = {};

flatpickr.l10ns.fr.firstDayOfWeek = 1;

flatpickr.l10ns.fr.weekdays = {
	shorthand: ["Dim", "<PERSON>n", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>i", "<PERSON><PERSON>", "<PERSON><PERSON>redi", "<PERSON><PERSON>"]
};

flatpickr.l10ns.fr.months = {
	shorthand: ["Janv", "Févr", "Mars", "Avr", "<PERSON>", "Juin", "Juil", "Août", "Sept", "Oct", "Nov", "Déc"],
	longhand: ["Jan<PERSON>", "Février", "Mars", "Avril", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>t", "Septem<PERSON>", "<PERSON><PERSON>re", "Novembre", "Décembre"]
};

flatpickr.l10ns.fr.ordinal = function (nth) {
	if (nth > 1) return "ème";

	return "er";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;

flatpickr.l10ns.fr.rangeSeparator = " au ";
flatpickr.l10ns.fr.weekAbbreviation = "Sem";
flatpickr.l10ns.fr.scrollTitle = "Défiler pour augmenter la valeur";
flatpickr.l10ns.fr.toggleTitle = "Cliquer pour basculer";