/* Serbian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.sr = {};

flatpickr.l10ns.sr.weekdays = {
	shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON>", "Ponedeljak", "U<PERSON><PERSON>", "<PERSON><PERSON>", "Četvrta<PERSON>", "Petak", "Subota", "Nedel<PERSON>"]
};

flatpickr.l10ns.sr.months = {
	shorthand: ["Jan", "Feb", "Mar", "Apr", "Maj", "Jun", "Jul", "Avg", "Sep", "Okt", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "April", "Maj", "Jun", "Jul", "Avgust", "Septembar", "Oktobar", "Novembar", "Decembar"]
};

flatpickr.l10ns.sr.firstDayOfWeek = 1;
flatpickr.l10ns.sr.weekAbbreviation = "Ned.";
flatpickr.l10ns.sr.rangeSeparator = " do ";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;