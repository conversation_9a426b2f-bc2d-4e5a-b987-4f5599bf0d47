<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-money"></i> <?php echo get_phrase('accountant'); ?> - <?php echo get_phrase('manage_invoice'); ?></h3>
                    <p>Manage invoices as an accountant would</p>
                </div>
            </div>
            <div class="panel-body">
                
                <a href="<?php echo site_url('role_operations/accountant_add_invoice'); ?>" 
                    class="btn btn-primary pull-right">
                        <i class="fa fa-plus"></i>&nbsp;<?php echo get_phrase('add_invoice'); ?>
                </a>
                <div style="clear:both;"></div>
                <br>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped datatable" id="table-2">
                        <thead>
                            <tr>
                                <th><?php echo get_phrase('invoice_number');?></th>
                                <th><?php echo get_phrase('patient');?></th>
                                <th><?php echo get_phrase('title');?></th>
                                <th><?php echo get_phrase('amount');?></th>
                                <th><?php echo get_phrase('status');?></th>
                                <th><?php echo get_phrase('creation_date');?></th>
                                <th><?php echo get_phrase('options');?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($invoice_info) && is_array($invoice_info)): ?>
                                <?php foreach ($invoice_info as $row): ?>   
                                    <tr>
                                        <td><strong><?php echo $row['invoice_number']?></strong></td>
                                        <td>
                                            <?php 
                                            $patient = $this->db->get_where('patient', array('patient_id' => $row['patient_id']))->row();
                                            echo $patient ? $patient->name : 'Unknown Patient';
                                            ?>
                                        </td>
                                        <td><?php echo $row['title']?></td>
                                        <td>
                                            <span class="label label-success">
                                                <?php echo $this->db->get_where('settings', array('type' => 'currency'))->row()->description; ?>
                                                <?php echo number_format($row['amount'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($row['status'] == 'paid'): ?>
                                                <span class="label label-success">
                                                    <i class="fa fa-check"></i> <?php echo get_phrase('paid'); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="label label-danger">
                                                    <i class="fa fa-times"></i> <?php echo get_phrase('unpaid'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d M Y', $row['creation_timestamp']); ?></td>
                                        <td>
                                            <a  onclick="showAjaxModal('<?php echo site_url('modal/popup/edit_invoice/'.$row['invoice_id']);?>');" 
                                                class="btn btn-info btn-sm">
                                                    <i class="fa fa-pencil"></i>&nbsp;
                                                    <?php echo get_phrase('edit');?>
                                            </a>
                                            <a onclick="confirm_modal('<?php echo site_url('role_operations/accountant_invoices/delete/'.$row['invoice_id']); ?>')"
                                                class="btn btn-danger btn-sm">
                                                    <i class="fa fa-trash-o"></i>&nbsp;
                                                    <?php echo get_phrase('delete');?>
                                            </a>
                                            <a href="<?php echo site_url('admin/view_invoice/'.$row['invoice_id']); ?>" target="_blank"
                                                class="btn btn-success btn-sm">
                                                    <i class="fa fa-print"></i>&nbsp;
                                                    <?php echo get_phrase('print');?>
                                            </a>
                                            <a onclick="mark_paid(<?php echo $row['invoice_id']; ?>)"
                                                class="btn btn-warning btn-sm" <?php if ($row['status'] == 'paid') echo 'style="display:none;"'; ?>>
                                                    <i class="fa fa-check"></i>&nbsp;
                                                    <?php echo get_phrase('mark_paid');?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No invoice data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="row" style="margin-top: 20px;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            <strong>Accountant Operations:</strong> You are performing invoice management operations as an accountant would. 
                            All actions are logged under your admin account for audit purposes.
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <a href="<?php echo site_url('role_operations'); ?>" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Back to Role Operations
                        </a>
                        <a href="<?php echo site_url('role_operations/accountant_add_invoice'); ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add New Invoice
                        </a>
                        <a href="<?php echo site_url('admin/payment_history'); ?>" class="btn btn-success">
                            <i class="fa fa-history"></i> Payment History
                        </a>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

<script>
function mark_paid(invoice_id) {
    if (confirm('Are you sure you want to mark this invoice as paid?')) {
        $.post('<?php echo site_url('admin/mark_invoice_paid'); ?>/' + invoice_id, function(data) {
            location.reload();
        });
    }
}
</script>
