/* Hungarian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.hu = {};

flatpickr.l10ns.hu.firstDayOfWeek = 1;

flatpickr.l10ns.hu.weekdays = {
	shorthand: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>törtök", "Péntek", "Szombat"]
};

flatpickr.l10ns.hu.months = {
	shorthand: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "November", "December"]
};

flatpickr.l10ns.hu.ordinal = function () {
	return ".";
};

flatpickr.l10ns.hu.weekAbbreviation = "Hét";
flatpickr.l10ns.hu.scrollTitle = "Görgessen";
flatpickr.l10ns.hu.toggleTitle = "Kattintson a váltáshoz";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;