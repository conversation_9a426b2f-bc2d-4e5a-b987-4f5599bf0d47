{"name": "aciTree", "title": "aciTree - A customizable treeview control", "description": "aciTree renders a treeview control based on a simple data structure. The tree can be lazy loaded node by node through AJAX or the entire tree can be loaded in one go, including directly from a JavaScript variable. A simple PHP implementation for a file system tree is provided as an example, but any server-side language can be used. The tree can be manipulated using the aciTree API. The tree items can be added, loaded, updated, removed, their order can be changed ... and much more.", "keywords": ["treeview", "tree", "ui", "ajax"], "version": "4.5.0-rc.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/dragosu"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/dragosu"}], "licenses": [{"type": "MIT", "url": "http://acoderinsights.ro/source/aciTree/mit-licence.txt"}, {"type": "GPLv2", "url": "http://acoderinsights.ro/source/aciTree/gpl-v2-licence.txt"}], "homepage": "http://acoderinsights.ro/en/aciTree-tree-view-with-jQuery", "demo": "http://acoderinsights.ro/source/aciTree/aciTree.html", "download": "http://acoderinsights.ro/source/zip/aciTree.zip", "docs": "http://acoderinsights.ro/source/aciTree/documentation.html", "bugs": "https://github.com/dragosu/jquery-aciTree/issues", "dependencies": {"jquery": ">=1.9.0", "aciPlugin": ">=1.5.1"}}