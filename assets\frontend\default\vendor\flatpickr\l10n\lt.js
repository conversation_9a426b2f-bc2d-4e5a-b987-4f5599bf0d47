/* Lithuanian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.lt = {};

flatpickr.l10ns.lt.weekdays = {
	shorthand: ["S", "Pr", "A", "T", "K", "Pn", "<PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pirmadienis", "Antradie<PERSON>", "Trečiadienis", "Ketvirtadien<PERSON>", "Penktadienis", "Šeštadienis"]
};

flatpickr.l10ns.lt.months = {
	shorthand: ["Sau", "Vas", "Kov", "Bal", "Geg", "Bir", "Lie", "Rgp", "Rgs", "Spl", "Lap", "Grd"],
	longhand: ["<PERSON>us<PERSON>", "<PERSON><PERSON><PERSON>", "Kovas", "<PERSON>landis", "Gegužė", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rugpjūtis", "Rugsėjis", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]
};

flatpickr.l10ns.lt.firstDayOfWeek = 1;

flatpickr.l10ns.lt.ordinal = function () {
	return "-a";
};

flatpickr.l10ns.lt.weekAbbreviation = "Sav";
flatpickr.l10ns.lt.scrollTitle = "Keisti laiką pelės rateliu";
flatpickr.l10ns.lt.toggleTitle = "Perjungti laiko formatą";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;