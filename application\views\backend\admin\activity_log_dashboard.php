<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-bar-chart"></i> <?php echo get_phrase('activity_statistics'); ?></h3>
                </div>
            </div>
            <div class="panel-body">
                <div class="row" id="activity-stats">
                    <div class="col-md-3">
                        <div class="tile-stats tile-red">
                            <div class="icon"><i class="fa fa-calendar"></i></div>
                            <div class="num" id="today-count">0</div>
                            <h3><?php echo get_phrase('today_activities'); ?></h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="tile-stats tile-green">
                            <div class="icon"><i class="fa fa-calendar-week"></i></div>
                            <div class="num" id="week-count">0</div>
                            <h3><?php echo get_phrase('week_activities'); ?></h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="tile-stats tile-blue">
                            <div class="icon"><i class="fa fa-calendar-month"></i></div>
                            <div class="num" id="month-count">0</div>
                            <h3><?php echo get_phrase('month_activities'); ?></h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="tile-stats tile-purple">
                            <div class="icon"><i class="fa fa-users"></i></div>
                            <div class="num" id="active-users-count">0</div>
                            <h3><?php echo get_phrase('most_active_users'); ?></h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-list"></i> <?php echo get_phrase('recent_activities'); ?></h3>
                </div>
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered" id="recent-activities-table">
                        <thead>
                            <tr>
                                <th><?php echo get_phrase('timestamp'); ?></th>
                                <th><?php echo get_phrase('user'); ?></th>
                                <th><?php echo get_phrase('activity'); ?></th>
                                <th><?php echo get_phrase('details'); ?></th>
                                <th>IP Address</th>
                            </tr>
                        </thead>
                        <tbody id="recent-activities-body">
                            <tr>
                                <td colspan="5" class="text-center">
                                    <i class="fa fa-spinner fa-spin"></i> Loading activities...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-users"></i> <?php echo get_phrase('most_active_users'); ?></h3>
                </div>
            </div>
            <div class="panel-body">
                <div id="most-active-users">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading users...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-pie-chart"></i> Activity Breakdown</h3>
                </div>
            </div>
            <div class="panel-body">
                <div id="activity-breakdown">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading breakdown...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-search"></i> Quick Actions</h3>
                </div>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="<?php echo site_url('activity_log/user_search'); ?>" class="btn btn-primary btn-lg btn-block">
                            <i class="fa fa-search"></i><br>
                            <?php echo get_phrase('user_search_monitor'); ?>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="<?php echo site_url('admin/view_as_user'); ?>" class="btn btn-success btn-lg btn-block">
                            <i class="fa fa-eye"></i><br>
                            <?php echo get_phrase('view_as_user'); ?>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button onclick="exportAllLogs()" class="btn btn-warning btn-lg btn-block">
                            <i class="fa fa-download"></i><br>
                            <?php echo get_phrase('export_logs'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadActivityStats();
    loadRecentActivities();
    
    // Refresh data every 30 seconds
    setInterval(function() {
        loadActivityStats();
        loadRecentActivities();
    }, 30000);
});

function loadActivityStats() {
    $.post('<?php echo site_url('activity_log/get_activity_stats'); ?>', function(data) {
        var stats = JSON.parse(data);
        
        $('#today-count').text(stats.today_activities || 0);
        $('#week-count').text(stats.week_activities || 0);
        $('#month-count').text(stats.month_activities || 0);
        $('#active-users-count').text(stats.most_active_users ? stats.most_active_users.length : 0);
        
        // Update most active users
        var activeUsersHtml = '';
        if (stats.most_active_users && stats.most_active_users.length > 0) {
            stats.most_active_users.forEach(function(user) {
                activeUsersHtml += '<div class="user-activity-item">';
                activeUsersHtml += '<strong>' + user.user_name + '</strong> ';
                activeUsersHtml += '<span class="label label-info">' + user.user_type + '</span>';
                activeUsersHtml += '<span class="pull-right badge badge-primary">' + user.activity_count + '</span>';
                activeUsersHtml += '</div><hr>';
            });
        } else {
            activeUsersHtml = '<div class="text-center text-muted">No active users today</div>';
        }
        $('#most-active-users').html(activeUsersHtml);
        
        // Update activity breakdown
        var breakdownHtml = '';
        if (stats.activity_breakdown && stats.activity_breakdown.length > 0) {
            stats.activity_breakdown.forEach(function(activity) {
                breakdownHtml += '<div class="activity-breakdown-item">';
                breakdownHtml += '<strong>' + activity.activity + '</strong>';
                breakdownHtml += '<span class="pull-right badge badge-success">' + activity.count + '</span>';
                breakdownHtml += '</div><hr>';
            });
        } else {
            breakdownHtml = '<div class="text-center text-muted">No activities today</div>';
        }
        $('#activity-breakdown').html(breakdownHtml);
    });
}

function loadRecentActivities() {
    $.post('<?php echo site_url('activity_log/get_user_logs'); ?>', function(data) {
        var logs = JSON.parse(data);
        var html = '';

        if (logs && logs.length > 0) {
            logs.forEach(function(log) {
                html += '<tr>';
                html += '<td>' + new Date(log.timestamp * 1000).toLocaleString() + '</td>';
                html += '<td><span class="label label-info">' + log.user_type + '</span> ' + (log.user_name || 'Unknown') + '</td>';
                html += '<td>' + log.activity + '</td>';
                html += '<td>' + (log.details || '-') + '</td>';
                html += '<td>' + log.ip_address + '</td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="5" class="text-center text-muted">No recent activities</td></tr>';
        }

        $('#recent-activities-body').html(html);
    });
}

function exportAllLogs() {
    var today = new Date();
    var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    var url = '<?php echo site_url('activity_log/export_logs'); ?>' + 
              '?date_from=' + lastMonth.toISOString().split('T')[0] + 
              '&date_to=' + today.toISOString().split('T')[0];
    
    window.open(url, '_blank');
}
</script>

<style>
.tile-stats {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12);
    text-align: center;
}

.tile-stats .icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.tile-stats .num {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 5px;
}

.tile-red .icon { color: #e74c3c; }
.tile-green .icon { color: #27ae60; }
.tile-blue .icon { color: #3498db; }
.tile-purple .icon { color: #9b59b6; }

.user-activity-item, .activity-breakdown-item {
    padding: 5px 0;
}
</style>
