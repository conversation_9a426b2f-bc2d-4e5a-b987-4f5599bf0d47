<div class="row">
    <div class="col-md-12">
        
        <!-- View As User Panel -->
        <div class="panel panel-primary" data-collapsed="0">
            <div class="panel-heading">
                <div class="panel-title">
                    <i class="fa fa-eye"></i>
                    <?php echo get_phrase('view_as_user'); ?> - <?php echo get_phrase('user_monitoring'); ?>
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Warning Notice -->
                <div class="alert alert-warning">
                    <i class="fa fa-warning"></i>
                    <strong>Super Admin Feature:</strong> This feature allows you to view the system from any user's perspective for monitoring and support purposes. 
                    All activities are logged for security and audit purposes.
                </div>
                
                <!-- User Type Tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li class="active">
                        <a href="#doctors" role="tab" data-toggle="tab">
                            <i class="fa fa-user-md"></i> <?php echo get_phrase('doctor'); ?>s
                        </a>
                    </li>
                    <li>
                        <a href="#patients" role="tab" data-toggle="tab">
                            <i class="fa fa-user"></i> <?php echo get_phrase('patient'); ?>s
                        </a>
                    </li>
                    <li>
                        <a href="#nurses" role="tab" data-toggle="tab">
                            <i class="fa fa-plus-square"></i> <?php echo get_phrase('nurse'); ?>s
                        </a>
                    </li>
                    <li>
                        <a href="#pharmacists" role="tab" data-toggle="tab">
                            <i class="fa fa-medkit"></i> <?php echo get_phrase('pharmacist'); ?>s
                        </a>
                    </li>
                    <li>
                        <a href="#staff" role="tab" data-toggle="tab">
                            <i class="fa fa-users"></i> Other Staff
                        </a>
                    </li>
                    <li>
                        <a href="#activity_log" role="tab" data-toggle="tab">
                            <i class="fa fa-list"></i> <?php echo get_phrase('activity_log'); ?>
                        </a>
                    </li>
                </ul>
                
                <!-- Tab Content -->
                <div class="tab-content">
                    
                    <!-- Doctors Tab -->
                    <div class="tab-pane active" id="doctors">
                        <div class="table-responsive" style="margin-top: 20px;">
                            <table class="table table-striped table-bordered datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo get_phrase('photo'); ?></th>
                                        <th><?php echo get_phrase('name'); ?></th>
                                        <th><?php echo get_phrase('email'); ?></th>
                                        <th><?php echo get_phrase('department'); ?></th>
                                        <th><?php echo get_phrase('phone'); ?></th>
                                        <th><?php echo get_phrase('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $doctors = $this->db->select('doctor.*, department.name as department_name')
                                                       ->from('doctor')
                                                       ->join('department', 'department.department_id = doctor.department_id', 'left')
                                                       ->get()->result_array();
                                    foreach ($doctors as $doctor):
                                    ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $this->crud_model->get_image_url('doctor', $doctor['doctor_id']); ?>" 
                                                 class="img-circle" width="40" height="40">
                                        </td>
                                        <td><?php echo $doctor['name']; ?></td>
                                        <td><?php echo $doctor['email']; ?></td>
                                        <td><?php echo $doctor['department_name']; ?></td>
                                        <td><?php echo $doctor['phone']; ?></td>
                                        <td>
                                            <button class="btn btn-info btn-sm" onclick="switchToUser('doctor', <?php echo $doctor['doctor_id']; ?>, '<?php echo $doctor['name']; ?>')">
                                                <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Patients Tab -->
                    <div class="tab-pane" id="patients">
                        <div class="table-responsive" style="margin-top: 20px;">
                            <table class="table table-striped table-bordered datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo get_phrase('photo'); ?></th>
                                        <th><?php echo get_phrase('patient_number'); ?></th>
                                        <th><?php echo get_phrase('name'); ?></th>
                                        <th><?php echo get_phrase('email'); ?></th>
                                        <th><?php echo get_phrase('phone'); ?></th>
                                        <th><?php echo get_phrase('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $patients = $this->db->get('patient')->result_array();
                                    foreach ($patients as $patient):
                                    ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $this->crud_model->get_image_url('patient', $patient['patient_id']); ?>" 
                                                 class="img-circle" width="40" height="40">
                                        </td>
                                        <td><strong><?php echo $patient['code']; ?></strong></td>
                                        <td><?php echo $patient['name']; ?></td>
                                        <td><?php echo $patient['email']; ?></td>
                                        <td><?php echo $patient['phone']; ?></td>
                                        <td>
                                            <button class="btn btn-info btn-sm" onclick="switchToUser('patient', <?php echo $patient['patient_id']; ?>, '<?php echo $patient['name']; ?>')">
                                                <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Nurses Tab -->
                    <div class="tab-pane" id="nurses">
                        <div class="table-responsive" style="margin-top: 20px;">
                            <table class="table table-striped table-bordered datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo get_phrase('photo'); ?></th>
                                        <th><?php echo get_phrase('name'); ?></th>
                                        <th><?php echo get_phrase('email'); ?></th>
                                        <th><?php echo get_phrase('phone'); ?></th>
                                        <th><?php echo get_phrase('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $nurses = $this->db->get('nurse')->result_array();
                                    foreach ($nurses as $nurse):
                                    ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $this->crud_model->get_image_url('nurse', $nurse['nurse_id']); ?>" 
                                                 class="img-circle" width="40" height="40">
                                        </td>
                                        <td><?php echo $nurse['name']; ?></td>
                                        <td><?php echo $nurse['email']; ?></td>
                                        <td><?php echo $nurse['phone']; ?></td>
                                        <td>
                                            <button class="btn btn-info btn-sm" onclick="switchToUser('nurse', <?php echo $nurse['nurse_id']; ?>, '<?php echo $nurse['name']; ?>')">
                                                <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pharmacists Tab -->
                    <div class="tab-pane" id="pharmacists">
                        <div class="table-responsive" style="margin-top: 20px;">
                            <table class="table table-striped table-bordered datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo get_phrase('photo'); ?></th>
                                        <th><?php echo get_phrase('name'); ?></th>
                                        <th><?php echo get_phrase('email'); ?></th>
                                        <th><?php echo get_phrase('phone'); ?></th>
                                        <th><?php echo get_phrase('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $pharmacists = $this->db->get('pharmacist')->result_array();
                                    foreach ($pharmacists as $pharmacist):
                                    ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $this->crud_model->get_image_url('pharmacist', $pharmacist['pharmacist_id']); ?>" 
                                                 class="img-circle" width="40" height="40">
                                        </td>
                                        <td><?php echo $pharmacist['name']; ?></td>
                                        <td><?php echo $pharmacist['email']; ?></td>
                                        <td><?php echo $pharmacist['phone']; ?></td>
                                        <td>
                                            <button class="btn btn-info btn-sm" onclick="switchToUser('pharmacist', <?php echo $pharmacist['pharmacist_id']; ?>, '<?php echo $pharmacist['name']; ?>')">
                                                <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Other Staff Tab -->
                    <div class="tab-pane" id="staff">
                        <div style="margin-top: 20px;">

                            <!-- Laboratorists -->
                            <h4><i class="fa fa-flask"></i> <?php echo get_phrase('laboratorist'); ?>s</h4>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th><?php echo get_phrase('photo'); ?></th>
                                            <th><?php echo get_phrase('name'); ?></th>
                                            <th><?php echo get_phrase('email'); ?></th>
                                            <th><?php echo get_phrase('phone'); ?></th>
                                            <th><?php echo get_phrase('action'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $laboratorists = $this->db->get('laboratorist')->result_array();
                                        foreach ($laboratorists as $laboratorist):
                                        ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo $this->crud_model->get_image_url('laboratorist', $laboratorist['laboratorist_id']); ?>"
                                                     class="img-circle" width="40" height="40">
                                            </td>
                                            <td><?php echo $laboratorist['name']; ?></td>
                                            <td><?php echo $laboratorist['email']; ?></td>
                                            <td><?php echo $laboratorist['phone']; ?></td>
                                            <td>
                                                <button class="btn btn-info btn-sm" onclick="switchToUser('laboratorist', <?php echo $laboratorist['laboratorist_id']; ?>, '<?php echo $laboratorist['name']; ?>')">
                                                    <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Accountants -->
                            <h4><i class="fa fa-money"></i> <?php echo get_phrase('accountant'); ?>s</h4>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th><?php echo get_phrase('photo'); ?></th>
                                            <th><?php echo get_phrase('name'); ?></th>
                                            <th><?php echo get_phrase('email'); ?></th>
                                            <th><?php echo get_phrase('phone'); ?></th>
                                            <th><?php echo get_phrase('action'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $accountants = $this->db->get('accountant')->result_array();
                                        foreach ($accountants as $accountant):
                                        ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo $this->crud_model->get_image_url('accountant', $accountant['accountant_id']); ?>"
                                                     class="img-circle" width="40" height="40">
                                            </td>
                                            <td><?php echo $accountant['name']; ?></td>
                                            <td><?php echo $accountant['email']; ?></td>
                                            <td><?php echo $accountant['phone']; ?></td>
                                            <td>
                                                <button class="btn btn-info btn-sm" onclick="switchToUser('accountant', <?php echo $accountant['accountant_id']; ?>, '<?php echo $accountant['name']; ?>')">
                                                    <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Receptionists -->
                            <h4><i class="fa fa-user"></i> <?php echo get_phrase('receptionist'); ?>s</h4>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th><?php echo get_phrase('photo'); ?></th>
                                            <th><?php echo get_phrase('name'); ?></th>
                                            <th><?php echo get_phrase('email'); ?></th>
                                            <th><?php echo get_phrase('phone'); ?></th>
                                            <th><?php echo get_phrase('action'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $receptionists = $this->db->get('receptionist')->result_array();
                                        foreach ($receptionists as $receptionist):
                                        ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo $this->crud_model->get_image_url('receptionist', $receptionist['receptionist_id']); ?>"
                                                     class="img-circle" width="40" height="40">
                                            </td>
                                            <td><?php echo $receptionist['name']; ?></td>
                                            <td><?php echo $receptionist['email']; ?></td>
                                            <td><?php echo $receptionist['phone']; ?></td>
                                            <td>
                                                <button class="btn btn-info btn-sm" onclick="switchToUser('receptionist', <?php echo $receptionist['receptionist_id']; ?>, '<?php echo $receptionist['name']; ?>')">
                                                    <i class="fa fa-eye"></i> <?php echo get_phrase('switch_to_account'); ?>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Log Tab -->
                    <div class="tab-pane" id="activity_log">
                        <div class="table-responsive" style="margin-top: 20px;">
                            <table class="table table-striped table-bordered datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo get_phrase('timestamp'); ?></th>
                                        <th>Admin</th>
                                        <th>Target User</th>
                                        <th><?php echo get_phrase('action'); ?></th>
                                        <th>IP Address</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $logs = $this->db->select('admin_view_as_log.*, admin.name as admin_name')
                                                    ->from('admin_view_as_log')
                                                    ->join('admin', 'admin.admin_id = admin_view_as_log.admin_id', 'left')
                                                    ->order_by('timestamp', 'DESC')
                                                    ->limit(100)
                                                    ->get()->result_array();
                                    foreach ($logs as $log):
                                        // Get target user name
                                        $target_user = $this->db->get_where($log['target_user_type'], array($log['target_user_type'] . '_id' => $log['target_user_id']))->row();
                                        $target_name = $target_user ? $target_user->name : 'Unknown';
                                    ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d H:i:s', $log['timestamp']); ?></td>
                                        <td><?php echo $log['admin_name']; ?></td>
                                        <td>
                                            <span class="label label-info"><?php echo ucfirst($log['target_user_type']); ?></span>
                                            <?php echo $target_name; ?>
                                        </td>
                                        <td>
                                            <?php if ($log['action'] == 'started_viewing'): ?>
                                                <span class="label label-warning">Started Viewing</span>
                                            <?php else: ?>
                                                <span class="label label-success">Stopped Viewing</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $log['ip_address']; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function switchToUser(userType, userId, userName) {
    if (confirm('Are you sure you want to view the system as ' + userName + '?\n\nThis will switch you to their account view. You can return to admin using the "Return to Admin" button that will appear.')) {
        window.location.href = '<?php echo site_url('admin/view_as_user/switch'); ?>/' + userType + '/' + userId;
    }
}

$(document).ready(function() {
    // Initialize DataTables for better user experience
    $('.datatable').DataTable({
        "pageLength": 10,
        "ordering": true,
        "searching": true,
        "responsive": true
    });
});
</script>
