/* Slovak locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.sk = {};

flatpickr.l10ns.sk.weekdays = {
	shorthand: ["<PERSON>", "<PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>tv", "<PERSON><PERSON>", "Sob"],
	longhand: ["Ned<PERSON>ľ<PERSON>", "Pondelok", "Utorok", "Streda", "Štvrtok", "Piatok", "Sobota"]
};

flatpickr.l10ns.sk.months = {
	shorthand: ["Jan", "Feb", "Mar", "Apr", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Aug", "Sep", "Okt", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Október", "November", "December"]
};

flatpickr.l10ns.sk.firstDayOfWeek = 1;
flatpickr.l10ns.sk.rangeSeparator = " do ";
flatpickr.l10ns.sk.ordinal = function () {
	return ".";
};

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;