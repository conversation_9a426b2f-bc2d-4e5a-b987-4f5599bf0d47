<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-medkit"></i> <?php echo get_phrase('pharmacist'); ?> - <?php echo get_phrase('manage_medicine'); ?></h3>
                    <p>Manage medicines as a pharmacist would</p>
                </div>
            </div>
            <div class="panel-body">
                
                <button onclick="showAjaxModal('<?php echo site_url('modal/popup/add_medicine');?>');" 
                    class="btn btn-primary pull-right">
                        <i class="fa fa-plus"></i>&nbsp;<?php echo get_phrase('add_medicine'); ?>
                </button>
                <div style="clear:both;"></div>
                <br>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped datatable" id="table-2">
                        <thead>
                            <tr>
                                <th><?php echo get_phrase('name');?></th>
                                <th><?php echo get_phrase('category');?></th>
                                <th><?php echo get_phrase('description');?></th>
                                <th><?php echo get_phrase('price');?></th>
                                <th><?php echo get_phrase('manufacturing_company');?></th>
                                <th><?php echo get_phrase('status');?></th>
                                <th><?php echo get_phrase('options');?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($medicine_info) && is_array($medicine_info)): ?>
                                <?php foreach ($medicine_info as $row): ?>   
                                    <tr>
                                        <td><strong><?php echo $row['name']?></strong></td>
                                        <td>
                                            <span class="label label-info">
                                                <?php echo $this->db->get_where('medicine_category', array('medicine_category_id' => $row['medicine_category_id']))->row()->name; ?>
                                            </span>
                                        </td>
                                        <td><?php echo substr($row['description'], 0, 50) . '...'; ?></td>
                                        <td>
                                            <span class="label label-success">
                                                <?php echo $this->db->get_where('settings', array('type' => 'currency'))->row()->description; ?>
                                                <?php echo number_format($row['price'], 2); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $row['manufacturing_company']; ?></td>
                                        <td>
                                            <?php if ($row['status'] == 'available'): ?>
                                                <span class="label label-success">
                                                    <i class="fa fa-check"></i> <?php echo get_phrase('available'); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="label label-danger">
                                                    <i class="fa fa-times"></i> <?php echo get_phrase('unavailable'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a  onclick="showAjaxModal('<?php echo site_url('modal/popup/edit_medicine/'.$row['medicine_id']);?>');" 
                                                class="btn btn-info btn-sm">
                                                    <i class="fa fa-pencil"></i>&nbsp;
                                                    <?php echo get_phrase('edit');?>
                                            </a>
                                            <a onclick="confirm_modal('<?php echo site_url('role_operations/pharmacist_medicines/delete/'.$row['medicine_id']); ?>')"
                                                class="btn btn-danger btn-sm">
                                                    <i class="fa fa-trash-o"></i>&nbsp;
                                                    <?php echo get_phrase('delete');?>
                                            </a>
                                            <a onclick="toggle_status(<?php echo $row['medicine_id']; ?>, '<?php echo $row['status']; ?>')"
                                                class="btn btn-warning btn-sm">
                                                    <i class="fa fa-toggle-on"></i>&nbsp;
                                                    <?php echo get_phrase('toggle_status');?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No medicine data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="row" style="margin-top: 20px;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            <strong>Pharmacist Operations:</strong> You are performing medicine management operations as a pharmacist would. 
                            All actions are logged under your admin account for audit purposes.
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <a href="<?php echo site_url('role_operations'); ?>" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Back to Role Operations
                        </a>
                        <a href="<?php echo site_url('admin/medicine'); ?>" class="btn btn-success">
                            <i class="fa fa-list"></i> View All Medicines
                        </a>
                        <a href="<?php echo site_url('admin/prescription'); ?>" class="btn btn-primary">
                            <i class="fa fa-file-text"></i> View Prescriptions
                        </a>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

<script>
function toggle_status(medicine_id, current_status) {
    var new_status = (current_status == 'available') ? 'unavailable' : 'available';
    var action_text = (new_status == 'available') ? 'make available' : 'make unavailable';
    
    if (confirm('Are you sure you want to ' + action_text + ' this medicine?')) {
        $.post('<?php echo site_url('admin/toggle_medicine_status'); ?>/' + medicine_id + '/' + new_status, function(data) {
            location.reload();
        });
    }
}
</script>
