<button onclick="showAjaxModal('<?php echo site_url('modal/popup/add_patient');?>');"
    class="btn btn-primary pull-right">
        <i class="fa fa-plus"></i>&nbsp;<?php echo get_phrase('add_patient'); ?>
</button>
<div style="clear:both;"></div>
<br>
<table class="table table-bordered table-striped datatable" id="table-2">
    <thead>
        <tr>
            <th><?php echo get_phrase('image');?></th>
            <th><?php echo get_phrase('patient_number');?></th>
            <th><?php echo get_phrase('name');?></th>
            <th><?php echo get_phrase('email');?></th>
            <th><?php echo get_phrase('phone');?></th>
            <th><?php echo get_phrase('sex');?></th>
            <th><?php echo get_phrase('birth_date');?></th>
            <th><?php echo get_phrase('age');?></th>
            <th><?php echo get_phrase('blood_group');?></th>
            <th><?php echo get_phrase('options');?></th>
        </tr>
    </thead>

    <tbody>
        <?php if (isset($patient_info) && is_array($patient_info)): ?>
            <?php foreach ($patient_info as $row): ?>
                <tr>
                    <td><img src="<?php echo $this->crud_model->get_image_url('patient' , $row['patient_id']);?>" class="img-circle" width="40px" height="40px"></td>
                    <td><strong><?php echo $row['code']?></strong></td>
                    <td><?php echo $row['name']?></td>
                    <td><?php echo $row['email']?></td>
                    <td><?php echo $row['phone']?></td>
                    <td><?php echo $row['sex']?></td>
                    <td><?php
                        if (is_numeric($row['birth_date'])) {
                            echo date('d/m/Y', $row['birth_date']);
                        } else {
                            echo date('d/m/Y', strtotime($row['birth_date']));
                        }
                    ?></td>
                    <td><?php echo $row['age']?></td>
                    <td><?php echo $row['blood_group']?></td>
                    <td>
                        <a  onclick="showAjaxModal('<?php echo site_url('modal/popup/edit_patient/'.$row['patient_id']);?>');"
                            class="btn btn-info btn-sm">
                                <i class="fa fa-pencil"></i>&nbsp;
                                <?php echo get_phrase('edit');?>
                        </a>
                        <a onclick="confirm_modal('<?php echo site_url('admin/patient/delete/'.$row['patient_id']); ?>')"
                            class="btn btn-danger btn-sm">
                                <i class="fa fa-trash-o"></i>&nbsp;
                                <?php echo get_phrase('delete');?>
                        </a>
                        <a onclick="viewAsUser('patient', <?php echo $row['patient_id']; ?>, '<?php echo $row['name']; ?>')"
                            class="btn btn-warning btn-sm" title="<?php echo get_phrase('view_as_user'); ?>">
                            <i class="fa fa-eye"></i>&nbsp;<?php echo get_phrase('view_as'); ?>
                        </a>
                        <a onclick="viewAsUser('patient', <?php echo $row['patient_id']; ?>, '<?php echo $row['name']; ?>')"
                            class="btn btn-warning btn-sm" title="<?php echo get_phrase('view_as_user'); ?>">
                                <i class="fa fa-eye"></i>&nbsp;
                                <?php echo get_phrase('view_as'); ?>
                        </a>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="10">No patient data available</td>
            </tr>
        <?php endif; ?>
    </tbody>
</table>

<script>
function viewAsUser(userType, userId, userName) {
    if (confirm('Are you sure you want to view the system as ' + userName + '?\n\nThis will switch you to their account view. You can return to admin using the "Return to Admin" button that will appear.')) {
        window.location.href = '<?php echo site_url('admin/view_as_user/switch'); ?>/' + userType + '/' + userId;
    }
}
</script>

<script type="text/javascript">
    jQuery(window).load(function ()
    {
        var $ = jQuery;

        $("#table-2").dataTable({
            "sPaginationType": "bootstrap",
            "sDom": "<'row'<'col-xs-3 col-left'l><'col-xs-9 col-right'<'export-data'T>f>r>t<'row'<'col-xs-3 col-left'i><'col-xs-9 col-right'p>>"
        });

        $(".dataTables_wrapper select").select2({
            minimumResultsForSearch: -1
        });

        // Highlighted rows
        $("#table-2 tbody input[type=checkbox]").each(function (i, el)
        {
            var $this = $(el),
                    $p = $this.closest('tr');

            $(el).on('change', function ()
            {
                var is_checked = $this.is(':checked');

                $p[is_checked ? 'addClass' : 'removeClass']('highlight');
            });
        });

        // Replace Checboxes
        $(".pagination a").click(function (ev)
        {
            replaceCheckboxes();
        });
    });
</script>