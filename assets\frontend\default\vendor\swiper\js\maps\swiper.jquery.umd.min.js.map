{"version": 3, "sources": ["swiper.jquery.umd.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "require", "Swiper", "j<PERSON><PERSON><PERSON>", "this", "$", "addLibraryPlugin", "lib", "fn", "swiper", "params", "firstInstance", "each", "s", "container", "round", "a", "Math", "floor", "autoplay", "autoplayDelay", "activeSlide", "slides", "eq", "activeIndex", "attr", "autoplayTimeoutId", "setTimeout", "loop", "fixLoop", "_slideNext", "emit", "isEnd", "autoplayStopOnLast", "stopAutoplay", "_slideTo", "findElementInEvent", "e", "selector", "el", "target", "is", "parents", "nodeType", "found", "index", "_el", "length", "initObserver", "options", "ObserverFunc", "window", "MutationObserver", "WebkitMutationObserver", "observer", "mutations", "for<PERSON>ach", "mutation", "onResize", "observe", "attributes", "childList", "characterData", "observers", "push", "handleKeyboard", "originalEvent", "kc", "keyCode", "charCode", "allowSwipeToNext", "isHorizontal", "allowSwipeToPrev", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "document", "activeElement", "nodeName", "toLowerCase", "inView", "slideClass", "slideActiveClass", "windowScroll", "left", "pageXOffset", "top", "pageYOffset", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "swiperOffset", "offset", "rtl", "scrollLeft", "swiperCoord", "width", "height", "i", "point", "preventDefault", "returnValue", "slideNext", "slidePrev", "isEventSupported", "eventName", "isSupported", "element", "createElement", "setAttribute", "implementation", "hasFeature", "handleMousewheel", "delta", "rtlFactor", "data", "normalizeWheel", "mousewheelForceToAxis", "abs", "pixelX", "pixelY", "mousewheelInvert", "freeMode", "position", "getWrapperTranslate", "mousewheelSensitivity", "wasBeginning", "isBeginning", "wasEnd", "minTranslate", "maxTranslate", "setWrapperTransition", "setWrapperTranslate", "updateProgress", "updateActiveIndex", "updateClasses", "freeModeSticky", "clearTimeout", "mousewheel", "timeout", "slideReset", "lazyLoading", "lazy", "load", "autoplayDisableOnInteraction", "Date", "getTime", "lastScrollTime", "animating", "mousewheelReleaseOnEdges", "event", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "setParallaxTransform", "progress", "p", "indexOf", "parseInt", "transform", "normalizeEventName", "toUpperCase", "substring", "defaults", "direction", "touchEventsTarget", "initialSlide", "speed", "iOSEdgeSwipeDetection", "iOSEdgeSwipeThreshold", "freeModeMomentum", "freeModeMomentumRatio", "freeModeMomentumBounce", "freeModeMomentumBounceRatio", "freeModeMomentumVelocityRatio", "freeModeMinimumVelocity", "autoHeight", "setWrapperSize", "virtualTranslate", "effect", "coverflow", "rotate", "stretch", "depth", "modifier", "slideShadows", "flip", "limitRotation", "cube", "shadow", "shadowOffset", "shadowScale", "fade", "crossFade", "parallax", "zoom", "zoomMax", "zoomMin", "zoomToggle", "scrollbar", "scrollbarHide", "scrollbarDraggable", "scrollbarSnapOnRelease", "keyboardControl", "mousewheelControl", "mousewheelEventsTarged", "<PERSON><PERSON><PERSON>", "hashnavWatchState", "history", "replaceState", "breakpoints", "undefined", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumn", "slidesPerColumnFill", "slidesPerGroup", "centeredSlides", "slidesOffsetBefore", "slidesOffsetAfter", "roundLengths", "touchRatio", "touchAngle", "simulate<PERSON>ouch", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "onlyEx<PERSON><PERSON>", "threshold", "touchMoveStopPropagation", "touchReleaseOnEdges", "uniqueNavElements", "pagination", "paginationElement", "paginationClickable", "paginationHide", "paginationBulletRender", "paginationProgressRender", "paginationFractionRender", "paginationCustomRender", "paginationType", "resistance", "resistanceRatio", "nextButton", "prevButton", "watchSlidesProgress", "watchSlidesVisibility", "grabCursor", "preventClicks", "preventClicksPropagation", "slideToClickedSlide", "lazyLoadingInPrevNext", "lazyLoadingInPrevNextAmount", "lazyLoadingOnTransitionStart", "preloadImages", "updateOnImagesReady", "loopAdditionalSlides", "loopedSlides", "control", "controlInverse", "controlBy", "normalizeSlideIndex", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "passiveListeners", "containerModifierClass", "slideDuplicateActiveClass", "slideVisibleClass", "slideDuplicateClass", "slideNextClass", "slideDuplicateNextClass", "slidePrevClass", "slideDuplicatePrevClass", "wrapperClass", "bulletClass", "bulletActiveClass", "buttonDisabledClass", "paginationCurrentClass", "paginationTotalClass", "paginationHiddenClass", "paginationProgressbarClass", "paginationClickableClass", "paginationModifierClass", "lazyLoadingClass", "lazyStatusLoadingClass", "lazyStatusLoadedClass", "lazyPreloaderClass", "notificationClass", "preloaderClass", "zoomContainerClass", "observeParents", "a11y", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "runCallbacksOnInit", "initialVirtualTranslate", "originalParams", "param", "Dom7", "deepParam", "def", "deepDef", "classNames", "Zepto", "currentBreakpoint", "getActiveBreakpoint", "breakpoint", "points", "hasOwnProperty", "sort", "b", "setBreakpoint", "breakPointsParams", "needsReLoop", "destroyLoop", "reLoop", "swipers", "support", "flexbox", "transforms3d", "touch", "wrapper", "children", "paginationContainer", "find", "addClass", "dir", "css", "wrongRTL", "device", "android", "join", "translate", "velocity", "lockSwipeToNext", "unsetGrabCursor", "lockSwipeToPrev", "lockSwipes", "unlockSwipeToNext", "setGrabCursor", "unlockSwipeToPrev", "unlockSwipes", "moving", "style", "cursor", "imagesToLoad", "imagesLoaded", "loadImage", "imgElement", "src", "srcset", "sizes", "checkForComplete", "callback", "onReady", "image", "complete", "Image", "onload", "onerror", "_onReady", "update", "currentSrc", "getAttribute", "autoplaying", "autoplayPaused", "startAutoplay", "internal", "pauseAutoplay", "transitionEnd", "snapGrid", "updateAutoHeight", "activeSlides", "newHeight", "ceil", "offsetHeight", "updateContainerSize", "clientWidth", "clientHeight", "size", "updateSlidesSize", "slidesGrid", "slidesSizesGrid", "slidePosition", "prevSlideSize", "parseFloat", "replace", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesNumberEvenToRows", "max", "slideSize", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "outerWidth", "outerHeight", "swiperSlideSize", "newSlidesGrid", "updateSlidesOffset", "swiperSlideOffset", "offsetLeft", "offsetTop", "currentSlidesPerView", "j", "spv", "breakLoop", "updateSlidesProgress", "offsetCenter", "removeClass", "slideProgress", "slideBefore", "slideAfter", "isVisible", "translatesDiff", "newActiveIndex", "snapIndex", "previousIndex", "updateRealIndex", "realIndex", "hasClass", "nextSlide", "next", "prevSlide", "prev", "current", "total", "bullets", "text", "scale", "scaleX", "scaleY", "transition", "html", "disable", "enable", "updatePagination", "paginationHTML", "numberOfBullets", "initPagination", "updateTranslate", "forceSetTranslate", "newTranslate", "min", "set", "translated", "controller", "spline", "slideTo", "forceUpdatePagination", "slideChangedBySlideTo", "touchEventsDesktop", "start", "move", "end", "navigator", "pointer<PERSON><PERSON>bled", "msPointer<PERSON><PERSON><PERSON>", "touchEvents", "initEvents", "detach", "actionDom", "action", "moveCapture", "nested", "browser", "ie", "onTouchStart", "onTouchMove", "onTouchEnd", "passiveListener", "passive", "capture", "ios", "onClickNext", "onEnterKey", "onClickPrev", "onClickIndex", "attachEvents", "detachEvents", "allowClick", "stopPropagation", "stopImmediatePropagation", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToIndex", "isTouched", "isMoved", "allowTouchCallbacks", "touchStartTime", "isScrolling", "currentTranslate", "startTranslate", "allowThresholdMove", "clickTimeout", "allowMomentumBounce", "formElements", "lastClickTime", "now", "velocities", "touches", "startX", "startY", "currentX", "currentY", "diff", "isTouchEvent", "startMoving", "type", "which", "targetTouches", "pageX", "pageY", "swipeDirection", "blur", "preventedByNestedSwiper", "atan2", "PI", "ieTouch", "trigger", "disableParentSwiper", "pow", "time", "touchEndTime", "timeDiff", "toggleClass", "currentPos", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "onTransitionStart", "onTransitionEnd", "stopIndex", "groupSize", "ratio", "slideIndex", "runCallbacks", "lteIE9", "setHistory", "setHash", "clientLeft", "_slidePrev", "disableTouchControl", "enableTouchControl", "duration", "byController", "effects", "setTransition", "x", "y", "z", "setTranslate", "getTranslate", "matrix", "curTransform", "curStyle", "transformMatrix", "getComputedStyle", "WebKitCSSMatrix", "webkitTransform", "split", "map", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "m41", "m42", "initObservers", "containerParents", "disconnectObservers", "disconnect", "createLoop", "remove", "prependSlides", "appendSlides", "append", "cloneNode", "prepend", "removeAttr", "updatePosition", "oldIndex", "newIndex", "appendSlide", "prependSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "tx", "ty", "slideOpacity", "opacity", "eventTriggered", "triggerEvents", "rotateY", "rotateX", "zIndex", "shadowBefore", "shadowAfter", "cubeShadow", "wrapperRotate", "slideAngle", "tz", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowAngle", "multiplier", "sin", "cos", "scale1", "scale2", "zFactor", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "center", "slideOffset", "offsetMultiplier", "translateZ", "translateY", "translateX", "slideTransform", "ws", "<PERSON><PERSON><PERSON><PERSON>", "initialImageLoaded", "loadImageInSlide", "loadInDuplicate", "img", "add", "_img", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "amount", "maxIndex", "minIndex", "setDragPosition", "sb", "pointerPosition", "clientX", "clientY", "track", "dragSize", "positionMin", "moveDivider", "positionMax", "dragStart", "dragTimeout", "drag", "dragMove", "dragEnd", "draggableEvents", "enableDraggable", "on", "disableDraggable", "off", "trackSize", "offsetWidth", "divider", "display", "newPos", "newSize", "LinearSpline", "lastIndex", "i1", "i3", "interpolate", "x2", "binarySearch", "guess", "array", "val", "getInterpolateFunction", "c", "setControlledTranslate", "controlledTranslate", "controlled", "isArray", "setControlledTransition", "onHashCange", "newHash", "location", "hash", "activeSlideHash", "initialized", "init", "slideHash", "destroy", "pushState", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "key", "value", "scrollToSlide", "addEventListener", "setHistoryPopState", "pathArray", "pathname", "slice", "slugify", "includes", "slideHistory", "disableKeyboardControl", "enableKeyboardControl", "userAgent", "disableMousewheelControl", "enableMousewheelControl", "parallaxDuration", "currentScale", "isScaling", "gesture", "slideWidth", "slideHeight", "imageWrap", "minX", "minY", "maxX", "maxY", "touchesStart", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "getDistanceBetweenTouches", "x1", "y1", "y2", "sqrt", "onGestureStart", "gestures", "scaleStart", "parent", "onGestureChange", "scaleMove", "onGestureEnd", "changedTouches", "os", "scaledWidth", "scaledHeight", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "toggleZoom", "touchX", "touchY", "offsetX", "offsetY", "diffX", "diffY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "_plugins", "plugin", "plugins", "callPlugins", "arguments", "emitterEventListeners", "handler", "splice", "once", "_handler", "makeFocusable", "$el", "addRole", "role", "addLabel", "label", "notify", "click", "liveRegion", "message", "notification", "bullet", "hashnavReplaceState", "cleanupStyles", "deleteInstance", "removeEventListener", "prototype", "ua", "test", "arr", "Object", "apply", "msMaxTouchPoints", "maxTouchPoints", "div", "innerHTML", "getElementsByTagName", "match", "ipad", "ipod", "iphone", "Modernizr", "DocumentTouch", "csstransforms3d", "styles", "supportsPassive", "opts", "defineProperty", "get", "domLib", "fireCallBack", "call", "events", "dom", "elStyle", "webkitTransitionDuration", "MsTransitionDuration", "msTransitionDuration", "MozTransitionDuration", "OTransitionDuration", "transitionDuration", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;CAcC,SAAUA,EAAMC,GAChB,YAEsB,mBAAXC,SAAyBA,OAAOC,IAE1CD,QAAQ,UAAWD,GACU,gBAAZG,SAIjBC,OAAOD,QAAUH,EAAQK,QAAQ,WAGjCN,EAAKO,OAASN,EAAQD,EAAKQ,SAE3BC,KAAM,SAAUC,GACjB,YAi9IG,SAASC,GAAiBC,GACtBA,EAAIC,GAAGC,OAAS,SAAUC,GACtB,GAAIC,EAKJ,OAJAJ,GAAIH,MAAMQ,KAAK,WACX,GAAIC,GAAI,GAAIX,GAAOE,KAAMM,EACpBC,KAAeA,EAAgBE,KAEjCF,GAn9If,GAAIT,GAAS,SAAUY,EAAWJ,GAqgB9B,QAASK,GAAMC,GACX,MAAOC,MAAKC,MAAMF,GAuEtB,QAASG,KACL,GAAIC,GAAgBP,EAAEH,OAAOS,SACzBE,EAAcR,EAAES,OAAOC,GAAGV,EAAEW,YAC5BH,GAAYI,KAAK,0BACjBL,EAAgBC,EAAYI,KAAK,yBAA2BZ,EAAEH,OAAOS,UAEzEN,EAAEa,kBAAoBC,WAAW,WACzBd,EAAEH,OAAOkB,MACTf,EAAEgB,UACFhB,EAAEiB,aACFjB,EAAEkB,KAAK,aAAclB,IAGhBA,EAAEmB,MAKEtB,EAAOuB,mBAKRpB,EAAEqB,gBAJFrB,EAAEsB,SAAS,GACXtB,EAAEkB,KAAK,aAAclB,KANzBA,EAAEiB,aACFjB,EAAEkB,KAAK,aAAclB,KAY9BO,GAqxBP,QAASgB,GAAmBC,EAAGC,GAC3B,GAAIC,GAAKlC,EAAEgC,EAAEG,OACb,KAAKD,EAAGE,GAAGH,GACP,GAAwB,gBAAbA,GACPC,EAAKA,EAAGG,QAAQJ,OAEf,IAAIA,EAASK,SAAU,CACxB,GAAIC,EAIJ,OAHAL,GAAGG,UAAU9B,KAAK,SAAUiC,EAAOC,GAC3BA,IAAQR,IAAUM,EAAQN,KAE7BM,EACON,EADA,OAIpB,GAAkB,IAAdC,EAAGQ,OAGP,MAAOR,GAAG,GAm2Bd,QAASS,GAAaR,EAAQS,GAC1BA,EAAUA,KAEV,IAAIC,GAAeC,OAAOC,kBAAoBD,OAAOE,uBACjDC,EAAW,GAAIJ,GAAa,SAAUK,GACtCA,EAAUC,QAAQ,SAAUC,GACxB5C,EAAE6C,UAAS,GACX7C,EAAEkB,KAAK,mBAAoBlB,EAAG4C,MAItCH,GAASK,QAAQnB,GACboB,WAA0C,mBAAvBX,GAAQW,YAAoCX,EAAQW,WACvEC,UAAwC,mBAAtBZ,GAAQY,WAAmCZ,EAAQY,UACrEC,cAAgD,mBAA1Bb,GAAQa,eAAuCb,EAAQa,gBAGjFjD,EAAEkD,UAAUC,KAAKV,GAo+BrB,QAASW,GAAe5B,GAChBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIC,GAAK9B,EAAE+B,SAAW/B,EAAEgC,QAExB,KAAKxD,EAAEH,OAAO4D,mBAAqBzD,EAAE0D,gBAAyB,KAAPJ,IAActD,EAAE0D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,KAAKtD,EAAEH,OAAO8D,mBAAqB3D,EAAE0D,gBAAyB,KAAPJ,IAActD,EAAE0D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,MAAI9B,EAAEoC,UAAYpC,EAAEqC,QAAUrC,EAAEsC,SAAWtC,EAAEuC,SAGzCC,SAASC,eAAiBD,SAASC,cAAcC,WAA+D,UAAlDF,SAASC,cAAcC,SAASC,eAA+E,aAAlDH,SAASC,cAAcC,SAASC,gBAA/J,CAGA,GAAW,KAAPb,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,EAAW,CAClD,GAAIc,IAAS,CAEb,IAAIpE,EAAEC,UAAU4B,QAAQ,IAAM7B,EAAEH,OAAOwE,YAAYnC,OAAS,GAAqE,IAAhElC,EAAEC,UAAU4B,QAAQ,IAAM7B,EAAEH,OAAOyE,kBAAkBpC,OAClH,MAEJ,IAAIqC,IACAC,KAAMlC,OAAOmC,YACbC,IAAKpC,OAAOqC,aAEZC,EAActC,OAAOuC,WACrBC,EAAexC,OAAOyC,YACtBC,EAAehF,EAAEC,UAAUgF,QAC3BjF,GAAEkF,MAAKF,EAAaR,KAAOQ,EAAaR,KAAOxE,EAAEC,UAAU,GAAGkF,WAOlE,KAAK,GANDC,KACCJ,EAAaR,KAAMQ,EAAaN,MAChCM,EAAaR,KAAOxE,EAAEqF,MAAOL,EAAaN,MAC1CM,EAAaR,KAAMQ,EAAaN,IAAM1E,EAAEsF,SACxCN,EAAaR,KAAOxE,EAAEqF,MAAOL,EAAaN,IAAM1E,EAAEsF,SAE9CC,EAAI,EAAGA,EAAIH,EAAYlD,OAAQqD,IAAK,CACzC,GAAIC,GAAQJ,EAAYG,EAEpBC,GAAM,IAAMjB,EAAaC,MAAQgB,EAAM,IAAMjB,EAAaC,KAAOI,GACjEY,EAAM,IAAMjB,EAAaG,KAAOc,EAAM,IAAMjB,EAAaG,IAAMI,IAE/DV,GAAS,GAIjB,IAAKA,EAAQ,OAEbpE,EAAE0D,gBACS,KAAPJ,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,IAEb,KAAPpC,IAActD,EAAEkF,KAAgB,KAAP5B,GAAatD,EAAEkF,MAAMlF,EAAE2F,aACzC,KAAPrC,IAActD,EAAEkF,KAAgB,KAAP5B,GAAatD,EAAEkF,MAAMlF,EAAE4F,cAG1C,KAAPtC,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GAEd,KAAPpC,GAAWtD,EAAE2F,YACN,KAAPrC,GAAWtD,EAAE4F,cAgCzB,QAASC,KACL,GAAIC,GAAY,UACZC,EAAcD,IAAa9B,SAE/B,KAAK+B,EAAa,CACd,GAAIC,GAAUhC,SAASiC,cAAc,MACrCD,GAAQE,aAAaJ,EAAW,WAChCC,EAA4C,kBAAvBC,GAAQF,GAajC,OAVKC,GACD/B,SAASmC,gBACTnC,SAASmC,eAAeC,YAGxBpC,SAASmC,eAAeC,WAAW,GAAI,OAAQ,IAE/CL,EAAc/B,SAASmC,eAAeC,WAAW,eAAgB,QAG9DL,EAGX,QAASM,GAAiB7E,GAClBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIiD,GAAQ,EACRC,EAAYvG,EAAEkF,KAAM,EAAK,EAEzBsB,EAAOC,EAAgBjF,EAE3B,IAAIxB,EAAEH,OAAO6G,sBACT,GAAI1G,EAAE0D,eAAgB,CAClB,KAAItD,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SACrC,MAD8CP,GAAQE,EAAKI,OAASL,MAGxE,CACD,KAAInG,KAAKuG,IAAIH,EAAKK,QAAUzG,KAAKuG,IAAIH,EAAKI,SACrC,MAD8CN,GAAQE,EAAKK,WAKpEP,GAAQlG,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SAAYL,EAAKI,OAASL,GAAcC,EAAKK,MAG/F,IAAc,IAAVP,EAAJ,CAIA,GAFItG,EAAEH,OAAOiH,mBAAkBR,GAASA,GAEnCtG,EAAEH,OAAOkH,SAoBT,CAED,GAAIC,GAAWhH,EAAEiH,sBAAwBX,EAAQtG,EAAEH,OAAOqH,sBACtDC,EAAenH,EAAEoH,YACjBC,EAASrH,EAAEmB,KAgCf,IA9BI6F,GAAYhH,EAAEsH,iBAAgBN,EAAWhH,EAAEsH,gBAC3CN,GAAYhH,EAAEuH,iBAAgBP,EAAWhH,EAAEuH,gBAE/CvH,EAAEwH,qBAAqB,GACvBxH,EAAEyH,oBAAoBT,GACtBhH,EAAE0H,iBACF1H,EAAE2H,sBAEGR,GAAgBnH,EAAEoH,cAAgBC,GAAUrH,EAAEmB,QAC/CnB,EAAE4H,gBAGF5H,EAAEH,OAAOgI,gBACTC,aAAa9H,EAAE+H,WAAWC,SAC1BhI,EAAE+H,WAAWC,QAAUlH,WAAW,WAC9Bd,EAAEiI,cACH,MAGCjI,EAAEH,OAAOqI,aAAelI,EAAEmI,MAC1BnI,EAAEmI,KAAKC,OAIfpI,EAAEkB,KAAK,WAAYlB,EAAGwB,GAGlBxB,EAAEH,OAAOS,UAAYN,EAAEH,OAAOwI,8BAA8BrI,EAAEqB,eAGjD,IAAb2F,GAAkBA,IAAahH,EAAEuH,eAAgB,WAxDjC,CACpB,IAAI,GAAKjF,QAAOgG,MAAQC,UAAYvI,EAAE+H,WAAWS,eAAiB,GAC9D,GAAIlC,EAAQ,EACR,GAAMtG,EAAEmB,QAASnB,EAAEH,OAAOkB,MAAUf,EAAEyI,WAIjC,GAAIzI,EAAEH,OAAO6I,yBAA0B,OAAO,MAH/C1I,GAAE2F,YACF3F,EAAEkB,KAAK,WAAYlB,EAAGwB,OAK1B,IAAMxB,EAAEoH,cAAepH,EAAEH,OAAOkB,MAAUf,EAAEyI,WAIvC,GAAIzI,EAAEH,OAAO6I,yBAA0B,OAAO,MAH/C1I,GAAE4F,YACF5F,EAAEkB,KAAK,WAAYlB,EAAGwB,EAKlCxB,GAAE+H,WAAWS,gBAAiB,GAAKlG,QAAOgG,MAAQC,UA4CtD,MAFI/G,GAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GACd,GA0HX,QAASe,GAA2BkC,GAEhC,GAAIC,GAAa,GACbC,EAAc,GACdC,EAAc,IAEdC,EAAK,EAAGC,EAAK,EACbC,EAAK,EAAGC,EAAK,CAkDjB,OA/CI,UAAYP,KACZK,EAAKL,EAAMQ,QAEX,cAAgBR,KAChBK,GAAML,EAAMS,WAAa,KAEzB,eAAiBT,KACjBK,GAAML,EAAMU,YAAc,KAE1B,eAAiBV,KACjBI,GAAMJ,EAAMW,YAAc,KAI1B,QAAUX,IAASA,EAAMY,OAASZ,EAAMa,kBACxCT,EAAKC,EACLA,EAAK,GAGTC,EAAKF,EAAKH,EACVM,EAAKF,EAAKJ,EAEN,UAAYD,KACZO,EAAKP,EAAMc,QAEX,UAAYd,KACZM,EAAKN,EAAMe,SAGVT,GAAMC,IAAOP,EAAMgB,YACI,IAApBhB,EAAMgB,WACNV,GAAMJ,EACNK,GAAML,IAENI,GAAMH,EACNI,GAAMJ,IAKVG,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,GAErBC,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,IAIrBU,MAAOb,EACPc,MAAOb,EACPpC,OAAQqC,EACRpC,OAAQqC,GAOhB,QAASY,GAAqBpI,EAAIqI,GAC9BrI,EAAKlC,EAAEkC,EACP,IAAIsI,GAAGf,EAAIC,EACP3C,EAAYvG,EAAEkF,KAAM,EAAK,CAE7B8E,GAAItI,EAAGd,KAAK,yBAA2B,IACvCqI,EAAKvH,EAAGd,KAAK,0BACbsI,EAAKxH,EAAGd,KAAK,0BACTqI,GAAMC,GACND,EAAKA,GAAM,IACXC,EAAKA,GAAM,KAGPlJ,EAAE0D,gBACFuF,EAAKe,EACLd,EAAK,MAGLA,EAAKc,EACLf,EAAK,KAKTA,EADA,EAAKgB,QAAQ,MAAQ,EAChBC,SAASjB,EAAI,IAAMc,EAAWxD,EAAY,IAG1C0C,EAAKc,EAAWxD,EAAY,KAGjC2C,EADA,EAAKe,QAAQ,MAAQ,EAChBC,SAAShB,EAAI,IAAMa,EAAW,IAG9Bb,EAAKa,EAAW,KAGzBrI,EAAGyI,UAAU,eAAiBlB,EAAK,KAAOC,EAAK,SA2ZnD,QAASkB,GAAoBtE,GASzB,MARgC,KAA5BA,EAAUmE,QAAQ,QAEdnE,EADAA,EAAU,KAAOA,EAAU,GAAGuE,cAClB,KAAOvE,EAAU,GAAGuE,cAAgBvE,EAAUwE,UAAU,GAGxD,KAAOxE,GAGpBA,EAvjIX,KAAMvG,eAAgBF,IAAS,MAAO,IAAIA,GAAOY,EAAWJ,EAE5D,IAAI0K,IACAC,UAAW,aACXC,kBAAmB,YACnBC,aAAc,EACdC,MAAO,IAEPrK,UAAU,EACV+H,8BAA8B,EAC9BjH,oBAAoB,EAEpBwJ,uBAAuB,EACvBC,sBAAuB,GAEvB9D,UAAU,EACV+D,kBAAkB,EAClBC,sBAAuB,EACvBC,wBAAwB,EACxBC,4BAA6B,EAC7BC,8BAA+B,EAC/BrD,gBAAgB,EAChBsD,wBAAyB,IAEzBC,YAAY,EAEZC,gBAAgB,EAEhBC,kBAAkB,EAElBC,OAAQ,QACRC,WACIC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPC,SAAU,EACVC,cAAe,GAEnBC,MACID,cAAe,EACfE,eAAe,GAEnBC,MACIH,cAAc,EACdI,QAAQ,EACRC,aAAc,GACdC,YAAa,KAEjBC,MACIC,WAAW,GAGfC,UAAU,EAEVC,MAAM,EACNC,QAAS,EACTC,QAAS,EACTC,YAAY,EAEZC,UAAW,KACXC,eAAe,EACfC,oBAAoB,EACpBC,wBAAwB,EAExBC,iBAAiB,EACjBC,mBAAmB,EACnBtE,0BAA0B,EAC1B5B,kBAAkB,EAClBJ,uBAAuB,EACvBQ,sBAAuB,EACvB+F,uBAAwB,YAExBC,SAAS,EACTC,mBAAmB,EAEnBC,SAAS,EAETC,cAAc,EAEdC,YAAaC,OAEbC,aAAc,EACdC,cAAe,EACfC,gBAAiB,EACjBC,oBAAqB,SACrBC,eAAgB,EAChBC,gBAAgB,EAChBC,mBAAoB,EACpBC,kBAAmB,EAEnBC,cAAc,EAEdC,WAAY,EACZC,WAAY,GACZC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,gBAAiB,GACjBC,aAAc,IACdC,cAAc,EACdC,cAAc,EACdC,UAAW,EACXC,0BAA0B,EAC1BC,qBAAqB,EAErBC,mBAAmB,EAEnBC,WAAY,KACZC,kBAAmB,OACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAwB,KACxBC,yBAA0B,KAC1BC,yBAA0B,KAC1BC,uBAAwB,KACxBC,eAAgB,UAEhBC,YAAY,EACZC,gBAAiB,IAEjBC,WAAY,KACZC,WAAY,KAEZC,qBAAqB,EACrBC,uBAAuB,EAEvBC,YAAY,EAEZC,eAAe,EACfC,0BAA0B,EAC1BC,qBAAqB,EAErB9H,aAAa,EACb+H,uBAAuB,EACvBC,4BAA6B,EAC7BC,8BAA8B,EAE9BC,eAAe,EACfC,qBAAqB,EAErBtP,MAAM,EACNuP,qBAAsB,EACtBC,aAAc,KAEdC,QAASjD,OACTkD,gBAAgB,EAChBC,UAAW,QACXC,qBAAqB,EAErBhN,kBAAkB,EAClBF,kBAAkB,EAClBmN,aAAc,KACdC,WAAW,EACXC,eAAgB,oBAEhBC,kBAAkB,EAElBC,uBAAwB,oBACxB3M,WAAY,eACZC,iBAAkB,sBAClB2M,0BAA2B,gCAC3BC,kBAAmB,uBACnBC,oBAAqB,yBACrBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,aAAc,iBACdC,YAAa,2BACbC,kBAAmB,kCACnBC,oBAAqB,yBACrBC,uBAAwB,4BACxBC,qBAAsB,0BACtBC,sBAAuB,2BACvBC,2BAA4B,gCAC5BC,yBAA0B,8BAC1BC,wBAAyB,qBACzBC,iBAAkB,cAClBC,uBAAwB,sBACxBC,sBAAuB,qBACvBC,mBAAoB,wBACpBC,kBAAmB,sBACnBC,eAAgB,YAChBC,mBAAoB,wBAGpB/P,UAAU,EACVgQ,gBAAgB,EAEhBC,MAAM,EACNC,iBAAkB,iBAClBC,iBAAkB,aAClBC,kBAAmB,0BACnBC,iBAAkB,yBAClBC,wBAAyB,wBAEzBC,oBAAoB,GA8BpBC,EAA0BpT,GAAUA,EAAOyL,gBAE/CzL,GAASA,KACT,IAAIqT,KACJ,KAAK,GAAIC,KAAStT,GACd,GAA6B,gBAAlBA,GAAOsT,IAAyC,OAAlBtT,EAAOsT,KAAqBtT,EAAOsT,GAAOrR,UAAYjC,EAAOsT,KAAW7Q,QAAUzC,EAAOsT,KAAWnP,UAA6B,mBAAToP,OAAwBvT,EAAOsT,YAAkBC,OAA4B,mBAAX9T,SAA0BO,EAAOsT,YAAkB7T,SAOlR4T,EAAeC,GAAStT,EAAOsT,OAP6P,CAC5RD,EAAeC,KACf,KAAK,GAAIE,KAAaxT,GAAOsT,GACzBD,EAAeC,GAAOE,GAAaxT,EAAOsT,GAAOE,GAO7D,IAAK,GAAIC,KAAO/I,GACZ,GAA2B,mBAAhB1K,GAAOyT,GACdzT,EAAOyT,GAAO/I,EAAS+I,OAEtB,IAA2B,gBAAhBzT,GAAOyT,GACnB,IAAK,GAAIC,KAAWhJ,GAAS+I,GACW,mBAAzBzT,GAAOyT,GAAKC,KACnB1T,EAAOyT,GAAKC,GAAWhJ,EAAS+I,GAAKC,GAOrD,IAAIvT,GAAIT,IAcR,IAXAS,EAAEH,OAASA,EACXG,EAAEkT,eAAiBA,EAGnBlT,EAAEwT,cAIe,mBAANhU,IAAqC,mBAAT4T,QACnC5T,EAAI4T,OAES,mBAAN5T,KAEHA,EADgB,mBAAT4T,MACH9Q,OAAO8Q,MAAQ9Q,OAAOmR,OAASnR,OAAOhD,OAGtC8T,SAKZpT,EAAER,EAAIA,EAKNQ,EAAE0T,kBAAoBnG,OACtBvN,EAAE2T,oBAAsB,WAEpB,IAAK3T,EAAEH,OAAOyN,YAAa,OAAO,CAClC,IACiB9H,GADboO,GAAa,EACbC,IACJ,KAAMrO,IAASxF,GAAEH,OAAOyN,YAChBtN,EAAEH,OAAOyN,YAAYwG,eAAetO,IACpCqO,EAAO1Q,KAAKqC,EAGpBqO,GAAOE,KAAK,SAAU5T,EAAG6T,GACrB,MAAO9J,UAAS/J,EAAG,IAAM+J,SAAS8J,EAAG,KAEzC,KAAK,GAAIzO,GAAI,EAAGA,EAAIsO,EAAO3R,OAAQqD,IAC/BC,EAAQqO,EAAOtO,GACXC,GAASlD,OAAOuC,aAAe+O,IAC/BA,EAAapO,EAGrB,OAAOoO,IAAc,OAEzB5T,EAAEiU,cAAgB,WAEd,GAAIL,GAAa5T,EAAE2T,qBACnB,IAAIC,GAAc5T,EAAE0T,oBAAsBE,EAAY,CAClD,GAAIM,GAAoBN,IAAc5T,GAAEH,OAAOyN,YAActN,EAAEH,OAAOyN,YAAYsG,GAAc5T,EAAEkT,eAC9FiB,EAAcnU,EAAEH,OAAOkB,MAASmT,EAAkBzG,gBAAkBzN,EAAEH,OAAO4N,aACjF,KAAM,GAAI0F,KAASe,GACflU,EAAEH,OAAOsT,GAASe,EAAkBf,EAExCnT,GAAE0T,kBAAoBE,EACnBO,GAAenU,EAAEoU,aAChBpU,EAAEqU,QAAO,KAKjBrU,EAAEH,OAAOyN,aACTtN,EAAEiU,gBAMNjU,EAAEC,UAAYT,EAAES,GACW,IAAvBD,EAAEC,UAAUiC,QAAhB,CACA,GAAIlC,EAAEC,UAAUiC,OAAS,EAAG,CACxB,GAAIoS,KAKJ,OAJAtU,GAAEC,UAAUF,KAAK,WAEbuU,EAAQnR,KAAK,GAAI9D,GAAOE,KAAMM,MAE3ByU,EAIXtU,EAAEC,UAAU,GAAGL,OAASI,EACxBA,EAAEC,UAAUuG,KAAK,SAAUxG,GAE3BA,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyBhR,EAAEH,OAAO2K,WAEzDxK,EAAEH,OAAOkH,UACT/G,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,aAEnDhR,EAAEuU,QAAQC,UACXxU,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,cACpDhR,EAAEH,OAAO6N,gBAAkB,GAE3B1N,EAAEH,OAAOuL,YACTpL,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,eAGpDhR,EAAEH,OAAOyM,UAAYtM,EAAEH,OAAO+P,yBAC9B5P,EAAEH,OAAO8P,qBAAsB,GAG/B3P,EAAEH,OAAO+O,sBACT5O,EAAEH,OAAO2P,gBAAkB,IAG1B,OAAQ,YAAa,QAAQvF,QAAQjK,EAAEH,OAAO0L,SAAW,IACtDvL,EAAEuU,QAAQE,cACVzU,EAAEH,OAAO8P,qBAAsB,EAC/B3P,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,OAGpDhR,EAAEH,OAAO0L,OAAS,SAGF,UAApBvL,EAAEH,OAAO0L,QACTvL,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyBhR,EAAEH,OAAO0L,QAEzC,SAApBvL,EAAEH,OAAO0L,SACTvL,EAAEH,OAAO2P,gBAAkB,EAC3BxP,EAAEH,OAAO4N,cAAgB,EACzBzN,EAAEH,OAAO6N,gBAAkB,EAC3B1N,EAAEH,OAAO+N,eAAiB,EAC1B5N,EAAEH,OAAOgO,gBAAiB,EAC1B7N,EAAEH,OAAO2N,aAAe,EACxBxN,EAAEH,OAAOyL,kBAAmB,EAC5BtL,EAAEH,OAAOwL,gBAAiB,GAEN,SAApBrL,EAAEH,OAAO0L,QAAyC,SAApBvL,EAAEH,OAAO0L,SACvCvL,EAAEH,OAAO4N,cAAgB,EACzBzN,EAAEH,OAAO6N,gBAAkB,EAC3B1N,EAAEH,OAAO+N,eAAiB,EAC1B5N,EAAEH,OAAO8P,qBAAsB,EAC/B3P,EAAEH,OAAO2N,aAAe,EACxBxN,EAAEH,OAAOwL,gBAAiB,EACa,mBAA5B4H,KACPjT,EAAEH,OAAOyL,kBAAmB,IAKhCtL,EAAEH,OAAOgQ,YAAc7P,EAAEuU,QAAQG,QACjC1U,EAAEH,OAAOgQ,YAAa,GAI1B7P,EAAE2U,QAAU3U,EAAEC,UAAU2U,SAAS,IAAM5U,EAAEH,OAAO2R,cAG5CxR,EAAEH,OAAOiP,aACT9O,EAAE6U,oBAAsBrV,EAAEQ,EAAEH,OAAOiP,YAC/B9O,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAOiP,YAA2B9O,EAAE6U,oBAAoB3S,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAOiP,YAAY5M,SACnJlC,EAAE6U,oBAAsB7U,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAOiP,aAGtB,YAA5B9O,EAAEH,OAAOyP,gBAAgCtP,EAAEH,OAAOmP,oBAClDhP,EAAE6U,oBAAoBE,SAAS/U,EAAEH,OAAOoS,wBAA0B,aAGlEjS,EAAEH,OAAOmP,qBAAsB,EAEnChP,EAAE6U,oBAAoBE,SAAS/U,EAAEH,OAAOoS,wBAA0BjS,EAAEH,OAAOyP,kBAG3EtP,EAAEH,OAAO4P,YAAczP,EAAEH,OAAO6P,cAC5B1P,EAAEH,OAAO4P,aACTzP,EAAEyP,WAAajQ,EAAEQ,EAAEH,OAAO4P,YACtBzP,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAO4P,YAA2BzP,EAAEyP,WAAWvN,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO4P,YAAYvN,SAC1IlC,EAAEyP,WAAazP,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO4P,cAG7CzP,EAAEH,OAAO6P,aACT1P,EAAE0P,WAAalQ,EAAEQ,EAAEH,OAAO6P,YACtB1P,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAO6P,YAA2B1P,EAAE0P,WAAWxN,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO6P,YAAYxN,SAC1IlC,EAAE0P,WAAa1P,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO6P,eAMrD1P,EAAE0D,aAAe,WACb,MAA8B,eAAvB1D,EAAEH,OAAO2K,WAKpBxK,EAAEkF,IAAMlF,EAAE0D,iBAAwD,QAArC1D,EAAEC,UAAU,GAAG+U,IAAI7Q,eAA4D,QAAjCnE,EAAEC,UAAUgV,IAAI,cACvFjV,EAAEkF,KACFlF,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,OAIpDhR,EAAEkF,MACFlF,EAAEkV,SAAwC,gBAA7BlV,EAAE2U,QAAQM,IAAI,YAI3BjV,EAAEH,OAAO6N,gBAAkB,GAC3B1N,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,YAIpDhR,EAAEmV,OAAOC,SACTpV,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,WAIxDhR,EAAEC,UAAU8U,SAAS/U,EAAEwT,WAAW6B,KAAK,MAGvCrV,EAAEsV,UAAY,EAGdtV,EAAE+J,SAAW,EAGb/J,EAAEuV,SAAW,EAKbvV,EAAEwV,gBAAkB,WAChBxV,EAAEH,OAAO4D,kBAAmB,EACxBzD,EAAEH,OAAO8D,oBAAqB,GAAS3D,EAAEH,OAAOgQ,YAChD7P,EAAEyV,mBAGVzV,EAAE0V,gBAAkB,WAChB1V,EAAEH,OAAO8D,kBAAmB,EACxB3D,EAAEH,OAAO4D,oBAAqB,GAASzD,EAAEH,OAAOgQ,YAChD7P,EAAEyV,mBAGVzV,EAAE2V,WAAa,WACX3V,EAAEH,OAAO4D,iBAAmBzD,EAAEH,OAAO8D,kBAAmB,EACpD3D,EAAEH,OAAOgQ,YAAY7P,EAAEyV,mBAE/BzV,EAAE4V,kBAAoB,WAClB5V,EAAEH,OAAO4D,kBAAmB,EACxBzD,EAAEH,OAAO8D,oBAAqB,GAAQ3D,EAAEH,OAAOgQ,YAC/C7P,EAAE6V,iBAGV7V,EAAE8V,kBAAoB,WAClB9V,EAAEH,OAAO8D,kBAAmB,EACxB3D,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAOgQ,YAC/C7P,EAAE6V,iBAGV7V,EAAE+V,aAAe,WACb/V,EAAEH,OAAO4D,iBAAmBzD,EAAEH,OAAO8D,kBAAmB,EACpD3D,EAAEH,OAAOgQ,YAAY7P,EAAE6V,iBAY/B7V,EAAE6V,cAAgB,SAASG,GACvBhW,EAAEC,UAAU,GAAGgW,MAAMC,OAAS,OAC9BlW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,mBAAqB,eAC5DhW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,eAAiB,YACxDhW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,WAAY,QAEvDhW,EAAEyV,gBAAkB,WAChBzV,EAAEC,UAAU,GAAGgW,MAAMC,OAAS,IAE9BlW,EAAEH,OAAOgQ,YACT7P,EAAE6V,gBAKN7V,EAAEmW,gBACFnW,EAAEoW,aAAe,EAEjBpW,EAAEqW,UAAY,SAAUC,EAAYC,EAAKC,EAAQC,EAAOC,EAAkBC,GAEtE,QAASC,KACDD,GAAUA,IAFlB,GAAIE,EAICP,GAAWQ,UAAaJ,EAmBzBE,IAlBIL,GACAM,EAAQ,GAAIvU,QAAOyU,MACnBF,EAAMG,OAASJ,EACfC,EAAMI,QAAUL,EACZH,IACAI,EAAMJ,MAAQA,GAEdD,IACAK,EAAML,OAASA,GAEfD,IACAM,EAAMN,IAAMA,IAGhBK,KAOZ5W,EAAEoQ,cAAgB,WAEd,QAAS8G,KACY,mBAANlX,IAA2B,OAANA,GAAeA,IACxBuN,SAAnBvN,EAAEoW,cAA4BpW,EAAEoW,eAChCpW,EAAEoW,eAAiBpW,EAAEmW,aAAajU,SAC9BlC,EAAEH,OAAOwQ,qBAAqBrQ,EAAEmX,SACpCnX,EAAEkB,KAAK,gBAAiBlB,KANhCA,EAAEmW,aAAenW,EAAEC,UAAU6U,KAAK,MASlC,KAAK,GAAIvP,GAAI,EAAGA,EAAIvF,EAAEmW,aAAajU,OAAQqD,IACvCvF,EAAEqW,UAAUrW,EAAEmW,aAAa5Q,GAAKvF,EAAEmW,aAAa5Q,GAAG6R,YAAcpX,EAAEmW,aAAa5Q,GAAG8R,aAAa,OAAUrX,EAAEmW,aAAa5Q,GAAGiR,QAAUxW,EAAEmW,aAAa5Q,GAAG8R,aAAa,UAAYrX,EAAEmW,aAAa5Q,GAAGkR,OAASzW,EAAEmW,aAAa5Q,GAAG8R,aAAa,UAAU,EAAMH,IAOlQlX,EAAEa,kBAAoB0M,OACtBvN,EAAEsX,aAAc,EAChBtX,EAAEuX,gBAAiB,EA8BnBvX,EAAEwX,cAAgB,WACd,MAAmC,mBAAxBxX,GAAEa,sBACRb,EAAEH,OAAOS,YACVN,EAAEsX,cACNtX,EAAEsX,aAAc,EAChBtX,EAAEkB,KAAK,kBAAmBlB,OAC1BM,SAEJN,EAAEqB,aAAe,SAAUoW,GAClBzX,EAAEa,oBACHb,EAAEa,mBAAmBiH,aAAa9H,EAAEa,mBACxCb,EAAEsX,aAAc,EAChBtX,EAAEa,kBAAoB0M,OACtBvN,EAAEkB,KAAK,iBAAkBlB,KAE7BA,EAAE0X,cAAgB,SAAU/M,GACpB3K,EAAEuX,iBACFvX,EAAEa,mBAAmBiH,aAAa9H,EAAEa,mBACxCb,EAAEuX,gBAAiB,EACL,IAAV5M,GACA3K,EAAEuX,gBAAiB,EACnBjX,KAGAN,EAAE2U,QAAQgD,cAAc,WACf3X,IACLA,EAAEuX,gBAAiB,EACdvX,EAAEsX,YAIHhX,IAHAN,EAAEqB,oBAWlBrB,EAAEsH,aAAe,WACb,OAAStH,EAAE4X,SAAS,IAExB5X,EAAEuH,aAAe,WACb,OAASvH,EAAE4X,SAAS5X,EAAE4X,SAAS1V,OAAS,IAK5ClC,EAAE6X,iBAAmB,WACjB,GAEItS,GAFAuS,KACAC,EAAY,CAIhB,IAA8B,SAA3B/X,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,EAC7D,IAAKlI,EAAI,EAAGA,EAAInF,KAAK4X,KAAKhY,EAAEH,OAAO4N,eAAgBlI,IAAK,CACpD,GAAIvD,GAAQhC,EAAEW,YAAc4E,CAC5B,IAAGvD,EAAQhC,EAAES,OAAOyB,OAAQ,KAC5B4V,GAAa3U,KAAKnD,EAAES,OAAOC,GAAGsB,GAAO,QAGzC8V,GAAa3U,KAAKnD,EAAES,OAAOC,GAAGV,EAAEW,aAAa,GAIjD,KAAK4E,EAAI,EAAGA,EAAIuS,EAAa5V,OAAQqD,IACjC,GAA+B,mBAApBuS,GAAavS,GAAoB,CACxC,GAAID,GAASwS,EAAavS,GAAG0S,YAC7BF,GAAYzS,EAASyS,EAAYzS,EAASyS,EAK9CA,GAAW/X,EAAE2U,QAAQM,IAAI,SAAU8C,EAAY,OAEvD/X,EAAEkY,oBAAsB,WACpB,GAAI7S,GAAOC,CAEPD,GAD0B,mBAAnBrF,GAAEH,OAAOwF,MACRrF,EAAEH,OAAOwF,MAGTrF,EAAEC,UAAU,GAAGkY,YAGvB7S,EAD2B,mBAApBtF,GAAEH,OAAOyF,OACPtF,EAAEH,OAAOyF,OAGTtF,EAAEC,UAAU,GAAGmY,aAEd,IAAV/S,GAAerF,EAAE0D,gBAA6B,IAAX4B,IAAiBtF,EAAE0D,iBAK1D2B,EAAQA,EAAQ6E,SAASlK,EAAEC,UAAUgV,IAAI,gBAAiB,IAAM/K,SAASlK,EAAEC,UAAUgV,IAAI,iBAAkB,IAC3G3P,EAASA,EAAS4E,SAASlK,EAAEC,UAAUgV,IAAI,eAAgB,IAAM/K,SAASlK,EAAEC,UAAUgV,IAAI,kBAAmB,IAG7GjV,EAAEqF,MAAQA,EACVrF,EAAEsF,OAASA,EACXtF,EAAEqY,KAAOrY,EAAE0D,eAAiB1D,EAAEqF,MAAQrF,EAAEsF,SAG5CtF,EAAEsY,iBAAmB,WACjBtY,EAAES,OAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,YAC7CrE,EAAE4X,YACF5X,EAAEuY,cACFvY,EAAEwY,kBAEF,IAEIjT,GAFAiI,EAAexN,EAAEH,OAAO2N,aACxBiL,GAAiBzY,EAAEH,OAAOiO,mBAE1B4K,EAAgB,EAChB1W,EAAQ,CACZ,IAAsB,mBAAXhC,GAAEqY,KAAb,CAC4B,gBAAjB7K,IAA6BA,EAAavD,QAAQ,MAAQ,IACjEuD,EAAemL,WAAWnL,EAAaoL,QAAQ,IAAK,KAAO,IAAM5Y,EAAEqY,MAGvErY,EAAE6Y,aAAerL,EAEbxN,EAAEkF,IAAKlF,EAAES,OAAOwU,KAAK6D,WAAY,GAAIC,UAAW,KAC/C/Y,EAAES,OAAOwU,KAAK+D,YAAa,GAAIC,aAAc,IAElD,IAAIC,EACAlZ,GAAEH,OAAO6N,gBAAkB,IAEvBwL,EADA9Y,KAAKC,MAAML,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,mBAAqB1N,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,gBAC7D1N,EAAES,OAAOyB,OAGT9B,KAAK4X,KAAKhY,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,iBAAmB1N,EAAEH,OAAO6N,gBAE/D,SAA3B1N,EAAEH,OAAO4N,eAA6D,QAAjCzN,EAAEH,OAAO8N,sBAC9CuL,EAAyB9Y,KAAK+Y,IAAID,EAAwBlZ,EAAEH,OAAO4N,cAAgBzN,EAAEH,OAAO6N,kBAKpG,IAAI0L,GACA1L,EAAkB1N,EAAEH,OAAO6N,gBAC3B2L,EAAeH,EAAyBxL,EACxC4L,EAAiBD,GAAgBrZ,EAAEH,OAAO6N,gBAAkB2L,EAAerZ,EAAES,OAAOyB,OACxF,KAAKqD,EAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CAClC6T,EAAY,CACZ,IAAIG,GAAQvZ,EAAES,OAAOC,GAAG6E,EACxB,IAAIvF,EAAEH,OAAO6N,gBAAkB,EAAG,CAE9B,GAAI8L,GACAC,EAAQC,CACyB,YAAjC1Z,EAAEH,OAAO8N,qBACT8L,EAASrZ,KAAKC,MAAMkF,EAAImI,GACxBgM,EAAMnU,EAAIkU,EAAS/L,GACf+L,EAASH,GAAmBG,IAAWH,GAAkBI,IAAQhM,EAAgB,MAC3EgM,GAAOhM,IACTgM,EAAM,EACND,KAGRD,EAAqBC,EAASC,EAAMR,EAAyBxL,EAC7D6L,EACKtE,KACG0E,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAASP,MAIjBE,EAAMtZ,KAAKC,MAAMkF,EAAI8T,GACrBI,EAASlU,EAAImU,EAAML,GAEvBE,EACKtE,IACG,WAAajV,EAAE0D,eAAiB,MAAQ,QAC/B,IAARgW,GAAa1Z,EAAEH,OAAO2N,cAAkBxN,EAAEH,OAAO2N,aAAe,MAEpE5M,KAAK,qBAAsB6Y,GAC3B7Y,KAAK,kBAAmB8Y,GAGJ,SAAzBH,EAAMtE,IAAI,aACiB,SAA3BjV,EAAEH,OAAO4N,eACT2L,EAAYpZ,EAAE0D,eAAiB6V,EAAMS,YAAW,GAAQT,EAAMU,aAAY,GACtEja,EAAEH,OAAOmO,eAAcoL,EAAYlZ,EAAMkZ,MAG7CA,GAAapZ,EAAEqY,MAAQrY,EAAEH,OAAO4N,cAAgB,GAAKD,GAAgBxN,EAAEH,OAAO4N,cAC1EzN,EAAEH,OAAOmO,eAAcoL,EAAYlZ,EAAMkZ,IAEzCpZ,EAAE0D,eACF1D,EAAES,OAAO8E,GAAG0Q,MAAM5Q,MAAQ+T,EAAY,KAGtCpZ,EAAES,OAAO8E,GAAG0Q,MAAM3Q,OAAS8T,EAAY,MAG/CpZ,EAAES,OAAO8E,GAAG2U,gBAAkBd,EAC9BpZ,EAAEwY,gBAAgBrV,KAAKiW,GAGnBpZ,EAAEH,OAAOgO,gBACT4K,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAIlL,EAC1D,IAANjI,IAASkT,EAAgBA,EAAgBzY,EAAEqY,KAAO,EAAI7K,GACtDpN,KAAKuG,IAAI8R,GAAiB,OAAUA,EAAgB,GACpD,EAAUzY,EAAEH,OAAO+N,iBAAmB,GAAG5N,EAAE4X,SAASzU,KAAKsV,GAC7DzY,EAAEuY,WAAWpV,KAAKsV,KAGd,EAAUzY,EAAEH,OAAO+N,iBAAmB,GAAG5N,EAAE4X,SAASzU,KAAKsV,GAC7DzY,EAAEuY,WAAWpV,KAAKsV,GAClBA,EAAgBA,EAAgBW,EAAY5L,GAGhDxN,EAAE6Y,aAAeO,EAAY5L,EAE7BkL,EAAgBU,EAEhBpX,KAEJhC,EAAE6Y,YAAczY,KAAK+Y,IAAInZ,EAAE6Y,YAAa7Y,EAAEqY,MAAQrY,EAAEH,OAAOkO,iBAC3D,IAAIoM,EAWJ,IARIna,EAAEkF,KAAOlF,EAAEkV,WAAiC,UAApBlV,EAAEH,OAAO0L,QAA0C,cAApBvL,EAAEH,OAAO0L,SAChEvL,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAE7DxN,EAAEuU,QAAQC,UAAWxU,EAAEH,OAAOwL,iBAC3BrL,EAAE0D,eAAgB1D,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAC/ExN,EAAE2U,QAAQM,KAAK3P,OAAQtF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,QAGpExN,EAAEH,OAAO6N,gBAAkB,IAC3B1N,EAAE6Y,aAAeO,EAAYpZ,EAAEH,OAAO2N,cAAgB0L,EACtDlZ,EAAE6Y,YAAczY,KAAK4X,KAAKhY,EAAE6Y,YAAc7Y,EAAEH,OAAO6N,iBAAmB1N,EAAEH,OAAO2N,aAC3ExN,EAAE0D,eAAgB1D,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAC/ExN,EAAE2U,QAAQM,KAAK3P,OAAQtF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAChExN,EAAEH,OAAOgO,gBAAgB,CAEzB,IADAsM,KACK5U,EAAI,EAAGA,EAAIvF,EAAE4X,SAAS1V,OAAQqD,IAC3BvF,EAAE4X,SAASrS,GAAKvF,EAAE6Y,YAAc7Y,EAAE4X,SAAS,IAAIuC,EAAchX,KAAKnD,EAAE4X,SAASrS,GAErFvF,GAAE4X,SAAWuC,EAKrB,IAAKna,EAAEH,OAAOgO,eAAgB,CAE1B,IADAsM,KACK5U,EAAI,EAAGA,EAAIvF,EAAE4X,SAAS1V,OAAQqD,IAC3BvF,EAAE4X,SAASrS,IAAMvF,EAAE6Y,YAAc7Y,EAAEqY,MACnC8B,EAAchX,KAAKnD,EAAE4X,SAASrS,GAGtCvF,GAAE4X,SAAWuC,EACT/Z,KAAKC,MAAML,EAAE6Y,YAAc7Y,EAAEqY,MAAQjY,KAAKC,MAAML,EAAE4X,SAAS5X,EAAE4X,SAAS1V,OAAS,IAAM,GACrFlC,EAAE4X,SAASzU,KAAKnD,EAAE6Y,YAAc7Y,EAAEqY,MAGhB,IAAtBrY,EAAE4X,SAAS1V,SAAclC,EAAE4X,UAAY,IAEb,IAA1B5X,EAAEH,OAAO2N,eACLxN,EAAE0D,eACE1D,EAAEkF,IAAKlF,EAAES,OAAOwU,KAAK6D,WAAYtL,EAAe,OAC/CxN,EAAES,OAAOwU,KAAK+D,YAAaxL,EAAe,OAE9CxN,EAAES,OAAOwU,KAAKgE,aAAczL,EAAe,QAEhDxN,EAAEH,OAAO8P,qBACT3P,EAAEoa,uBAGVpa,EAAEoa,mBAAqB,WACnB,IAAK,GAAI7U,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IACjCvF,EAAES,OAAO8E,GAAG8U,kBAAoBra,EAAE0D,eAAiB1D,EAAES,OAAO8E,GAAG+U,WAAata,EAAES,OAAO8E,GAAGgV,WAOhGva,EAAEwa,qBAAuB,WACrB,GAAajV,GAAGkV,EAAZC,EAAM,CACV,IAAI1a,EAAEH,OAAOgO,eAAgB,CACzB,GACI8M,GADAtC,EAAOrY,EAAES,OAAOT,EAAEW,aAAauZ,eAEnC,KAAK3U,EAAIvF,EAAEW,YAAc,EAAG4E,EAAIvF,EAAES,OAAOyB,OAAQqD,IACzCvF,EAAES,OAAO8E,KAAOoV,IAChBtC,GAAQrY,EAAES,OAAO8E,GAAG2U,gBACpBQ,IACIrC,EAAOrY,EAAEqY,OAAMsC,GAAY,GAGvC,KAAKF,EAAIza,EAAEW,YAAc,EAAG8Z,GAAK,EAAGA,IAC5Bza,EAAES,OAAOga,KAAOE,IAChBtC,GAAQrY,EAAES,OAAOga,GAAGP,gBACpBQ,IACIrC,EAAOrY,EAAEqY,OAAMsC,GAAY,QAKvC,KAAKpV,EAAIvF,EAAEW,YAAc,EAAG4E,EAAIvF,EAAES,OAAOyB,OAAQqD,IACzCvF,EAAEuY,WAAWhT,GAAKvF,EAAEuY,WAAWvY,EAAEW,aAAeX,EAAEqY,MAClDqC,GAIZ,OAAOA,IAKX1a,EAAE4a,qBAAuB,SAAUtF,GAI/B,GAHyB,mBAAdA,KACPA,EAAYtV,EAAEsV,WAAa,GAEP,IAApBtV,EAAES,OAAOyB,OAAb,CAC6C,mBAAlClC,GAAES,OAAO,GAAG4Z,mBAAmCra,EAAEoa,oBAE5D,IAAIS,IAAgBvF,CAChBtV,GAAEkF,MAAK2V,EAAevF,GAG1BtV,EAAES,OAAOqa,YAAY9a,EAAEH,OAAOqR,kBAC9B,KAAK,GAAI3L,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAO8E,GACjBwV,GAAiBF,GAAgB7a,EAAEH,OAAOgO,eAAiB7N,EAAEsH,eAAiB,GAAKiS,EAAMc,oBAAsBd,EAAMW,gBAAkBla,EAAEH,OAAO2N,aACpJ,IAAIxN,EAAEH,OAAO+P,sBAAuB,CAChC,GAAIoL,KAAgBH,EAAetB,EAAMc,mBACrCY,EAAaD,EAAchb,EAAEwY,gBAAgBjT,GAC7C2V,EACCF,GAAe,GAAKA,EAAchb,EAAEqY,MACpC4C,EAAa,GAAKA,GAAcjb,EAAEqY,MAClC2C,GAAe,GAAKC,GAAcjb,EAAEqY,IACrC6C,IACAlb,EAAES,OAAOC,GAAG6E,GAAGwP,SAAS/U,EAAEH,OAAOqR,mBAGzCqI,EAAMxP,SAAW/J,EAAEkF,KAAO6V,EAAgBA,KAGlD/a,EAAE0H,eAAiB,SAAU4N,GACA,mBAAdA,KACPA,EAAYtV,EAAEsV,WAAa,EAE/B,IAAI6F,GAAiBnb,EAAEuH,eAAiBvH,EAAEsH,eACtCH,EAAenH,EAAEoH,YACjBC,EAASrH,EAAEmB,KACQ,KAAnBga,GACAnb,EAAE+J,SAAW,EACb/J,EAAEoH,YAAcpH,EAAEmB,OAAQ,IAG1BnB,EAAE+J,UAAYuL,EAAYtV,EAAEsH,gBAAkB,EAC9CtH,EAAEoH,YAAcpH,EAAE+J,UAAY,EAC9B/J,EAAEmB,MAAQnB,EAAE+J,UAAY,GAExB/J,EAAEoH,cAAgBD,GAAcnH,EAAEkB,KAAK,mBAAoBlB,GAC3DA,EAAEmB,QAAUkG,GAAQrH,EAAEkB,KAAK,aAAclB,GAEzCA,EAAEH,OAAO8P,qBAAqB3P,EAAE4a,qBAAqBtF,GACzDtV,EAAEkB,KAAK,aAAclB,EAAGA,EAAE+J,WAE9B/J,EAAE2H,kBAAoB,WAClB,GACIyT,GAAgB7V,EAAG8V,EADnB/F,EAAYtV,EAAEkF,IAAMlF,EAAEsV,WAAatV,EAAEsV,SAEzC,KAAK/P,EAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,IACE,mBAAxBvF,GAAEuY,WAAWhT,EAAI,GACpB+P,GAAatV,EAAEuY,WAAWhT,IAAM+P,EAAYtV,EAAEuY,WAAWhT,EAAI,IAAMvF,EAAEuY,WAAWhT,EAAI,GAAKvF,EAAEuY,WAAWhT,IAAM,EAC5G6V,EAAiB7V,EAEZ+P,GAAatV,EAAEuY,WAAWhT,IAAM+P,EAAYtV,EAAEuY,WAAWhT,EAAI,KAClE6V,EAAiB7V,EAAI,GAIrB+P,GAAatV,EAAEuY,WAAWhT,KAC1B6V,EAAiB7V,EAK1BvF,GAAEH,OAAO8Q,sBACJyK,EAAiB,GAA+B,mBAAnBA,MAAgCA,EAAiB,GAOtFC,EAAYjb,KAAKC,MAAM+a,EAAiBpb,EAAEH,OAAO+N,gBAC7CyN,GAAarb,EAAE4X,SAAS1V,SAAQmZ,EAAYrb,EAAE4X,SAAS1V,OAAS,GAEhEkZ,IAAmBpb,EAAEW,cAGzBX,EAAEqb,UAAYA,EACdrb,EAAEsb,cAAgBtb,EAAEW,YACpBX,EAAEW,YAAcya,EAChBpb,EAAE4H,gBACF5H,EAAEub,oBAENvb,EAAEub,gBAAkB,WAChBvb,EAAEwb,UAAYtR,SAASlK,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,4BAA8BZ,EAAEW,YAAa,KAMxGX,EAAE4H,cAAgB,WACd5H,EAAES,OAAOqa,YAAY9a,EAAEH,OAAOyE,iBAAmB,IAAMtE,EAAEH,OAAOuR,eAAiB,IAAMpR,EAAEH,OAAOyR,eAAiB,IAAMtR,EAAEH,OAAOoR,0BAA4B,IAAMjR,EAAEH,OAAOwR,wBAA0B,IAAMrR,EAAEH,OAAO0R,wBACpN,IAAI/Q,GAAcR,EAAES,OAAOC,GAAGV,EAAEW,YAEhCH,GAAYuU,SAAS/U,EAAEH,OAAOyE,kBAC1BzE,EAAOkB,OAEHP,EAAYib,SAASzb,EAAEH,OAAOsR,qBAC9BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCnR,EAAEwb,UAAY,MAAMzG,SAAS/U,EAAEH,OAAOoR,2BAG/JjR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+BnR,EAAEwb,UAAY,MAAMzG,SAAS/U,EAAEH,OAAOoR,2BAIjK,IAAIyK,GAAYlb,EAAYmb,KAAK,IAAM3b,EAAEH,OAAOwE,YAAY0Q,SAAS/U,EAAEH,OAAOuR,eAC1EpR,GAAEH,OAAOkB,MAA6B,IAArB2a,EAAUxZ,SAC3BwZ,EAAY1b,EAAES,OAAOC,GAAG,GACxBgb,EAAU3G,SAAS/U,EAAEH,OAAOuR,gBAGhC,IAAIwK,GAAYpb,EAAYqb,KAAK,IAAM7b,EAAEH,OAAOwE,YAAY0Q,SAAS/U,EAAEH,OAAOyR,eAsB9E,IArBItR,EAAEH,OAAOkB,MAA6B,IAArB6a,EAAU1Z,SAC3B0Z,EAAY5b,EAAES,OAAOC,IAAG,GACxBkb,EAAU7G,SAAS/U,EAAEH,OAAOyR,iBAE5BzR,EAAOkB,OAEH2a,EAAUD,SAASzb,EAAEH,OAAOsR,qBAC5BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCuK,EAAU9a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAOwR,yBAG7LrR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+BuK,EAAU9a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAOwR,yBAEvLuK,EAAUH,SAASzb,EAAEH,OAAOsR,qBAC5BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCyK,EAAUhb,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAO0R,yBAG7LvR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+ByK,EAAUhb,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAO0R,0BAK3LvR,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,OAAS,EAAG,CAE3D,GAAI4Z,GACAC,EAAQ/b,EAAEH,OAAOkB,KAAOX,KAAK4X,MAAMhY,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAAoBvQ,EAAEH,OAAO+N,gBAAkB5N,EAAE4X,SAAS1V,MAiCrH,IAhCIlC,EAAEH,OAAOkB,MACT+a,EAAU1b,KAAK4X,MAAMhY,EAAEW,YAAcX,EAAEuQ,cAAcvQ,EAAEH,OAAO+N,gBAC1DkO,EAAU9b,EAAES,OAAOyB,OAAS,EAAqB,EAAjBlC,EAAEuQ,eAClCuL,GAAqB9b,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAEzCuL,EAAUC,EAAQ,IAAGD,GAAoBC,GACzCD,EAAU,GAAiC,YAA5B9b,EAAEH,OAAOyP,iBAA8BwM,EAAUC,EAAQD,IAIxEA,EADuB,mBAAhB9b,GAAEqb,UACCrb,EAAEqb,UAGFrb,EAAEW,aAAe,EAIH,YAA5BX,EAAEH,OAAOyP,gBAAgCtP,EAAEgc,SAAWhc,EAAEgc,QAAQ9Z,OAAS,IACzElC,EAAEgc,QAAQlB,YAAY9a,EAAEH,OAAO6R,mBAC3B1R,EAAE6U,oBAAoB3S,OAAS,EAC/BlC,EAAEgc,QAAQjc,KAAK,WACPP,EAAED,MAAMyC,UAAY8Z,GAAStc,EAAED,MAAMwV,SAAS/U,EAAEH,OAAO6R,qBAI/D1R,EAAEgc,QAAQtb,GAAGob,GAAS/G,SAAS/U,EAAEH,OAAO6R,oBAGhB,aAA5B1R,EAAEH,OAAOyP,iBACTtP,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAO+R,wBAAwBqK,KAAKH,EAAU,GACjF9b,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAOgS,sBAAsBoK,KAAKF,IAEzC,aAA5B/b,EAAEH,OAAOyP,eAA+B,CACxC,GAAI4M,IAASJ,EAAU,GAAKC,EACxBI,EAASD,EACTE,EAAS,CACRpc,GAAE0D,iBACH0Y,EAASF,EACTC,EAAS,GAEbnc,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAOkS,4BAA4B5H,UAAU,6BAA+BgS,EAAS,YAAcC,EAAS,KAAKC,WAAWrc,EAAEH,OAAO8K,OAE5I,WAA5B3K,EAAEH,OAAOyP,gBAA+BtP,EAAEH,OAAOwP,yBACjDrP,EAAE6U,oBAAoByH,KAAKtc,EAAEH,OAAOwP,uBAAuBrP,EAAG8b,EAAU,EAAGC,IAC3E/b,EAAEkB,KAAK,uBAAwBlB,EAAGA,EAAE6U,oBAAoB,KAK3D7U,EAAEH,OAAOkB,OACNf,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IACzDlC,EAAEoH,aACFpH,EAAE0P,WAAWqF,SAAS/U,EAAEH,OAAO8R,qBAC3B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK6J,QAAQvc,EAAE0P,cAG9C1P,EAAE0P,WAAWoL,YAAY9a,EAAEH,OAAO8R,qBAC9B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK8J,OAAOxc,EAAE0P,cAGjD1P,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IACzDlC,EAAEmB,OACFnB,EAAEyP,WAAWsF,SAAS/U,EAAEH,OAAO8R,qBAC3B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK6J,QAAQvc,EAAEyP,cAG9CzP,EAAEyP,WAAWqL,YAAY9a,EAAEH,OAAO8R,qBAC9B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK8J,OAAOxc,EAAEyP,gBAS7DzP,EAAEyc,iBAAmB,WACjB,GAAKzc,EAAEH,OAAOiP,YACV9O,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,OAAS,EAAG,CAC3D,GAAIwa,GAAiB,EACrB,IAAgC,YAA5B1c,EAAEH,OAAOyP,eAA8B,CAEvC,IAAK,GADDqN,GAAkB3c,EAAEH,OAAOkB,KAAOX,KAAK4X,MAAMhY,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAAoBvQ,EAAEH,OAAO+N,gBAAkB5N,EAAE4X,SAAS1V,OACtHqD,EAAI,EAAGA,EAAIoX,EAAiBpX,IAE7BmX,GADA1c,EAAEH,OAAOqP,uBACSlP,EAAEH,OAAOqP,uBAAuBlP,EAAGuF,EAAGvF,EAAEH,OAAO4R,aAG/C,IAAMzR,EAAEH,OAAOkP,kBAAkB,WAAa/O,EAAEH,OAAO4R,YAAc,OAASzR,EAAEH,OAAOkP,kBAAoB,GAGrI/O,GAAE6U,oBAAoByH,KAAKI,GAC3B1c,EAAEgc,QAAUhc,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAO4R,aAClDzR,EAAEH,OAAOmP,qBAAuBhP,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MACnD1S,EAAE0S,KAAKkK,iBAGiB,aAA5B5c,EAAEH,OAAOyP,iBAELoN,EADA1c,EAAEH,OAAOuP,yBACQpP,EAAEH,OAAOuP,yBAAyBpP,EAAGA,EAAEH,OAAO+R,uBAAwB5R,EAAEH,OAAOgS,sBAI5F,gBAAkB7R,EAAEH,OAAO+R,uBAAyB,4BAElC5R,EAAEH,OAAOgS,qBAAqB,YAExD7R,EAAE6U,oBAAoByH,KAAKI,IAEC,aAA5B1c,EAAEH,OAAOyP,iBAELoN,EADA1c,EAAEH,OAAOsP,yBACQnP,EAAEH,OAAOsP,yBAAyBnP,EAAGA,EAAEH,OAAOkS,4BAG9C,gBAAkB/R,EAAEH,OAAOkS,2BAA6B,YAE7E/R,EAAE6U,oBAAoByH,KAAKI,IAEC,WAA5B1c,EAAEH,OAAOyP,gBACTtP,EAAEkB,KAAK,uBAAwBlB,EAAGA,EAAE6U,oBAAoB,MAOpE7U,EAAEmX,OAAS,SAAU0F,GAUjB,QAASC,KACW9c,EAAEkF,KAAOlF,EAAEsV,UAAYtV,EAAEsV,SACzCyH,GAAe3c,KAAK4c,IAAI5c,KAAK+Y,IAAInZ,EAAEsV,UAAWtV,EAAEuH,gBAAiBvH,EAAEsH,gBACnEtH,EAAEyH,oBAAoBsV,GACtB/c,EAAE2H,oBACF3H,EAAE4H,gBAdN,GAAK5H,EAgBL,GAfAA,EAAEkY,sBACFlY,EAAEsY,mBACFtY,EAAE0H,iBACF1H,EAAEyc,mBACFzc,EAAE4H,gBACE5H,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUsQ,MASZJ,EAAiB,CACjB,GAAIK,GAAYH,CACZ/c,GAAEmd,YAAcnd,EAAEmd,WAAWC,SAC7Bpd,EAAEmd,WAAWC,OAAS7P,QAEtBvN,EAAEH,OAAOkH,UACT+V,IACI9c,EAAEH,OAAOuL,YACTpL,EAAE6X,qBAKFqF,GAD4B,SAA3Bld,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,IAAMzN,EAAEmB,QAAUnB,EAAEH,OAAOgO,eAC7E7N,EAAEqd,QAAQrd,EAAES,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzClC,EAAEqd,QAAQrd,EAAEW,YAAa,GAAG,GAAO,GAE/Cuc,GACDJ,SAIH9c,GAAEH,OAAOuL,YACdpL,EAAE6X,oBAOV7X,EAAE6C,SAAW,SAAUya,GAEftd,EAAEH,OAAOyN,aACTtN,EAAEiU,eAIN,IAAItQ,GAAmB3D,EAAEH,OAAO8D,iBAC5BF,EAAmBzD,EAAEH,OAAO4D,gBAChCzD,GAAEH,OAAO8D,iBAAmB3D,EAAEH,OAAO4D,kBAAmB,EAExDzD,EAAEkY,sBACFlY,EAAEsY,oBAC6B,SAA3BtY,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAOkH,UAAYuW,IAAuBtd,EAAEyc,mBACnFzc,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUsQ,MAEZjd,EAAEmd,YAAcnd,EAAEmd,WAAWC,SAC7Bpd,EAAEmd,WAAWC,OAAS7P,OAE1B,IAAIgQ,IAAwB,CAC5B,IAAIvd,EAAEH,OAAOkH,SAAU,CACnB,GAAIgW,GAAe3c,KAAK4c,IAAI5c,KAAK+Y,IAAInZ,EAAEsV,UAAWtV,EAAEuH,gBAAiBvH,EAAEsH,eACvEtH,GAAEyH,oBAAoBsV,GACtB/c,EAAE2H,oBACF3H,EAAE4H,gBAEE5H,EAAEH,OAAOuL,YACTpL,EAAE6X,uBAIN7X,GAAE4H,gBAEE2V,GAD4B,SAA3Bvd,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,IAAMzN,EAAEmB,QAAUnB,EAAEH,OAAOgO,eAClE7N,EAAEqd,QAAQrd,EAAES,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzClC,EAAEqd,QAAQrd,EAAEW,YAAa,GAAG,GAAO,EAG/DX,GAAEH,OAAOqI,cAAgBqV,GAAyBvd,EAAEmI,MACpDnI,EAAEmI,KAAKC,OAGXpI,EAAEH,OAAO8D,iBAAmBA,EAC5B3D,EAAEH,OAAO4D,iBAAmBA,GAQhCzD,EAAEwd,oBAAsBC,MAAO,YAAaC,KAAM,YAAaC,IAAK,WAChErb,OAAOsb,UAAUC,eAAgB7d,EAAEwd,oBAAsBC,MAAO,cAAeC,KAAM,cAAeC,IAAK,aACpGrb,OAAOsb,UAAUE,mBAAkB9d,EAAEwd,oBAAsBC,MAAO,gBAAiBC,KAAM,gBAAiBC,IAAK,gBACxH3d,EAAE+d,aACEN,MAAQzd,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAiB,aAAenO,EAAEwd,mBAAmBC,MAC1FC,KAAO1d,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAgB,YAAcnO,EAAEwd,mBAAmBE,KACvFC,IAAM3d,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAgB,WAAanO,EAAEwd,mBAAmBG,MAKrFrb,OAAOsb,UAAUC,gBAAkBvb,OAAOsb,UAAUE,oBACpB,cAA/B9d,EAAEH,OAAO4K,kBAAoCzK,EAAEC,UAAYD,EAAE2U,SAASI,SAAS,cAAgB/U,EAAEH,OAAO2K,WAI7GxK,EAAEge,WAAa,SAAUC,GACrB,GAAIC,GAAYD,EAAS,MAAQ,KAC7BE,EAASF,EAAS,sBAAwB,mBAC1CxT,EAAmD,cAA/BzK,EAAEH,OAAO4K,kBAAoCzK,EAAEC,UAAU,GAAKD,EAAE2U,QAAQ,GAC5FhT,EAAS3B,EAAEuU,QAAQG,MAAQjK,EAAoBzG,SAE/Coa,IAAcpe,EAAEH,OAAOwe,MAG3B,IAAIre,EAAEse,QAAQC,GACV9T,EAAkB0T,GAAQne,EAAE+d,YAAYN,MAAOzd,EAAEwe,cAAc,GAC/D7c,EAAOwc,GAAQne,EAAE+d,YAAYL,KAAM1d,EAAEye,YAAaL,GAClDzc,EAAOwc,GAAQne,EAAE+d,YAAYJ,IAAK3d,EAAE0e,YAAY,OAE/C,CACD,GAAI1e,EAAEuU,QAAQG,MAAO,CACjB,GAAIiK,KAA0C,eAAxB3e,EAAE+d,YAAYN,QAA0Bzd,EAAEuU,QAAQoK,kBAAmB3e,EAAEH,OAAOkR,oBAAoB6N,SAAS,EAAMC,SAAS,EAChJpU,GAAkB0T,GAAQne,EAAE+d,YAAYN,MAAOzd,EAAEwe,aAAcG,GAC/DlU,EAAkB0T,GAAQne,EAAE+d,YAAYL,KAAM1d,EAAEye,YAAaL,GAC7D3T,EAAkB0T,GAAQne,EAAE+d,YAAYJ,IAAK3d,EAAE0e,WAAYC,IAE1D9e,EAAOsO,gBAAkBnO,EAAEmV,OAAO2J,MAAQ9e,EAAEmV,OAAOC,SAAavV,EAAOsO,gBAAkBnO,EAAEuU,QAAQG,OAAS1U,EAAEmV,OAAO2J,OACtHrU,EAAkB0T,GAAQ,YAAane,EAAEwe,cAAc,GACvDxa,SAASma,GAAQ,YAAane,EAAEye,YAAaL,GAC7Cpa,SAASma,GAAQ,UAAWne,EAAE0e,YAAY,IAGlDpc,OAAO6b,GAAQ,SAAUne,EAAE6C,UAGvB7C,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IAC7DlC,EAAEyP,WAAWyO,GAAW,QAASle,EAAE+e,aAC/B/e,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAEyP,WAAWyO,GAAW,UAAWle,EAAE0S,KAAKsM,aAEvEhf,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IAC7DlC,EAAE0P,WAAWwO,GAAW,QAASle,EAAEif,aAC/Bjf,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0P,WAAWwO,GAAW,UAAWle,EAAE0S,KAAKsM,aAEvEhf,EAAEH,OAAOiP,YAAc9O,EAAEH,OAAOmP,sBAChChP,EAAE6U,oBAAoBqJ,GAAW,QAAS,IAAMle,EAAEH,OAAO4R,YAAazR,EAAEkf,cACpElf,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE6U,oBAAoBqJ,GAAW,UAAW,IAAMle,EAAEH,OAAO4R,YAAazR,EAAE0S,KAAKsM;CAI5Ghf,EAAEH,OAAOiQ,eAAiB9P,EAAEH,OAAOkQ,2BAA0BtF,EAAkB0T,GAAQ,QAASne,EAAE8P,eAAe,IAEzH9P,EAAEmf,aAAe,WACbnf,EAAEge,cAENhe,EAAEof,aAAe,WACbpf,EAAEge,YAAW,IAOjBhe,EAAEqf,YAAa,EACfrf,EAAE8P,cAAgB,SAAUtO,GACnBxB,EAAEqf,aACCrf,EAAEH,OAAOiQ,eAAetO,EAAEiE,iBAC1BzF,EAAEH,OAAOkQ,0BAA4B/P,EAAEyI,YACvCjH,EAAE8d,kBACF9d,EAAE+d,8BAKdvf,EAAE+e,YAAc,SAAUvd,GACtBA,EAAEiE,iBACEzF,EAAEmB,QAAUnB,EAAEH,OAAOkB,MACzBf,EAAE2F,aAEN3F,EAAEif,YAAc,SAAUzd,GACtBA,EAAEiE,iBACEzF,EAAEoH,cAAgBpH,EAAEH,OAAOkB,MAC/Bf,EAAE4F,aAEN5F,EAAEkf,aAAe,SAAU1d,GACvBA,EAAEiE,gBACF,IAAIzD,GAAQxC,EAAED,MAAMyC,QAAUhC,EAAEH,OAAO+N,cACnC5N,GAAEH,OAAOkB,OAAMiB,GAAgBhC,EAAEuQ,cACrCvQ,EAAEqd,QAAQrb,IA0BdhC,EAAEwf,mBAAqB,SAAUhe,GAC7B,GAAI+X,GAAQhY,EAAmBC,EAAG,IAAMxB,EAAEH,OAAOwE,YAC7Cob,GAAa,CACjB,IAAIlG,EACA,IAAK,GAAIhU,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAC7BvF,EAAES,OAAO8E,KAAOgU,IAAOkG,GAAa,EAIhD,KAAIlG,IAASkG,EAOT,MAFAzf,GAAE0f,aAAenS,YACjBvN,EAAE2f,aAAepS,OAGrB,IARIvN,EAAE0f,aAAenG,EACjBvZ,EAAE2f,aAAengB,EAAE+Z,GAAOvX,QAO1BhC,EAAEH,OAAOmQ,qBAA0CzC,SAAnBvN,EAAE2f,cAA8B3f,EAAE2f,eAAiB3f,EAAEW,YAAa,CAClG,GACI6a,GADAoE,EAAe5f,EAAE2f,aAGjBlS,EAA2C,SAA3BzN,EAAEH,OAAO4N,cAA2BzN,EAAEwa,uBAAyBxa,EAAEH,OAAO4N,aAC5F,IAAIzN,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,MACjB+S,GAAYtR,SAAS1K,EAAEQ,EAAE0f,cAAc9e,KAAK,2BAA4B,IACpEZ,EAAEH,OAAOgO,eACJ+R,EAAe5f,EAAEuQ,aAAe9C,EAAc,GAAOmS,EAAe5f,EAAES,OAAOyB,OAASlC,EAAEuQ,aAAe9C,EAAc,GACtHzN,EAAEgB,UACF4e,EAAe5f,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,6BAA+BmX,EAAY,WAAaxb,EAAEH,OAAOsR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPd,EAAEqd,QAAQuC,IACX,IAGH5f,EAAEqd,QAAQuC,GAIVA,EAAe5f,EAAES,OAAOyB,OAASuL,GACjCzN,EAAEgB,UACF4e,EAAe5f,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,6BAA+BmX,EAAY,WAAaxb,EAAEH,OAAOsR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPd,EAAEqd,QAAQuC,IACX,IAGH5f,EAAEqd,QAAQuC,OAKlB5f,GAAEqd,QAAQuC,IAKtB,IAAIC,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAI4BC,EAG5BC,EALAC,EAAe,yCAEfC,EAAgBlY,KAAKmY,MAErBC,IAIJ1gB,GAAEyI,WAAY,EAGdzI,EAAE2gB,SACEC,OAAQ,EACRC,OAAQ,EACRC,SAAU,EACVC,SAAU,EACVC,KAAM,EAIV,IAAIC,GAAcC,CAClBlhB,GAAEwe,aAAe,SAAUhd,GAGvB,GAFIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eAC3B4d,EAA0B,eAAXzf,EAAE2f,KACZF,KAAgB,SAAWzf,KAAiB,IAAZA,EAAE4f,MAAvC,CACA,GAAIphB,EAAEH,OAAOgR,WAAatP,EAAmBC,EAAG,IAAMxB,EAAEH,OAAOiR,gBAE3D,YADA9Q,EAAEqf,YAAa,EAGnB,KAAIrf,EAAEH,OAAO+Q,cACJrP,EAAmBC,EAAGxB,EAAEH,OAAO+Q,cADxC,CAIA,GAAIgQ,GAAS5gB,EAAE2gB,QAAQG,SAAsB,eAAXtf,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,MACrFT,EAAS7gB,EAAE2gB,QAAQI,SAAsB,eAAXvf,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,KAGzF,MAAGvhB,EAAEmV,OAAO2J,KAAO9e,EAAEH,OAAO+K,uBAAyBgW,GAAU5gB,EAAEH,OAAOgL,uBAAxE,CAgBA,GAZAgV,GAAY,EACZC,GAAU,EACVC,GAAsB,EACtBE,EAAc1S,OACd2T,EAAc3T,OACdvN,EAAE2gB,QAAQC,OAASA,EACnB5gB,EAAE2gB,QAAQE,OAASA,EACnBb,EAAiB1X,KAAKmY,MACtBzgB,EAAEqf,YAAa,EACfrf,EAAEkY,sBACFlY,EAAEwhB,eAAiBjU,OACfvN,EAAEH,OAAO6O,UAAY,IAAG0R,GAAqB,GAClC,eAAX5e,EAAE2f,KAAuB,CACzB,GAAI1b,IAAiB,CACjBjG,GAAEgC,EAAEG,QAAQC,GAAG2e,KAAe9a,GAAiB,GAC/CzB,SAASC,eAAiBzE,EAAEwE,SAASC,eAAerC,GAAG2e,IACvDvc,SAASC,cAAcwd,OAEvBhc,GACAjE,EAAEiE,iBAGVzF,EAAEkB,KAAK,eAAgBlB,EAAGwB,OAG9BxB,EAAEye,YAAc,SAAUjd,GAEtB,GADIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,gBACvB4d,GAA2B,cAAXzf,EAAE2f,KAAtB,CACA,GAAI3f,EAAEkgB,wBAGF,MAFA1hB,GAAE2gB,QAAQC,OAAoB,cAAXpf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,WACzEthB,EAAE2gB,QAAQE,OAAoB,cAAXrf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,MAG7E,IAAIvhB,EAAEH,OAAO4O,aAQT,MANAzO,GAAEqf,YAAa,OACXQ,IACA7f,EAAE2gB,QAAQC,OAAS5gB,EAAE2gB,QAAQG,SAAsB,cAAXtf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,MAC9FthB,EAAE2gB,QAAQE,OAAS7gB,EAAE2gB,QAAQI,SAAsB,cAAXvf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,MAC9FvB,EAAiB1X,KAAKmY,OAI9B,IAAIQ,GAAgBjhB,EAAEH,OAAO+O,sBAAwB5O,EAAEH,OAAOkB,KAC1D,GAAKf,EAAE0D,gBAUH,GACK1D,EAAE2gB,QAAQG,SAAW9gB,EAAE2gB,QAAQC,QAAU5gB,EAAEsV,WAAatV,EAAEuH,gBAC1DvH,EAAE2gB,QAAQG,SAAW9gB,EAAE2gB,QAAQC,QAAU5gB,EAAEsV,WAAatV,EAAEsH,eAE3D,WAZJ,IACKtH,EAAE2gB,QAAQI,SAAW/gB,EAAE2gB,QAAQE,QAAU7gB,EAAEsV,WAAatV,EAAEuH,gBAC1DvH,EAAE2gB,QAAQI,SAAW/gB,EAAE2gB,QAAQE,QAAU7gB,EAAEsV,WAAatV,EAAEsH,eAE3D,MAYZ,IAAI2Z,GAAgBjd,SAASC,eACrBzC,EAAEG,SAAWqC,SAASC,eAAiBzE,EAAEgC,EAAEG,QAAQC,GAAG2e,GAGtD,MAFAT,IAAU,OACV9f,EAAEqf,YAAa,EAOvB,IAHIU,GACA/f,EAAEkB,KAAK,cAAelB,EAAGwB,KAEzBA,EAAE6f,eAAiB7f,EAAE6f,cAAcnf,OAAS,GAAhD,CAKA,GAHAlC,EAAE2gB,QAAQG,SAAsB,cAAXtf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,MAC3EthB,EAAE2gB,QAAQI,SAAsB,cAAXvf,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,MAEhD,mBAAhBtB,GAA6B,CACpC,GAAI/R,EACAlO,GAAE0D,gBAAkB1D,EAAE2gB,QAAQI,WAAa/gB,EAAE2gB,QAAQE,SAAW7gB,EAAE0D,gBAAkB1D,EAAE2gB,QAAQG,WAAa9gB,EAAE2gB,QAAQC,OACrHX,GAAc,GAGd/R,EAA4H,IAA/G9N,KAAKuhB,MAAMvhB,KAAKuG,IAAI3G,EAAE2gB,QAAQI,SAAW/gB,EAAE2gB,QAAQE,QAASzgB,KAAKuG,IAAI3G,EAAE2gB,QAAQG,SAAW9gB,EAAE2gB,QAAQC,SAAiBxgB,KAAKwhB,GACvI3B,EAAcjgB,EAAE0D,eAAiBwK,EAAalO,EAAEH,OAAOqO,WAAc,GAAKA,EAAalO,EAAEH,OAAOqO,YAWxG,GARI+R,GACAjgB,EAAEkB,KAAK,sBAAuBlB,EAAGwB,GAEV,mBAAhB0f,IAA+BlhB,EAAEse,QAAQuD,UAC5C7hB,EAAE2gB,QAAQG,WAAa9gB,EAAE2gB,QAAQC,QAAU5gB,EAAE2gB,QAAQI,WAAa/gB,EAAE2gB,QAAQE,SAC5EK,GAAc,IAGjBrB,EAAL,CACA,GAAII,EAEA,YADAJ,GAAY,EAGhB,IAAKqB,IAAelhB,EAAEse,QAAQuD,QAA9B,CAGA7hB,EAAEqf,YAAa,EACfrf,EAAEkB,KAAK,eAAgBlB,EAAGwB,GAC1BA,EAAEiE,iBACEzF,EAAEH,OAAO8O,2BAA6B3O,EAAEH,OAAOwe,QAC/C7c,EAAE8d,kBAGDQ,IACGjgB,EAAOkB,MACPf,EAAEgB,UAENmf,EAAiBngB,EAAEiH,sBACnBjH,EAAEwH,qBAAqB,GACnBxH,EAAEyI,WACFzI,EAAE2U,QAAQmN,QAAQ,oFAElB9hB,EAAEH,OAAOS,UAAYN,EAAEsX,cACnBtX,EAAEH,OAAOwI,6BACTrI,EAAEqB,eAGFrB,EAAE0X,iBAGV4I,GAAsB,GAElBtgB,EAAEH,OAAOgQ,YAAe7P,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAO8D,oBAAqB,GAC5F3D,EAAE6V,eAAc,IAGxBiK,GAAU,CAEV,IAAIkB,GAAOhhB,EAAE2gB,QAAQK,KAAOhhB,EAAE0D,eAAiB1D,EAAE2gB,QAAQG,SAAW9gB,EAAE2gB,QAAQC,OAAS5gB,EAAE2gB,QAAQI,SAAW/gB,EAAE2gB,QAAQE,MAEtHG,IAAchhB,EAAEH,OAAOoO,WACnBjO,EAAEkF,MAAK8b,GAAQA,GAEnBhhB,EAAEwhB,eAAiBR,EAAO,EAAI,OAAS,OACvCd,EAAmBc,EAAOb,CAE1B,IAAI4B,IAAsB,CAwB1B,IAvBKf,EAAO,GAAKd,EAAmBlgB,EAAEsH,gBAClCya,GAAsB,EAClB/hB,EAAEH,OAAO0P,aAAY2Q,EAAmBlgB,EAAEsH,eAAiB,EAAIlH,KAAK4hB,KAAKhiB,EAAEsH,eAAiB6Y,EAAiBa,EAAMhhB,EAAEH,OAAO2P,mBAE3HwR,EAAO,GAAKd,EAAmBlgB,EAAEuH,iBACtCwa,GAAsB,EAClB/hB,EAAEH,OAAO0P,aAAY2Q,EAAmBlgB,EAAEuH,eAAiB,EAAInH,KAAK4hB,IAAIhiB,EAAEuH,eAAiB4Y,EAAiBa,EAAMhhB,EAAEH,OAAO2P,mBAG/HuS,IACAvgB,EAAEkgB,yBAA0B,IAI3B1hB,EAAEH,OAAO4D,kBAAyC,SAArBzD,EAAEwhB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,IAElBngB,EAAEH,OAAO8D,kBAAyC,SAArB3D,EAAEwhB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,GAKnBngB,EAAEH,OAAO6O,UAAY,EAAG,CACxB,KAAItO,KAAKuG,IAAIqa,GAAQhhB,EAAEH,OAAO6O,WAAa0R,GAYvC,YADAF,EAAmBC,EAVnB,KAAKC,EAMD,MALAA,IAAqB,EACrBpgB,EAAE2gB,QAAQC,OAAS5gB,EAAE2gB,QAAQG,SAC7B9gB,EAAE2gB,QAAQE,OAAS7gB,EAAE2gB,QAAQI,SAC7Bb,EAAmBC,OACnBngB,EAAE2gB,QAAQK,KAAOhhB,EAAE0D,eAAiB1D,EAAE2gB,QAAQG,SAAW9gB,EAAE2gB,QAAQC,OAAS5gB,EAAE2gB,QAAQI,SAAW/gB,EAAE2gB,QAAQE,QAUlH7gB,EAAEH,OAAO2O,gBAGVxO,EAAEH,OAAOkH,UAAY/G,EAAEH,OAAO8P,sBAC9B3P,EAAE2H,oBAEF3H,EAAEH,OAAOkH,WAEiB,IAAtB2Z,EAAWxe,QACXwe,EAAWvd,MACP6D,SAAUhH,EAAE2gB,QAAQ3gB,EAAE0D,eAAiB,SAAW,UAClDue,KAAMjC,IAGdU,EAAWvd,MACP6D,SAAUhH,EAAE2gB,QAAQ3gB,EAAE0D,eAAiB,WAAa,YACpDue,MAAM,GAAK3f,QAAOgG,MAAQC,aAIlCvI,EAAE0H,eAAewY,GAEjBlgB,EAAEyH,oBAAoByY,SAE1BlgB,EAAE0e,WAAa,SAAUld,GAMrB,GALIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eACvB0c,GACA/f,EAAEkB,KAAK,aAAclB,EAAGwB,GAE5Bue,GAAsB,EACjBF,EAAL,CAEI7f,EAAEH,OAAOgQ,YAAciQ,GAAWD,IAAe7f,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAO8D,oBAAqB,IACrH3D,EAAE6V,eAAc,EAIpB,IAAIqM,GAAe5Z,KAAKmY,MACpB0B,EAAWD,EAAelC,CA4B9B,IAzBIhgB,EAAEqf,aACFrf,EAAEwf,mBAAmBhe,GACrBxB,EAAEkB,KAAK,QAASlB,EAAGwB,GACf2gB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcvY,aAAauY,GAC/BA,EAAevf,WAAW,WACjBd,IACDA,EAAEH,OAAOoP,gBAAkBjP,EAAE6U,oBAAoB3S,OAAS,IAAM1C,EAAEgC,EAAEG,QAAQ8Z,SAASzb,EAAEH,OAAO4R,cAC9FzR,EAAE6U,oBAAoBuN,YAAYpiB,EAAEH,OAAOiS,uBAE/C9R,EAAEkB,KAAK,UAAWlB,EAAGwB,KACtB,MAGH2gB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcvY,aAAauY,GAC/BrgB,EAAEkB,KAAK,cAAelB,EAAGwB,KAIjCgf,EAAgBlY,KAAKmY,MACrB3f,WAAW,WACHd,IAAGA,EAAEqf,YAAa,IACvB,IAEEQ,IAAcC,IAAY9f,EAAEwhB,gBAAqC,IAAnBxhB,EAAE2gB,QAAQK,MAAcd,IAAqBC,EAE5F,YADAN,EAAYC,GAAU,EAG1BD,GAAYC,GAAU,CAEtB,IAAIuC,EAOJ,IALIA,EADAriB,EAAEH,OAAO2O,aACIxO,EAAEkF,IAAMlF,EAAEsV,WAAatV,EAAEsV,WAGxB4K,EAEdlgB,EAAEH,OAAOkH,SAAU,CACnB,GAAIsb,GAAcriB,EAAEsH,eAEhB,WADAtH,GAAEqd,QAAQrd,EAAEW,YAGX,IAAI0hB,GAAcriB,EAAEuH,eAOrB,YANIvH,EAAES,OAAOyB,OAASlC,EAAE4X,SAAS1V,OAC7BlC,EAAEqd,QAAQrd,EAAE4X,SAAS1V,OAAS,GAG9BlC,EAAEqd,QAAQrd,EAAES,OAAOyB,OAAS,GAKpC,IAAIlC,EAAEH,OAAOiL,iBAAkB,CAC3B,GAAI4V,EAAWxe,OAAS,EAAG,CACvB,GAAIogB,GAAgB5B,EAAW6B,MAAOC,EAAgB9B,EAAW6B,MAE7DE,EAAWH,EAActb,SAAWwb,EAAcxb,SAClDib,EAAOK,EAAcL,KAAOO,EAAcP,IAC9CjiB,GAAEuV,SAAWkN,EAAWR,EACxBjiB,EAAEuV,SAAWvV,EAAEuV,SAAW,EACtBnV,KAAKuG,IAAI3G,EAAEuV,UAAYvV,EAAEH,OAAOsL,0BAChCnL,EAAEuV,SAAW,IAIb0M,EAAO,MAAQ,GAAI3f,QAAOgG,MAAOC,UAAY+Z,EAAcL,KAAQ,OACnEjiB,EAAEuV,SAAW,OAGjBvV,GAAEuV,SAAW,CAEjBvV,GAAEuV,SAAWvV,EAAEuV,SAAWvV,EAAEH,OAAOqL,8BAEnCwV,EAAWxe,OAAS,CACpB,IAAIwgB,GAAmB,IAAO1iB,EAAEH,OAAOkL,sBACnC4X,EAAmB3iB,EAAEuV,SAAWmN,EAEhCE,EAAc5iB,EAAEsV,UAAYqN,CAC5B3iB,GAAEkF,MAAK0d,GAAgBA,EAC3B,IACIC,GADAC,GAAW,EAEXC,EAAsC,GAAvB3iB,KAAKuG,IAAI3G,EAAEuV,UAAiBvV,EAAEH,OAAOoL,2BACxD,IAAI2X,EAAc5iB,EAAEuH,eACZvH,EAAEH,OAAOmL,wBACL4X,EAAc5iB,EAAEuH,gBAAkBwb,IAClCH,EAAc5iB,EAAEuH,eAAiBwb,GAErCF,EAAsB7iB,EAAEuH,eACxBub,GAAW,EACXxC,GAAsB,GAGtBsC,EAAc5iB,EAAEuH,mBAGnB,IAAIqb,EAAc5iB,EAAEsH,eACjBtH,EAAEH,OAAOmL,wBACL4X,EAAc5iB,EAAEsH,eAAiByb,IACjCH,EAAc5iB,EAAEsH,eAAiByb,GAErCF,EAAsB7iB,EAAEsH,eACxBwb,GAAW,EACXxC,GAAsB,GAGtBsC,EAAc5iB,EAAEsH,mBAGnB,IAAItH,EAAEH,OAAOgI,eAAgB,CAC9B,GACI6T,GADAjB,EAAI,CAER,KAAKA,EAAI,EAAGA,EAAIza,EAAE4X,SAAS1V,OAAQuY,GAAK,EACpC,GAAIza,EAAE4X,SAAS6C,IAAMmI,EAAa,CAC9BlH,EAAYjB,CACZ,OAKJmI,EADAxiB,KAAKuG,IAAI3G,EAAE4X,SAAS8D,GAAakH,GAAexiB,KAAKuG,IAAI3G,EAAE4X,SAAS8D,EAAY,GAAKkH,IAAqC,SAArB5iB,EAAEwhB,eACzFxhB,EAAE4X,SAAS8D,GAEX1b,EAAE4X,SAAS8D,EAAY,GAEpC1b,EAAEkF,MAAK0d,GAAgBA,GAGhC,GAAmB,IAAf5iB,EAAEuV,SAEEmN,EADA1iB,EAAEkF,IACiB9E,KAAKuG,MAAMic,EAAc5iB,EAAEsV,WAAatV,EAAEuV,UAG1CnV,KAAKuG,KAAKic,EAAc5iB,EAAEsV,WAAatV,EAAEuV,cAG/D,IAAIvV,EAAEH,OAAOgI,eAEd,WADA7H,GAAEiI,YAIFjI,GAAEH,OAAOmL,wBAA0B8X,GACnC9iB,EAAE0H,eAAemb,GACjB7iB,EAAEwH,qBAAqBkb,GACvB1iB,EAAEyH,oBAAoBmb,GACtB5iB,EAAEgjB,oBACFhjB,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GAAMsgB,IACXtgB,EAAEkB,KAAK,mBAAoBlB,GAE3BA,EAAEwH,qBAAqBxH,EAAEH,OAAO8K,OAChC3K,EAAEyH,oBAAoBob,GACtB7iB,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAEijB,wBAGHjjB,EAAEuV,UACTvV,EAAE0H,eAAekb,GACjB5iB,EAAEwH,qBAAqBkb,GACvB1iB,EAAEyH,oBAAoBmb,GACtB5iB,EAAEgjB,oBACGhjB,EAAEyI,YACHzI,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAEijB,sBAKVjjB,EAAE0H,eAAekb,GAGrB5iB,EAAE2H,oBAMN,cAJK3H,EAAEH,OAAOiL,kBAAoBqX,GAAYniB,EAAEH,OAAO0O,gBACnDvO,EAAE0H,iBACF1H,EAAE2H,sBAMV,GAAIpC,GAAG2d,EAAY,EAAGC,EAAYnjB,EAAEwY,gBAAgB,EACpD,KAAKjT,EAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,GAAKvF,EAAEH,OAAO+N,eACU,mBAA9C5N,GAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,gBAC7ByU,GAAcriB,EAAEuY,WAAWhT,IAAM8c,EAAariB,EAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,kBACxEsV,EAAY3d,EACZ4d,EAAYnjB,EAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,gBAAkB5N,EAAEuY,WAAWhT,IAIrE8c,GAAcriB,EAAEuY,WAAWhT,KAC3B2d,EAAY3d,EACZ4d,EAAYnjB,EAAEuY,WAAWvY,EAAEuY,WAAWrW,OAAS,GAAKlC,EAAEuY,WAAWvY,EAAEuY,WAAWrW,OAAS,GAMnG,IAAIkhB,IAASf,EAAariB,EAAEuY,WAAW2K,IAAcC,CAErD,IAAIhB,EAAWniB,EAAEH,OAAO0O,aAAc,CAElC,IAAKvO,EAAEH,OAAOwO,WAEV,WADArO,GAAEqd,QAAQrd,EAAEW,YAGS,UAArBX,EAAEwhB,iBACE4B,GAASpjB,EAAEH,OAAOyO,gBAAiBtO,EAAEqd,QAAQ6F,EAAYljB,EAAEH,OAAO+N,gBACjE5N,EAAEqd,QAAQ6F,IAGM,SAArBljB,EAAEwhB,iBACE4B,EAAS,EAAIpjB,EAAEH,OAAOyO,gBAAkBtO,EAAEqd,QAAQ6F,EAAYljB,EAAEH,OAAO+N,gBACtE5N,EAAEqd,QAAQ6F,QAGlB,CAED,IAAKljB,EAAEH,OAAOuO,YAEV,WADApO,GAAEqd,QAAQrd,EAAEW,YAGS,UAArBX,EAAEwhB,gBACFxhB,EAAEqd,QAAQ6F,EAAYljB,EAAEH,OAAO+N,gBAGV,SAArB5N,EAAEwhB,gBACFxhB,EAAEqd,QAAQ6F,MAOtBljB,EAAEsB,SAAW,SAAU+hB,EAAY1Y,GAC/B,MAAO3K,GAAEqd,QAAQgG,EAAY1Y,GAAO,GAAM,IAE9C3K,EAAEqd,QAAU,SAAUgG,EAAY1Y,EAAO2Y,EAAc7L,GACvB,mBAAjB6L,KAA8BA,GAAe,GAC9B,mBAAfD,KAA4BA,EAAa,GAChDA,EAAa,IAAGA,EAAa,GACjCrjB,EAAEqb,UAAYjb,KAAKC,MAAMgjB,EAAarjB,EAAEH,OAAO+N,gBAC3C5N,EAAEqb,WAAarb,EAAE4X,SAAS1V,SAAQlC,EAAEqb,UAAYrb,EAAE4X,SAAS1V,OAAS,EAExE,IAAIoT,IAActV,EAAE4X,SAAS5X,EAAEqb,UAc/B,IAZIrb,EAAEH,OAAOS,UAAYN,EAAEsX,cACnBG,IAAazX,EAAEH,OAAOwI,6BACtBrI,EAAE0X,cAAc/M,GAGhB3K,EAAEqB,gBAIVrB,EAAE0H,eAAe4N,GAGdtV,EAAEH,OAAO8Q,oBACR,IAAK,GAAIpL,GAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,KAC/BnF,KAAKC,MAAkB,IAAZiV,IAAoBlV,KAAKC,MAAwB,IAAlBL,EAAEuY,WAAWhT,MACzD8d,EAAa9d,EAMzB,UAAKvF,EAAEH,OAAO4D,kBAAoB6R,EAAYtV,EAAEsV,WAAaA,EAAYtV,EAAEsH,qBAGtEtH,EAAEH,OAAO8D,kBAAoB2R,EAAYtV,EAAEsV,WAAaA,EAAYtV,EAAEuH,iBAClEvH,EAAEW,aAAe,KAAO0iB,KAIZ,mBAAV1Y,KAAuBA,EAAQ3K,EAAEH,OAAO8K,OACnD3K,EAAEsb,cAAgBtb,EAAEW,aAAe,EACnCX,EAAEW,YAAc0iB,EAChBrjB,EAAEub,kBACGvb,EAAEkF,MAAQoQ,IAActV,EAAEsV,YAAgBtV,EAAEkF,KAAOoQ,IAActV,EAAEsV,WAEhEtV,EAAEH,OAAOuL,YACTpL,EAAE6X,mBAEN7X,EAAE4H,gBACsB,UAApB5H,EAAEH,OAAO0L,QACTvL,EAAEyH,oBAAoB6N,IAEnB,IAEXtV,EAAE4H,gBACF5H,EAAEgjB,kBAAkBM,GAEN,IAAV3Y,GAAe3K,EAAEse,QAAQiF,QACzBvjB,EAAEyH,oBAAoB6N,GACtBtV,EAAEwH,qBAAqB,GACvBxH,EAAEijB,gBAAgBK,KAGlBtjB,EAAEyH,oBAAoB6N,GACtBtV,EAAEwH,qBAAqBmD,GAClB3K,EAAEyI,YACHzI,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAEijB,gBAAgBK,QAMvB,MAGXtjB,EAAEgjB,kBAAoB,SAAUM,GACA,mBAAjBA,KAA8BA,GAAe,GACpDtjB,EAAEH,OAAOuL,YACTpL,EAAE6X,mBAEF7X,EAAEmI,MAAMnI,EAAEmI,KAAK6a,oBACfM,IACAtjB,EAAEkB,KAAK,oBAAqBlB,GACxBA,EAAEW,cAAgBX,EAAEsb,gBACpBtb,EAAEkB,KAAK,qBAAsBlB,GACzBA,EAAEW,YAAcX,EAAEsb,cAClBtb,EAAEkB,KAAK,mBAAoBlB,GAG3BA,EAAEkB,KAAK,mBAAoBlB,MAM3CA,EAAEijB,gBAAkB,SAAUK,GAC1BtjB,EAAEyI,WAAY,EACdzI,EAAEwH,qBAAqB,GACK,mBAAjB8b,KAA8BA,GAAe,GACpDtjB,EAAEmI,MAAMnI,EAAEmI,KAAK8a,kBACfK,IACAtjB,EAAEkB,KAAK,kBAAmBlB,GACtBA,EAAEW,cAAgBX,EAAEsb,gBACpBtb,EAAEkB,KAAK,mBAAoBlB,GACvBA,EAAEW,YAAcX,EAAEsb,cAClBtb,EAAEkB,KAAK,iBAAkBlB,GAGzBA,EAAEkB,KAAK,iBAAkBlB,KAIjCA,EAAEH,OAAOuN,SAAWpN,EAAEoN,SACtBpN,EAAEoN,QAAQoW,WAAWxjB,EAAEH,OAAOuN,QAASpN,EAAEW,aAEzCX,EAAEH,OAAOqN,SAAWlN,EAAEkN,SACtBlN,EAAEkN,QAAQuW,WAIlBzjB,EAAE2F,UAAY,SAAU2d,EAAc3Y,EAAO8M,GACzC,GAAIzX,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,OAAO,CACxBzI,GAAEgB,SACehB,GAAEC,UAAU,GAAGyjB,UAChC,OAAO1jB,GAAEqd,QAAQrd,EAAEW,YAAcX,EAAEH,OAAO+N,eAAgBjD,EAAO2Y,EAAc7L,GAE9E,MAAOzX,GAAEqd,QAAQrd,EAAEW,YAAcX,EAAEH,OAAO+N,eAAgBjD,EAAO2Y,EAAc7L,IAExFzX,EAAEiB,WAAa,SAAU0J,GACrB,MAAO3K,GAAE2F,WAAU,EAAMgF,GAAO,IAEpC3K,EAAE4F,UAAY,SAAU0d,EAAc3Y,EAAO8M,GACzC,GAAIzX,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,OAAO,CACxBzI,GAAEgB,SACehB,GAAEC,UAAU,GAAGyjB,UAChC,OAAO1jB,GAAEqd,QAAQrd,EAAEW,YAAc,EAAGgK,EAAO2Y,EAAc7L,GAExD,MAAOzX,GAAEqd,QAAQrd,EAAEW,YAAc,EAAGgK,EAAO2Y,EAAc7L,IAElEzX,EAAE2jB,WAAa,SAAUhZ,GACrB,MAAO3K,GAAE4F,WAAU,EAAM+E,GAAO,IAEpC3K,EAAEiI,WAAa,SAAUqb,EAAc3Y,EAAO8M,GAC1C,MAAOzX,GAAEqd,QAAQrd,EAAEW,YAAagK,EAAO2Y,IAG3CtjB,EAAE4jB,oBAAsB,WAEpB,MADA5jB,GAAEH,OAAO4O,cAAe,GACjB,GAEXzO,EAAE6jB,mBAAqB,WAEnB,MADA7jB,GAAEH,OAAO4O,cAAe,GACjB,GAMXzO,EAAEwH,qBAAuB,SAAUsc,EAAUC,GACzC/jB,EAAE2U,QAAQ0H,WAAWyH,GACG,UAApB9jB,EAAEH,OAAO0L,QAAsBvL,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,SAClDvL,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,QAAQ0Y,cAAcH,GAEzC9jB,EAAEH,OAAOyM,UAAYtM,EAAEsM,UACvBtM,EAAEsM,SAAS2X,cAAcH,GAEzB9jB,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUsX,cAAcH,GAE1B9jB,EAAEH,OAAO2Q,SAAWxQ,EAAEmd,YACtBnd,EAAEmd,WAAW8G,cAAcH,EAAUC,GAEzC/jB,EAAEkB,KAAK,kBAAmBlB,EAAG8jB,IAEjC9jB,EAAEyH,oBAAsB,SAAU6N,EAAW3N,EAAmBoc,GAC5D,GAAIG,GAAI,EAAGC,EAAI,EAAGC,EAAI,CAClBpkB,GAAE0D,eACFwgB,EAAIlkB,EAAEkF,KAAOoQ,EAAYA,EAGzB6O,EAAI7O,EAGJtV,EAAEH,OAAOmO,eACTkW,EAAIhkB,EAAMgkB,GACVC,EAAIjkB,EAAMikB,IAGTnkB,EAAEH,OAAOyL,mBACNtL,EAAEuU,QAAQE,aAAczU,EAAE2U,QAAQxK,UAAU,eAAiB+Z,EAAI,OAASC,EAAI,OAASC,EAAI,OAC1FpkB,EAAE2U,QAAQxK,UAAU,aAAe+Z,EAAI,OAASC,EAAI,QAG7DnkB,EAAEsV,UAAYtV,EAAE0D,eAAiBwgB,EAAIC,CAGrC,IAAIpa,GACAoR,EAAiBnb,EAAEuH,eAAiBvH,EAAEsH,cAEtCyC,GADmB,IAAnBoR,EACW,GAGC7F,EAAYtV,EAAEsH,gBAAkB,EAE5CyC,IAAa/J,EAAE+J,UACf/J,EAAE0H,eAAe4N,GAGjB3N,GAAmB3H,EAAE2H,oBACD,UAApB3H,EAAEH,OAAO0L,QAAsBvL,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,SAClDvL,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,QAAQ8Y,aAAarkB,EAAEsV,WAE1CtV,EAAEH,OAAOyM,UAAYtM,EAAEsM,UACvBtM,EAAEsM,SAAS+X,aAAarkB,EAAEsV,WAE1BtV,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAU0X,aAAarkB,EAAEsV,WAE3BtV,EAAEH,OAAO2Q,SAAWxQ,EAAEmd,YACtBnd,EAAEmd,WAAWkH,aAAarkB,EAAEsV,UAAWyO,GAE3C/jB,EAAEkB,KAAK,iBAAkBlB,EAAGA,EAAEsV,YAGlCtV,EAAEskB,aAAe,SAAU5iB,EAAI6H,GAC3B,GAAIgb,GAAQC,EAAcC,EAAUC,CAOpC,OAJoB,mBAATnb,KACPA,EAAO,KAGPvJ,EAAEH,OAAOyL,iBACFtL,EAAEkF,KAAOlF,EAAEsV,UAAYtV,EAAEsV,WAGpCmP,EAAWniB,OAAOqiB,iBAAiBjjB,EAAI,MACnCY,OAAOsiB,iBACPJ,EAAeC,EAASta,WAAasa,EAASI,gBAC1CL,EAAaM,MAAM,KAAK5iB,OAAS,IACjCsiB,EAAeA,EAAaM,MAAM,MAAMC,IAAI,SAAS5kB,GACjD,MAAOA,GAAEyY,QAAQ,IAAI,OACtBvD,KAAK,OAIZqP,EAAkB,GAAIpiB,QAAOsiB,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAG5EE,EAAkBD,EAASO,cAAgBP,EAASQ,YAAcR,EAASS,aAAeT,EAASU,aAAgBV,EAASta,WAAasa,EAASW,iBAAiB,aAAaxM,QAAQ,aAAc,sBACtM2L,EAASG,EAAgBW,WAAWP,MAAM,MAGjC,MAATvb,IAGIib,EADAliB,OAAOsiB,gBACQF,EAAgBY,IAER,KAAlBf,EAAOriB,OACGyW,WAAW4L,EAAO,KAGlB5L,WAAW4L,EAAO,KAE5B,MAAThb,IAGIib,EADAliB,OAAOsiB,gBACQF,EAAgBa,IAER,KAAlBhB,EAAOriB,OACGyW,WAAW4L,EAAO,KAGlB5L,WAAW4L,EAAO,KAErCvkB,EAAEkF,KAAOsf,IAAcA,GAAgBA,GACpCA,GAAgB,IAE3BxkB,EAAEiH,oBAAsB,SAAUsC,GAI9B,MAHoB,mBAATA,KACPA,EAAOvJ,EAAE0D,eAAiB,IAAM,KAE7B1D,EAAEskB,aAAatkB,EAAE2U,QAAQ,GAAIpL,IAMxCvJ,EAAEkD,aAoBFlD,EAAEwlB,cAAgB,WACd,GAAIxlB,EAAEH,OAAO4S,eAET,IAAK,GADDgT,GAAmBzlB,EAAEC,UAAU4B,UAC1B0D,EAAI,EAAGA,EAAIkgB,EAAiBvjB,OAAQqD,IACzCpD,EAAasjB,EAAiBlgB,GAKtCpD,GAAanC,EAAEC,UAAU,IAAK+C,WAAW,IAGzCb,EAAanC,EAAE2U,QAAQ,IAAK5R,YAAY,KAE5C/C,EAAE0lB,oBAAsB,WACpB,IAAK,GAAIngB,GAAI,EAAGA,EAAIvF,EAAEkD,UAAUhB,OAAQqD,IACpCvF,EAAEkD,UAAUqC,GAAGogB,YAEnB3lB,GAAEkD,cAMNlD,EAAE4lB,WAAa,WAEX5lB,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,qBAAqB0U,QAEnF,IAAIplB,GAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAEjB,UAA3BrE,EAAEH,OAAO4N,eAA6BzN,EAAEH,OAAO0Q,eAAcvQ,EAAEH,OAAO0Q,aAAe9P,EAAOyB,QAE/FlC,EAAEuQ,aAAerG,SAASlK,EAAEH,OAAO0Q,cAAgBvQ,EAAEH,OAAO4N,cAAe,IAC3EzN,EAAEuQ,aAAevQ,EAAEuQ,aAAevQ,EAAEH,OAAOyQ,qBACvCtQ,EAAEuQ,aAAe9P,EAAOyB,SACxBlC,EAAEuQ,aAAe9P,EAAOyB,OAG5B,IAA2CqD,GAAvCugB,KAAoBC,IAOxB,KANAtlB,EAAOV,KAAK,SAAUiC,EAAON,GACzB,GAAI6X,GAAQ/Z,EAAED,KACVyC,GAAQhC,EAAEuQ,cAAcwV,EAAa5iB,KAAKzB,GAC1CM,EAAQvB,EAAOyB,QAAUF,GAASvB,EAAOyB,OAASlC,EAAEuQ,cAAcuV,EAAc3iB,KAAKzB,GACzF6X,EAAM3Y,KAAK,0BAA2BoB,KAErCuD,EAAI,EAAGA,EAAIwgB,EAAa7jB,OAAQqD,IACjCvF,EAAE2U,QAAQqR,OAAOxmB,EAAEumB,EAAaxgB,GAAG0gB,WAAU,IAAOlR,SAAS/U,EAAEH,OAAOsR,qBAE1E,KAAK5L,EAAIugB,EAAc5jB,OAAS,EAAGqD,GAAK,EAAGA,IACvCvF,EAAE2U,QAAQuR,QAAQ1mB,EAAEsmB,EAAcvgB,GAAG0gB,WAAU,IAAOlR,SAAS/U,EAAEH,OAAOsR,uBAGhFnR,EAAEoU,YAAc,WACZpU,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,qBAAqB0U,SACnF7lB,EAAES,OAAO0lB,WAAW,4BAExBnmB,EAAEqU,OAAS,SAAU+R,GACjB,GAAIC,GAAWrmB,EAAEW,YAAcX,EAAEuQ,YACjCvQ,GAAEoU,cACFpU,EAAE4lB,aACF5lB,EAAEsY,mBACE8N,GACApmB,EAAEqd,QAAQgJ,EAAWrmB,EAAEuQ,aAAc,GAAG,IAIhDvQ,EAAEgB,QAAU,WACR,GAAIslB,EAEAtmB,GAAEW,YAAcX,EAAEuQ,cAClB+V,EAAWtmB,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,aAAmBvQ,EAAEW,YACpD2lB,GAAsBtmB,EAAEuQ,aACxBvQ,EAAEqd,QAAQiJ,EAAU,GAAG,GAAO,KAGG,SAA3BtmB,EAAEH,OAAO4N,eAA4BzN,EAAEW,aAAgC,EAAjBX,EAAEuQ,cAAsBvQ,EAAEW,YAAcX,EAAES,OAAOyB,OAAkC,EAAzBlC,EAAEH,OAAO4N,iBAC/H6Y,GAAYtmB,EAAES,OAAOyB,OAASlC,EAAEW,YAAcX,EAAEuQ,aAChD+V,GAAsBtmB,EAAEuQ,aACxBvQ,EAAEqd,QAAQiJ,EAAU,GAAG,GAAO,KAMtCtmB,EAAEumB,YAAc,SAAU9lB,GAItB,GAHIT,EAAEH,OAAOkB,MACTf,EAAEoU,cAEgB,gBAAX3T,IAAuBA,EAAOyB,OACrC,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIvF,EAAE2U,QAAQqR,OAAOvlB,EAAO8E,QAI3CvF,GAAE2U,QAAQqR,OAAOvlB,EAEjBT,GAAEH,OAAOkB,MACTf,EAAE4lB,aAEA5lB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,IAGjBnX,EAAEwmB,aAAe,SAAU/lB,GACnBT,EAAEH,OAAOkB,MACTf,EAAEoU,aAEN,IAAIgH,GAAiBpb,EAAEW,YAAc,CACrC,IAAsB,gBAAXF,IAAuBA,EAAOyB,OAAQ,CAC7C,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIvF,EAAE2U,QAAQuR,QAAQzlB,EAAO8E,GAE5C6V,GAAiBpb,EAAEW,YAAcF,EAAOyB,WAGxClC,GAAE2U,QAAQuR,QAAQzlB,EAElBT,GAAEH,OAAOkB,MACTf,EAAE4lB,aAEA5lB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,GAEbnX,EAAEqd,QAAQjC,EAAgB,GAAG,IAEjCpb,EAAEymB,YAAc,SAAUC,GAClB1mB,EAAEH,OAAOkB,OACTf,EAAEoU,cACFpU,EAAES,OAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,YAEjD,IACIsiB,GADAvL,EAAiBpb,EAAEW,WAEvB,IAA6B,gBAAlB+lB,IAA8BA,EAAcxkB,OAAQ,CAC3D,IAAK,GAAIqD,GAAI,EAAGA,EAAImhB,EAAcxkB,OAAQqD,IACtCohB,EAAgBD,EAAcnhB,GAC1BvF,EAAES,OAAOkmB,IAAgB3mB,EAAES,OAAOC,GAAGimB,GAAed,SACpDc,EAAgBvL,GAAgBA,GAExCA,GAAiBhb,KAAK+Y,IAAIiC,EAAgB,OAG1CuL,GAAgBD,EACZ1mB,EAAES,OAAOkmB,IAAgB3mB,EAAES,OAAOC,GAAGimB,GAAed,SACpDc,EAAgBvL,GAAgBA,IACpCA,EAAiBhb,KAAK+Y,IAAIiC,EAAgB,EAG1Cpb,GAAEH,OAAOkB,MACTf,EAAE4lB,aAGA5lB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,GAETnX,EAAEH,OAAOkB,KACTf,EAAEqd,QAAQjC,EAAiBpb,EAAEuQ,aAAc,GAAG,GAG9CvQ,EAAEqd,QAAQjC,EAAgB,GAAG,IAIrCpb,EAAE4mB,gBAAkB,WAEhB,IAAK,GADDF,MACKnhB,EAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IACjCmhB,EAAcvjB,KAAKoC,EAEvBvF,GAAEymB,YAAYC,IAOlB1mB,EAAEgkB,SACE5X,MACIiY,aAAc,WACV,IAAK,GAAI9e,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBN,EAASsU,EAAM,GAAGc,kBAClBwM,GAAM5hB,CACLjF,GAAEH,OAAOyL,mBAAkBub,GAAU7mB,EAAEsV,UAC5C,IAAIwR,GAAK,CACJ9mB,GAAE0D,iBACHojB,EAAKD,EACLA,EAAK,EAET,IAAIE,GAAe/mB,EAAEH,OAAOuM,KAAKC,UACzBjM,KAAK+Y,IAAI,EAAI/Y,KAAKuG,IAAI4S,EAAM,GAAGxP,UAAW,GAC1C,EAAI3J,KAAK4c,IAAI5c,KAAK+Y,IAAII,EAAM,GAAGxP,UAAU,GAAK,EACtDwP,GACKtE,KACG+R,QAASD,IAEZ5c,UAAU,eAAiB0c,EAAK,OAASC,EAAK,cAK3D7C,cAAe,SAAUH,GAErB,GADA9jB,EAAES,OAAO4b,WAAWyH,GAChB9jB,EAAEH,OAAOyL,kBAAiC,IAAbwY,EAAgB,CAC7C,GAAImD,IAAiB,CACrBjnB,GAAES,OAAOkX,cAAc,WACnB,IAAIsP,GACCjnB,EAAL,CACAinB,GAAiB,EACjBjnB,EAAEyI,WAAY,CAEd,KAAK,GADDye,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzF3hB,EAAI,EAAGA,EAAI2hB,EAAchlB,OAAQqD,IACtCvF,EAAE2U,QAAQmN,QAAQoF,EAAc3hB,UAMpDuG,MACIuY,aAAc,WACV,IAAK,GAAI9e,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBwE,EAAWwP,EAAM,GAAGxP,QACpB/J,GAAEH,OAAOiM,KAAKC,gBACdhC,EAAW3J,KAAK+Y,IAAI/Y,KAAK4c,IAAIzD,EAAM,GAAGxP,SAAU,IAAI,GAExD,IAAI9E,GAASsU,EAAM,GAAGc,kBAClB5O,GAAS,IAAO1B,EAChBod,EAAU1b,EACV2b,EAAU,EACVP,GAAM5hB,EACN6hB,EAAK,CAaT,IAZK9mB,EAAE0D,eAME1D,EAAEkF,MACPiiB,GAAWA,IANXL,EAAKD,EACLA,EAAK,EACLO,GAAWD,EACXA,EAAU,GAMd5N,EAAM,GAAGtD,MAAMoR,QAAUjnB,KAAKuG,IAAIvG,KAAKF,MAAM6J,IAAa/J,EAAES,OAAOyB,OAE/DlC,EAAEH,OAAOiM,KAAKD,aAAc,CAE5B,GAAIyb,GAAetnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAcvnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAaplB,SACbolB,EAAe9nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYrlB,SACZqlB,EAAc/nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMyM,OAAOuB,IAEbD,EAAaplB,SAAQolB,EAAa,GAAGrR,MAAM+Q,QAAU5mB,KAAK+Y,KAAKpP,EAAU,IACzEwd,EAAYrlB,SAAQqlB,EAAY,GAAGtR,MAAM+Q,QAAU5mB,KAAK+Y,IAAIpP,EAAU,IAG9EwP,EACKpP,UAAU,eAAiB0c,EAAK,OAASC,EAAK,oBAAsBM,EAAU,gBAAkBD,EAAU,UAGvHlD,cAAe,SAAUH,GAErB,GADA9jB,EAAES,OAAO4b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,GAC1J9jB,EAAEH,OAAOyL,kBAAiC,IAAbwY,EAAgB,CAC7C,GAAImD,IAAiB,CACrBjnB,GAAES,OAAOC,GAAGV,EAAEW,aAAagX,cAAc,WACrC,IAAIsP,GACCjnB,GACAR,EAAED,MAAMkc,SAASzb,EAAEH,OAAOyE,kBAA/B,CACA2iB,GAAiB,EACjBjnB,EAAEyI,WAAY,CAEd,KAAK,GADDye,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzF3hB,EAAI,EAAGA,EAAI2hB,EAAchlB,OAAQqD,IACtCvF,EAAE2U,QAAQmN,QAAQoF,EAAc3hB,UAMpDyG,MACIqY,aAAc,WACV,GAAuBmD,GAAnBC,EAAgB,CAChBznB,GAAEH,OAAOmM,KAAKC,SACVjM,EAAE0D,gBACF8jB,EAAaxnB,EAAE2U,QAAQG,KAAK,uBACF,IAAtB0S,EAAWtlB,SACXslB,EAAahoB,EAAE,0CACfQ,EAAE2U,QAAQqR,OAAOwB,IAErBA,EAAWvS,KAAK3P,OAAQtF,EAAEqF,MAAQ,SAGlCmiB,EAAaxnB,EAAEC,UAAU6U,KAAK,uBACJ,IAAtB0S,EAAWtlB,SACXslB,EAAahoB,EAAE,0CACfQ,EAAEC,UAAU+lB,OAAOwB,KAI/B,KAAK,GAAIjiB,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBmiB,EAAiB,GAAJniB,EACbrF,EAAQE,KAAKC,MAAMqnB,EAAa,IAChC1nB,GAAEkF,MACFwiB,GAAcA,EACdxnB,EAAQE,KAAKC,OAAOqnB,EAAa,KAErC,IAAI3d,GAAW3J,KAAK+Y,IAAI/Y,KAAK4c,IAAIzD,EAAM,GAAGxP,SAAU,IAAI,GACpD8c,EAAK,EAAGC,EAAK,EAAGa,EAAK,CACrBpiB,GAAI,IAAM,GACVshB,EAAe,GAAR3mB,EAAYF,EAAEqY,KACrBsP,EAAK,IAECpiB,EAAI,GAAK,IAAM,GACrBshB,EAAK,EACLc,EAAe,GAARznB,EAAYF,EAAEqY,OAEf9S,EAAI,GAAK,IAAM,GACrBshB,EAAK7mB,EAAEqY,KAAe,EAARnY,EAAYF,EAAEqY,KAC5BsP,EAAK3nB,EAAEqY,OAED9S,EAAI,GAAK,IAAM,IACrBshB,GAAO7mB,EAAEqY,KACTsP,EAAK,EAAI3nB,EAAEqY,KAAgB,EAATrY,EAAEqY,KAAWnY,GAE/BF,EAAEkF,MACF2hB,GAAMA,GAGL7mB,EAAE0D,iBACHojB,EAAKD,EACLA,EAAK,EAGT,IAAI1c,GAAY,YAAcnK,EAAE0D,eAAiB,GAAKgkB,GAAc,iBAAmB1nB,EAAE0D,eAAiBgkB,EAAa,GAAK,oBAAsBb,EAAK,OAASC,EAAK,OAASa,EAAK,KAMnL,IALI5d,GAAY,GAAKA,GAAW,IAC5B0d,EAAoB,GAAJliB,EAAoB,GAAXwE,EACrB/J,EAAEkF,MAAKuiB,EAAqB,IAAJliB,EAAoB,GAAXwE,IAEzCwP,EAAMpP,UAAUA,GACZnK,EAAEH,OAAOmM,KAAKH,aAAc,CAE5B,GAAIyb,GAAetnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAcvnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAaplB,SACbolB,EAAe9nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYrlB,SACZqlB,EAAc/nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMyM,OAAOuB,IAEbD,EAAaplB,SAAQolB,EAAa,GAAGrR,MAAM+Q,QAAU5mB,KAAK+Y,KAAKpP,EAAU,IACzEwd,EAAYrlB,SAAQqlB,EAAY,GAAGtR,MAAM+Q,QAAU5mB,KAAK+Y,IAAIpP,EAAU,KAUlF,GAPA/J,EAAE2U,QAAQM,KACN2S,2BAA4B,YAAe5nB,EAAEqY,KAAO,EAAK,KACzDwP,wBAAyB,YAAe7nB,EAAEqY,KAAO,EAAK,KACtDyP,uBAAwB,YAAe9nB,EAAEqY,KAAO,EAAK,KACrD0P,mBAAoB,YAAe/nB,EAAEqY,KAAO,EAAK,OAGjDrY,EAAEH,OAAOmM,KAAKC,OACd,GAAIjM,EAAE0D,eACF8jB,EAAWrd,UAAU,qBAAuBnK,EAAEqF,MAAQ,EAAIrF,EAAEH,OAAOmM,KAAKE,cAAgB,QAAWlM,EAAEqF,MAAQ,EAAK,0CAA6CrF,EAAEH,OAAOmM,KAAgB,YAAI,SAE3L,CACD,GAAIgc,GAAc5nB,KAAKuG,IAAI8gB,GAA4D,GAA3CrnB,KAAKC,MAAMD,KAAKuG,IAAI8gB,GAAiB,IAC7EQ,EAAa,KAAO7nB,KAAK8nB,IAAkB,EAAdF,EAAkB5nB,KAAKwhB,GAAK,KAAO,EAAIxhB,KAAK+nB,IAAkB,EAAdH,EAAkB5nB,KAAKwhB,GAAK,KAAO,GAChHwG,EAASpoB,EAAEH,OAAOmM,KAAKG,YACvBkc,EAASroB,EAAEH,OAAOmM,KAAKG,YAAc8b,EACrChjB,EAASjF,EAAEH,OAAOmM,KAAKE,YAC3Bsb,GAAWrd,UAAU,WAAaie,EAAS,QAAUC,EAAS,uBAAyBroB,EAAEsF,OAAS,EAAIL,GAAU,QAAWjF,EAAEsF,OAAS,EAAI+iB,EAAU,uBAG5J,GAAIC,GAAWtoB,EAAEuoB,UAAYvoB,EAAEwoB,aAAiBxoB,EAAEqY,KAAO,EAAK,CAC9DrY,GAAE2U,QAAQxK,UAAU,qBAAuBme,EAAU,gBAAkBtoB,EAAE0D,eAAiB,EAAI+jB,GAAiB,iBAAmBznB,EAAE0D,gBAAkB+jB,EAAgB,GAAK,SAE/KxD,cAAe,SAAUH,GACrB9jB,EAAES,OAAO4b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,GAC1J9jB,EAAEH,OAAOmM,KAAKC,SAAWjM,EAAE0D,gBAC3B1D,EAAEC,UAAU6U,KAAK,uBAAuBuH,WAAWyH,KAI/DtY,WACI6Y,aAAc,WAMV,IAAK,GALDla,GAAYnK,EAAEsV,UACdmT,EAASzoB,EAAE0D,gBAAkByG,EAAYnK,EAAEqF,MAAQ,GAAK8E,EAAYnK,EAAEsF,OAAS,EAC/EmG,EAASzL,EAAE0D,eAAiB1D,EAAEH,OAAO2L,UAAUC,QAASzL,EAAEH,OAAO2L,UAAUC,OAC3E6J,EAAYtV,EAAEH,OAAO2L,UAAUG,MAE1BpG,EAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpB6T,EAAYpZ,EAAEwY,gBAAgBjT,GAC9BmjB,EAAcnP,EAAM,GAAGc,kBACvBsO,GAAoBF,EAASC,EAActP,EAAY,GAAKA,EAAYpZ,EAAEH,OAAO2L,UAAUI,SAE3Fub,EAAUnnB,EAAE0D,eAAiB+H,EAASkd,EAAmB,EACzDvB,EAAUpnB,EAAE0D,eAAiB,EAAI+H,EAASkd,EAE1CC,GAActT,EAAYlV,KAAKuG,IAAIgiB,GAEnCE,EAAa7oB,EAAE0D,eAAiB,EAAI1D,EAAEH,OAAO2L,UAAUE,QAAU,EACjEod,EAAa9oB,EAAE0D,eAAiB1D,EAAEH,OAAO2L,UAAUE,QAAU,EAAqB,CAGlFtL,MAAKuG,IAAImiB,GAAc,OAAOA,EAAa,GAC3C1oB,KAAKuG,IAAIkiB,GAAc,OAAOA,EAAa,GAC3CzoB,KAAKuG,IAAIiiB,GAAc,OAAOA,EAAa,GAC3CxoB,KAAKuG,IAAIwgB,GAAW,OAAOA,EAAU,GACrC/mB,KAAKuG,IAAIygB,GAAW,OAAOA,EAAU,EAEzC,IAAI2B,GAAiB,eAAiBD,EAAa,MAAQD,EAAa,MAAQD,EAAa,gBAAkBxB,EAAU,gBAAkBD,EAAU,MAIrJ,IAFA5N,EAAMpP,UAAU4e,GAChBxP,EAAM,GAAGtD,MAAMoR,QAAUjnB,KAAKuG,IAAIvG,KAAKF,MAAMyoB,IAAqB,EAC9D3oB,EAAEH,OAAO2L,UAAUK,aAAc,CAEjC,GAAIyb,GAAetnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAcvnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAaplB,SACbolB,EAAe9nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYrlB,SACZqlB,EAAc/nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMyM,OAAOuB,IAEbD,EAAaplB,SAAQolB,EAAa,GAAGrR,MAAM+Q,QAAU2B,EAAmB,EAAIA,EAAmB,GAC/FpB,EAAYrlB,SAAQqlB,EAAY,GAAGtR,MAAM+Q,SAAY2B,EAAoB,GAAKA,EAAmB,IAK7G,GAAI3oB,EAAEse,QAAQC,GAAI,CACd,GAAIyK,GAAKhpB,EAAE2U,QAAQ,GAAGsB,KACtB+S,GAAGC,kBAAoBR,EAAS,WAGxCxE,cAAe,SAAUH,GACrB9jB,EAAES,OAAO4b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,MAQ1K9jB,EAAEmI,MACE+gB,oBAAoB,EACpBC,iBAAkB,SAAUnnB,EAAOonB,GAC/B,GAAqB,mBAAVpnB,KACoB,mBAApBonB,KAAiCA,GAAkB,GACtC,IAApBppB,EAAES,OAAOyB,QAAb,CAEA,GAAIqX,GAAQvZ,EAAES,OAAOC,GAAGsB,GACpBqnB,EAAM9P,EAAMzE,KAAK,IAAM9U,EAAEH,OAAOqS,iBAAmB,SAAWlS,EAAEH,OAAOuS,sBAAwB,UAAYpS,EAAEH,OAAOsS,uBAAyB,MAC7IoH,EAAMkC,SAASzb,EAAEH,OAAOqS,mBAAsBqH,EAAMkC,SAASzb,EAAEH,OAAOuS,wBAA2BmH,EAAMkC,SAASzb,EAAEH,OAAOsS,0BACzHkX,EAAMA,EAAIC,IAAI/P,EAAM,KAEL,IAAf8P,EAAInnB,QAERmnB,EAAItpB,KAAK,WACL,GAAIwpB,GAAO/pB,EAAED,KACbgqB,GAAKxU,SAAS/U,EAAEH,OAAOsS,uBACvB,IAAIqX,GAAaD,EAAK3oB,KAAK,mBACvB2V,EAAMgT,EAAK3oB,KAAK,YAChB4V,EAAS+S,EAAK3oB,KAAK,eACnB6V,EAAQ8S,EAAK3oB,KAAK,aACtBZ,GAAEqW,UAAUkT,EAAK,GAAKhT,GAAOiT,EAAahT,EAAQC,GAAO,EAAO,WAuB5D,GAtBI+S,GACAD,EAAKtU,IAAI,mBAAoB,QAAUuU,EAAa,MACpDD,EAAKpD,WAAW,qBAGZ3P,IACA+S,EAAK3oB,KAAK,SAAU4V,GACpB+S,EAAKpD,WAAW,gBAEhB1P,IACA8S,EAAK3oB,KAAK,QAAS6V,GACnB8S,EAAKpD,WAAW,eAEhB5P,IACAgT,EAAK3oB,KAAK,MAAO2V,GACjBgT,EAAKpD,WAAW,cAKxBoD,EAAKxU,SAAS/U,EAAEH,OAAOuS,uBAAuB0I,YAAY9a,EAAEH,OAAOsS,wBACnEoH,EAAMzE,KAAK,IAAM9U,EAAEH,OAAOwS,mBAAqB,MAAQrS,EAAEH,OAAO0S,gBAAgBsT,SAC5E7lB,EAAEH,OAAOkB,MAAQqoB,EAAiB,CAClC,GAAIK,GAAqBlQ,EAAM3Y,KAAK,0BACpC,IAAI2Y,EAAMkC,SAASzb,EAAEH,OAAOsR,qBAAsB,CAC9C,GAAIuY,GAAgB1pB,EAAE2U,QAAQC,SAAS,6BAA+B6U,EAAqB,WAAazpB,EAAEH,OAAOsR,oBAAsB,IACvInR,GAAEmI,KAAKghB,iBAAiBO,EAAc1nB,SAAS,OAE9C,CACD,GAAI2nB,GAAkB3pB,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOsR,oBAAsB,6BAA+BsY,EAAqB,KAClIzpB,GAAEmI,KAAKghB,iBAAiBQ,EAAgB3nB,SAAS,IAGzDhC,EAAEkB,KAAK,mBAAoBlB,EAAGuZ,EAAM,GAAIgQ,EAAK,MAGjDvpB,EAAEkB,KAAK,kBAAmBlB,EAAGuZ,EAAM,GAAIgQ,EAAK,QAIpDnhB,KAAM,WACF,GAAI7C,GACAkI,EAAgBzN,EAAEH,OAAO4N,aAK7B,IAJsB,SAAlBA,IACAA,EAAgB,GAEfzN,EAAEmI,KAAK+gB,qBAAoBlpB,EAAEmI,KAAK+gB,oBAAqB,GACxDlpB,EAAEH,OAAO+P,sBACT5P,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOqR,mBAAmBnR,KAAK,WACtDC,EAAEmI,KAAKghB,iBAAiB3pB,EAAED,MAAMyC,eAIpC,IAAIyL,EAAgB,EAChB,IAAKlI,EAAIvF,EAAEW,YAAa4E,EAAIvF,EAAEW,YAAc8M,EAAgBlI,IACpDvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAKghB,iBAAiB5jB,OAI7CvF,GAAEmI,KAAKghB,iBAAiBnpB,EAAEW,YAGlC,IAAIX,EAAEH,OAAOoQ,sBACT,GAAIxC,EAAgB,GAAMzN,EAAEH,OAAOqQ,6BAA+BlQ,EAAEH,OAAOqQ,4BAA8B,EAAI,CACzG,GAAI0Z,GAAS5pB,EAAEH,OAAOqQ,4BAClBwK,EAAMjN,EACNoc,EAAWzpB,KAAK4c,IAAIhd,EAAEW,YAAc+Z,EAAMta,KAAK+Y,IAAIyQ,EAAQlP,GAAM1a,EAAES,OAAOyB,QAC1E4nB,EAAW1pB,KAAK+Y,IAAInZ,EAAEW,YAAcP,KAAK+Y,IAAIuB,EAAKkP,GAAS,EAE/D,KAAKrkB,EAAIvF,EAAEW,YAAc8M,EAAelI,EAAIskB,EAAUtkB,IAC9CvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAKghB,iBAAiB5jB,EAG7C,KAAKA,EAAIukB,EAAUvkB,EAAIvF,EAAEW,YAAc4E,IAC/BvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAKghB,iBAAiB5jB,OAG5C,CACD,GAAImW,GAAY1b,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOuR,eAC9CsK,GAAUxZ,OAAS,GAAGlC,EAAEmI,KAAKghB,iBAAiBzN,EAAU1Z,QAE5D,IAAI4Z,GAAY5b,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOyR,eAC9CsK,GAAU1Z,OAAS,GAAGlC,EAAEmI,KAAKghB,iBAAiBvN,EAAU5Z,WAIxEghB,kBAAmB,WACXhjB,EAAEH,OAAOqI,cACLlI,EAAEH,OAAOsQ,+BAAkCnQ,EAAEH,OAAOsQ,+BAAiCnQ,EAAEmI,KAAK+gB,qBAC5FlpB,EAAEmI,KAAKC,QAInB6a,gBAAiB,WACTjjB,EAAEH,OAAOqI,cAAgBlI,EAAEH,OAAOsQ,8BAClCnQ,EAAEmI,KAAKC,SASnBpI,EAAE2M,WACEkT,WAAW,EACXkK,gBAAiB,SAAUvoB,GACvB,GAAIwoB,GAAKhqB,EAAE2M,UAGPsd,EAAkBjqB,EAAE0D,eACP,eAAXlC,EAAE2f,MAAoC,cAAX3f,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,OAAS9f,EAAE0oB,QAClF,eAAX1oB,EAAE2f,MAAoC,cAAX3f,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,OAAS/f,EAAE2oB,QAC/FnjB,EAAW,EAAoBgjB,EAAGI,MAAMnlB,SAASjF,EAAE0D,eAAiB,OAAS,OAASsmB,EAAGK,SAAW,EACpGC,GAAetqB,EAAEsH,eAAiB0iB,EAAGO,YACrCC,GAAexqB,EAAEuH,eAAiByiB,EAAGO,WACrCvjB,GAAWsjB,EACXtjB,EAAWsjB,EAENtjB,EAAWwjB,IAChBxjB,EAAWwjB,GAEfxjB,GAAYA,EAAWgjB,EAAGO,YAC1BvqB,EAAE0H,eAAeV,GACjBhH,EAAEyH,oBAAoBT,GAAU,IAEpCyjB,UAAW,SAAUjpB,GACjB,GAAIwoB,GAAKhqB,EAAE2M,SACXqd,GAAGnK,WAAY,EACfre,EAAEiE,iBACFjE,EAAE8d,kBAEF0K,EAAGD,gBAAgBvoB,GACnBsG,aAAakiB,EAAGU,aAEhBV,EAAGI,MAAM/N,WAAW,GAChBrc,EAAEH,OAAO+M,eACTod,EAAGI,MAAMnV,IAAI,UAAW,GAE5BjV,EAAE2U,QAAQ0H,WAAW,KACrB2N,EAAGW,KAAKtO,WAAW,KACnBrc,EAAEkB,KAAK,uBAAwBlB,IAEnC4qB,SAAU,SAAUppB,GAChB,GAAIwoB,GAAKhqB,EAAE2M,SACNqd,GAAGnK,YACJre,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,EACrBskB,EAAGD,gBAAgBvoB,GACnBxB,EAAE2U,QAAQ0H,WAAW,GACrB2N,EAAGI,MAAM/N,WAAW,GACpB2N,EAAGW,KAAKtO,WAAW,GACnBrc,EAAEkB,KAAK,sBAAuBlB,KAElC6qB,QAAS,SAAUrpB,GACf,GAAIwoB,GAAKhqB,EAAE2M,SACNqd,GAAGnK,YACRmK,EAAGnK,WAAY,EACX7f,EAAEH,OAAO+M,gBACT9E,aAAakiB,EAAGU,aAChBV,EAAGU,YAAc5pB,WAAW,WACxBkpB,EAAGI,MAAMnV,IAAI,UAAW,GACxB+U,EAAGI,MAAM/N,WAAW,MACrB,MAGPrc,EAAEkB,KAAK,qBAAsBlB,GACzBA,EAAEH,OAAOiN,wBACT9M,EAAEiI,eAGV6iB,gBAAiB,WACb,MAAK9qB,GAAEH,OAAOsO,iBAAkB,GAAUnO,EAAEuU,QAAQG,MACxC1U,EAAE+d,YADqD/d,EAAEwd,sBAGzEuN,gBAAiB,WACb,GAAIf,GAAKhqB,EAAE2M,UACPhL,EAAS3B,EAAEuU,QAAQG,MAAQsV,EAAGI,MAAQpmB,QAC1CxE,GAAEwqB,EAAGI,OAAOY,GAAGhB,EAAGc,gBAAgBrN,MAAOuM,EAAGS,WAC5CjrB,EAAEmC,GAAQqpB,GAAGhB,EAAGc,gBAAgBpN,KAAMsM,EAAGY,UACzCprB,EAAEmC,GAAQqpB,GAAGhB,EAAGc,gBAAgBnN,IAAKqM,EAAGa,UAE5CI,iBAAkB,WACd,GAAIjB,GAAKhqB,EAAE2M,UACPhL,EAAS3B,EAAEuU,QAAQG,MAAQsV,EAAGI,MAAQpmB,QAC1CxE,GAAEwqB,EAAGI,OAAOc,IAAIlB,EAAGc,gBAAgBrN,MAAOuM,EAAGS,WAC7CjrB,EAAEmC,GAAQupB,IAAIlB,EAAGc,gBAAgBpN,KAAMsM,EAAGY,UAC1CprB,EAAEmC,GAAQupB,IAAIlB,EAAGc,gBAAgBnN,IAAKqM,EAAGa,UAE7C5N,IAAK,WACD,GAAKjd,EAAEH,OAAO8M,UAAd,CACA,GAAIqd,GAAKhqB,EAAE2M,SACXqd,GAAGI,MAAQ5qB,EAAEQ,EAAEH,OAAO8M,WAClB3M,EAAEH,OAAOgP,mBAAmD,gBAAvB7O,GAAEH,OAAO8M,WAA0Bqd,EAAGI,MAAMloB,OAAS,GAAqD,IAAhDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO8M,WAAWzK,SACpI8nB,EAAGI,MAAQpqB,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO8M,YAEzCqd,EAAGW,KAAOX,EAAGI,MAAMtV,KAAK,0BACD,IAAnBkV,EAAGW,KAAKzoB,SACR8nB,EAAGW,KAAOnrB,EAAE,6CACZwqB,EAAGI,MAAMpE,OAAOgE,EAAGW,OAEvBX,EAAGW,KAAK,GAAG1U,MAAM5Q,MAAQ,GACzB2kB,EAAGW,KAAK,GAAG1U,MAAM3Q,OAAS,GAC1B0kB,EAAGmB,UAAYnrB,EAAE0D,eAAiBsmB,EAAGI,MAAM,GAAGgB,YAAcpB,EAAGI,MAAM,GAAGnS,aAExE+R,EAAGqB,QAAUrrB,EAAEqY,KAAOrY,EAAE6Y,YACxBmR,EAAGO,YAAcP,EAAGqB,SAAWrB,EAAGmB,UAAYnrB,EAAEqY,MAChD2R,EAAGK,SAAWL,EAAGmB,UAAYnB,EAAGqB,QAE5BrrB,EAAE0D,eACFsmB,EAAGW,KAAK,GAAG1U,MAAM5Q,MAAQ2kB,EAAGK,SAAW,KAGvCL,EAAGW,KAAK,GAAG1U,MAAM3Q,OAAS0kB,EAAGK,SAAW,KAGxCL,EAAGqB,SAAW,EACdrB,EAAGI,MAAM,GAAGnU,MAAMqV,QAAU,OAG5BtB,EAAGI,MAAM,GAAGnU,MAAMqV,QAAU,GAE5BtrB,EAAEH,OAAO+M,gBACTod,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,KAGpC3C,aAAc,WACV,GAAKrkB,EAAEH,OAAO8M,UAAd,CACA,GAGI4e,GAFAvB,EAAKhqB,EAAE2M,UAIP6e,GAHYxrB,EAAEsV,WAAa,EAGjB0U,EAAGK,SACjBkB,IAAUvB,EAAGmB,UAAYnB,EAAGK,UAAYrqB,EAAE+J,SACtC/J,EAAEkF,KAAOlF,EAAE0D,gBACX6nB,GAAUA,EACNA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,IAEHA,EAASvB,EAAGK,SAAWL,EAAGmB,YAChCK,EAAUxB,EAAGmB,UAAYI,IAIzBA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,GAEJA,EAASvB,EAAGK,SAAWL,EAAGmB,YAC/BK,EAAUxB,EAAGmB,UAAYI,GAG7BvrB,EAAE0D,gBACE1D,EAAEuU,QAAQE,aACVuV,EAAGW,KAAKxgB,UAAU,eAAiB,EAAW,aAG9C6f,EAAGW,KAAKxgB,UAAU,cAAgB,EAAW,OAEjD6f,EAAGW,KAAK,GAAG1U,MAAM5Q,MAAQmmB,EAAU,OAG/BxrB,EAAEuU,QAAQE,aACVuV,EAAGW,KAAKxgB,UAAU,oBAAsB,EAAW,UAGnD6f,EAAGW,KAAKxgB,UAAU,cAAgB,EAAW,OAEjD6f,EAAGW,KAAK,GAAG1U,MAAM3Q,OAASkmB,EAAU,MAEpCxrB,EAAEH,OAAO+M,gBACT9E,aAAakiB,EAAGhiB,SAChBgiB,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,EAC5BgD,EAAGhiB,QAAUlH,WAAW,WACpBkpB,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,EAC5BgD,EAAGI,MAAM/N,WAAW,MACrB,QAGX4H,cAAe,SAAUH,GAChB9jB,EAAEH,OAAO8M,WACd3M,EAAE2M,UAAUge,KAAKtO,WAAWyH,KAOpC9jB,EAAEmd,YACEsO,aAAc,SAAUvH,EAAGC,GACvB5kB,KAAK2kB,EAAIA,EACT3kB,KAAK4kB,EAAIA,EACT5kB,KAAKmsB,UAAYxH,EAAEhiB,OAAS,CAI5B,IAAIypB,GAAIC,CACArsB,MAAK2kB,EAAEhiB,MAEf3C,MAAKssB,YAAc,SAAUC,GACzB,MAAKA,IAGLF,EAAKG,EAAaxsB,KAAK2kB,EAAG4H,GAC1BH,EAAKC,EAAK,GAIDE,EAAKvsB,KAAK2kB,EAAEyH,KAAQpsB,KAAK4kB,EAAEyH,GAAMrsB,KAAK4kB,EAAEwH,KAASpsB,KAAK2kB,EAAE0H,GAAMrsB,KAAK2kB,EAAEyH,IAAOpsB,KAAK4kB,EAAEwH,IAR5E,EAWpB,IAAII,GAAe,WACf,GAAIlC,GAAUC,EAAUkC,CACxB,OAAO,UAASC,EAAOC,GAGnB,IAFApC,GAAW,EACXD,EAAWoC,EAAM/pB,OACV2nB,EAAWC,EAAW,GACrBmC,EAAMD,EAAQnC,EAAWC,GAAY,IAAMoC,EAC3CpC,EAAWkC,EAEXnC,EAAWmC,CAEnB,OAAOnC,QAKnBsC,uBAAwB,SAASC,GACzBpsB,EAAEmd,WAAWC,SAAQpd,EAAEmd,WAAWC,OAASpd,EAAEH,OAAOkB,KACpD,GAAIf,GAAEmd,WAAWsO,aAAazrB,EAAEuY,WAAY6T,EAAE7T,YAC9C,GAAIvY,GAAEmd,WAAWsO,aAAazrB,EAAE4X,SAAUwU,EAAExU,YAEpDyM,aAAc,SAAU/O,EAAWyO,GAGhC,QAASsI,GAAuBD,GAK3B9W,EAAY8W,EAAElnB,KAA8B,eAAvBknB,EAAEvsB,OAAO2K,WAA8BxK,EAAEsV,UAAYtV,EAAEsV,UACjD,UAAvBtV,EAAEH,OAAO6Q,YACT1Q,EAAEmd,WAAWgP,uBAAuBC,GAGpCE,GAAuBtsB,EAAEmd,WAAWC,OAAOyO,aAAavW,IAGxDgX,GAA8C,cAAvBtsB,EAAEH,OAAO6Q,YAChCuX,GAAcmE,EAAE7kB,eAAiB6kB,EAAE9kB,iBAAmBtH,EAAEuH,eAAiBvH,EAAEsH,gBAC3EglB,GAAuBhX,EAAYtV,EAAEsH,gBAAkB2gB,EAAamE,EAAE9kB,gBAGtEtH,EAAEH,OAAO4Q,iBACT6b,EAAsBF,EAAE7kB,eAAiB+kB,GAE7CF,EAAE1kB,eAAe4kB,GACjBF,EAAE3kB,oBAAoB6kB,GAAqB,EAAOtsB,GAClDosB,EAAEzkB,oBAzBP,GACIsgB,GAAYqE,EADZC,EAAavsB,EAAEH,OAAO2Q,OA2B1B,IAAIxQ,EAAEwsB,QAAQD,GACV,IAAK,GAAIhnB,GAAI,EAAGA,EAAIgnB,EAAWrqB,OAAQqD,IAC/BgnB,EAAWhnB,KAAOwe,GAAgBwI,EAAWhnB,YAAclG,IAC3DgtB,EAAuBE,EAAWhnB,QAIrCgnB,aAAsBltB,IAAU0kB,IAAiBwI,GAEtDF,EAAuBE;EAG9BtI,cAAe,SAAUH,EAAUC,GAG/B,QAAS0I,GAAwBL,GAC7BA,EAAE5kB,qBAAqBsc,EAAU9jB,GAChB,IAAb8jB,IACAsI,EAAEpJ,oBACFoJ,EAAEzX,QAAQgD,cAAc,WACf4U,IACDH,EAAEvsB,OAAOkB,MAA+B,UAAvBf,EAAEH,OAAO6Q,WAC1B0b,EAAEprB,UAENorB,EAAEnJ,sBAXd,GACI1d,GADAgnB,EAAavsB,EAAEH,OAAO2Q,OAgB1B,IAAIxQ,EAAEwsB,QAAQD,GACV,IAAKhnB,EAAI,EAAGA,EAAIgnB,EAAWrqB,OAAQqD,IAC3BgnB,EAAWhnB,KAAOwe,GAAgBwI,EAAWhnB,YAAclG,IAC3DotB,EAAwBF,EAAWhnB,QAItCgnB,aAAsBltB,IAAU0kB,IAAiBwI,GACtDE,EAAwBF,KAQpCvsB,EAAEkN,SACEwf,YAAa,SAAUlrB,EAAGrB,GACtB,GAAIwsB,GAAU3oB,SAAS4oB,SAASC,KAAKjU,QAAQ,IAAK,IAC9CkU,EAAkB9sB,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,YAClD+rB,KAAYG,GACZ9sB,EAAEqd,QAAQrd,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,eAAiB,EAAY,MAAMrC,UAGpGmd,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAC9Bze,GAAE8C,QAAQ6b,GAAQ,aAAcne,EAAEkN,QAAQwf,cAE9CjJ,QAAS,WACL,GAAKzjB,EAAEkN,QAAQ6f,aAAgB/sB,EAAEH,OAAOqN,QACxC,GAAIlN,EAAEH,OAAOwN,cAAgB/K,OAAO8K,SAAW9K,OAAO8K,QAAQC,aAC1D/K,OAAO8K,QAAQC,aAAa,KAAM,KAAO,IAAMrN,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,cAAgB,QAC5F,CACH,GAAI2Y,GAAQvZ,EAAES,OAAOC,GAAGV,EAAEW,aACtBksB,EAAOtT,EAAM3Y,KAAK,cAAgB2Y,EAAM3Y,KAAK,eACjDoD,UAAS4oB,SAASC,KAAOA,GAAQ,KAGzCG,KAAM,WACF,GAAKhtB,EAAEH,OAAOqN,UAAWlN,EAAEH,OAAOuN,QAAlC,CACApN,EAAEkN,QAAQ6f,aAAc,CACxB,IAAIF,GAAO7oB,SAAS4oB,SAASC,KAAKjU,QAAQ,IAAK,GAC/C,IAAIiU,EAEA,IAAK,GADDliB,GAAQ,EACHpF,EAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpB0nB,EAAY1T,EAAM3Y,KAAK,cAAgB2Y,EAAM3Y,KAAK,eACtD,IAAIqsB,IAAcJ,IAAStT,EAAMkC,SAASzb,EAAEH,OAAOsR,qBAAsB,CACrE,GAAInP,GAAQuX,EAAMvX,OAClBhC,GAAEqd,QAAQrb,EAAO2I,EAAO3K,EAAEH,OAAOmT,oBAAoB,IAI7DhT,EAAEH,OAAOsN,mBAAmBnN,EAAEkN,QAAQiS,iBAE9C+N,QAAS,WACDltB,EAAEH,OAAOsN,mBAAmBnN,EAAEkN,QAAQiS,cAAa,KAO/Dnf,EAAEoN,SACE4f,KAAM,WACF,GAAKhtB,EAAEH,OAAOuN,QAAd,CACA,IAAK9K,OAAO8K,UAAY9K,OAAO8K,QAAQ+f,UAGnC,MAFAntB,GAAEH,OAAOuN,SAAU,OACnBpN,EAAEH,OAAOqN,SAAU,EAGvBlN,GAAEoN,QAAQ2f,aAAc,EACxBxtB,KAAK6tB,MAAQ7tB,KAAK8tB,iBACb9tB,KAAK6tB,MAAME,KAAQ/tB,KAAK6tB,MAAMG,SACnChuB,KAAKiuB,cAAc,EAAGjuB,KAAK6tB,MAAMG,MAAOvtB,EAAEH,OAAOmT,oBAC5ChT,EAAEH,OAAOwN,cACV/K,OAAOmrB,iBAAiB,WAAYluB,KAAKmuB,uBAGjDA,mBAAoB,WAChB1tB,EAAEoN,QAAQggB,MAAQptB,EAAEoN,QAAQigB,gBAC5BrtB,EAAEoN,QAAQogB,cAAcxtB,EAAEH,OAAO8K,MAAO3K,EAAEoN,QAAQggB,MAAMG,OAAO,IAEnEF,cAAe,WACX,GAAIM,GAAYrrB,OAAOsqB,SAASgB,SAASC,MAAM,GAAG/I,MAAM,KACpD/I,EAAQ4R,EAAUzrB,OAClBorB,EAAMK,EAAU5R,EAAQ,GACxBwR,EAAQI,EAAU5R,EAAQ,EAC9B,QAASuR,IAAKA,EAAKC,MAAOA,IAE9B/J,WAAY,SAAU8J,EAAKtrB,GACvB,GAAKhC,EAAEoN,QAAQ2f,aAAgB/sB,EAAEH,OAAOuN,QAAxC,CACA,GAAImM,GAAQvZ,EAAES,OAAOC,GAAGsB,GACpBurB,EAAQhuB,KAAKuuB,QAAQvU,EAAM3Y,KAAK,gBAC/B0B,QAAOsqB,SAASgB,SAASG,SAAST,KACnCC,EAAQD,EAAM,IAAMC,GAEpBvtB,EAAEH,OAAOwN,aACT/K,OAAO8K,QAAQC,aAAa,KAAM,KAAMkgB,GAExCjrB,OAAO8K,QAAQ+f,UAAU,KAAM,KAAMI,KAG7CO,QAAS,SAAS7R,GACd,MAAOA,GAAKoJ,WAAWlhB,cAClByU,QAAQ,OAAQ,KAChBA,QAAQ,YAAa,IACrBA,QAAQ,SAAU,KAClBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAExB4U,cAAe,SAAS7iB,EAAO4iB,EAAOjK,GAClC,GAAIiK,EACA,IAAK,GAAIhoB,GAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpByoB,EAAezuB,KAAKuuB,QAAQvU,EAAM3Y,KAAK,gBAC3C,IAAIotB,IAAiBT,IAAUhU,EAAMkC,SAASzb,EAAEH,OAAOsR,qBAAsB,CACzE,GAAInP,GAAQuX,EAAMvX,OAClBhC,GAAEqd,QAAQrb,EAAO2I,EAAO2Y,QAIhCtjB,GAAEqd,QAAQ,EAAG1S,EAAO2Y,KAyEhCtjB,EAAEiuB,uBAAyB,WACvBjuB,EAAEH,OAAOkN,iBAAkB,EAC3BvN,EAAEwE,UAAUknB,IAAI,UAAW9nB,IAE/BpD,EAAEkuB,sBAAwB,WACtBluB,EAAEH,OAAOkN,iBAAkB,EAC3BvN,EAAEwE,UAAUgnB,GAAG,UAAW5nB,IAO9BpD,EAAE+H,YACEY,OAAO,EACPH,gBAAgB,GAAKlG,QAAOgG,MAAQC,WAEpCvI,EAAEH,OAAOmN,oBAMThN,EAAE+H,WAAWY,MAASiV,UAAUuQ,UAAUlkB,QAAQ,YAAa,EAC3D,iBACApE,IACI,QAAU,cAkHtB7F,EAAEouB,yBAA2B,WACzB,IAAKpuB,EAAE+H,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS3B,EAAEC,SAKf,OAJwC,cAApCD,EAAEH,OAAOoN,yBACTtL,EAASnC,EAAEQ,EAAEH,OAAOoN,yBAExBtL,EAAOupB,IAAIlrB,EAAE+H,WAAWY,MAAOtC,IACxB,GAGXrG,EAAEquB,wBAA0B,WACxB,IAAKruB,EAAE+H,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS3B,EAAEC,SAKf,OAJwC,cAApCD,EAAEH,OAAOoN,yBACTtL,EAASnC,EAAEQ,EAAEH,OAAOoN,yBAExBtL,EAAOqpB,GAAGhrB,EAAE+H,WAAWY,MAAOtC,IACvB,GAiNXrG,EAAEsM,UACE+X,aAAc,WACVrkB,EAAEC,UAAU2U,SAAS,8EAA8E7U,KAAK,WACpG+J,EAAqBvK,KAAMS,EAAE+J,YAGjC/J,EAAES,OAAOV,KAAK,WACV,GAAIwZ,GAAQ/Z,EAAED,KACdga,GAAMzE,KAAK,8EAA8E/U,KAAK,WAC1F,GAAIgK,GAAW3J,KAAK4c,IAAI5c,KAAK+Y,IAAII,EAAM,GAAGxP,UAAU,GAAK,EACzDD,GAAqBvK,KAAMwK,QAIvCka,cAAe,SAAUH,GACG,mBAAbA,KAA0BA,EAAW9jB,EAAEH,OAAO8K,OACzD3K,EAAEC,UAAU6U,KAAK,8EAA8E/U,KAAK,WAChG,GAAI2B,GAAKlC,EAAED,MACP+uB,EAAmBpkB,SAASxI,EAAGd,KAAK,iCAAkC,KAAOkjB,CAChE,KAAbA,IAAgBwK,EAAmB,GACvC5sB,EAAG2a,WAAWiS,OAS1BtuB,EAAEuM,MAEE2P,MAAO,EACPqS,aAAc,EACdC,WAAW,EACXC,SACIlV,MAAOhM,OACPmhB,WAAYnhB,OACZohB,YAAaphB,OACbsJ,MAAOtJ,OACPqhB,UAAWrhB,OACXf,QAASxM,EAAEH,OAAO2M,SAEtBqK,OACIgJ,UAAWtS,OACXuS,QAASvS,OACTuT,SAAUvT,OACVwT,SAAUxT,OACVshB,KAAMthB,OACNuhB,KAAMvhB,OACNwhB,KAAMxhB,OACNyhB,KAAMzhB,OACNlI,MAAOkI,OACPjI,OAAQiI,OACRqT,OAAQrT,OACRsT,OAAQtT,OACR0hB,gBACAC,mBAEJ3Z,UACI2O,EAAG3W,OACH4W,EAAG5W,OACH4hB,cAAe5hB,OACf6hB,cAAe7hB,OACf8hB,SAAU9hB,QAGd+hB,0BAA2B,SAAU9tB,GACjC,GAAIA,EAAE6f,cAAcnf,OAAS,EAAG,MAAO,EACvC,IAAIqtB,GAAK/tB,EAAE6f,cAAc,GAAGC,MACxBkO,EAAKhuB,EAAE6f,cAAc,GAAGE,MACxBuK,EAAKtqB,EAAE6f,cAAc,GAAGC,MACxBmO,EAAKjuB,EAAE6f,cAAc,GAAGE,MACxBkB,EAAWriB,KAAKsvB,KAAKtvB,KAAK4hB,IAAI8J,EAAKyD,EAAI,GAAKnvB,KAAK4hB,IAAIyN,EAAKD,EAAI,GAClE,OAAO/M,IAGXkN,eAAgB,SAAUnuB,GACtB,GAAI4iB,GAAIpkB,EAAEuM,IACV,KAAKvM,EAAEuU,QAAQqb,SAAU,CACrB,GAAe,eAAXpuB,EAAE2f,MAAoC,eAAX3f,EAAE2f,MAAyB3f,EAAE6f,cAAcnf,OAAS,EAC/E,MAEJkiB,GAAEqK,QAAQoB,WAAazL,EAAEkL,0BAA0B9tB,GAEvD,MAAK4iB,GAAEqK,QAAQlV,OAAU6K,EAAEqK,QAAQlV,MAAMrX,SACrCkiB,EAAEqK,QAAQlV,MAAQ/Z,EAAED,MACW,IAA3B6kB,EAAEqK,QAAQlV,MAAMrX,SAAckiB,EAAEqK,QAAQlV,MAAQvZ,EAAES,OAAOC,GAAGV,EAAEW,cAClEyjB,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQlV,MAAMzE,KAAK,oBACvCsP,EAAEqK,QAAQG,UAAYxK,EAAEqK,QAAQ5X,MAAMiZ,OAAO,IAAM9vB,EAAEH,OAAO2S,oBAC5D4R,EAAEqK,QAAQjiB,QAAU4X,EAAEqK,QAAQG,UAAUhuB,KAAK,qBAAuBZ,EAAEH,OAAO2M,QAC1C,IAA/B4X,EAAEqK,QAAQG,UAAU1sB,SAK5BkiB,EAAEqK,QAAQ5X,MAAMwF,WAAW,QAC3B+H,EAAEoK,WAAY,SALNpK,EAAEqK,QAAQ5X,MAAQtJ,SAO9BwiB,gBAAiB,SAAUvuB,GACvB,GAAI4iB,GAAIpkB,EAAEuM,IACV,KAAKvM,EAAEuU,QAAQqb,SAAU,CACrB,GAAe,cAAXpuB,EAAE2f,MAAmC,cAAX3f,EAAE2f,MAAwB3f,EAAE6f,cAAcnf,OAAS,EAC7E,MAEJkiB,GAAEqK,QAAQuB,UAAY5L,EAAEkL,0BAA0B9tB,GAEjD4iB,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,SACpClC,EAAEuU,QAAQqb,SACVxL,EAAElI,MAAQ1a,EAAE0a,MAAQkI,EAAEmK,aAGtBnK,EAAElI,MAASkI,EAAEqK,QAAQuB,UAAY5L,EAAEqK,QAAQoB,WAAczL,EAAEmK,aAE3DnK,EAAElI,MAAQkI,EAAEqK,QAAQjiB,UACpB4X,EAAElI,MAAQkI,EAAEqK,QAAQjiB,QAAU,EAAIpM,KAAK4hB,IAAKoC,EAAElI,MAAQkI,EAAEqK,QAAQjiB,QAAU,EAAI,KAE9E4X,EAAElI,MAAQlc,EAAEH,OAAO4M,UACnB2X,EAAElI,MAASlc,EAAEH,OAAO4M,QAAU,EAAIrM,KAAK4hB,IAAKhiB,EAAEH,OAAO4M,QAAU2X,EAAElI,MAAQ,EAAI,KAEjFkI,EAAEqK,QAAQ5X,MAAM1M,UAAU,4BAA8Bia,EAAElI,MAAQ,OAEtE+T,aAAc,SAAUzuB,GACpB,GAAI4iB,GAAIpkB,EAAEuM,MACLvM,EAAEuU,QAAQqb,WACI,aAAXpuB,EAAE2f,MAAkC,aAAX3f,EAAE2f,MAAuB3f,EAAE0uB,eAAehuB,OAAS,IAI/EkiB,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,SACxCkiB,EAAElI,MAAQ9b,KAAK+Y,IAAI/Y,KAAK4c,IAAIoH,EAAElI,MAAOkI,EAAEqK,QAAQjiB,SAAUxM,EAAEH,OAAO4M,SAClE2X,EAAEqK,QAAQ5X,MAAMwF,WAAWrc,EAAEH,OAAO8K,OAAOR,UAAU,4BAA8Bia,EAAElI,MAAQ,KAC7FkI,EAAEmK,aAAenK,EAAElI,MACnBkI,EAAEoK,WAAY,EACE,IAAZpK,EAAElI,QAAakI,EAAEqK,QAAQlV,MAAQhM,UAEzCiR,aAAc,SAAUxe,EAAGwB,GACvB,GAAI4iB,GAAIpkB,EAAEuM,IACL6X,GAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,SACpCkiB,EAAEvN,MAAMgJ,YACQ,YAAhB7f,EAAEmV,OAAOgb,IAAkB3uB,EAAEiE,iBACjC2e,EAAEvN,MAAMgJ,WAAY,EACpBuE,EAAEvN,MAAMoY,aAAa/K,EAAe,eAAX1iB,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,MAChF8C,EAAEvN,MAAMoY,aAAa9K,EAAe,eAAX3iB,EAAE2f,KAAwB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,SAEpF9C,YAAa,SAAUjd,GACnB,GAAI4iB,GAAIpkB,EAAEuM,IACV,IAAK6X,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,SACxClC,EAAEqf,YAAa,EACV+E,EAAEvN,MAAMgJ,WAAcuE,EAAEqK,QAAQlV,OAArC,CAEK6K,EAAEvN,MAAMiJ,UACTsE,EAAEvN,MAAMxR,MAAQ+e,EAAEqK,QAAQ5X,MAAM,GAAGuU,YACnChH,EAAEvN,MAAMvR,OAAS8e,EAAEqK,QAAQ5X,MAAM,GAAGoB,aACpCmM,EAAEvN,MAAM+J,OAAS5gB,EAAEskB,aAAaF,EAAEqK,QAAQG,UAAU,GAAI,MAAQ,EAChExK,EAAEvN,MAAMgK,OAAS7gB,EAAEskB,aAAaF,EAAEqK,QAAQG,UAAU,GAAI,MAAQ,EAChExK,EAAEqK,QAAQC,WAAatK,EAAEqK,QAAQlV,MAAM,GAAG6R,YAC1ChH,EAAEqK,QAAQE,YAAcvK,EAAEqK,QAAQlV,MAAM,GAAGtB,aAC3CmM,EAAEqK,QAAQG,UAAUvS,WAAW,GAC3Brc,EAAEkF,MAAKkf,EAAEvN,MAAM+J,QAAUwD,EAAEvN,MAAM+J,QACjC5gB,EAAEkF,MAAKkf,EAAEvN,MAAMgK,QAAUuD,EAAEvN,MAAMgK,QAGzC,IAAIuP,GAAchM,EAAEvN,MAAMxR,MAAQ+e,EAAElI,MAChCmU,EAAejM,EAAEvN,MAAMvR,OAAS8e,EAAElI,KAEtC,MAAIkU,EAAchM,EAAEqK,QAAQC,YAAc2B,EAAejM,EAAEqK,QAAQE,aAAnE,CAUA,GARAvK,EAAEvN,MAAMgY,KAAOzuB,KAAK4c,IAAKoH,EAAEqK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEhM,EAAEvN,MAAMkY,MAAQ3K,EAAEvN,MAAMgY,KACxBzK,EAAEvN,MAAMiY,KAAO1uB,KAAK4c,IAAKoH,EAAEqK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxEjM,EAAEvN,MAAMmY,MAAQ5K,EAAEvN,MAAMiY,KAExB1K,EAAEvN,MAAMqY,eAAehL,EAAe,cAAX1iB,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGC,MAAQ9f,EAAE8f,MACjF8C,EAAEvN,MAAMqY,eAAe/K,EAAe,cAAX3iB,EAAE2f,KAAuB3f,EAAE6f,cAAc,GAAGE,MAAQ/f,EAAE+f,OAE5E6C,EAAEvN,MAAMiJ,UAAYsE,EAAEoK,UAAW,CAClC,GAAIxuB,EAAE0D,gBACDtD,KAAKC,MAAM+jB,EAAEvN,MAAMgY,QAAUzuB,KAAKC,MAAM+jB,EAAEvN,MAAM+J,SAAWwD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,GAC3G9jB,KAAKC,MAAM+jB,EAAEvN,MAAMkY,QAAU3uB,KAAKC,MAAM+jB,EAAEvN,MAAM+J,SAAWwD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,EAG5G,YADAE,EAAEvN,MAAMgJ,WAAY,EAGnB,KAAK7f,EAAE0D,gBACPtD,KAAKC,MAAM+jB,EAAEvN,MAAMiY,QAAU1uB,KAAKC,MAAM+jB,EAAEvN,MAAMgK,SAAWuD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,GAC3G/jB,KAAKC,MAAM+jB,EAAEvN,MAAMmY,QAAU5uB,KAAKC,MAAM+jB,EAAEvN,MAAMgK,SAAWuD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,EAG5G,YADAC,EAAEvN,MAAMgJ,WAAY,GAI5Bre,EAAEiE,iBACFjE,EAAE8d,kBAEF8E,EAAEvN,MAAMiJ,SAAU,EAClBsE,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,EAAIE,EAAEvN,MAAM+J,OAC/EwD,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,EAAIC,EAAEvN,MAAMgK,OAE3EuD,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMgY,OAC3BzK,EAAEvN,MAAMiK,SAAYsD,EAAEvN,MAAMgY,KAAO,EAAIzuB,KAAK4hB,IAAKoC,EAAEvN,MAAMgY,KAAOzK,EAAEvN,MAAMiK,SAAW,EAAI,KAEvFsD,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,OAC3B3K,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,KAAO,EAAI3uB,KAAK4hB,IAAKoC,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,KAAO,EAAI,KAGtF3K,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMiY,OAC3B1K,EAAEvN,MAAMkK,SAAYqD,EAAEvN,MAAMiY,KAAO,EAAI1uB,KAAK4hB,IAAKoC,EAAEvN,MAAMiY,KAAO1K,EAAEvN,MAAMkK,SAAW,EAAI,KAEvFqD,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,OAC3B5K,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,KAAO,EAAI5uB,KAAK4hB,IAAKoC,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,KAAO,EAAI,KAIrF5K,EAAE7O,SAAS4Z,gBAAe/K,EAAE7O,SAAS4Z,cAAgB/K,EAAEvN,MAAMqY,eAAehL,GAC5EE,EAAE7O,SAAS6Z,gBAAehL,EAAE7O,SAAS6Z,cAAgBhL,EAAEvN,MAAMqY,eAAe/K,GAC5EC,EAAE7O,SAAS8Z,WAAUjL,EAAE7O,SAAS8Z,SAAW/mB,KAAKmY,OACrD2D,EAAE7O,SAAS2O,GAAKE,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAE7O,SAAS4Z,gBAAkB7mB,KAAKmY,MAAQ2D,EAAE7O,SAAS8Z,UAAY,EAC5GjL,EAAE7O,SAAS4O,GAAKC,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAE7O,SAAS6Z,gBAAkB9mB,KAAKmY,MAAQ2D,EAAE7O,SAAS8Z,UAAY,EACxGjvB,KAAKuG,IAAIyd,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAE7O,SAAS4Z,eAAiB,IAAG/K,EAAE7O,SAAS2O,EAAI,GAClF9jB,KAAKuG,IAAIyd,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAE7O,SAAS6Z,eAAiB,IAAGhL,EAAE7O,SAAS4O,EAAI,GACtFC,EAAE7O,SAAS4Z,cAAgB/K,EAAEvN,MAAMqY,eAAehL,EAClDE,EAAE7O,SAAS6Z,cAAgBhL,EAAEvN,MAAMqY,eAAe/K,EAClDC,EAAE7O,SAAS8Z,SAAW/mB,KAAKmY,MAE3B2D,EAAEqK,QAAQG,UAAUzkB,UAAU,eAAiBia,EAAEvN,MAAMiK,SAAW,OAASsD,EAAEvN,MAAMkK,SAAW,YAElGrC,WAAY,SAAU1e,EAAGwB,GACrB,GAAI4iB,GAAIpkB,EAAEuM,IACV,IAAK6X,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,OAAxC,CACA,IAAKkiB,EAAEvN,MAAMgJ,YAAcuE,EAAEvN,MAAMiJ,QAG/B,MAFAsE,GAAEvN,MAAMgJ,WAAY,OACpBuE,EAAEvN,MAAMiJ,SAAU,EAGtBsE,GAAEvN,MAAMgJ,WAAY,EACpBuE,EAAEvN,MAAMiJ,SAAU,CAClB,IAAIwQ,GAAoB,IACpBC,EAAoB,IACpBC,EAAoBpM,EAAE7O,SAAS2O,EAAIoM,EACnCG,EAAerM,EAAEvN,MAAMiK,SAAW0P,EAClCE,EAAoBtM,EAAE7O,SAAS4O,EAAIoM,EACnCI,EAAevM,EAAEvN,MAAMkK,SAAW2P,CAGjB,KAAjBtM,EAAE7O,SAAS2O,IAASoM,EAAoBlwB,KAAKuG,KAAK8pB,EAAerM,EAAEvN,MAAMiK,UAAYsD,EAAE7O,SAAS2O,IAC/E,IAAjBE,EAAE7O,SAAS4O,IAASoM,EAAoBnwB,KAAKuG,KAAKgqB,EAAevM,EAAEvN,MAAMkK,UAAYqD,EAAE7O,SAAS4O,GACpG,IAAIzB,GAAmBtiB,KAAK+Y,IAAImX,EAAmBC,EAEnDnM,GAAEvN,MAAMiK,SAAW2P,EACnBrM,EAAEvN,MAAMkK,SAAW4P,CAGnB,IAAIP,GAAchM,EAAEvN,MAAMxR,MAAQ+e,EAAElI,MAChCmU,EAAejM,EAAEvN,MAAMvR,OAAS8e,EAAElI,KACtCkI,GAAEvN,MAAMgY,KAAOzuB,KAAK4c,IAAKoH,EAAEqK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEhM,EAAEvN,MAAMkY,MAAQ3K,EAAEvN,MAAMgY,KACxBzK,EAAEvN,MAAMiY,KAAO1uB,KAAK4c,IAAKoH,EAAEqK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxEjM,EAAEvN,MAAMmY,MAAQ5K,EAAEvN,MAAMiY,KACxB1K,EAAEvN,MAAMiK,SAAW1gB,KAAK+Y,IAAI/Y,KAAK4c,IAAIoH,EAAEvN,MAAMiK,SAAUsD,EAAEvN,MAAMkY,MAAO3K,EAAEvN,MAAMgY,MAC9EzK,EAAEvN,MAAMkK,SAAW3gB,KAAK+Y,IAAI/Y,KAAK4c,IAAIoH,EAAEvN,MAAMkK,SAAUqD,EAAEvN,MAAMmY,MAAO5K,EAAEvN,MAAMiY,MAE9E1K,EAAEqK,QAAQG,UAAUvS,WAAWqG,GAAkBvY,UAAU,eAAiBia,EAAEvN,MAAMiK,SAAW,OAASsD,EAAEvN,MAAMkK,SAAW,WAE/HkC,gBAAiB,SAAUjjB,GACvB,GAAIokB,GAAIpkB,EAAEuM,IACN6X,GAAEqK,QAAQlV,OAASvZ,EAAEsb,gBAAkBtb,EAAEW,cACzCyjB,EAAEqK,QAAQ5X,MAAM1M,UAAU,+BAC1Bia,EAAEqK,QAAQG,UAAUzkB,UAAU,sBAC9Bia,EAAEqK,QAAQlV,MAAQ6K,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQG,UAAYrhB,OAC1D6W,EAAElI,MAAQkI,EAAEmK,aAAe,IAInCqC,WAAY,SAAU5wB,EAAGwB,GACrB,GAAI4iB,GAAIpkB,EAAEuM,IAMV,IALK6X,EAAEqK,QAAQlV,QACX6K,EAAEqK,QAAQlV,MAAQvZ,EAAE0f,aAAelgB,EAAEQ,EAAE0f,cAAgB1f,EAAES,OAAOC,GAAGV,EAAEW,aACrEyjB,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQlV,MAAMzE,KAAK,oBACvCsP,EAAEqK,QAAQG,UAAYxK,EAAEqK,QAAQ5X,MAAMiZ,OAAO,IAAM9vB,EAAEH,OAAO2S,qBAE3D4R,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM3U,OAAxC,CAEA,GAAI2uB,GAAQC,EAAQC,EAASC,EAASC,EAAOC,EAAOpI,EAAYD,EAAYsI,EAAYC,EAAahB,EAAaC,EAAcgB,EAAeC,EAAeC,EAAeC,EAAe9C,EAAYC,CAElK,oBAA3BvK,GAAEvN,MAAMoY,aAAa/K,GAAqB1iB,GACjDqvB,EAAoB,aAAXrvB,EAAE2f,KAAsB3f,EAAE0uB,eAAe,GAAG5O,MAAQ9f,EAAE8f,MAC/DwP,EAAoB,aAAXtvB,EAAE2f,KAAsB3f,EAAE0uB,eAAe,GAAG3O,MAAQ/f,EAAE+f,QAG/DsP,EAASzM,EAAEvN,MAAMoY,aAAa/K,EAC9B4M,EAAS1M,EAAEvN,MAAMoY,aAAa9K,GAG9BC,EAAElI,OAAqB,IAAZkI,EAAElI,OAEbkI,EAAElI,MAAQkI,EAAEmK,aAAe,EAC3BnK,EAAEqK,QAAQG,UAAUvS,WAAW,KAAKlS,UAAU,sBAC9Cia,EAAEqK,QAAQ5X,MAAMwF,WAAW,KAAKlS,UAAU,+BAC1Cia,EAAEqK,QAAQlV,MAAQhM,SAIlB6W,EAAElI,MAAQkI,EAAEmK,aAAenK,EAAEqK,QAAQG,UAAUhuB,KAAK,qBAAuBZ,EAAEH,OAAO2M,QAChFhL,GACAktB,EAAatK,EAAEqK,QAAQlV,MAAM,GAAG6R,YAChCuD,EAAcvK,EAAEqK,QAAQlV,MAAM,GAAGtB,aACjC8Y,EAAU3M,EAAEqK,QAAQlV,MAAMtU,SAAST,KACnCwsB,EAAU5M,EAAEqK,QAAQlV,MAAMtU,SAASP,IACnCusB,EAAQF,EAAUrC,EAAW,EAAImC,EACjCK,EAAQF,EAAUrC,EAAY,EAAImC,EAElCK,EAAa/M,EAAEqK,QAAQ5X,MAAM,GAAGuU,YAChCgG,EAAchN,EAAEqK,QAAQ5X,MAAM,GAAGoB,aACjCmY,EAAce,EAAa/M,EAAElI,MAC7BmU,EAAee,EAAchN,EAAElI,MAE/BmV,EAAgBjxB,KAAK4c,IAAK0R,EAAa,EAAI0B,EAAc,EAAI,GAC7DkB,EAAgBlxB,KAAK4c,IAAK2R,EAAc,EAAI0B,EAAe,EAAI,GAC/DkB,GAAiBF,EACjBG,GAAiBF,EAEjBxI,EAAamI,EAAQ7M,EAAElI,MACvB2M,EAAaqI,EAAQ9M,EAAElI,MAEnB4M,EAAauI,IACbvI,EAAcuI,GAEdvI,EAAayI,IACbzI,EAAayI,GAGb1I,EAAayI,IACbzI,EAAcyI,GAEdzI,EAAa2I,IACb3I,EAAa2I,KAIjB1I,EAAa,EACbD,EAAa,GAEjBzE,EAAEqK,QAAQG,UAAUvS,WAAW,KAAKlS,UAAU,eAAiB2e,EAAa,OAASD,EAAa,SAClGzE,EAAEqK,QAAQ5X,MAAMwF,WAAW,KAAKlS,UAAU,4BAA8Bia,EAAElI,MAAQ,QAI1FiD,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAE9B,IAAIje,EAAEH,OAAO0M,KAAM,CACf,GACIoS,IADS3e,EAAES,SAC+B,eAAxBT,EAAE+d,YAAYN,QAA0Bzd,EAAEuU,QAAQoK,kBAAmB3e,EAAEH,OAAOkR,oBAAoB6N,SAAS,EAAMC,SAAS,GAE5I7e,GAAEuU,QAAQqb,UACV5vB,EAAES,OAAO0d,GAAQ,eAAgBne,EAAEuM,KAAKojB,eAAgBhR,GACxD3e,EAAES,OAAO0d,GAAQ,gBAAiBne,EAAEuM,KAAKwjB,gBAAiBpR,GAC1D3e,EAAES,OAAO0d,GAAQ,aAAcne,EAAEuM,KAAK0jB,aAActR,IAEvB,eAAxB3e,EAAE+d,YAAYN,QACnBzd,EAAES,OAAO0d,GAAQne,EAAE+d,YAAYN,MAAOzd,EAAEuM,KAAKojB,eAAgBhR,GAC7D3e,EAAES,OAAO0d,GAAQne,EAAE+d,YAAYL,KAAM1d,EAAEuM,KAAKwjB,gBAAiBpR,GAC7D3e,EAAES,OAAO0d,GAAQne,EAAE+d,YAAYJ,IAAK3d,EAAEuM,KAAK0jB,aAActR,IAI7D3e,EAAEme,GAAQ,aAAcne,EAAEuM,KAAKiS,cAC/Bxe,EAAES,OAAOV,KAAK,SAAUiC,EAAOuX,GACvB/Z,EAAE+Z,GAAOzE,KAAK,IAAM9U,EAAEH,OAAO2S,oBAAoBtQ,OAAS,GAC1D1C,EAAE+Z,GAAO4E,GAAQne,EAAE+d,YAAYL,KAAM1d,EAAEuM,KAAKkS,eAGpDze,EAAEme,GAAQ,WAAYne,EAAEuM,KAAKmS,YAG7B1e,EAAEme,GAAQ,gBAAiBne,EAAEuM,KAAK0W,iBAC9BjjB,EAAEH,OAAO6M,YACT1M,EAAEgrB,GAAG,YAAahrB,EAAEuM,KAAKqkB,cAIrC5D,KAAM,WACFhtB,EAAEuM,KAAK4S,gBAEX+N,QAAS,WACLltB,EAAEuM,KAAK4S,cAAa,KAO5Bnf,EAAEyxB,WACF,KAAK,GAAIC,KAAU1xB,GAAE2xB,QAAS,CAC1B,GAAI3nB,GAAIhK,EAAE2xB,QAAQD,GAAQ1xB,EAAGA,EAAEH,OAAO6xB,GAClC1nB,IAAGhK,EAAEyxB,SAAStuB,KAAK6G,GAkU3B,MA/TAhK,GAAE4xB,YAAc,SAAU9rB,GACtB,IAAK,GAAIP,GAAI,EAAGA,EAAIvF,EAAEyxB,SAASvvB,OAAQqD,IAC/BO,IAAa9F,GAAEyxB,SAASlsB,IACxBvF,EAAEyxB,SAASlsB,GAAGO,GAAW+rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAmBvG7xB,EAAE8xB,yBAGF9xB,EAAEkB,KAAO,SAAU4E,GAEX9F,EAAEH,OAAOiG,IACT9F,EAAEH,OAAOiG,GAAW+rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAE1F,IAAItsB,EAEJ,IAAIvF,EAAE8xB,sBAAsBhsB,GACxB,IAAKP,EAAI,EAAGA,EAAIvF,EAAE8xB,sBAAsBhsB,GAAW5D,OAAQqD,IACvDvF,EAAE8xB,sBAAsBhsB,GAAWP,GAAGssB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAI5G7xB,GAAE4xB,aAAa5xB,EAAE4xB,YAAY9rB,EAAW+rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAElH7xB,EAAEgrB,GAAK,SAAUllB,EAAWisB,GAIxB,MAHAjsB,GAAYsE,EAAmBtE,GAC1B9F,EAAE8xB,sBAAsBhsB,KAAY9F,EAAE8xB,sBAAsBhsB,OACjE9F,EAAE8xB,sBAAsBhsB,GAAW3C,KAAK4uB,GACjC/xB,GAEXA,EAAEkrB,IAAM,SAAUplB,EAAWisB,GACzB,GAAIxsB,EAEJ,IADAO,EAAYsE,EAAmBtE,GACR,mBAAZisB,GAGP,MADA/xB,GAAE8xB,sBAAsBhsB,MACjB9F,CAEX,IAAKA,EAAE8xB,sBAAsBhsB,IAA4D,IAA9C9F,EAAE8xB,sBAAsBhsB,GAAW5D,OAA9E,CACA,IAAKqD,EAAI,EAAGA,EAAIvF,EAAE8xB,sBAAsBhsB,GAAW5D,OAAQqD,IACpDvF,EAAE8xB,sBAAsBhsB,GAAWP,KAAOwsB,GAAS/xB,EAAE8xB,sBAAsBhsB,GAAWksB,OAAOzsB,EAAG,EAEvG,OAAOvF,KAEXA,EAAEiyB,KAAO,SAAUnsB,EAAWisB,GAC1BjsB,EAAYsE,EAAmBtE,EAC/B,IAAIosB,GAAW,WACXH,EAAQF,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,IAC1E7xB,EAAEkrB,IAAIplB,EAAWosB,GAGrB,OADAlyB,GAAEgrB,GAAGllB,EAAWosB,GACTlyB,GAIXA,EAAE0S,MACEyf,cAAe,SAAUC,GAErB,MADAA,GAAIxxB,KAAK,WAAY,KACdwxB,GAEXC,QAAS,SAAUD,EAAKE,GAEpB,MADAF,GAAIxxB,KAAK,OAAQ0xB,GACVF,GAGXG,SAAU,SAAUH,EAAKI,GAErB,MADAJ,GAAIxxB,KAAK,aAAc4xB,GAChBJ,GAGX7V,QAAS,SAAU6V,GAEf,MADAA,GAAIxxB,KAAK,iBAAiB,GACnBwxB,GAGX5V,OAAQ,SAAU4V,GAEd,MADAA,GAAIxxB,KAAK,iBAAiB,GACnBwxB,GAGXpT,WAAY,SAAUrW,GACI,KAAlBA,EAAMpF,UACN/D,EAAEmJ,EAAMhH,QAAQC,GAAG5B,EAAEH,OAAO4P,aAC5BzP,EAAE+e,YAAYpW,GACV3I,EAAEmB,MACFnB,EAAE0S,KAAK+f,OAAOzyB,EAAEH,OAAOiT,kBAGvB9S,EAAE0S,KAAK+f,OAAOzyB,EAAEH,OAAO+S,mBAGtBpT,EAAEmJ,EAAMhH,QAAQC,GAAG5B,EAAEH,OAAO6P,cACjC1P,EAAEif,YAAYtW,GACV3I,EAAEoH,YACFpH,EAAE0S,KAAK+f,OAAOzyB,EAAEH,OAAOgT,mBAGvB7S,EAAE0S,KAAK+f,OAAOzyB,EAAEH,OAAO8S,mBAG3BnT,EAAEmJ,EAAMhH,QAAQC,GAAG,IAAM5B,EAAEH,OAAO4R,cAClCjS,EAAEmJ,EAAMhH,QAAQ,GAAG+wB,UAI3BC,WAAYnzB,EAAE,gBAAkBQ,EAAEH,OAAOyS,kBAAoB,sDAE7DmgB,OAAQ,SAAUG,GACd,GAAIC,GAAe7yB,EAAE0S,KAAKigB,UACE,KAAxBE,EAAa3wB,SACjB2wB,EAAavW,KAAK,IAClBuW,EAAavW,KAAKsW,KAEtB5F,KAAM,WAEEhtB,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IAC7DlC,EAAE0S,KAAKyf,cAAcnyB,EAAEyP,YACvBzP,EAAE0S,KAAK2f,QAAQryB,EAAEyP,WAAY,UAC7BzP,EAAE0S,KAAK6f,SAASvyB,EAAEyP,WAAYzP,EAAEH,OAAO+S,mBAEvC5S,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IAC7DlC,EAAE0S,KAAKyf,cAAcnyB,EAAE0P,YACvB1P,EAAE0S,KAAK2f,QAAQryB,EAAE0P,WAAY,UAC7B1P,EAAE0S,KAAK6f,SAASvyB,EAAE0P,WAAY1P,EAAEH,OAAO8S,mBAG3CnT,EAAEQ,EAAEC,WAAW+lB,OAAOhmB,EAAE0S,KAAKigB,aAEjC/V,eAAgB,WACR5c,EAAEH,OAAOiP,YAAc9O,EAAEH,OAAOmP,qBAAuBhP,EAAEgc,SAAWhc,EAAEgc,QAAQ9Z,QAC9ElC,EAAEgc,QAAQjc,KAAK,WACX,GAAI+yB,GAAStzB,EAAED,KACfS,GAAE0S,KAAKyf,cAAcW,GACrB9yB,EAAE0S,KAAK2f,QAAQS,EAAQ,UACvB9yB,EAAE0S,KAAK6f,SAASO,EAAQ9yB,EAAEH,OAAOkT,wBAAwB6F,QAAQ,YAAaka,EAAO9wB,QAAU,OAI3GkrB,QAAS,WACDltB,EAAE0S,KAAKigB,YAAc3yB,EAAE0S,KAAKigB,WAAWzwB,OAAS,GAAGlC,EAAE0S,KAAKigB,WAAW9M,WAQjF7lB,EAAEgtB,KAAO,WACDhtB,EAAEH,OAAOkB,MAAMf,EAAE4lB,aACrB5lB,EAAEkY,sBACFlY,EAAEsY,mBACFtY,EAAEyc,mBACEzc,EAAEH,OAAO8M,WAAa3M,EAAE2M,YACxB3M,EAAE2M,UAAUsQ,MACRjd,EAAEH,OAAOgN,oBACT7M,EAAE2M,UAAUoe,mBAGI,UAApB/qB,EAAEH,OAAO0L,QAAsBvL,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,UAC7CvL,EAAEH,OAAOkB,MAAMf,EAAE0H,iBACtB1H,EAAEgkB,QAAQhkB,EAAEH,OAAO0L,QAAQ8Y,gBAE3BrkB,EAAEH,OAAOkB,KACTf,EAAEqd,QAAQrd,EAAEH,OAAO6K,aAAe1K,EAAEuQ,aAAc,EAAGvQ,EAAEH,OAAOmT,qBAG9DhT,EAAEqd,QAAQrd,EAAEH,OAAO6K,aAAc,EAAG1K,EAAEH,OAAOmT,oBACf,IAA1BhT,EAAEH,OAAO6K,eACL1K,EAAEsM,UAAYtM,EAAEH,OAAOyM,UAAUtM,EAAEsM,SAAS+X,eAC5CrkB,EAAEmI,MAAQnI,EAAEH,OAAOqI,cACnBlI,EAAEmI,KAAKC,OACPpI,EAAEmI,KAAK+gB,oBAAqB,KAIxClpB,EAAEmf,eACEnf,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UAC/BzC,EAAEwlB,gBAEFxlB,EAAEH,OAAOuQ,gBAAkBpQ,EAAEH,OAAOqI,aACpClI,EAAEoQ,gBAEFpQ,EAAEH,OAAO0M,MAAQvM,EAAEuM,MACnBvM,EAAEuM,KAAKygB,OAEPhtB,EAAEH,OAAOS,UACTN,EAAEwX,gBAEFxX,EAAEH,OAAOkN,iBACL/M,EAAEkuB,uBAAuBluB,EAAEkuB,wBAE/BluB,EAAEH,OAAOmN,mBACLhN,EAAEquB,yBAAyBruB,EAAEquB,0BAGjCruB,EAAEH,OAAOkzB,sBACT/yB,EAAEH,OAAOwN,aAAerN,EAAEH,OAAOkzB,qBAEjC/yB,EAAEH,OAAOuN,SACLpN,EAAEoN,SAASpN,EAAEoN,QAAQ4f,OAEzBhtB,EAAEH,OAAOqN,SACLlN,EAAEkN,SAASlN,EAAEkN,QAAQ8f,OAEzBhtB,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKsa,OACpChtB,EAAEkB,KAAK,SAAUlB,IAIrBA,EAAEgzB,cAAgB,WAEdhzB,EAAEC,UAAU6a,YAAY9a,EAAEwT,WAAW6B,KAAK,MAAM8Q,WAAW,SAG3DnmB,EAAE2U,QAAQwR,WAAW,SAGjBnmB,EAAES,QAAUT,EAAES,OAAOyB,QACrBlC,EAAES,OACGqa,aACC9a,EAAEH,OAAOqR,kBACTlR,EAAEH,OAAOyE,iBACTtE,EAAEH,OAAOuR,eACTpR,EAAEH,OAAOyR,gBACT+D,KAAK,MACN8Q,WAAW,SACXA,WAAW,sBACXA,WAAW,mBAIhBnmB,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,QAC/ClC,EAAE6U,oBAAoBiG,YAAY9a,EAAEH,OAAOiS,uBAE3C9R,EAAEgc,SAAWhc,EAAEgc,QAAQ9Z,QACvBlC,EAAEgc,QAAQlB,YAAY9a,EAAEH,OAAO6R,mBAI/B1R,EAAEH,OAAO6P,YAAYlQ,EAAEQ,EAAEH,OAAO6P,YAAYoL,YAAY9a,EAAEH,OAAO8R,qBACjE3R,EAAEH,OAAO4P,YAAYjQ,EAAEQ,EAAEH,OAAO4P,YAAYqL,YAAY9a,EAAEH,OAAO8R,qBAGjE3R,EAAEH,OAAO8M,WAAa3M,EAAE2M,YACpB3M,EAAE2M,UAAUyd,OAASpqB,EAAE2M,UAAUyd,MAAMloB,QAAQlC,EAAE2M,UAAUyd,MAAMjE,WAAW,SAC5EnmB,EAAE2M,UAAUge,MAAQ3qB,EAAE2M,UAAUge,KAAKzoB,QAAQlC,EAAE2M,UAAUge,KAAKxE,WAAW,WAKrFnmB,EAAEktB,QAAU,SAAU+F,EAAgBD,GAElChzB,EAAEof,eAEFpf,EAAEqB,eAEErB,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACpB3M,EAAEH,OAAOgN,oBACT7M,EAAE2M,UAAUse,mBAIhBjrB,EAAEH,OAAOkB,MACTf,EAAEoU,cAGF4e,GACAhzB,EAAEgzB,gBAGNhzB,EAAE0lB,sBAGE1lB,EAAEH,OAAO0M,MAAQvM,EAAEuM,MACnBvM,EAAEuM,KAAK2gB,UAGPltB,EAAEH,OAAOkN,iBACL/M,EAAEiuB,wBAAwBjuB,EAAEiuB,yBAEhCjuB,EAAEH,OAAOmN,mBACLhN,EAAEouB,0BAA0BpuB,EAAEouB,2BAGlCpuB,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKwa,UAEhCltB,EAAEH,OAAOuN,UAAYpN,EAAEH,OAAOwN,cAC9B/K,OAAO4wB,oBAAoB,WAAYlzB,EAAEoN,QAAQsgB,oBAEjD1tB,EAAEH,OAAOqN,SAAWlN,EAAEkN,SACtBlN,EAAEkN,QAAQggB,UAGdltB,EAAEkB,KAAK,aAEH+xB,KAAmB,IAAOjzB,EAAI,OAGtCA,EAAEgtB,OAKKhtB,GAOXX,GAAO8zB,WACH5K,SAAU,WACN,GAAI6K,GAAK9wB,OAAOsb,UAAUuQ,UAAUhqB,aACpC,OAAQivB,GAAGnpB,QAAQ,WAAa,GAAKmpB,EAAGnpB,QAAQ,UAAY,GAAKmpB,EAAGnpB,QAAQ,WAAa,KAE7Fue,YAAa,+CAA+C6K,KAAK/wB,OAAOsb,UAAUuQ,WAClF3B,QAAS,SAAU8G,GACf,MAAgD,mBAAzCC,OAAOJ,UAAU9N,SAASmO,MAAMF,IAK3ChV,SACIC,GAAIjc,OAAOsb,UAAUC,gBAAkBvb,OAAOsb,UAAUE,iBACxD+D,QAAUvf,OAAOsb,UAAUE,kBAAoBxb,OAAOsb,UAAU6V,iBAAmB,GAAOnxB,OAAOsb,UAAUC,gBAAkBvb,OAAOsb,UAAU8V,eAAiB,EAC/JnQ,OAAQ,WAEJ,GAAIoQ,GAAM3vB,SAASiC,cAAc,MAIjC,OAFA0tB,GAAIC,UAAY,wCAEgC,IAAzCD,EAAIE,qBAAqB,KAAK3xB,WAM7CiT,OAAQ,WACJ,GAAIie,GAAK9wB,OAAOsb,UAAUuQ,UACtB/Y,EAAUge,EAAGU,MAAM,+BACnBC,EAAOX,EAAGU,MAAM,wBAChBE,EAAOZ,EAAGU,MAAM,2BAChBG,GAAUF,GAAQX,EAAGU,MAAM,6BAC/B,QACIhV,IAAKiV,GAAQE,GAAUD,EACvB5e,QAASA,MAMjBb,SACIG,MAASpS,OAAO4xB,WAAaA,UAAUxf,SAAU,GAAS,WACtD,SAAW,gBAAkBpS,SAAWA,OAAO6xB,eAAiBnwB,mBAAoBmwB,mBAGxF1f,aAAgBnS,OAAO4xB,WAAaA,UAAUE,mBAAoB,GAAS,WACvE,GAAIT,GAAM3vB,SAASiC,cAAc,OAAOgQ,KACxC,OAAQ,qBAAuB0d,IAAO,kBAAoBA,IAAO,gBAAkBA,IAAO,iBAAmBA,IAAO,eAAiBA,MAGzInf,QAAS,WAGL,IAAK,GAFDmf,GAAM3vB,SAASiC,cAAc,OAAOgQ,MACpCoe,EAAS,yKAA2KvP,MAAM,KACrLvf,EAAI,EAAGA,EAAI8uB,EAAOnyB,OAAQqD,IAC/B,GAAI8uB,EAAO9uB,IAAMouB,GAAK,OAAO,KAIrClxB,SAAU,WACN,MAAQ,oBAAsBH,SAAU,0BAA4BA,WAGxEqc,gBAAiB,WACb,GAAI2V,IAAkB,CACtB,KACI,GAAIC,GAAOhB,OAAOiB,kBAAmB,WACjCC,IAAK,WACDH,GAAkB,IAG1BhyB,QAAOmrB,iBAAiB,sBAAuB,KAAM8G,GACvD,MAAO/yB,IACT,MAAO8yB,MAGX1E,SAAU,WACN,MAAO,kBAAoBttB,YAMnCqvB,YAQJlyB,EAAiBD,EAEjB,IAAIk1B,GAASl1B,CAuEhB,OAvDOk1B,KACM,iBAAmBA,GAAO/0B,KAC5B+0B,EAAO/0B,GAAGgY,cAAgB,SAAUhB,GAGhC,QAASge,GAAanzB,GAElB,GAAIA,EAAEG,SAAWpC,KAEjB,IADAoX,EAASie,KAAKr1B,KAAMiC,GACf+D,EAAI,EAAGA,EAAIsvB,EAAO3yB,OAAQqD,IAC3BuvB,EAAI5J,IAAI2J,EAAOtvB,GAAIovB,GAP3B,GACIpvB,GADAsvB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFC,EAAMv1B,IAShB,IAAIoX,EACA,IAAKpR,EAAI,EAAGA,EAAIsvB,EAAO3yB,OAAQqD,IAC3BuvB,EAAI9J,GAAG6J,EAAOtvB,GAAIovB,EAG1B,OAAOp1B,QAGT,aAAem1B,GAAO/0B,KACxB+0B,EAAO/0B,GAAGwK,UAAY,SAAUA,GAC5B,IAAK,GAAI5E,GAAI,EAAGA,EAAIhG,KAAK2C,OAAQqD,IAAK,CAClC,GAAIwvB,GAAUx1B,KAAKgG,GAAG0Q,KACtB8e,GAAQlQ,gBAAkBkQ,EAAQ7P,YAAc6P,EAAQ5P,YAAc4P,EAAQ/P,aAAe+P,EAAQ9P,WAAa8P,EAAQ5qB,UAAYA,EAE1I,MAAO5K,QAGT,cAAgBm1B,GAAO/0B,KACzB+0B,EAAO/0B,GAAG0c,WAAa,SAAUyH,GACL,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAIve,GAAI,EAAGA,EAAIhG,KAAK2C,OAAQqD,IAAK,CAClC,GAAIwvB,GAAUx1B,KAAKgG,GAAG0Q,KACtB8e,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBvR,EAEhM,MAAOvkB,QAGT,cAAgBm1B,GAAO/0B,KACzB+0B,EAAO/0B,GAAGqa,WAAa,SAAUsb,GAC7B,MAAI/1B,MAAK2C,OAAS,EACVozB,EACO/1B,KAAK,GAAG6rB,YAAczS,WAAWpZ,KAAK0V,IAAI,iBAAmB0D,WAAWpZ,KAAK0V,IAAI,gBAEjF1V,KAAK,GAAG6rB,YAEX,QAKpB/rB", "file": "../swiper.jquery.umd.min.js", "sourcesContent": ["/**\n * Swiper 3.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * \n * http://www.idangero.us/swiper/\n * \n * Copyright 2016, <PERSON>\n * The iDangero.us\n * http://www.idangero.us/\n * \n * Licensed under MIT\n * \n * Released on: December 13, 2016\n */\n(function (root, factory) {\n\t'use strict';\n\n\tif (typeof define === 'function' && define.amd) {\n\t\t// AMD. Register as an anonymous module.\n\t\tdefine(['jquery'], factory);\n\t} else if (typeof exports === 'object') {\n\t\t// Node. Does not work with strict CommonJS, but\n\t\t// only CommonJS-like environments that support module.exports,\n\t\t// like Node.\n\t\tmodule.exports = factory(require('jquery'));\n\t} else {\n\t\t// Browser globals (root is window)\n\t\troot.Swiper = factory(root.jQuery);\n\t}\n}(this, function ($) {\n\t'use strict';\n\n    /*===========================\n    Swiper\n    ===========================*/\n    var Swiper = function (container, params) {\n        if (!(this instanceof Swiper)) return new Swiper(container, params);\n\n        var defaults = {\n            direction: 'horizontal',\n            touchEventsTarget: 'container',\n            initialSlide: 0,\n            speed: 300,\n            // autoplay\n            autoplay: false,\n            autoplayDisableOnInteraction: true,\n            autoplayStopOnLast: false,\n            // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n            iOSEdgeSwipeDetection: false,\n            iOSEdgeSwipeThreshold: 20,\n            // Free mode\n            freeMode: false,\n            freeModeMomentum: true,\n            freeModeMomentumRatio: 1,\n            freeModeMomentumBounce: true,\n            freeModeMomentumBounceRatio: 1,\n            freeModeMomentumVelocityRatio: 1,\n            freeModeSticky: false,\n            freeModeMinimumVelocity: 0.02,\n            // Autoheight\n            autoHeight: false,\n            // Set wrapper width\n            setWrapperSize: false,\n            // Virtual Translate\n            virtualTranslate: false,\n            // Effects\n            effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n            coverflow: {\n                rotate: 50,\n                stretch: 0,\n                depth: 100,\n                modifier: 1,\n                slideShadows : true\n            },\n            flip: {\n                slideShadows : true,\n                limitRotation: true\n            },\n            cube: {\n                slideShadows: true,\n                shadow: true,\n                shadowOffset: 20,\n                shadowScale: 0.94\n            },\n            fade: {\n                crossFade: false\n            },\n            // Parallax\n            parallax: false,\n            // Zoom\n            zoom: false,\n            zoomMax: 3,\n            zoomMin: 1,\n            zoomToggle: true,\n            // Scrollbar\n            scrollbar: null,\n            scrollbarHide: true,\n            scrollbarDraggable: false,\n            scrollbarSnapOnRelease: false,\n            // Keyboard Mousewheel\n            keyboardControl: false,\n            mousewheelControl: false,\n            mousewheelReleaseOnEdges: false,\n            mousewheelInvert: false,\n            mousewheelForceToAxis: false,\n            mousewheelSensitivity: 1,\n            mousewheelEventsTarged: 'container',\n            // Hash Navigation\n            hashnav: false,\n            hashnavWatchState: false,\n            // History\n            history: false,\n            // Commong Nav State\n            replaceState: false,\n            // Breakpoints\n            breakpoints: undefined,\n            // Slides grid\n            spaceBetween: 0,\n            slidesPerView: 1,\n            slidesPerColumn: 1,\n            slidesPerColumnFill: 'column',\n            slidesPerGroup: 1,\n            centeredSlides: false,\n            slidesOffsetBefore: 0, // in px\n            slidesOffsetAfter: 0, // in px\n            // Round length\n            roundLengths: false,\n            // Touches\n            touchRatio: 1,\n            touchAngle: 45,\n            simulateTouch: true,\n            shortSwipes: true,\n            longSwipes: true,\n            longSwipesRatio: 0.5,\n            longSwipesMs: 300,\n            followFinger: true,\n            onlyExternal: false,\n            threshold: 0,\n            touchMoveStopPropagation: true,\n            touchReleaseOnEdges: false,\n            // Unique Navigation Elements\n            uniqueNavElements: true,\n            // Pagination\n            pagination: null,\n            paginationElement: 'span',\n            paginationClickable: false,\n            paginationHide: false,\n            paginationBulletRender: null,\n            paginationProgressRender: null,\n            paginationFractionRender: null,\n            paginationCustomRender: null,\n            paginationType: 'bullets', // 'bullets' or 'progress' or 'fraction' or 'custom'\n            // Resistance\n            resistance: true,\n            resistanceRatio: 0.85,\n            // Next/prev buttons\n            nextButton: null,\n            prevButton: null,\n            // Progress\n            watchSlidesProgress: false,\n            watchSlidesVisibility: false,\n            // Cursor\n            grabCursor: false,\n            // Clicks\n            preventClicks: true,\n            preventClicksPropagation: true,\n            slideToClickedSlide: false,\n            // Lazy Loading\n            lazyLoading: false,\n            lazyLoadingInPrevNext: false,\n            lazyLoadingInPrevNextAmount: 1,\n            lazyLoadingOnTransitionStart: false,\n            // Images\n            preloadImages: true,\n            updateOnImagesReady: true,\n            // loop\n            loop: false,\n            loopAdditionalSlides: 0,\n            loopedSlides: null,\n            // Control\n            control: undefined,\n            controlInverse: false,\n            controlBy: 'slide', //or 'container'\n            normalizeSlideIndex: true,\n            // Swiping/no swiping\n            allowSwipeToPrev: true,\n            allowSwipeToNext: true,\n            swipeHandler: null, //'.swipe-handler',\n            noSwiping: true,\n            noSwipingClass: 'swiper-no-swiping',\n            // Passive Listeners\n            passiveListeners: true,\n            // NS\n            containerModifierClass: 'swiper-container-', // NEW\n            slideClass: 'swiper-slide',\n            slideActiveClass: 'swiper-slide-active',\n            slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n            slideVisibleClass: 'swiper-slide-visible',\n            slideDuplicateClass: 'swiper-slide-duplicate',\n            slideNextClass: 'swiper-slide-next',\n            slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n            slidePrevClass: 'swiper-slide-prev',\n            slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n            wrapperClass: 'swiper-wrapper',\n            bulletClass: 'swiper-pagination-bullet',\n            bulletActiveClass: 'swiper-pagination-bullet-active',\n            buttonDisabledClass: 'swiper-button-disabled',\n            paginationCurrentClass: 'swiper-pagination-current',\n            paginationTotalClass: 'swiper-pagination-total',\n            paginationHiddenClass: 'swiper-pagination-hidden',\n            paginationProgressbarClass: 'swiper-pagination-progressbar',\n            paginationClickableClass: 'swiper-pagination-clickable', // NEW\n            paginationModifierClass: 'swiper-pagination-', // NEW\n            lazyLoadingClass: 'swiper-lazy',\n            lazyStatusLoadingClass: 'swiper-lazy-loading',\n            lazyStatusLoadedClass: 'swiper-lazy-loaded',\n            lazyPreloaderClass: 'swiper-lazy-preloader',\n            notificationClass: 'swiper-notification',\n            preloaderClass: 'preloader',\n            zoomContainerClass: 'swiper-zoom-container',\n        \n            // Observer\n            observer: false,\n            observeParents: false,\n            // Accessibility\n            a11y: false,\n            prevSlideMessage: 'Previous slide',\n            nextSlideMessage: 'Next slide',\n            firstSlideMessage: 'This is the first slide',\n            lastSlideMessage: 'This is the last slide',\n            paginationBulletMessage: 'Go to slide {{index}}',\n            // Callbacks\n            runCallbacksOnInit: true\n            /*\n            Callbacks:\n            onInit: function (swiper)\n            onDestroy: function (swiper)\n            onClick: function (swiper, e)\n            onTap: function (swiper, e)\n            onDoubleTap: function (swiper, e)\n            onSliderMove: function (swiper, e)\n            onSlideChangeStart: function (swiper)\n            onSlideChangeEnd: function (swiper)\n            onTransitionStart: function (swiper)\n            onTransitionEnd: function (swiper)\n            onImagesReady: function (swiper)\n            onProgress: function (swiper, progress)\n            onTouchStart: function (swiper, e)\n            onTouchMove: function (swiper, e)\n            onTouchMoveOpposite: function (swiper, e)\n            onTouchEnd: function (swiper, e)\n            onReachBeginning: function (swiper)\n            onReachEnd: function (swiper)\n            onSetTransition: function (swiper, duration)\n            onSetTranslate: function (swiper, translate)\n            onAutoplayStart: function (swiper)\n            onAutoplayStop: function (swiper),\n            onLazyImageLoad: function (swiper, slide, image)\n            onLazyImageReady: function (swiper, slide, image)\n            */\n        \n        };\n        var initialVirtualTranslate = params && params.virtualTranslate;\n        \n        params = params || {};\n        var originalParams = {};\n        for (var param in params) {\n            if (typeof params[param] === 'object' && params[param] !== null && !(params[param].nodeType || params[param] === window || params[param] === document || (typeof Dom7 !== 'undefined' && params[param] instanceof Dom7) || (typeof jQuery !== 'undefined' && params[param] instanceof jQuery))) {\n                originalParams[param] = {};\n                for (var deepParam in params[param]) {\n                    originalParams[param][deepParam] = params[param][deepParam];\n                }\n            }\n            else {\n                originalParams[param] = params[param];\n            }\n        }\n        for (var def in defaults) {\n            if (typeof params[def] === 'undefined') {\n                params[def] = defaults[def];\n            }\n            else if (typeof params[def] === 'object') {\n                for (var deepDef in defaults[def]) {\n                    if (typeof params[def][deepDef] === 'undefined') {\n                        params[def][deepDef] = defaults[def][deepDef];\n                    }\n                }\n            }\n        }\n        \n        // Swiper\n        var s = this;\n        \n        // Params\n        s.params = params;\n        s.originalParams = originalParams;\n        \n        // Classname\n        s.classNames = [];\n        /*=========================\n          Dom Library and plugins\n          ===========================*/\n        if (typeof $ !== 'undefined' && typeof Dom7 !== 'undefined'){\n            $ = Dom7;\n        }\n        if (typeof $ === 'undefined') {\n            if (typeof Dom7 === 'undefined') {\n                $ = window.Dom7 || window.Zepto || window.jQuery;\n            }\n            else {\n                $ = Dom7;\n            }\n            if (!$) return;\n        }\n        // Export it to Swiper instance\n        s.$ = $;\n        \n        /*=========================\n          Breakpoints\n          ===========================*/\n        s.currentBreakpoint = undefined;\n        s.getActiveBreakpoint = function () {\n            //Get breakpoint for window width\n            if (!s.params.breakpoints) return false;\n            var breakpoint = false;\n            var points = [], point;\n            for ( point in s.params.breakpoints ) {\n                if (s.params.breakpoints.hasOwnProperty(point)) {\n                    points.push(point);\n                }\n            }\n            points.sort(function (a, b) {\n                return parseInt(a, 10) > parseInt(b, 10);\n            });\n            for (var i = 0; i < points.length; i++) {\n                point = points[i];\n                if (point >= window.innerWidth && !breakpoint) {\n                    breakpoint = point;\n                }\n            }\n            return breakpoint || 'max';\n        };\n        s.setBreakpoint = function () {\n            //Set breakpoint for window width and update parameters\n            var breakpoint = s.getActiveBreakpoint();\n            if (breakpoint && s.currentBreakpoint !== breakpoint) {\n                var breakPointsParams = breakpoint in s.params.breakpoints ? s.params.breakpoints[breakpoint] : s.originalParams;\n                var needsReLoop = s.params.loop && (breakPointsParams.slidesPerView !== s.params.slidesPerView);\n                for ( var param in breakPointsParams ) {\n                    s.params[param] = breakPointsParams[param];\n                }\n                s.currentBreakpoint = breakpoint;\n                if(needsReLoop && s.destroyLoop) {\n                    s.reLoop(true);\n                }\n            }\n        };\n        // Set breakpoint on load\n        if (s.params.breakpoints) {\n            s.setBreakpoint();\n        }\n        \n        /*=========================\n          Preparation - Define Container, Wrapper and Pagination\n          ===========================*/\n        s.container = $(container);\n        if (s.container.length === 0) return;\n        if (s.container.length > 1) {\n            var swipers = [];\n            s.container.each(function () {\n                var container = this;\n                swipers.push(new Swiper(this, params));\n            });\n            return swipers;\n        }\n        \n        // Save instance in container HTML Element and in data\n        s.container[0].swiper = s;\n        s.container.data('swiper', s);\n        \n        s.classNames.push(s.params.containerModifierClass + s.params.direction);\n        \n        if (s.params.freeMode) {\n            s.classNames.push(s.params.containerModifierClass + 'free-mode');\n        }\n        if (!s.support.flexbox) {\n            s.classNames.push(s.params.containerModifierClass + 'no-flexbox');\n            s.params.slidesPerColumn = 1;\n        }\n        if (s.params.autoHeight) {\n            s.classNames.push(s.params.containerModifierClass + 'autoheight');\n        }\n        // Enable slides progress when required\n        if (s.params.parallax || s.params.watchSlidesVisibility) {\n            s.params.watchSlidesProgress = true;\n        }\n        // Max resistance when touchReleaseOnEdges\n        if (s.params.touchReleaseOnEdges) {\n            s.params.resistanceRatio = 0;\n        }\n        // Coverflow / 3D\n        if (['cube', 'coverflow', 'flip'].indexOf(s.params.effect) >= 0) {\n            if (s.support.transforms3d) {\n                s.params.watchSlidesProgress = true;\n                s.classNames.push(s.params.containerModifierClass + '3d');\n            }\n            else {\n                s.params.effect = 'slide';\n            }\n        }\n        if (s.params.effect !== 'slide') {\n            s.classNames.push(s.params.containerModifierClass + s.params.effect);\n        }\n        if (s.params.effect === 'cube') {\n            s.params.resistanceRatio = 0;\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.centeredSlides = false;\n            s.params.spaceBetween = 0;\n            s.params.virtualTranslate = true;\n            s.params.setWrapperSize = false;\n        }\n        if (s.params.effect === 'fade' || s.params.effect === 'flip') {\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.watchSlidesProgress = true;\n            s.params.spaceBetween = 0;\n            s.params.setWrapperSize = false;\n            if (typeof initialVirtualTranslate === 'undefined') {\n                s.params.virtualTranslate = true;\n            }\n        }\n        \n        // Grab Cursor\n        if (s.params.grabCursor && s.support.touch) {\n            s.params.grabCursor = false;\n        }\n        \n        // Wrapper\n        s.wrapper = s.container.children('.' + s.params.wrapperClass);\n        \n        // Pagination\n        if (s.params.pagination) {\n            s.paginationContainer = $(s.params.pagination);\n            if (s.params.uniqueNavElements && typeof s.params.pagination === 'string' && s.paginationContainer.length > 1 && s.container.find(s.params.pagination).length === 1) {\n                s.paginationContainer = s.container.find(s.params.pagination);\n            }\n        \n            if (s.params.paginationType === 'bullets' && s.params.paginationClickable) {\n                s.paginationContainer.addClass(s.params.paginationModifierClass + 'clickable');\n            }\n            else {\n                s.params.paginationClickable = false;\n            }\n            s.paginationContainer.addClass(s.params.paginationModifierClass + s.params.paginationType);\n        }\n        // Next/Prev Buttons\n        if (s.params.nextButton || s.params.prevButton) {\n            if (s.params.nextButton) {\n                s.nextButton = $(s.params.nextButton);\n                if (s.params.uniqueNavElements && typeof s.params.nextButton === 'string' && s.nextButton.length > 1 && s.container.find(s.params.nextButton).length === 1) {\n                    s.nextButton = s.container.find(s.params.nextButton);\n                }\n            }\n            if (s.params.prevButton) {\n                s.prevButton = $(s.params.prevButton);\n                if (s.params.uniqueNavElements && typeof s.params.prevButton === 'string' && s.prevButton.length > 1 && s.container.find(s.params.prevButton).length === 1) {\n                    s.prevButton = s.container.find(s.params.prevButton);\n                }\n            }\n        }\n        \n        // Is Horizontal\n        s.isHorizontal = function () {\n            return s.params.direction === 'horizontal';\n        };\n        // s.isH = isH;\n        \n        // RTL\n        s.rtl = s.isHorizontal() && (s.container[0].dir.toLowerCase() === 'rtl' || s.container.css('direction') === 'rtl');\n        if (s.rtl) {\n            s.classNames.push(s.params.containerModifierClass + 'rtl');\n        }\n        \n        // Wrong RTL support\n        if (s.rtl) {\n            s.wrongRTL = s.wrapper.css('display') === '-webkit-box';\n        }\n        \n        // Columns\n        if (s.params.slidesPerColumn > 1) {\n            s.classNames.push(s.params.containerModifierClass + 'multirow');\n        }\n        \n        // Check for Android\n        if (s.device.android) {\n            s.classNames.push(s.params.containerModifierClass + 'android');\n        }\n        \n        // Add classes\n        s.container.addClass(s.classNames.join(' '));\n        \n        // Translate\n        s.translate = 0;\n        \n        // Progress\n        s.progress = 0;\n        \n        // Velocity\n        s.velocity = 0;\n        \n        /*=========================\n          Locks, unlocks\n          ===========================*/\n        s.lockSwipeToNext = function () {\n            s.params.allowSwipeToNext = false;\n            if (s.params.allowSwipeToPrev === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = false;\n            if (s.params.allowSwipeToNext === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = false;\n            if (s.params.grabCursor) s.unsetGrabCursor();\n        };\n        s.unlockSwipeToNext = function () {\n            s.params.allowSwipeToNext = true;\n            if (s.params.allowSwipeToPrev === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = true;\n            if (s.params.allowSwipeToNext === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = true;\n            if (s.params.grabCursor) s.setGrabCursor();\n        };\n        \n        /*=========================\n          Round helper\n          ===========================*/\n        function round(a) {\n            return Math.floor(a);\n        }\n        /*=========================\n          Set grab cursor\n          ===========================*/\n        s.setGrabCursor = function(moving) {\n            s.container[0].style.cursor = 'move';\n            s.container[0].style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n            s.container[0].style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n            s.container[0].style.cursor = moving ? 'grabbing': 'grab';\n        };\n        s.unsetGrabCursor = function () {\n            s.container[0].style.cursor = '';\n        };\n        if (s.params.grabCursor) {\n            s.setGrabCursor();\n        }\n        /*=========================\n          Update on Images Ready\n          ===========================*/\n        s.imagesToLoad = [];\n        s.imagesLoaded = 0;\n        \n        s.loadImage = function (imgElement, src, srcset, sizes, checkForComplete, callback) {\n            var image;\n            function onReady () {\n                if (callback) callback();\n            }\n            if (!imgElement.complete || !checkForComplete) {\n                if (src) {\n                    image = new window.Image();\n                    image.onload = onReady;\n                    image.onerror = onReady;\n                    if (sizes) {\n                        image.sizes = sizes;\n                    }\n                    if (srcset) {\n                        image.srcset = srcset;\n                    }\n                    if (src) {\n                        image.src = src;\n                    }\n                } else {\n                    onReady();\n                }\n        \n            } else {//image already loaded...\n                onReady();\n            }\n        };\n        s.preloadImages = function () {\n            s.imagesToLoad = s.container.find('img');\n            function _onReady() {\n                if (typeof s === 'undefined' || s === null || !s) return;\n                if (s.imagesLoaded !== undefined) s.imagesLoaded++;\n                if (s.imagesLoaded === s.imagesToLoad.length) {\n                    if (s.params.updateOnImagesReady) s.update();\n                    s.emit('onImagesReady', s);\n                }\n            }\n            for (var i = 0; i < s.imagesToLoad.length; i++) {\n                s.loadImage(s.imagesToLoad[i], (s.imagesToLoad[i].currentSrc || s.imagesToLoad[i].getAttribute('src')), (s.imagesToLoad[i].srcset || s.imagesToLoad[i].getAttribute('srcset')), s.imagesToLoad[i].sizes || s.imagesToLoad[i].getAttribute('sizes'), true, _onReady);\n            }\n        };\n        \n        /*=========================\n          Autoplay\n          ===========================*/\n        s.autoplayTimeoutId = undefined;\n        s.autoplaying = false;\n        s.autoplayPaused = false;\n        function autoplay() {\n            var autoplayDelay = s.params.autoplay;\n            var activeSlide = s.slides.eq(s.activeIndex);\n            if (activeSlide.attr('data-swiper-autoplay')) {\n                autoplayDelay = activeSlide.attr('data-swiper-autoplay') || s.params.autoplay;\n            }\n            s.autoplayTimeoutId = setTimeout(function () {\n                if (s.params.loop) {\n                    s.fixLoop();\n                    s._slideNext();\n                    s.emit('onAutoplay', s);\n                }\n                else {\n                    if (!s.isEnd) {\n                        s._slideNext();\n                        s.emit('onAutoplay', s);\n                    }\n                    else {\n                        if (!params.autoplayStopOnLast) {\n                            s._slideTo(0);\n                            s.emit('onAutoplay', s);\n                        }\n                        else {\n                            s.stopAutoplay();\n                        }\n                    }\n                }\n            }, autoplayDelay);\n        }\n        s.startAutoplay = function () {\n            if (typeof s.autoplayTimeoutId !== 'undefined') return false;\n            if (!s.params.autoplay) return false;\n            if (s.autoplaying) return false;\n            s.autoplaying = true;\n            s.emit('onAutoplayStart', s);\n            autoplay();\n        };\n        s.stopAutoplay = function (internal) {\n            if (!s.autoplayTimeoutId) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplaying = false;\n            s.autoplayTimeoutId = undefined;\n            s.emit('onAutoplayStop', s);\n        };\n        s.pauseAutoplay = function (speed) {\n            if (s.autoplayPaused) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplayPaused = true;\n            if (speed === 0) {\n                s.autoplayPaused = false;\n                autoplay();\n            }\n            else {\n                s.wrapper.transitionEnd(function () {\n                    if (!s) return;\n                    s.autoplayPaused = false;\n                    if (!s.autoplaying) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        autoplay();\n                    }\n                });\n            }\n        };\n        /*=========================\n          Min/Max Translate\n          ===========================*/\n        s.minTranslate = function () {\n            return (-s.snapGrid[0]);\n        };\n        s.maxTranslate = function () {\n            return (-s.snapGrid[s.snapGrid.length - 1]);\n        };\n        /*=========================\n          Slider/slides sizes\n          ===========================*/\n        s.updateAutoHeight = function () {\n            var activeSlides = [];\n            var newHeight = 0;\n            var i;\n        \n            // Find slides currently in view\n            if(s.params.slidesPerView !== 'auto' && s.params.slidesPerView > 1) {\n                for (i = 0; i < Math.ceil(s.params.slidesPerView); i++) {\n                    var index = s.activeIndex + i;\n                    if(index > s.slides.length) break;\n                    activeSlides.push(s.slides.eq(index)[0]);\n                }\n            } else {\n                activeSlides.push(s.slides.eq(s.activeIndex)[0]);\n            }\n        \n            // Find new height from heighest slide in view\n            for (i = 0; i < activeSlides.length; i++) {\n                if (typeof activeSlides[i] !== 'undefined') {\n                    var height = activeSlides[i].offsetHeight;\n                    newHeight = height > newHeight ? height : newHeight;\n                }\n            }\n        \n            // Update Height\n            if (newHeight) s.wrapper.css('height', newHeight + 'px');\n        };\n        s.updateContainerSize = function () {\n            var width, height;\n            if (typeof s.params.width !== 'undefined') {\n                width = s.params.width;\n            }\n            else {\n                width = s.container[0].clientWidth;\n            }\n            if (typeof s.params.height !== 'undefined') {\n                height = s.params.height;\n            }\n            else {\n                height = s.container[0].clientHeight;\n            }\n            if (width === 0 && s.isHorizontal() || height === 0 && !s.isHorizontal()) {\n                return;\n            }\n        \n            //Subtract paddings\n            width = width - parseInt(s.container.css('padding-left'), 10) - parseInt(s.container.css('padding-right'), 10);\n            height = height - parseInt(s.container.css('padding-top'), 10) - parseInt(s.container.css('padding-bottom'), 10);\n        \n            // Store values\n            s.width = width;\n            s.height = height;\n            s.size = s.isHorizontal() ? s.width : s.height;\n        };\n        \n        s.updateSlidesSize = function () {\n            s.slides = s.wrapper.children('.' + s.params.slideClass);\n            s.snapGrid = [];\n            s.slidesGrid = [];\n            s.slidesSizesGrid = [];\n        \n            var spaceBetween = s.params.spaceBetween,\n                slidePosition = -s.params.slidesOffsetBefore,\n                i,\n                prevSlideSize = 0,\n                index = 0;\n            if (typeof s.size === 'undefined') return;\n            if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n                spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * s.size;\n            }\n        \n            s.virtualSize = -spaceBetween;\n            // reset margins\n            if (s.rtl) s.slides.css({marginLeft: '', marginTop: ''});\n            else s.slides.css({marginRight: '', marginBottom: ''});\n        \n            var slidesNumberEvenToRows;\n            if (s.params.slidesPerColumn > 1) {\n                if (Math.floor(s.slides.length / s.params.slidesPerColumn) === s.slides.length / s.params.slidesPerColumn) {\n                    slidesNumberEvenToRows = s.slides.length;\n                }\n                else {\n                    slidesNumberEvenToRows = Math.ceil(s.slides.length / s.params.slidesPerColumn) * s.params.slidesPerColumn;\n                }\n                if (s.params.slidesPerView !== 'auto' && s.params.slidesPerColumnFill === 'row') {\n                    slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, s.params.slidesPerView * s.params.slidesPerColumn);\n                }\n            }\n        \n            // Calc slides\n            var slideSize;\n            var slidesPerColumn = s.params.slidesPerColumn;\n            var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n            var numFullColumns = slidesPerRow - (s.params.slidesPerColumn * slidesPerRow - s.slides.length);\n            for (i = 0; i < s.slides.length; i++) {\n                slideSize = 0;\n                var slide = s.slides.eq(i);\n                if (s.params.slidesPerColumn > 1) {\n                    // Set slides order\n                    var newSlideOrderIndex;\n                    var column, row;\n                    if (s.params.slidesPerColumnFill === 'column') {\n                        column = Math.floor(i / slidesPerColumn);\n                        row = i - column * slidesPerColumn;\n                        if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn-1)) {\n                            if (++row >= slidesPerColumn) {\n                                row = 0;\n                                column++;\n                            }\n                        }\n                        newSlideOrderIndex = column + row * slidesNumberEvenToRows / slidesPerColumn;\n                        slide\n                            .css({\n                                '-webkit-box-ordinal-group': newSlideOrderIndex,\n                                '-moz-box-ordinal-group': newSlideOrderIndex,\n                                '-ms-flex-order': newSlideOrderIndex,\n                                '-webkit-order': newSlideOrderIndex,\n                                'order': newSlideOrderIndex\n                            });\n                    }\n                    else {\n                        row = Math.floor(i / slidesPerRow);\n                        column = i - row * slidesPerRow;\n                    }\n                    slide\n                        .css(\n                            'margin-' + (s.isHorizontal() ? 'top' : 'left'),\n                            (row !== 0 && s.params.spaceBetween) && (s.params.spaceBetween + 'px')\n                        )\n                        .attr('data-swiper-column', column)\n                        .attr('data-swiper-row', row);\n        \n                }\n                if (slide.css('display') === 'none') continue;\n                if (s.params.slidesPerView === 'auto') {\n                    slideSize = s.isHorizontal() ? slide.outerWidth(true) : slide.outerHeight(true);\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n                }\n                else {\n                    slideSize = (s.size - (s.params.slidesPerView - 1) * spaceBetween) / s.params.slidesPerView;\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n        \n                    if (s.isHorizontal()) {\n                        s.slides[i].style.width = slideSize + 'px';\n                    }\n                    else {\n                        s.slides[i].style.height = slideSize + 'px';\n                    }\n                }\n                s.slides[i].swiperSlideSize = slideSize;\n                s.slidesSizesGrid.push(slideSize);\n        \n        \n                if (s.params.centeredSlides) {\n                    slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n                    if (i === 0) slidePosition = slidePosition - s.size / 2 - spaceBetween;\n                    if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                }\n                else {\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                    slidePosition = slidePosition + slideSize + spaceBetween;\n                }\n        \n                s.virtualSize += slideSize + spaceBetween;\n        \n                prevSlideSize = slideSize;\n        \n                index ++;\n            }\n            s.virtualSize = Math.max(s.virtualSize, s.size) + s.params.slidesOffsetAfter;\n            var newSlidesGrid;\n        \n            if (\n                s.rtl && s.wrongRTL && (s.params.effect === 'slide' || s.params.effect === 'coverflow')) {\n                s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n            if (!s.support.flexbox || s.params.setWrapperSize) {\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n        \n            if (s.params.slidesPerColumn > 1) {\n                s.virtualSize = (slideSize + s.params.spaceBetween) * slidesNumberEvenToRows;\n                s.virtualSize = Math.ceil(s.virtualSize / s.params.slidesPerColumn) - s.params.spaceBetween;\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n                if (s.params.centeredSlides) {\n                    newSlidesGrid = [];\n                    for (i = 0; i < s.snapGrid.length; i++) {\n                        if (s.snapGrid[i] < s.virtualSize + s.snapGrid[0]) newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                    s.snapGrid = newSlidesGrid;\n                }\n            }\n        \n            // Remove last grid elements depending on width\n            if (!s.params.centeredSlides) {\n                newSlidesGrid = [];\n                for (i = 0; i < s.snapGrid.length; i++) {\n                    if (s.snapGrid[i] <= s.virtualSize - s.size) {\n                        newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                }\n                s.snapGrid = newSlidesGrid;\n                if (Math.floor(s.virtualSize - s.size) - Math.floor(s.snapGrid[s.snapGrid.length - 1]) > 1) {\n                    s.snapGrid.push(s.virtualSize - s.size);\n                }\n            }\n            if (s.snapGrid.length === 0) s.snapGrid = [0];\n        \n            if (s.params.spaceBetween !== 0) {\n                if (s.isHorizontal()) {\n                    if (s.rtl) s.slides.css({marginLeft: spaceBetween + 'px'});\n                    else s.slides.css({marginRight: spaceBetween + 'px'});\n                }\n                else s.slides.css({marginBottom: spaceBetween + 'px'});\n            }\n            if (s.params.watchSlidesProgress) {\n                s.updateSlidesOffset();\n            }\n        };\n        s.updateSlidesOffset = function () {\n            for (var i = 0; i < s.slides.length; i++) {\n                s.slides[i].swiperSlideOffset = s.isHorizontal() ? s.slides[i].offsetLeft : s.slides[i].offsetTop;\n            }\n        };\n        \n        /*=========================\n          Dynamic Slides Per View\n          ===========================*/\n        s.currentSlidesPerView = function () {\n            var spv = 1, i, j;\n            if (s.params.centeredSlides) {\n                var size = s.slides[s.activeIndex].swiperSlideSize;\n                var breakLoop;\n                for (i = s.activeIndex + 1; i < s.slides.length; i++) {\n                    if (s.slides[i] && !breakLoop) {\n                        size += s.slides[i].swiperSlideSize;\n                        spv ++;\n                        if (size > s.size) breakLoop = true;\n                    }\n                }\n                for (j = s.activeIndex - 1; j >= 0; j--) {\n                    if (s.slides[j] && !breakLoop) {\n                        size += s.slides[j].swiperSlideSize;\n                        spv ++;\n                        if (size > s.size) breakLoop = true;\n                    }\n                }\n            }\n            else {\n                for (i = s.activeIndex + 1; i < s.slides.length; i++) {\n                    if (s.slidesGrid[i] - s.slidesGrid[s.activeIndex] < s.size) {\n                        spv++;\n                    }\n                }\n            }\n            return spv;\n        };\n        /*=========================\n          Slider/slides progress\n          ===========================*/\n        s.updateSlidesProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            if (s.slides.length === 0) return;\n            if (typeof s.slides[0].swiperSlideOffset === 'undefined') s.updateSlidesOffset();\n        \n            var offsetCenter = -translate;\n            if (s.rtl) offsetCenter = translate;\n        \n            // Visible Slides\n            s.slides.removeClass(s.params.slideVisibleClass);\n            for (var i = 0; i < s.slides.length; i++) {\n                var slide = s.slides[i];\n                var slideProgress = (offsetCenter + (s.params.centeredSlides ? s.minTranslate() : 0) - slide.swiperSlideOffset) / (slide.swiperSlideSize + s.params.spaceBetween);\n                if (s.params.watchSlidesVisibility) {\n                    var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n                    var slideAfter = slideBefore + s.slidesSizesGrid[i];\n                    var isVisible =\n                        (slideBefore >= 0 && slideBefore < s.size) ||\n                        (slideAfter > 0 && slideAfter <= s.size) ||\n                        (slideBefore <= 0 && slideAfter >= s.size);\n                    if (isVisible) {\n                        s.slides.eq(i).addClass(s.params.slideVisibleClass);\n                    }\n                }\n                slide.progress = s.rtl ? -slideProgress : slideProgress;\n            }\n        };\n        s.updateProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            var wasBeginning = s.isBeginning;\n            var wasEnd = s.isEnd;\n            if (translatesDiff === 0) {\n                s.progress = 0;\n                s.isBeginning = s.isEnd = true;\n            }\n            else {\n                s.progress = (translate - s.minTranslate()) / (translatesDiff);\n                s.isBeginning = s.progress <= 0;\n                s.isEnd = s.progress >= 1;\n            }\n            if (s.isBeginning && !wasBeginning) s.emit('onReachBeginning', s);\n            if (s.isEnd && !wasEnd) s.emit('onReachEnd', s);\n        \n            if (s.params.watchSlidesProgress) s.updateSlidesProgress(translate);\n            s.emit('onProgress', s, s.progress);\n        };\n        s.updateActiveIndex = function () {\n            var translate = s.rtl ? s.translate : -s.translate;\n            var newActiveIndex, i, snapIndex;\n            for (i = 0; i < s.slidesGrid.length; i ++) {\n                if (typeof s.slidesGrid[i + 1] !== 'undefined') {\n                    if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1] - (s.slidesGrid[i + 1] - s.slidesGrid[i]) / 2) {\n                        newActiveIndex = i;\n                    }\n                    else if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1]) {\n                        newActiveIndex = i + 1;\n                    }\n                }\n                else {\n                    if (translate >= s.slidesGrid[i]) {\n                        newActiveIndex = i;\n                    }\n                }\n            }\n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                if (newActiveIndex < 0 || typeof newActiveIndex === 'undefined') newActiveIndex = 0;\n            }\n            // for (i = 0; i < s.slidesGrid.length; i++) {\n                // if (- translate >= s.slidesGrid[i]) {\n                    // newActiveIndex = i;\n                // }\n            // }\n            snapIndex = Math.floor(newActiveIndex / s.params.slidesPerGroup);\n            if (snapIndex >= s.snapGrid.length) snapIndex = s.snapGrid.length - 1;\n        \n            if (newActiveIndex === s.activeIndex) {\n                return;\n            }\n            s.snapIndex = snapIndex;\n            s.previousIndex = s.activeIndex;\n            s.activeIndex = newActiveIndex;\n            s.updateClasses();\n            s.updateRealIndex();\n        };\n        s.updateRealIndex = function(){\n            s.realIndex = parseInt(s.slides.eq(s.activeIndex).attr('data-swiper-slide-index') || s.activeIndex, 10);\n        };\n        \n        /*=========================\n          Classes\n          ===========================*/\n        s.updateClasses = function () {\n            s.slides.removeClass(s.params.slideActiveClass + ' ' + s.params.slideNextClass + ' ' + s.params.slidePrevClass + ' ' + s.params.slideDuplicateActiveClass + ' ' + s.params.slideDuplicateNextClass + ' ' + s.params.slideDuplicatePrevClass);\n            var activeSlide = s.slides.eq(s.activeIndex);\n            // Active classes\n            activeSlide.addClass(s.params.slideActiveClass);\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (activeSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n            }\n            // Next Slide\n            var nextSlide = activeSlide.next('.' + s.params.slideClass).addClass(s.params.slideNextClass);\n            if (s.params.loop && nextSlide.length === 0) {\n                nextSlide = s.slides.eq(0);\n                nextSlide.addClass(s.params.slideNextClass);\n            }\n            // Prev Slide\n            var prevSlide = activeSlide.prev('.' + s.params.slideClass).addClass(s.params.slidePrevClass);\n            if (s.params.loop && prevSlide.length === 0) {\n                prevSlide = s.slides.eq(-1);\n                prevSlide.addClass(s.params.slidePrevClass);\n            }\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (nextSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                if (prevSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n            }\n        \n            // Pagination\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                // Current/Total\n                var current,\n                    total = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                if (s.params.loop) {\n                    current = Math.ceil((s.activeIndex - s.loopedSlides)/s.params.slidesPerGroup);\n                    if (current > s.slides.length - 1 - s.loopedSlides * 2) {\n                        current = current - (s.slides.length - s.loopedSlides * 2);\n                    }\n                    if (current > total - 1) current = current - total;\n                    if (current < 0 && s.params.paginationType !== 'bullets') current = total + current;\n                }\n                else {\n                    if (typeof s.snapIndex !== 'undefined') {\n                        current = s.snapIndex;\n                    }\n                    else {\n                        current = s.activeIndex || 0;\n                    }\n                }\n                // Types\n                if (s.params.paginationType === 'bullets' && s.bullets && s.bullets.length > 0) {\n                    s.bullets.removeClass(s.params.bulletActiveClass);\n                    if (s.paginationContainer.length > 1) {\n                        s.bullets.each(function () {\n                            if ($(this).index() === current) $(this).addClass(s.params.bulletActiveClass);\n                        });\n                    }\n                    else {\n                        s.bullets.eq(current).addClass(s.params.bulletActiveClass);\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    s.paginationContainer.find('.' + s.params.paginationCurrentClass).text(current + 1);\n                    s.paginationContainer.find('.' + s.params.paginationTotalClass).text(total);\n                }\n                if (s.params.paginationType === 'progress') {\n                    var scale = (current + 1) / total,\n                        scaleX = scale,\n                        scaleY = 1;\n                    if (!s.isHorizontal()) {\n                        scaleY = scale;\n                        scaleX = 1;\n                    }\n                    s.paginationContainer.find('.' + s.params.paginationProgressbarClass).transform('translate3d(0,0,0) scaleX(' + scaleX + ') scaleY(' + scaleY + ')').transition(s.params.speed);\n                }\n                if (s.params.paginationType === 'custom' && s.params.paginationCustomRender) {\n                    s.paginationContainer.html(s.params.paginationCustomRender(s, current + 1, total));\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        \n            // Next/active buttons\n            if (!s.params.loop) {\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    if (s.isBeginning) {\n                        s.prevButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.prevButton);\n                    }\n                    else {\n                        s.prevButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.prevButton);\n                    }\n                }\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    if (s.isEnd) {\n                        s.nextButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.nextButton);\n                    }\n                    else {\n                        s.nextButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.nextButton);\n                    }\n                }\n            }\n        };\n        \n        /*=========================\n          Pagination\n          ===========================*/\n        s.updatePagination = function () {\n            if (!s.params.pagination) return;\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                var paginationHTML = '';\n                if (s.params.paginationType === 'bullets') {\n                    var numberOfBullets = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                    for (var i = 0; i < numberOfBullets; i++) {\n                        if (s.params.paginationBulletRender) {\n                            paginationHTML += s.params.paginationBulletRender(s, i, s.params.bulletClass);\n                        }\n                        else {\n                            paginationHTML += '<' + s.params.paginationElement+' class=\"' + s.params.bulletClass + '\"></' + s.params.paginationElement + '>';\n                        }\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                    s.bullets = s.paginationContainer.find('.' + s.params.bulletClass);\n                    if (s.params.paginationClickable && s.params.a11y && s.a11y) {\n                        s.a11y.initPagination();\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    if (s.params.paginationFractionRender) {\n                        paginationHTML = s.params.paginationFractionRender(s, s.params.paginationCurrentClass, s.params.paginationTotalClass);\n                    }\n                    else {\n                        paginationHTML =\n                            '<span class=\"' + s.params.paginationCurrentClass + '\"></span>' +\n                            ' / ' +\n                            '<span class=\"' + s.params.paginationTotalClass+'\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType === 'progress') {\n                    if (s.params.paginationProgressRender) {\n                        paginationHTML = s.params.paginationProgressRender(s, s.params.paginationProgressbarClass);\n                    }\n                    else {\n                        paginationHTML = '<span class=\"' + s.params.paginationProgressbarClass + '\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType !== 'custom') {\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        };\n        /*=========================\n          Common update method\n          ===========================*/\n        s.update = function (updateTranslate) {\n            if (!s) return;\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updateProgress();\n            s.updatePagination();\n            s.updateClasses();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            function forceSetTranslate() {\n                var translate = s.rtl ? -s.translate : s.translate;\n                newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n            }\n            if (updateTranslate) {\n                var translated, newTranslate;\n                if (s.controller && s.controller.spline) {\n                    s.controller.spline = undefined;\n                }\n                if (s.params.freeMode) {\n                    forceSetTranslate();\n                    if (s.params.autoHeight) {\n                        s.updateAutoHeight();\n                    }\n                }\n                else {\n                    if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                        translated = s.slideTo(s.slides.length - 1, 0, false, true);\n                    }\n                    else {\n                        translated = s.slideTo(s.activeIndex, 0, false, true);\n                    }\n                    if (!translated) {\n                        forceSetTranslate();\n                    }\n                }\n            }\n            else if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n        };\n        \n        /*=========================\n          Resize Handler\n          ===========================*/\n        s.onResize = function (forceUpdatePagination) {\n            //Breakpoints\n            if (s.params.breakpoints) {\n                s.setBreakpoint();\n            }\n        \n            // Disable locks on resize\n            var allowSwipeToPrev = s.params.allowSwipeToPrev;\n            var allowSwipeToNext = s.params.allowSwipeToNext;\n            s.params.allowSwipeToPrev = s.params.allowSwipeToNext = true;\n        \n            s.updateContainerSize();\n            s.updateSlidesSize();\n            if (s.params.slidesPerView === 'auto' || s.params.freeMode || forceUpdatePagination) s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            if (s.controller && s.controller.spline) {\n                s.controller.spline = undefined;\n            }\n            var slideChangedBySlideTo = false;\n            if (s.params.freeMode) {\n                var newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n        \n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n            }\n            else {\n                s.updateClasses();\n                if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                    slideChangedBySlideTo = s.slideTo(s.slides.length - 1, 0, false, true);\n                }\n                else {\n                    slideChangedBySlideTo = s.slideTo(s.activeIndex, 0, false, true);\n                }\n            }\n            if (s.params.lazyLoading && !slideChangedBySlideTo && s.lazy) {\n                s.lazy.load();\n            }\n            // Return locks after resize\n            s.params.allowSwipeToPrev = allowSwipeToPrev;\n            s.params.allowSwipeToNext = allowSwipeToNext;\n        };\n        \n        /*=========================\n          Events\n          ===========================*/\n        \n        //Define Touch Events\n        s.touchEventsDesktop = {start: 'mousedown', move: 'mousemove', end: 'mouseup'};\n        if (window.navigator.pointerEnabled) s.touchEventsDesktop = {start: 'pointerdown', move: 'pointermove', end: 'pointerup'};\n        else if (window.navigator.msPointerEnabled) s.touchEventsDesktop = {start: 'MSPointerDown', move: 'MSPointerMove', end: 'MSPointerUp'};\n        s.touchEvents = {\n            start : s.support.touch || !s.params.simulateTouch  ? 'touchstart' : s.touchEventsDesktop.start,\n            move : s.support.touch || !s.params.simulateTouch ? 'touchmove' : s.touchEventsDesktop.move,\n            end : s.support.touch || !s.params.simulateTouch ? 'touchend' : s.touchEventsDesktop.end\n        };\n        \n        \n        // WP8 Touch Events Fix\n        if (window.navigator.pointerEnabled || window.navigator.msPointerEnabled) {\n            (s.params.touchEventsTarget === 'container' ? s.container : s.wrapper).addClass('swiper-wp8-' + s.params.direction);\n        }\n        \n        // Attach/detach events\n        s.initEvents = function (detach) {\n            var actionDom = detach ? 'off' : 'on';\n            var action = detach ? 'removeEventListener' : 'addEventListener';\n            var touchEventsTarget = s.params.touchEventsTarget === 'container' ? s.container[0] : s.wrapper[0];\n            var target = s.support.touch ? touchEventsTarget : document;\n        \n            var moveCapture = s.params.nested ? true : false;\n        \n            //Touch Events\n            if (s.browser.ie) {\n                touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, false);\n                target[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                target[action](s.touchEvents.end, s.onTouchEnd, false);\n            }\n            else {\n                if (s.support.touch) {\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, passiveListener);\n                    touchEventsTarget[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                    touchEventsTarget[action](s.touchEvents.end, s.onTouchEnd, passiveListener);\n                }\n                if ((params.simulateTouch && !s.device.ios && !s.device.android) || (params.simulateTouch && !s.support.touch && s.device.ios)) {\n                    touchEventsTarget[action]('mousedown', s.onTouchStart, false);\n                    document[action]('mousemove', s.onTouchMove, moveCapture);\n                    document[action]('mouseup', s.onTouchEnd, false);\n                }\n            }\n            window[action]('resize', s.onResize);\n        \n            // Next, Prev, Index\n            if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                s.nextButton[actionDom]('click', s.onClickNext);\n                if (s.params.a11y && s.a11y) s.nextButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                s.prevButton[actionDom]('click', s.onClickPrev);\n                if (s.params.a11y && s.a11y) s.prevButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.pagination && s.params.paginationClickable) {\n                s.paginationContainer[actionDom]('click', '.' + s.params.bulletClass, s.onClickIndex);\n                if (s.params.a11y && s.a11y) s.paginationContainer[actionDom]('keydown', '.' + s.params.bulletClass, s.a11y.onEnterKey);\n            }\n        \n            // Prevent Links Clicks\n            if (s.params.preventClicks || s.params.preventClicksPropagation) touchEventsTarget[action]('click', s.preventClicks, true);\n        };\n        s.attachEvents = function () {\n            s.initEvents();\n        };\n        s.detachEvents = function () {\n            s.initEvents(true);\n        };\n        \n        /*=========================\n          Handle Clicks\n          ===========================*/\n        // Prevent Clicks\n        s.allowClick = true;\n        s.preventClicks = function (e) {\n            if (!s.allowClick) {\n                if (s.params.preventClicks) e.preventDefault();\n                if (s.params.preventClicksPropagation && s.animating) {\n                    e.stopPropagation();\n                    e.stopImmediatePropagation();\n                }\n            }\n        };\n        // Clicks\n        s.onClickNext = function (e) {\n            e.preventDefault();\n            if (s.isEnd && !s.params.loop) return;\n            s.slideNext();\n        };\n        s.onClickPrev = function (e) {\n            e.preventDefault();\n            if (s.isBeginning && !s.params.loop) return;\n            s.slidePrev();\n        };\n        s.onClickIndex = function (e) {\n            e.preventDefault();\n            var index = $(this).index() * s.params.slidesPerGroup;\n            if (s.params.loop) index = index + s.loopedSlides;\n            s.slideTo(index);\n        };\n        \n        /*=========================\n          Handle Touches\n          ===========================*/\n        function findElementInEvent(e, selector) {\n            var el = $(e.target);\n            if (!el.is(selector)) {\n                if (typeof selector === 'string') {\n                    el = el.parents(selector);\n                }\n                else if (selector.nodeType) {\n                    var found;\n                    el.parents().each(function (index, _el) {\n                        if (_el === selector) found = selector;\n                    });\n                    if (!found) return undefined;\n                    else return selector;\n                }\n            }\n            if (el.length === 0) {\n                return undefined;\n            }\n            return el[0];\n        }\n        s.updateClickedSlide = function (e) {\n            var slide = findElementInEvent(e, '.' + s.params.slideClass);\n            var slideFound = false;\n            if (slide) {\n                for (var i = 0; i < s.slides.length; i++) {\n                    if (s.slides[i] === slide) slideFound = true;\n                }\n            }\n        \n            if (slide && slideFound) {\n                s.clickedSlide = slide;\n                s.clickedIndex = $(slide).index();\n            }\n            else {\n                s.clickedSlide = undefined;\n                s.clickedIndex = undefined;\n                return;\n            }\n            if (s.params.slideToClickedSlide && s.clickedIndex !== undefined && s.clickedIndex !== s.activeIndex) {\n                var slideToIndex = s.clickedIndex,\n                    realIndex,\n                    duplicatedSlides,\n                    slidesPerView = s.params.slidesPerView === 'auto' ? s.currentSlidesPerView() : s.params.slidesPerView;\n                if (s.params.loop) {\n                    if (s.animating) return;\n                    realIndex = parseInt($(s.clickedSlide).attr('data-swiper-slide-index'), 10);\n                    if (s.params.centeredSlides) {\n                        if ((slideToIndex < s.loopedSlides - slidesPerView/2) || (slideToIndex > s.slides.length - s.loopedSlides + slidesPerView/2)) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                    else {\n                        if (slideToIndex > s.slides.length - slidesPerView) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                }\n                else {\n                    s.slideTo(slideToIndex);\n                }\n            }\n        };\n        \n        var isTouched,\n            isMoved,\n            allowTouchCallbacks,\n            touchStartTime,\n            isScrolling,\n            currentTranslate,\n            startTranslate,\n            allowThresholdMove,\n            // Form elements to match\n            formElements = 'input, select, textarea, button, video',\n            // Last click time\n            lastClickTime = Date.now(), clickTimeout,\n            //Velocities\n            velocities = [],\n            allowMomentumBounce;\n        \n        // Animating Flag\n        s.animating = false;\n        \n        // Touches information\n        s.touches = {\n            startX: 0,\n            startY: 0,\n            currentX: 0,\n            currentY: 0,\n            diff: 0\n        };\n        \n        // Touch handlers\n        var isTouchEvent, startMoving;\n        s.onTouchStart = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            isTouchEvent = e.type === 'touchstart';\n            if (!isTouchEvent && 'which' in e && e.which === 3) return;\n            if (s.params.noSwiping && findElementInEvent(e, '.' + s.params.noSwipingClass)) {\n                s.allowClick = true;\n                return;\n            }\n            if (s.params.swipeHandler) {\n                if (!findElementInEvent(e, s.params.swipeHandler)) return;\n            }\n        \n            var startX = s.touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n            var startY = s.touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n        \n            // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n            if(s.device.ios && s.params.iOSEdgeSwipeDetection && startX <= s.params.iOSEdgeSwipeThreshold) {\n                return;\n            }\n        \n            isTouched = true;\n            isMoved = false;\n            allowTouchCallbacks = true;\n            isScrolling = undefined;\n            startMoving = undefined;\n            s.touches.startX = startX;\n            s.touches.startY = startY;\n            touchStartTime = Date.now();\n            s.allowClick = true;\n            s.updateContainerSize();\n            s.swipeDirection = undefined;\n            if (s.params.threshold > 0) allowThresholdMove = false;\n            if (e.type !== 'touchstart') {\n                var preventDefault = true;\n                if ($(e.target).is(formElements)) preventDefault = false;\n                if (document.activeElement && $(document.activeElement).is(formElements)) {\n                    document.activeElement.blur();\n                }\n                if (preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            s.emit('onTouchStart', s, e);\n        };\n        \n        s.onTouchMove = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (isTouchEvent && e.type === 'mousemove') return;\n            if (e.preventedByNestedSwiper) {\n                s.touches.startX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                s.touches.startY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                return;\n            }\n            if (s.params.onlyExternal) {\n                // isMoved = true;\n                s.allowClick = false;\n                if (isTouched) {\n                    s.touches.startX = s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                    s.touches.startY = s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                    touchStartTime = Date.now();\n                }\n                return;\n            }\n            if (isTouchEvent && s.params.touchReleaseOnEdges && !s.params.loop) {\n                if (!s.isHorizontal()) {\n                    // Vertical\n                    if (\n                        (s.touches.currentY < s.touches.startY && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentY > s.touches.startY && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n                else {\n                    if (\n                        (s.touches.currentX < s.touches.startX && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentX > s.touches.startX && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n            }\n            if (isTouchEvent && document.activeElement) {\n                if (e.target === document.activeElement && $(e.target).is(formElements)) {\n                    isMoved = true;\n                    s.allowClick = false;\n                    return;\n                }\n            }\n            if (allowTouchCallbacks) {\n                s.emit('onTouchMove', s, e);\n            }\n            if (e.targetTouches && e.targetTouches.length > 1) return;\n        \n            s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n            s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n            if (typeof isScrolling === 'undefined') {\n                var touchAngle;\n                if (s.isHorizontal() && s.touches.currentY === s.touches.startY || !s.isHorizontal() && s.touches.currentX === s.touches.startX) {\n                    isScrolling = false;\n                }\n                else {\n                    touchAngle = Math.atan2(Math.abs(s.touches.currentY - s.touches.startY), Math.abs(s.touches.currentX - s.touches.startX)) * 180 / Math.PI;\n                    isScrolling = s.isHorizontal() ? touchAngle > s.params.touchAngle : (90 - touchAngle > s.params.touchAngle);\n                }\n            }\n            if (isScrolling) {\n                s.emit('onTouchMoveOpposite', s, e);\n            }\n            if (typeof startMoving === 'undefined' && s.browser.ieTouch) {\n                if (s.touches.currentX !== s.touches.startX || s.touches.currentY !== s.touches.startY) {\n                    startMoving = true;\n                }\n            }\n            if (!isTouched) return;\n            if (isScrolling)  {\n                isTouched = false;\n                return;\n            }\n            if (!startMoving && s.browser.ieTouch) {\n                return;\n            }\n            s.allowClick = false;\n            s.emit('onSliderMove', s, e);\n            e.preventDefault();\n            if (s.params.touchMoveStopPropagation && !s.params.nested) {\n                e.stopPropagation();\n            }\n        \n            if (!isMoved) {\n                if (params.loop) {\n                    s.fixLoop();\n                }\n                startTranslate = s.getWrapperTranslate();\n                s.setWrapperTransition(0);\n                if (s.animating) {\n                    s.wrapper.trigger('webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd');\n                }\n                if (s.params.autoplay && s.autoplaying) {\n                    if (s.params.autoplayDisableOnInteraction) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        s.pauseAutoplay();\n                    }\n                }\n                allowMomentumBounce = false;\n                //Grab Cursor\n                if (s.params.grabCursor && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                    s.setGrabCursor(true);\n                }\n            }\n            isMoved = true;\n        \n            var diff = s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n        \n            diff = diff * s.params.touchRatio;\n            if (s.rtl) diff = -diff;\n        \n            s.swipeDirection = diff > 0 ? 'prev' : 'next';\n            currentTranslate = diff + startTranslate;\n        \n            var disableParentSwiper = true;\n            if ((diff > 0 && currentTranslate > s.minTranslate())) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.minTranslate() - 1 + Math.pow(-s.minTranslate() + startTranslate + diff, s.params.resistanceRatio);\n            }\n            else if (diff < 0 && currentTranslate < s.maxTranslate()) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.maxTranslate() + 1 - Math.pow(s.maxTranslate() - startTranslate - diff, s.params.resistanceRatio);\n            }\n        \n            if (disableParentSwiper) {\n                e.preventedByNestedSwiper = true;\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && s.swipeDirection === 'next' && currentTranslate < startTranslate) {\n                currentTranslate = startTranslate;\n            }\n            if (!s.params.allowSwipeToPrev && s.swipeDirection === 'prev' && currentTranslate > startTranslate) {\n                currentTranslate = startTranslate;\n            }\n        \n        \n            // Threshold\n            if (s.params.threshold > 0) {\n                if (Math.abs(diff) > s.params.threshold || allowThresholdMove) {\n                    if (!allowThresholdMove) {\n                        allowThresholdMove = true;\n                        s.touches.startX = s.touches.currentX;\n                        s.touches.startY = s.touches.currentY;\n                        currentTranslate = startTranslate;\n                        s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n                        return;\n                    }\n                }\n                else {\n                    currentTranslate = startTranslate;\n                    return;\n                }\n            }\n        \n            if (!s.params.followFinger) return;\n        \n            // Update active index in free mode\n            if (s.params.freeMode || s.params.watchSlidesProgress) {\n                s.updateActiveIndex();\n            }\n            if (s.params.freeMode) {\n                //Velocity\n                if (velocities.length === 0) {\n                    velocities.push({\n                        position: s.touches[s.isHorizontal() ? 'startX' : 'startY'],\n                        time: touchStartTime\n                    });\n                }\n                velocities.push({\n                    position: s.touches[s.isHorizontal() ? 'currentX' : 'currentY'],\n                    time: (new window.Date()).getTime()\n                });\n            }\n            // Update progress\n            s.updateProgress(currentTranslate);\n            // Update translate\n            s.setWrapperTranslate(currentTranslate);\n        };\n        s.onTouchEnd = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (allowTouchCallbacks) {\n                s.emit('onTouchEnd', s, e);\n            }\n            allowTouchCallbacks = false;\n            if (!isTouched) return;\n            //Return Grab Cursor\n            if (s.params.grabCursor && isMoved && isTouched  && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                s.setGrabCursor(false);\n            }\n        \n            // Time diff\n            var touchEndTime = Date.now();\n            var timeDiff = touchEndTime - touchStartTime;\n        \n            // Tap, doubleTap, Click\n            if (s.allowClick) {\n                s.updateClickedSlide(e);\n                s.emit('onTap', s, e);\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) > 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    clickTimeout = setTimeout(function () {\n                        if (!s) return;\n                        if (s.params.paginationHide && s.paginationContainer.length > 0 && !$(e.target).hasClass(s.params.bulletClass)) {\n                            s.paginationContainer.toggleClass(s.params.paginationHiddenClass);\n                        }\n                        s.emit('onClick', s, e);\n                    }, 300);\n        \n                }\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) < 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    s.emit('onDoubleTap', s, e);\n                }\n            }\n        \n            lastClickTime = Date.now();\n            setTimeout(function () {\n                if (s) s.allowClick = true;\n            }, 0);\n        \n            if (!isTouched || !isMoved || !s.swipeDirection || s.touches.diff === 0 || currentTranslate === startTranslate) {\n                isTouched = isMoved = false;\n                return;\n            }\n            isTouched = isMoved = false;\n        \n            var currentPos;\n            if (s.params.followFinger) {\n                currentPos = s.rtl ? s.translate : -s.translate;\n            }\n            else {\n                currentPos = -currentTranslate;\n            }\n            if (s.params.freeMode) {\n                if (currentPos < -s.minTranslate()) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                else if (currentPos > -s.maxTranslate()) {\n                    if (s.slides.length < s.snapGrid.length) {\n                        s.slideTo(s.snapGrid.length - 1);\n                    }\n                    else {\n                        s.slideTo(s.slides.length - 1);\n                    }\n                    return;\n                }\n        \n                if (s.params.freeModeMomentum) {\n                    if (velocities.length > 1) {\n                        var lastMoveEvent = velocities.pop(), velocityEvent = velocities.pop();\n        \n                        var distance = lastMoveEvent.position - velocityEvent.position;\n                        var time = lastMoveEvent.time - velocityEvent.time;\n                        s.velocity = distance / time;\n                        s.velocity = s.velocity / 2;\n                        if (Math.abs(s.velocity) < s.params.freeModeMinimumVelocity) {\n                            s.velocity = 0;\n                        }\n                        // this implies that the user stopped moving a finger then released.\n                        // There would be no events with distance zero, so the last event is stale.\n                        if (time > 150 || (new window.Date().getTime() - lastMoveEvent.time) > 300) {\n                            s.velocity = 0;\n                        }\n                    } else {\n                        s.velocity = 0;\n                    }\n                    s.velocity = s.velocity * s.params.freeModeMomentumVelocityRatio;\n        \n                    velocities.length = 0;\n                    var momentumDuration = 1000 * s.params.freeModeMomentumRatio;\n                    var momentumDistance = s.velocity * momentumDuration;\n        \n                    var newPosition = s.translate + momentumDistance;\n                    if (s.rtl) newPosition = - newPosition;\n                    var doBounce = false;\n                    var afterBouncePosition;\n                    var bounceAmount = Math.abs(s.velocity) * 20 * s.params.freeModeMomentumBounceRatio;\n                    if (newPosition < s.maxTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition + s.maxTranslate() < -bounceAmount) {\n                                newPosition = s.maxTranslate() - bounceAmount;\n                            }\n                            afterBouncePosition = s.maxTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.maxTranslate();\n                        }\n                    }\n                    else if (newPosition > s.minTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition - s.minTranslate() > bounceAmount) {\n                                newPosition = s.minTranslate() + bounceAmount;\n                            }\n                            afterBouncePosition = s.minTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.minTranslate();\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        var j = 0,\n                            nextSlide;\n                        for (j = 0; j < s.snapGrid.length; j += 1) {\n                            if (s.snapGrid[j] > -newPosition) {\n                                nextSlide = j;\n                                break;\n                            }\n        \n                        }\n                        if (Math.abs(s.snapGrid[nextSlide] - newPosition) < Math.abs(s.snapGrid[nextSlide - 1] - newPosition) || s.swipeDirection === 'next') {\n                            newPosition = s.snapGrid[nextSlide];\n                        } else {\n                            newPosition = s.snapGrid[nextSlide - 1];\n                        }\n                        if (!s.rtl) newPosition = - newPosition;\n                    }\n                    //Fix duration\n                    if (s.velocity !== 0) {\n                        if (s.rtl) {\n                            momentumDuration = Math.abs((-newPosition - s.translate) / s.velocity);\n                        }\n                        else {\n                            momentumDuration = Math.abs((newPosition - s.translate) / s.velocity);\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        s.slideReset();\n                        return;\n                    }\n        \n                    if (s.params.freeModeMomentumBounce && doBounce) {\n                        s.updateProgress(afterBouncePosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        s.animating = true;\n                        s.wrapper.transitionEnd(function () {\n                            if (!s || !allowMomentumBounce) return;\n                            s.emit('onMomentumBounce', s);\n        \n                            s.setWrapperTransition(s.params.speed);\n                            s.setWrapperTranslate(afterBouncePosition);\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        });\n                    } else if (s.velocity) {\n                        s.updateProgress(newPosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        if (!s.animating) {\n                            s.animating = true;\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        }\n        \n                    } else {\n                        s.updateProgress(newPosition);\n                    }\n        \n                    s.updateActiveIndex();\n                }\n                if (!s.params.freeModeMomentum || timeDiff >= s.params.longSwipesMs) {\n                    s.updateProgress();\n                    s.updateActiveIndex();\n                }\n                return;\n            }\n        \n            // Find current slide\n            var i, stopIndex = 0, groupSize = s.slidesSizesGrid[0];\n            for (i = 0; i < s.slidesGrid.length; i += s.params.slidesPerGroup) {\n                if (typeof s.slidesGrid[i + s.params.slidesPerGroup] !== 'undefined') {\n                    if (currentPos >= s.slidesGrid[i] && currentPos < s.slidesGrid[i + s.params.slidesPerGroup]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[i + s.params.slidesPerGroup] - s.slidesGrid[i];\n                    }\n                }\n                else {\n                    if (currentPos >= s.slidesGrid[i]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[s.slidesGrid.length - 1] - s.slidesGrid[s.slidesGrid.length - 2];\n                    }\n                }\n            }\n        \n            // Find current slide size\n            var ratio = (currentPos - s.slidesGrid[stopIndex]) / groupSize;\n        \n            if (timeDiff > s.params.longSwipesMs) {\n                // Long touches\n                if (!s.params.longSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    if (ratio >= s.params.longSwipesRatio) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    if (ratio > (1 - s.params.longSwipesRatio)) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n                }\n            }\n            else {\n                // Short swipes\n                if (!s.params.shortSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    s.slideTo(stopIndex + s.params.slidesPerGroup);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    s.slideTo(stopIndex);\n                }\n            }\n        };\n        /*=========================\n          Transitions\n          ===========================*/\n        s._slideTo = function (slideIndex, speed) {\n            return s.slideTo(slideIndex, speed, true, true);\n        };\n        s.slideTo = function (slideIndex, speed, runCallbacks, internal) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (typeof slideIndex === 'undefined') slideIndex = 0;\n            if (slideIndex < 0) slideIndex = 0;\n            s.snapIndex = Math.floor(slideIndex / s.params.slidesPerGroup);\n            if (s.snapIndex >= s.snapGrid.length) s.snapIndex = s.snapGrid.length - 1;\n        \n            var translate = - s.snapGrid[s.snapIndex];\n            // Stop autoplay\n            if (s.params.autoplay && s.autoplaying) {\n                if (internal || !s.params.autoplayDisableOnInteraction) {\n                    s.pauseAutoplay(speed);\n                }\n                else {\n                    s.stopAutoplay();\n                }\n            }\n            // Update progress\n            s.updateProgress(translate);\n        \n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                for (var i = 0; i < s.slidesGrid.length; i++) {\n                    if (- Math.floor(translate * 100) >= Math.floor(s.slidesGrid[i] * 100)) {\n                        slideIndex = i;\n                    }\n                }\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && translate < s.translate && translate < s.minTranslate()) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && translate > s.translate && translate > s.maxTranslate()) {\n                if ((s.activeIndex || 0) !== slideIndex ) return false;\n            }\n        \n            // Update Index\n            if (typeof speed === 'undefined') speed = s.params.speed;\n            s.previousIndex = s.activeIndex || 0;\n            s.activeIndex = slideIndex;\n            s.updateRealIndex();\n            if ((s.rtl && -translate === s.translate) || (!s.rtl && translate === s.translate)) {\n                // Update Height\n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n                s.updateClasses();\n                if (s.params.effect !== 'slide') {\n                    s.setWrapperTranslate(translate);\n                }\n                return false;\n            }\n            s.updateClasses();\n            s.onTransitionStart(runCallbacks);\n        \n            if (speed === 0 || s.browser.lteIE9) {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(0);\n                s.onTransitionEnd(runCallbacks);\n            }\n            else {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(speed);\n                if (!s.animating) {\n                    s.animating = true;\n                    s.wrapper.transitionEnd(function () {\n                        if (!s) return;\n                        s.onTransitionEnd(runCallbacks);\n                    });\n                }\n        \n            }\n        \n            return true;\n        };\n        \n        s.onTransitionStart = function (runCallbacks) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n            if (s.lazy) s.lazy.onTransitionStart();\n            if (runCallbacks) {\n                s.emit('onTransitionStart', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeStart', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextStart', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevStart', s);\n                    }\n                }\n        \n            }\n        };\n        s.onTransitionEnd = function (runCallbacks) {\n            s.animating = false;\n            s.setWrapperTransition(0);\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.lazy) s.lazy.onTransitionEnd();\n            if (runCallbacks) {\n                s.emit('onTransitionEnd', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeEnd', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextEnd', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevEnd', s);\n                    }\n                }\n            }\n            if (s.params.history && s.history) {\n                s.history.setHistory(s.params.history, s.activeIndex);\n            }\n            if (s.params.hashnav && s.hashnav) {\n                s.hashnav.setHash();\n            }\n        \n        };\n        s.slideNext = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n        };\n        s._slideNext = function (speed) {\n            return s.slideNext(true, speed, true);\n        };\n        s.slidePrev = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n        };\n        s._slidePrev = function (speed) {\n            return s.slidePrev(true, speed, true);\n        };\n        s.slideReset = function (runCallbacks, speed, internal) {\n            return s.slideTo(s.activeIndex, speed, runCallbacks);\n        };\n        \n        s.disableTouchControl = function () {\n            s.params.onlyExternal = true;\n            return true;\n        };\n        s.enableTouchControl = function () {\n            s.params.onlyExternal = false;\n            return true;\n        };\n        \n        /*=========================\n          Translate/transition helpers\n          ===========================*/\n        s.setWrapperTransition = function (duration, byController) {\n            s.wrapper.transition(duration);\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTransition(duration);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTransition(duration);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTransition(duration);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTransition(duration, byController);\n            }\n            s.emit('onSetTransition', s, duration);\n        };\n        s.setWrapperTranslate = function (translate, updateActiveIndex, byController) {\n            var x = 0, y = 0, z = 0;\n            if (s.isHorizontal()) {\n                x = s.rtl ? -translate : translate;\n            }\n            else {\n                y = translate;\n            }\n        \n            if (s.params.roundLengths) {\n                x = round(x);\n                y = round(y);\n            }\n        \n            if (!s.params.virtualTranslate) {\n                if (s.support.transforms3d) s.wrapper.transform('translate3d(' + x + 'px, ' + y + 'px, ' + z + 'px)');\n                else s.wrapper.transform('translate(' + x + 'px, ' + y + 'px)');\n            }\n        \n            s.translate = s.isHorizontal() ? x : y;\n        \n            // Check if we need to update progress\n            var progress;\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            if (translatesDiff === 0) {\n                progress = 0;\n            }\n            else {\n                progress = (translate - s.minTranslate()) / (translatesDiff);\n            }\n            if (progress !== s.progress) {\n                s.updateProgress(translate);\n            }\n        \n            if (updateActiveIndex) s.updateActiveIndex();\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTranslate(s.translate);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTranslate(s.translate);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTranslate(s.translate);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTranslate(s.translate, byController);\n            }\n            s.emit('onSetTranslate', s, s.translate);\n        };\n        \n        s.getTranslate = function (el, axis) {\n            var matrix, curTransform, curStyle, transformMatrix;\n        \n            // automatic axis detection\n            if (typeof axis === 'undefined') {\n                axis = 'x';\n            }\n        \n            if (s.params.virtualTranslate) {\n                return s.rtl ? -s.translate : s.translate;\n            }\n        \n            curStyle = window.getComputedStyle(el, null);\n            if (window.WebKitCSSMatrix) {\n                curTransform = curStyle.transform || curStyle.webkitTransform;\n                if (curTransform.split(',').length > 6) {\n                    curTransform = curTransform.split(', ').map(function(a){\n                        return a.replace(',','.');\n                    }).join(', ');\n                }\n                // Some old versions of Webkit choke when 'none' is passed; pass\n                // empty string instead in this case\n                transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n            }\n            else {\n                transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform  || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n                matrix = transformMatrix.toString().split(',');\n            }\n        \n            if (axis === 'x') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m41;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[12]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[4]);\n            }\n            if (axis === 'y') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m42;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[13]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[5]);\n            }\n            if (s.rtl && curTransform) curTransform = -curTransform;\n            return curTransform || 0;\n        };\n        s.getWrapperTranslate = function (axis) {\n            if (typeof axis === 'undefined') {\n                axis = s.isHorizontal() ? 'x' : 'y';\n            }\n            return s.getTranslate(s.wrapper[0], axis);\n        };\n        \n        /*=========================\n          Observer\n          ===========================*/\n        s.observers = [];\n        function initObserver(target, options) {\n            options = options || {};\n            // create an observer instance\n            var ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n            var observer = new ObserverFunc(function (mutations) {\n                mutations.forEach(function (mutation) {\n                    s.onResize(true);\n                    s.emit('onObserverUpdate', s, mutation);\n                });\n            });\n        \n            observer.observe(target, {\n                attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n                childList: typeof options.childList === 'undefined' ? true : options.childList,\n                characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n            });\n        \n            s.observers.push(observer);\n        }\n        s.initObservers = function () {\n            if (s.params.observeParents) {\n                var containerParents = s.container.parents();\n                for (var i = 0; i < containerParents.length; i++) {\n                    initObserver(containerParents[i]);\n                }\n            }\n        \n            // Observe container\n            initObserver(s.container[0], {childList: false});\n        \n            // Observe wrapper\n            initObserver(s.wrapper[0], {attributes: false});\n        };\n        s.disconnectObservers = function () {\n            for (var i = 0; i < s.observers.length; i++) {\n                s.observers[i].disconnect();\n            }\n            s.observers = [];\n        };\n        /*=========================\n          Loop\n          ===========================*/\n        // Create looped slides\n        s.createLoop = function () {\n            // Remove duplicated slides\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n        \n            var slides = s.wrapper.children('.' + s.params.slideClass);\n        \n            if(s.params.slidesPerView === 'auto' && !s.params.loopedSlides) s.params.loopedSlides = slides.length;\n        \n            s.loopedSlides = parseInt(s.params.loopedSlides || s.params.slidesPerView, 10);\n            s.loopedSlides = s.loopedSlides + s.params.loopAdditionalSlides;\n            if (s.loopedSlides > slides.length) {\n                s.loopedSlides = slides.length;\n            }\n        \n            var prependSlides = [], appendSlides = [], i;\n            slides.each(function (index, el) {\n                var slide = $(this);\n                if (index < s.loopedSlides) appendSlides.push(el);\n                if (index < slides.length && index >= slides.length - s.loopedSlides) prependSlides.push(el);\n                slide.attr('data-swiper-slide-index', index);\n            });\n            for (i = 0; i < appendSlides.length; i++) {\n                s.wrapper.append($(appendSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n            for (i = prependSlides.length - 1; i >= 0; i--) {\n                s.wrapper.prepend($(prependSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n        };\n        s.destroyLoop = function () {\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n            s.slides.removeAttr('data-swiper-slide-index');\n        };\n        s.reLoop = function (updatePosition) {\n            var oldIndex = s.activeIndex - s.loopedSlides;\n            s.destroyLoop();\n            s.createLoop();\n            s.updateSlidesSize();\n            if (updatePosition) {\n                s.slideTo(oldIndex + s.loopedSlides, 0, false);\n            }\n        \n        };\n        s.fixLoop = function () {\n            var newIndex;\n            //Fix For Negative Oversliding\n            if (s.activeIndex < s.loopedSlides) {\n                newIndex = s.slides.length - s.loopedSlides * 3 + s.activeIndex;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n            //Fix For Positive Oversliding\n            else if ((s.params.slidesPerView === 'auto' && s.activeIndex >= s.loopedSlides * 2) || (s.activeIndex > s.slides.length - s.params.slidesPerView * 2)) {\n                newIndex = -s.slides.length + s.activeIndex + s.loopedSlides;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n        };\n        /*=========================\n          Append/Prepend/Remove Slides\n          ===========================*/\n        s.appendSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.append(slides[i]);\n                }\n            }\n            else {\n                s.wrapper.append(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n        };\n        s.prependSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            var newActiveIndex = s.activeIndex + 1;\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.prepend(slides[i]);\n                }\n                newActiveIndex = s.activeIndex + slides.length;\n            }\n            else {\n                s.wrapper.prepend(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            s.slideTo(newActiveIndex, 0, false);\n        };\n        s.removeSlide = function (slidesIndexes) {\n            if (s.params.loop) {\n                s.destroyLoop();\n                s.slides = s.wrapper.children('.' + s.params.slideClass);\n            }\n            var newActiveIndex = s.activeIndex,\n                indexToRemove;\n            if (typeof slidesIndexes === 'object' && slidesIndexes.length) {\n                for (var i = 0; i < slidesIndexes.length; i++) {\n                    indexToRemove = slidesIndexes[i];\n                    if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                    if (indexToRemove < newActiveIndex) newActiveIndex--;\n                }\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n            else {\n                indexToRemove = slidesIndexes;\n                if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                if (indexToRemove < newActiveIndex) newActiveIndex--;\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n        \n            if (s.params.loop) {\n                s.createLoop();\n            }\n        \n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            if (s.params.loop) {\n                s.slideTo(newActiveIndex + s.loopedSlides, 0, false);\n            }\n            else {\n                s.slideTo(newActiveIndex, 0, false);\n            }\n        \n        };\n        s.removeAllSlides = function () {\n            var slidesIndexes = [];\n            for (var i = 0; i < s.slides.length; i++) {\n                slidesIndexes.push(i);\n            }\n            s.removeSlide(slidesIndexes);\n        };\n        \n\n        /*=========================\n          Effects\n          ===========================*/\n        s.effects = {\n            fade: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var offset = slide[0].swiperSlideOffset;\n                        var tx = -offset;\n                        if (!s.params.virtualTranslate) tx = tx - s.translate;\n                        var ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n                        var slideOpacity = s.params.fade.crossFade ?\n                                Math.max(1 - Math.abs(slide[0].progress), 0) :\n                                1 + Math.min(Math.max(slide[0].progress, -1), 0);\n                        slide\n                            .css({\n                                opacity: slideOpacity\n                            })\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px)');\n        \n                    }\n        \n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            flip: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var progress = slide[0].progress;\n                        if (s.params.flip.limitRotation) {\n                            progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        }\n                        var offset = slide[0].swiperSlideOffset;\n                        var rotate = -180 * progress,\n                            rotateY = rotate,\n                            rotateX = 0,\n                            tx = -offset,\n                            ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                            rotateX = -rotateY;\n                            rotateY = 0;\n                        }\n                        else if (s.rtl) {\n                            rotateY = -rotateY;\n                        }\n        \n                        slide[0].style.zIndex = -Math.abs(Math.round(progress)) + s.slides.length;\n        \n                        if (s.params.flip.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n        \n                        slide\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)');\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.eq(s.activeIndex).transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            if (!$(this).hasClass(s.params.slideActiveClass)) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            cube: {\n                setTranslate: function () {\n                    var wrapperRotate = 0, cubeShadow;\n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow = s.wrapper.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.wrapper.append(cubeShadow);\n                            }\n                            cubeShadow.css({height: s.width + 'px'});\n                        }\n                        else {\n                            cubeShadow = s.container.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.container.append(cubeShadow);\n                            }\n                        }\n                    }\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideAngle = i * 90;\n                        var round = Math.floor(slideAngle / 360);\n                        if (s.rtl) {\n                            slideAngle = -slideAngle;\n                            round = Math.floor(-slideAngle / 360);\n                        }\n                        var progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        var tx = 0, ty = 0, tz = 0;\n                        if (i % 4 === 0) {\n                            tx = - round * 4 * s.size;\n                            tz = 0;\n                        }\n                        else if ((i - 1) % 4 === 0) {\n                            tx = 0;\n                            tz = - round * 4 * s.size;\n                        }\n                        else if ((i - 2) % 4 === 0) {\n                            tx = s.size + round * 4 * s.size;\n                            tz = s.size;\n                        }\n                        else if ((i - 3) % 4 === 0) {\n                            tx = - s.size;\n                            tz = 3 * s.size + s.size * 4 * round;\n                        }\n                        if (s.rtl) {\n                            tx = -tx;\n                        }\n        \n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n        \n                        var transform = 'rotateX(' + (s.isHorizontal() ? 0 : -slideAngle) + 'deg) rotateY(' + (s.isHorizontal() ? slideAngle : 0) + 'deg) translate3d(' + tx + 'px, ' + ty + 'px, ' + tz + 'px)';\n                        if (progress <= 1 && progress > -1) {\n                            wrapperRotate = i * 90 + progress * 90;\n                            if (s.rtl) wrapperRotate = -i * 90 - progress * 90;\n                        }\n                        slide.transform(transform);\n                        if (s.params.cube.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n                    }\n                    s.wrapper.css({\n                        '-webkit-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-moz-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-ms-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        'transform-origin': '50% 50% -' + (s.size / 2) + 'px'\n                    });\n        \n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow.transform('translate3d(0px, ' + (s.width / 2 + s.params.cube.shadowOffset) + 'px, ' + (-s.width / 2) + 'px) rotateX(90deg) rotateZ(0deg) scale(' + (s.params.cube.shadowScale) + ')');\n                        }\n                        else {\n                            var shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n                            var multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n                            var scale1 = s.params.cube.shadowScale,\n                                scale2 = s.params.cube.shadowScale / multiplier,\n                                offset = s.params.cube.shadowOffset;\n                            cubeShadow.transform('scale3d(' + scale1 + ', 1, ' + scale2 + ') translate3d(0px, ' + (s.height / 2 + offset) + 'px, ' + (-s.height / 2 / scale2) + 'px) rotateX(-90deg)');\n                        }\n                    }\n                    var zFactor = (s.isSafari || s.isUiWebView) ? (-s.size / 2) : 0;\n                    s.wrapper.transform('translate3d(0px,0,' + zFactor + 'px) rotateX(' + (s.isHorizontal() ? 0 : wrapperRotate) + 'deg) rotateY(' + (s.isHorizontal() ? -wrapperRotate : 0) + 'deg)');\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.cube.shadow && !s.isHorizontal()) {\n                        s.container.find('.swiper-cube-shadow').transition(duration);\n                    }\n                }\n            },\n            coverflow: {\n                setTranslate: function () {\n                    var transform = s.translate;\n                    var center = s.isHorizontal() ? -transform + s.width / 2 : -transform + s.height / 2;\n                    var rotate = s.isHorizontal() ? s.params.coverflow.rotate: -s.params.coverflow.rotate;\n                    var translate = s.params.coverflow.depth;\n                    //Each slide offset from center\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideSize = s.slidesSizesGrid[i];\n                        var slideOffset = slide[0].swiperSlideOffset;\n                        var offsetMultiplier = (center - slideOffset - slideSize / 2) / slideSize * s.params.coverflow.modifier;\n        \n                        var rotateY = s.isHorizontal() ? rotate * offsetMultiplier : 0;\n                        var rotateX = s.isHorizontal() ? 0 : rotate * offsetMultiplier;\n                        // var rotateZ = 0\n                        var translateZ = -translate * Math.abs(offsetMultiplier);\n        \n                        var translateY = s.isHorizontal() ? 0 : s.params.coverflow.stretch * (offsetMultiplier);\n                        var translateX = s.isHorizontal() ? s.params.coverflow.stretch * (offsetMultiplier) : 0;\n        \n                        //Fix for ultra small values\n                        if (Math.abs(translateX) < 0.001) translateX = 0;\n                        if (Math.abs(translateY) < 0.001) translateY = 0;\n                        if (Math.abs(translateZ) < 0.001) translateZ = 0;\n                        if (Math.abs(rotateY) < 0.001) rotateY = 0;\n                        if (Math.abs(rotateX) < 0.001) rotateX = 0;\n        \n                        var slideTransform = 'translate3d(' + translateX + 'px,' + translateY + 'px,' + translateZ + 'px)  rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';\n        \n                        slide.transform(slideTransform);\n                        slide[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n                        if (s.params.coverflow.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0;\n                        }\n                    }\n        \n                    //Set correct perspective for IE10\n                    if (s.browser.ie) {\n                        var ws = s.wrapper[0].style;\n                        ws.perspectiveOrigin = center + 'px 50%';\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                }\n            }\n        };\n\n        /*=========================\n          Images Lazy Loading\n          ===========================*/\n        s.lazy = {\n            initialImageLoaded: false,\n            loadImageInSlide: function (index, loadInDuplicate) {\n                if (typeof index === 'undefined') return;\n                if (typeof loadInDuplicate === 'undefined') loadInDuplicate = true;\n                if (s.slides.length === 0) return;\n        \n                var slide = s.slides.eq(index);\n                var img = slide.find('.' + s.params.lazyLoadingClass + ':not(.' + s.params.lazyStatusLoadedClass + '):not(.' + s.params.lazyStatusLoadingClass + ')');\n                if (slide.hasClass(s.params.lazyLoadingClass) && !slide.hasClass(s.params.lazyStatusLoadedClass) && !slide.hasClass(s.params.lazyStatusLoadingClass)) {\n                    img = img.add(slide[0]);\n                }\n                if (img.length === 0) return;\n        \n                img.each(function () {\n                    var _img = $(this);\n                    _img.addClass(s.params.lazyStatusLoadingClass);\n                    var background = _img.attr('data-background');\n                    var src = _img.attr('data-src'),\n                        srcset = _img.attr('data-srcset'),\n                        sizes = _img.attr('data-sizes');\n                    s.loadImage(_img[0], (src || background), srcset, sizes, false, function () {\n                        if (background) {\n                            _img.css('background-image', 'url(\"' + background + '\")');\n                            _img.removeAttr('data-background');\n                        }\n                        else {\n                            if (srcset) {\n                                _img.attr('srcset', srcset);\n                                _img.removeAttr('data-srcset');\n                            }\n                            if (sizes) {\n                                _img.attr('sizes', sizes);\n                                _img.removeAttr('data-sizes');\n                            }\n                            if (src) {\n                                _img.attr('src', src);\n                                _img.removeAttr('data-src');\n                            }\n        \n                        }\n        \n                        _img.addClass(s.params.lazyStatusLoadedClass).removeClass(s.params.lazyStatusLoadingClass);\n                        slide.find('.' + s.params.lazyPreloaderClass + ', .' + s.params.preloaderClass).remove();\n                        if (s.params.loop && loadInDuplicate) {\n                            var slideOriginalIndex = slide.attr('data-swiper-slide-index');\n                            if (slide.hasClass(s.params.slideDuplicateClass)) {\n                                var originalSlide = s.wrapper.children('[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')');\n                                s.lazy.loadImageInSlide(originalSlide.index(), false);\n                            }\n                            else {\n                                var duplicatedSlide = s.wrapper.children('.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]');\n                                s.lazy.loadImageInSlide(duplicatedSlide.index(), false);\n                            }\n                        }\n                        s.emit('onLazyImageReady', s, slide[0], _img[0]);\n                    });\n        \n                    s.emit('onLazyImageLoad', s, slide[0], _img[0]);\n                });\n        \n            },\n            load: function () {\n                var i;\n                var slidesPerView = s.params.slidesPerView;\n                if (slidesPerView === 'auto') {\n                    slidesPerView = 0;\n                }\n                if (!s.lazy.initialImageLoaded) s.lazy.initialImageLoaded = true;\n                if (s.params.watchSlidesVisibility) {\n                    s.wrapper.children('.' + s.params.slideVisibleClass).each(function () {\n                        s.lazy.loadImageInSlide($(this).index());\n                    });\n                }\n                else {\n                    if (slidesPerView > 1) {\n                        for (i = s.activeIndex; i < s.activeIndex + slidesPerView ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        s.lazy.loadImageInSlide(s.activeIndex);\n                    }\n                }\n                if (s.params.lazyLoadingInPrevNext) {\n                    if (slidesPerView > 1 || (s.params.lazyLoadingInPrevNextAmount && s.params.lazyLoadingInPrevNextAmount > 1)) {\n                        var amount = s.params.lazyLoadingInPrevNextAmount;\n                        var spv = slidesPerView;\n                        var maxIndex = Math.min(s.activeIndex + spv + Math.max(amount, spv), s.slides.length);\n                        var minIndex = Math.max(s.activeIndex - Math.max(spv, amount), 0);\n                        // Next Slides\n                        for (i = s.activeIndex + slidesPerView; i < maxIndex; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                        // Prev Slides\n                        for (i = minIndex; i < s.activeIndex ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        var nextSlide = s.wrapper.children('.' + s.params.slideNextClass);\n                        if (nextSlide.length > 0) s.lazy.loadImageInSlide(nextSlide.index());\n        \n                        var prevSlide = s.wrapper.children('.' + s.params.slidePrevClass);\n                        if (prevSlide.length > 0) s.lazy.loadImageInSlide(prevSlide.index());\n                    }\n                }\n            },\n            onTransitionStart: function () {\n                if (s.params.lazyLoading) {\n                    if (s.params.lazyLoadingOnTransitionStart || (!s.params.lazyLoadingOnTransitionStart && !s.lazy.initialImageLoaded)) {\n                        s.lazy.load();\n                    }\n                }\n            },\n            onTransitionEnd: function () {\n                if (s.params.lazyLoading && !s.params.lazyLoadingOnTransitionStart) {\n                    s.lazy.load();\n                }\n            }\n        };\n        \n\n        /*=========================\n          Scrollbar\n          ===========================*/\n        s.scrollbar = {\n            isTouched: false,\n            setDragPosition: function (e) {\n                var sb = s.scrollbar;\n                var x = 0, y = 0;\n                var translate;\n                var pointerPosition = s.isHorizontal() ?\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX) :\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY) ;\n                var position = (pointerPosition) - sb.track.offset()[s.isHorizontal() ? 'left' : 'top'] - sb.dragSize / 2;\n                var positionMin = -s.minTranslate() * sb.moveDivider;\n                var positionMax = -s.maxTranslate() * sb.moveDivider;\n                if (position < positionMin) {\n                    position = positionMin;\n                }\n                else if (position > positionMax) {\n                    position = positionMax;\n                }\n                position = -position / sb.moveDivider;\n                s.updateProgress(position);\n                s.setWrapperTranslate(position, true);\n            },\n            dragStart: function (e) {\n                var sb = s.scrollbar;\n                sb.isTouched = true;\n                e.preventDefault();\n                e.stopPropagation();\n        \n                sb.setDragPosition(e);\n                clearTimeout(sb.dragTimeout);\n        \n                sb.track.transition(0);\n                if (s.params.scrollbarHide) {\n                    sb.track.css('opacity', 1);\n                }\n                s.wrapper.transition(100);\n                sb.drag.transition(100);\n                s.emit('onScrollbarDragStart', s);\n            },\n            dragMove: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                if (e.preventDefault) e.preventDefault();\n                else e.returnValue = false;\n                sb.setDragPosition(e);\n                s.wrapper.transition(0);\n                sb.track.transition(0);\n                sb.drag.transition(0);\n                s.emit('onScrollbarDragMove', s);\n            },\n            dragEnd: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                sb.isTouched = false;\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.dragTimeout);\n                    sb.dragTimeout = setTimeout(function () {\n                        sb.track.css('opacity', 0);\n                        sb.track.transition(400);\n                    }, 1000);\n        \n                }\n                s.emit('onScrollbarDragEnd', s);\n                if (s.params.scrollbarSnapOnRelease) {\n                    s.slideReset();\n                }\n            },\n            draggableEvents: (function () {\n                if ((s.params.simulateTouch === false && !s.support.touch)) return s.touchEventsDesktop;\n                else return s.touchEvents;\n            })(),\n            enableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).on(sb.draggableEvents.start, sb.dragStart);\n                $(target).on(sb.draggableEvents.move, sb.dragMove);\n                $(target).on(sb.draggableEvents.end, sb.dragEnd);\n            },\n            disableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).off(sb.draggableEvents.start, sb.dragStart);\n                $(target).off(sb.draggableEvents.move, sb.dragMove);\n                $(target).off(sb.draggableEvents.end, sb.dragEnd);\n            },\n            set: function () {\n                if (!s.params.scrollbar) return;\n                var sb = s.scrollbar;\n                sb.track = $(s.params.scrollbar);\n                if (s.params.uniqueNavElements && typeof s.params.scrollbar === 'string' && sb.track.length > 1 && s.container.find(s.params.scrollbar).length === 1) {\n                    sb.track = s.container.find(s.params.scrollbar);\n                }\n                sb.drag = sb.track.find('.swiper-scrollbar-drag');\n                if (sb.drag.length === 0) {\n                    sb.drag = $('<div class=\"swiper-scrollbar-drag\"></div>');\n                    sb.track.append(sb.drag);\n                }\n                sb.drag[0].style.width = '';\n                sb.drag[0].style.height = '';\n                sb.trackSize = s.isHorizontal() ? sb.track[0].offsetWidth : sb.track[0].offsetHeight;\n        \n                sb.divider = s.size / s.virtualSize;\n                sb.moveDivider = sb.divider * (sb.trackSize / s.size);\n                sb.dragSize = sb.trackSize * sb.divider;\n        \n                if (s.isHorizontal()) {\n                    sb.drag[0].style.width = sb.dragSize + 'px';\n                }\n                else {\n                    sb.drag[0].style.height = sb.dragSize + 'px';\n                }\n        \n                if (sb.divider >= 1) {\n                    sb.track[0].style.display = 'none';\n                }\n                else {\n                    sb.track[0].style.display = '';\n                }\n                if (s.params.scrollbarHide) {\n                    sb.track[0].style.opacity = 0;\n                }\n            },\n            setTranslate: function () {\n                if (!s.params.scrollbar) return;\n                var diff;\n                var sb = s.scrollbar;\n                var translate = s.translate || 0;\n                var newPos;\n        \n                var newSize = sb.dragSize;\n                newPos = (sb.trackSize - sb.dragSize) * s.progress;\n                if (s.rtl && s.isHorizontal()) {\n                    newPos = -newPos;\n                    if (newPos > 0) {\n                        newSize = sb.dragSize - newPos;\n                        newPos = 0;\n                    }\n                    else if (-newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize + newPos;\n                    }\n                }\n                else {\n                    if (newPos < 0) {\n                        newSize = sb.dragSize + newPos;\n                        newPos = 0;\n                    }\n                    else if (newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize - newPos;\n                    }\n                }\n                if (s.isHorizontal()) {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(' + (newPos) + 'px, 0, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateX(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.width = newSize + 'px';\n                }\n                else {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(0px, ' + (newPos) + 'px, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateY(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.height = newSize + 'px';\n                }\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.timeout);\n                    sb.track[0].style.opacity = 1;\n                    sb.timeout = setTimeout(function () {\n                        sb.track[0].style.opacity = 0;\n                        sb.track.transition(400);\n                    }, 1000);\n                }\n            },\n            setTransition: function (duration) {\n                if (!s.params.scrollbar) return;\n                s.scrollbar.drag.transition(duration);\n            }\n        };\n\n        /*=========================\n          Controller\n          ===========================*/\n        s.controller = {\n            LinearSpline: function (x, y) {\n                this.x = x;\n                this.y = y;\n                this.lastIndex = x.length - 1;\n                // Given an x value (x2), return the expected y2 value:\n                // (x1,y1) is the known point before given value,\n                // (x3,y3) is the known point after given value.\n                var i1, i3;\n                var l = this.x.length;\n        \n                this.interpolate = function (x2) {\n                    if (!x2) return 0;\n        \n                    // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n                    i3 = binarySearch(this.x, x2);\n                    i1 = i3 - 1;\n        \n                    // We have our indexes i1 & i3, so we can calculate already:\n                    // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n                    return ((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1]) + this.y[i1];\n                };\n        \n                var binarySearch = (function() {\n                    var maxIndex, minIndex, guess;\n                    return function(array, val) {\n                        minIndex = -1;\n                        maxIndex = array.length;\n                        while (maxIndex - minIndex > 1)\n                            if (array[guess = maxIndex + minIndex >> 1] <= val) {\n                                minIndex = guess;\n                            } else {\n                                maxIndex = guess;\n                            }\n                        return maxIndex;\n                    };\n                })();\n            },\n            //xxx: for now i will just save one spline function to to\n            getInterpolateFunction: function(c){\n                if(!s.controller.spline) s.controller.spline = s.params.loop ?\n                    new s.controller.LinearSpline(s.slidesGrid, c.slidesGrid) :\n                    new s.controller.LinearSpline(s.snapGrid, c.snapGrid);\n            },\n            setTranslate: function (translate, byController) {\n               var controlled = s.params.control;\n               var multiplier, controlledTranslate;\n               function setControlledTranslate(c) {\n                    // this will create an Interpolate function based on the snapGrids\n                    // x is the Grid of the scrolled scroller and y will be the controlled scroller\n                    // it makes sense to create this only once and recall it for the interpolation\n                    // the function does a lot of value caching for performance\n                    translate = c.rtl && c.params.direction === 'horizontal' ? -s.translate : s.translate;\n                    if (s.params.controlBy === 'slide') {\n                        s.controller.getInterpolateFunction(c);\n                        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n                        // but it did not work out\n                        controlledTranslate = -s.controller.spline.interpolate(-translate);\n                    }\n        \n                    if(!controlledTranslate || s.params.controlBy === 'container'){\n                        multiplier = (c.maxTranslate() - c.minTranslate()) / (s.maxTranslate() - s.minTranslate());\n                        controlledTranslate = (translate - s.minTranslate()) * multiplier + c.minTranslate();\n                    }\n        \n                    if (s.params.controlInverse) {\n                        controlledTranslate = c.maxTranslate() - controlledTranslate;\n                    }\n                    c.updateProgress(controlledTranslate);\n                    c.setWrapperTranslate(controlledTranslate, false, s);\n                    c.updateActiveIndex();\n               }\n               if (s.isArray(controlled)) {\n                   for (var i = 0; i < controlled.length; i++) {\n                       if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                           setControlledTranslate(controlled[i]);\n                       }\n                   }\n               }\n               else if (controlled instanceof Swiper && byController !== controlled) {\n        \n                   setControlledTranslate(controlled);\n               }\n            },\n            setTransition: function (duration, byController) {\n                var controlled = s.params.control;\n                var i;\n                function setControlledTransition(c) {\n                    c.setWrapperTransition(duration, s);\n                    if (duration !== 0) {\n                        c.onTransitionStart();\n                        c.wrapper.transitionEnd(function(){\n                            if (!controlled) return;\n                            if (c.params.loop && s.params.controlBy === 'slide') {\n                                c.fixLoop();\n                            }\n                            c.onTransitionEnd();\n        \n                        });\n                    }\n                }\n                if (s.isArray(controlled)) {\n                    for (i = 0; i < controlled.length; i++) {\n                        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                            setControlledTransition(controlled[i]);\n                        }\n                    }\n                }\n                else if (controlled instanceof Swiper && byController !== controlled) {\n                    setControlledTransition(controlled);\n                }\n            }\n        };\n\n        /*=========================\n          Hash Navigation\n          ===========================*/\n        s.hashnav = {\n            onHashCange: function (e, a) {\n                var newHash = document.location.hash.replace('#', '');\n                var activeSlideHash = s.slides.eq(s.activeIndex).attr('data-hash');\n                if (newHash !== activeSlideHash) {\n                    s.slideTo(s.wrapper.children('.' + s.params.slideClass + '[data-hash=\"' + (newHash) + '\"]').index());\n                }\n            },\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n                $(window)[action]('hashchange', s.hashnav.onHashCange);\n            },\n            setHash: function () {\n                if (!s.hashnav.initialized || !s.params.hashnav) return;\n                if (s.params.replaceState && window.history && window.history.replaceState) {\n                    window.history.replaceState(null, null, ('#' + s.slides.eq(s.activeIndex).attr('data-hash') || ''));\n                } else {\n                    var slide = s.slides.eq(s.activeIndex);\n                    var hash = slide.attr('data-hash') || slide.attr('data-history');\n                    document.location.hash = hash || '';\n                }\n            },\n            init: function () {\n                if (!s.params.hashnav || s.params.history) return;\n                s.hashnav.initialized = true;\n                var hash = document.location.hash.replace('#', '');\n                if (hash) {\n                    var speed = 0;\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n                        if (slideHash === hash && !slide.hasClass(s.params.slideDuplicateClass)) {\n                            var index = slide.index();\n                            s.slideTo(index, speed, s.params.runCallbacksOnInit, true);\n                        }\n                    }\n                }\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents();\n            },\n            destroy: function () {\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          History Api with fallback to Hashnav\n          ===========================*/\n        s.history = {\n            init: function () {\n                if (!s.params.history) return;\n                if (!window.history || !window.history.pushState) {\n                    s.params.history = false;\n                    s.params.hashnav = true;\n                    return;\n                }\n                s.history.initialized = true;\n                this.paths = this.getPathValues();\n                if (!this.paths.key && !this.paths.value) return;\n                this.scrollToSlide(0, this.paths.value, s.params.runCallbacksOnInit);\n                if (!s.params.replaceState) {\n                    window.addEventListener('popstate', this.setHistoryPopState);\n                }\n            },\n            setHistoryPopState: function() {\n                s.history.paths = s.history.getPathValues();\n                s.history.scrollToSlide(s.params.speed, s.history.paths.value, false);\n            },\n            getPathValues: function() {\n                var pathArray = window.location.pathname.slice(1).split('/');\n                var total = pathArray.length;\n                var key = pathArray[total - 2];\n                var value = pathArray[total - 1];\n                return { key: key, value: value };\n            },\n            setHistory: function (key, index) {\n                if (!s.history.initialized || !s.params.history) return;\n                var slide = s.slides.eq(index);\n                var value = this.slugify(slide.attr('data-history'));\n                if (!window.location.pathname.includes(key)) {\n                    value = key + '/' + value;\n                }\n                if (s.params.replaceState) {\n                    window.history.replaceState(null, null, value);\n                } else {\n                    window.history.pushState(null, null, value);\n                }\n            },\n            slugify: function(text) {\n                return text.toString().toLowerCase()\n                    .replace(/\\s+/g, '-')\n                    .replace(/[^\\w\\-]+/g, '')\n                    .replace(/\\-\\-+/g, '-')\n                    .replace(/^-+/, '')\n                    .replace(/-+$/, '');\n            },\n            scrollToSlide: function(speed, value, runCallbacks) {\n                if (value) {\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideHistory = this.slugify(slide.attr('data-history'));\n                        if (slideHistory === value && !slide.hasClass(s.params.slideDuplicateClass)) {\n                            var index = slide.index();\n                            s.slideTo(index, speed, runCallbacks);\n                        }\n                    }\n                } else {\n                    s.slideTo(0, speed, runCallbacks);\n                }\n            }\n        };\n\n        /*=========================\n          Keyboard Control\n          ===========================*/\n        function handleKeyboard(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var kc = e.keyCode || e.charCode;\n            // Directions locks\n            if (!s.params.allowSwipeToNext && (s.isHorizontal() && kc === 39 || !s.isHorizontal() && kc === 40)) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && (s.isHorizontal() && kc === 37 || !s.isHorizontal() && kc === 38)) {\n                return false;\n            }\n            if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n                return;\n            }\n            if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n                return;\n            }\n            if (kc === 37 || kc === 39 || kc === 38 || kc === 40) {\n                var inView = false;\n                //Check that swiper should be inside of visible area of window\n                if (s.container.parents('.' + s.params.slideClass).length > 0 && s.container.parents('.' + s.params.slideActiveClass).length === 0) {\n                    return;\n                }\n                var windowScroll = {\n                    left: window.pageXOffset,\n                    top: window.pageYOffset\n                };\n                var windowWidth = window.innerWidth;\n                var windowHeight = window.innerHeight;\n                var swiperOffset = s.container.offset();\n                if (s.rtl) swiperOffset.left = swiperOffset.left - s.container[0].scrollLeft;\n                var swiperCoord = [\n                    [swiperOffset.left, swiperOffset.top],\n                    [swiperOffset.left + s.width, swiperOffset.top],\n                    [swiperOffset.left, swiperOffset.top + s.height],\n                    [swiperOffset.left + s.width, swiperOffset.top + s.height]\n                ];\n                for (var i = 0; i < swiperCoord.length; i++) {\n                    var point = swiperCoord[i];\n                    if (\n                        point[0] >= windowScroll.left && point[0] <= windowScroll.left + windowWidth &&\n                        point[1] >= windowScroll.top && point[1] <= windowScroll.top + windowHeight\n                    ) {\n                        inView = true;\n                    }\n        \n                }\n                if (!inView) return;\n            }\n            if (s.isHorizontal()) {\n                if (kc === 37 || kc === 39) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if ((kc === 39 && !s.rtl) || (kc === 37 && s.rtl)) s.slideNext();\n                if ((kc === 37 && !s.rtl) || (kc === 39 && s.rtl)) s.slidePrev();\n            }\n            else {\n                if (kc === 38 || kc === 40) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if (kc === 40) s.slideNext();\n                if (kc === 38) s.slidePrev();\n            }\n        }\n        s.disableKeyboardControl = function () {\n            s.params.keyboardControl = false;\n            $(document).off('keydown', handleKeyboard);\n        };\n        s.enableKeyboardControl = function () {\n            s.params.keyboardControl = true;\n            $(document).on('keydown', handleKeyboard);\n        };\n        \n\n        /*=========================\n          Mousewheel Control\n          ===========================*/\n        s.mousewheel = {\n            event: false,\n            lastScrollTime: (new window.Date()).getTime()\n        };\n        if (s.params.mousewheelControl) {\n            /**\n             * The best combination if you prefer spinX + spinY normalization.  It favors\n             * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n             * 'wheel' event, making spin speed determination impossible.\n             */\n            s.mousewheel.event = (navigator.userAgent.indexOf('firefox') > -1) ?\n                'DOMMouseScroll' :\n                isEventSupported() ?\n                    'wheel' : 'mousewheel';\n        }\n        \n        function isEventSupported() {\n            var eventName = 'onwheel';\n            var isSupported = eventName in document;\n        \n            if (!isSupported) {\n                var element = document.createElement('div');\n                element.setAttribute(eventName, 'return;');\n                isSupported = typeof element[eventName] === 'function';\n            }\n        \n            if (!isSupported &&\n                document.implementation &&\n                document.implementation.hasFeature &&\n                    // always returns true in newer browsers as per the standard.\n                    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n                document.implementation.hasFeature('', '') !== true ) {\n                // This is the only way to test support for the `wheel` event in IE9+.\n                isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n            }\n        \n            return isSupported;\n        }\n        \n        function handleMousewheel(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var delta = 0;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            var data = normalizeWheel( e );\n        \n            if (s.params.mousewheelForceToAxis) {\n                if (s.isHorizontal()) {\n                    if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = data.pixelX * rtlFactor;\n                    else return;\n                }\n                else {\n                    if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = data.pixelY;\n                    else return;\n                }\n            }\n            else {\n                delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? - data.pixelX * rtlFactor : - data.pixelY;\n            }\n        \n            if (delta === 0) return;\n        \n            if (s.params.mousewheelInvert) delta = -delta;\n        \n            if (!s.params.freeMode) {\n                if ((new window.Date()).getTime() - s.mousewheel.lastScrollTime > 60) {\n                    if (delta < 0) {\n                        if ((!s.isEnd || s.params.loop) && !s.animating) {\n                            s.slideNext();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                    else {\n                        if ((!s.isBeginning || s.params.loop) && !s.animating) {\n                            s.slidePrev();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                }\n                s.mousewheel.lastScrollTime = (new window.Date()).getTime();\n        \n            }\n            else {\n                //Freemode or scrollContainer:\n                var position = s.getWrapperTranslate() + delta * s.params.mousewheelSensitivity;\n                var wasBeginning = s.isBeginning,\n                    wasEnd = s.isEnd;\n        \n                if (position >= s.minTranslate()) position = s.minTranslate();\n                if (position <= s.maxTranslate()) position = s.maxTranslate();\n        \n                s.setWrapperTransition(0);\n                s.setWrapperTranslate(position);\n                s.updateProgress();\n                s.updateActiveIndex();\n        \n                if (!wasBeginning && s.isBeginning || !wasEnd && s.isEnd) {\n                    s.updateClasses();\n                }\n        \n                if (s.params.freeModeSticky) {\n                    clearTimeout(s.mousewheel.timeout);\n                    s.mousewheel.timeout = setTimeout(function () {\n                        s.slideReset();\n                    }, 300);\n                }\n                else {\n                    if (s.params.lazyLoading && s.lazy) {\n                        s.lazy.load();\n                    }\n                }\n                // Emit event\n                s.emit('onScroll', s, e);\n        \n                // Stop autoplay\n                if (s.params.autoplay && s.params.autoplayDisableOnInteraction) s.stopAutoplay();\n        \n                // Return page scroll on edge positions\n                if (position === 0 || position === s.maxTranslate()) return;\n            }\n        \n            if (e.preventDefault) e.preventDefault();\n            else e.returnValue = false;\n            return false;\n        }\n        s.disableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.off(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        s.enableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.on(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        /**\n         * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n         * complicated, thus this doc is long and (hopefully) detailed enough to answer\n         * your questions.\n         *\n         * If you need to react to the mouse wheel in a predictable way, this code is\n         * like your bestest friend. * hugs *\n         *\n         * As of today, there are 4 DOM event types you can listen to:\n         *\n         *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n         *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n         *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n         *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n         *\n         * So what to do?  The is the best:\n         *\n         *   normalizeWheel.getEventType();\n         *\n         * In your event callback, use this code to get sane interpretation of the\n         * deltas.  This code will return an object with properties:\n         *\n         *   spinX   -- normalized spin speed (use for zoom) - x plane\n         *   spinY   -- \" - y plane\n         *   pixelX  -- normalized distance (to pixels) - x plane\n         *   pixelY  -- \" - y plane\n         *\n         * Wheel values are provided by the browser assuming you are using the wheel to\n         * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n         * significantly on different platforms and browsers, forgetting that you can\n         * scroll at different speeds.  Some devices (like trackpads) emit more events\n         * at smaller increments with fine granularity, and some emit massive jumps with\n         * linear speed or acceleration.\n         *\n         * This code does its best to normalize the deltas for you:\n         *\n         *   - spin is trying to normalize how far the wheel was spun (or trackpad\n         *     dragged).  This is super useful for zoom support where you want to\n         *     throw away the chunky scroll steps on the PC and make those equal to\n         *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n         *     resolve a single slow step on a wheel to 1.\n         *\n         *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n         *     get the crazy differences between browsers, but at least it'll be in\n         *     pixels!\n         *\n         *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n         *     should translate to positive value zooming IN, negative zooming OUT.\n         *     This matches the newer 'wheel' event.\n         *\n         * Why are there spinX, spinY (or pixels)?\n         *\n         *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n         *     with a mouse.  It results in side-scrolling in the browser by default.\n         *\n         *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n         *\n         *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n         *     probably is by browsers in conjunction with fancy 3D controllers .. but\n         *     you know.\n         *\n         * Implementation info:\n         *\n         * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n         * average mouse:\n         *\n         *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n         *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n         *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n         *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n         *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n         *\n         * On the trackpad:\n         *\n         *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n         *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n         *\n         * On other/older browsers.. it's more complicated as there can be multiple and\n         * also missing delta values.\n         *\n         * The 'wheel' event is more standard:\n         *\n         * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n         *\n         * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n         * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n         * backward compatibility with older events.  Those other values help us\n         * better normalize spin speed.  Example of what the browsers provide:\n         *\n         *                          | event.wheelDelta | event.detail\n         *        ------------------+------------------+--------------\n         *          Safari v5/OS X  |       -120       |       0\n         *          Safari v5/Win7  |       -120       |       0\n         *         Chrome v17/OS X  |       -120       |       0\n         *         Chrome v17/Win7  |       -120       |       0\n         *                IE9/Win7  |       -120       |   undefined\n         *         Firefox v4/OS X  |     undefined    |       1\n         *         Firefox v4/Win7  |     undefined    |       3\n         *\n         */\n        function normalizeWheel( /*object*/ event ) /*object*/ {\n            // Reasonable defaults\n            var PIXEL_STEP = 10;\n            var LINE_HEIGHT = 40;\n            var PAGE_HEIGHT = 800;\n        \n            var sX = 0, sY = 0,       // spinX, spinY\n                pX = 0, pY = 0;       // pixelX, pixelY\n        \n            // Legacy\n            if( 'detail' in event ) {\n                sY = event.detail;\n            }\n            if( 'wheelDelta' in event ) {\n                sY = -event.wheelDelta / 120;\n            }\n            if( 'wheelDeltaY' in event ) {\n                sY = -event.wheelDeltaY / 120;\n            }\n            if( 'wheelDeltaX' in event ) {\n                sX = -event.wheelDeltaX / 120;\n            }\n        \n            // side scrolling on FF with DOMMouseScroll\n            if( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n                sX = sY;\n                sY = 0;\n            }\n        \n            pX = sX * PIXEL_STEP;\n            pY = sY * PIXEL_STEP;\n        \n            if( 'deltaY' in event ) {\n                pY = event.deltaY;\n            }\n            if( 'deltaX' in event ) {\n                pX = event.deltaX;\n            }\n        \n            if( (pX || pY) && event.deltaMode ) {\n                if( event.deltaMode === 1 ) {          // delta in LINE units\n                    pX *= LINE_HEIGHT;\n                    pY *= LINE_HEIGHT;\n                } else {                             // delta in PAGE units\n                    pX *= PAGE_HEIGHT;\n                    pY *= PAGE_HEIGHT;\n                }\n            }\n        \n            // Fall-back if spin cannot be determined\n            if( pX && !sX ) {\n                sX = (pX < 1) ? -1 : 1;\n            }\n            if( pY && !sY ) {\n                sY = (pY < 1) ? -1 : 1;\n            }\n        \n            return {\n                spinX: sX,\n                spinY: sY,\n                pixelX: pX,\n                pixelY: pY\n            };\n        }\n\n        /*=========================\n          Parallax\n          ===========================*/\n        function setParallaxTransform(el, progress) {\n            el = $(el);\n            var p, pX, pY;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            p = el.attr('data-swiper-parallax') || '0';\n            pX = el.attr('data-swiper-parallax-x');\n            pY = el.attr('data-swiper-parallax-y');\n            if (pX || pY) {\n                pX = pX || '0';\n                pY = pY || '0';\n            }\n            else {\n                if (s.isHorizontal()) {\n                    pX = p;\n                    pY = '0';\n                }\n                else {\n                    pY = p;\n                    pX = '0';\n                }\n            }\n        \n            if ((pX).indexOf('%') >= 0) {\n                pX = parseInt(pX, 10) * progress * rtlFactor + '%';\n            }\n            else {\n                pX = pX * progress * rtlFactor + 'px' ;\n            }\n            if ((pY).indexOf('%') >= 0) {\n                pY = parseInt(pY, 10) * progress + '%';\n            }\n            else {\n                pY = pY * progress + 'px' ;\n            }\n        \n            el.transform('translate3d(' + pX + ', ' + pY + ',0px)');\n        }\n        s.parallax = {\n            setTranslate: function () {\n                s.container.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    setParallaxTransform(this, s.progress);\n        \n                });\n                s.slides.each(function () {\n                    var slide = $(this);\n                    slide.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function () {\n                        var progress = Math.min(Math.max(slide[0].progress, -1), 1);\n                        setParallaxTransform(this, progress);\n                    });\n                });\n            },\n            setTransition: function (duration) {\n                if (typeof duration === 'undefined') duration = s.params.speed;\n                s.container.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    var el = $(this);\n                    var parallaxDuration = parseInt(el.attr('data-swiper-parallax-duration'), 10) || duration;\n                    if (duration === 0) parallaxDuration = 0;\n                    el.transition(parallaxDuration);\n                });\n            }\n        };\n        \n\n        /*=========================\n          Zoom\n          ===========================*/\n        s.zoom = {\n            // \"Global\" Props\n            scale: 1,\n            currentScale: 1,\n            isScaling: false,\n            gesture: {\n                slide: undefined,\n                slideWidth: undefined,\n                slideHeight: undefined,\n                image: undefined,\n                imageWrap: undefined,\n                zoomMax: s.params.zoomMax\n            },\n            image: {\n                isTouched: undefined,\n                isMoved: undefined,\n                currentX: undefined,\n                currentY: undefined,\n                minX: undefined,\n                minY: undefined,\n                maxX: undefined,\n                maxY: undefined,\n                width: undefined,\n                height: undefined,\n                startX: undefined,\n                startY: undefined,\n                touchesStart: {},\n                touchesCurrent: {}\n            },\n            velocity: {\n                x: undefined,\n                y: undefined,\n                prevPositionX: undefined,\n                prevPositionY: undefined,\n                prevTime: undefined\n            },\n            // Calc Scale From Multi-touches\n            getDistanceBetweenTouches: function (e) {\n                if (e.targetTouches.length < 2) return 1;\n                var x1 = e.targetTouches[0].pageX,\n                    y1 = e.targetTouches[0].pageY,\n                    x2 = e.targetTouches[1].pageX,\n                    y2 = e.targetTouches[1].pageY;\n                var distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));\n                return distance;\n            },\n            // Events\n            onGestureStart: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchstart' || e.type === 'touchstart' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleStart = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.slide || !z.gesture.slide.length) {\n                    z.gesture.slide = $(this);\n                    if (z.gesture.slide.length === 0) z.gesture.slide = s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                    z.gesture.zoomMax = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax ;\n                    if (z.gesture.imageWrap.length === 0) {\n                        z.gesture.image = undefined;\n                        return;\n                    }\n                }\n                z.gesture.image.transition(0);\n                z.isScaling = true;\n            },\n            onGestureChange: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchmove' || e.type === 'touchmove' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleMove = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (s.support.gestures) {\n                    z.scale = e.scale * z.currentScale;\n                }\n                else {\n                    z.scale = (z.gesture.scaleMove / z.gesture.scaleStart) * z.currentScale;\n                }\n                if (z.scale > z.gesture.zoomMax) {\n                    z.scale = z.gesture.zoomMax - 1 + Math.pow((z.scale - z.gesture.zoomMax + 1), 0.5);\n                }\n                if (z.scale < s.params.zoomMin) {\n                    z.scale =  s.params.zoomMin + 1 - Math.pow((s.params.zoomMin - z.scale + 1), 0.5);\n                }\n                z.gesture.image.transform('translate3d(0,0,0) scale(' + z.scale + ')');\n            },\n            onGestureEnd: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchend' || e.type === 'touchend' && e.changedTouches.length < 2) {\n                        return;\n                    }\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                z.scale = Math.max(Math.min(z.scale, z.gesture.zoomMax), s.params.zoomMin);\n                z.gesture.image.transition(s.params.speed).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                z.currentScale = z.scale;\n                z.isScaling = false;\n                if (z.scale === 1) z.gesture.slide = undefined;\n            },\n            onTouchStart: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (z.image.isTouched) return;\n                if (s.device.os === 'android') e.preventDefault();\n                z.image.isTouched = true;\n                z.image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n            },\n            onTouchMove: function (e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                s.allowClick = false;\n                if (!z.image.isTouched || !z.gesture.slide) return;\n        \n                if (!z.image.isMoved) {\n                    z.image.width = z.gesture.image[0].offsetWidth;\n                    z.image.height = z.gesture.image[0].offsetHeight;\n                    z.image.startX = s.getTranslate(z.gesture.imageWrap[0], 'x') || 0;\n                    z.image.startY = s.getTranslate(z.gesture.imageWrap[0], 'y') || 0;\n                    z.gesture.slideWidth = z.gesture.slide[0].offsetWidth;\n                    z.gesture.slideHeight = z.gesture.slide[0].offsetHeight;\n                    z.gesture.imageWrap.transition(0);\n                    if (s.rtl) z.image.startX = -z.image.startX;\n                    if (s.rtl) z.image.startY = -z.image.startY;\n                }\n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n        \n                if (scaledWidth < z.gesture.slideWidth && scaledHeight < z.gesture.slideHeight) return;\n        \n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n        \n                z.image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n                if (!z.image.isMoved && !z.isScaling) {\n                    if (s.isHorizontal() &&\n                        (Math.floor(z.image.minX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x < z.image.touchesStart.x) ||\n                        (Math.floor(z.image.maxX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x > z.image.touchesStart.x)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                    else if (!s.isHorizontal() &&\n                        (Math.floor(z.image.minY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y < z.image.touchesStart.y) ||\n                        (Math.floor(z.image.maxY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y > z.image.touchesStart.y)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                }\n                e.preventDefault();\n                e.stopPropagation();\n        \n                z.image.isMoved = true;\n                z.image.currentX = z.image.touchesCurrent.x - z.image.touchesStart.x + z.image.startX;\n                z.image.currentY = z.image.touchesCurrent.y - z.image.touchesStart.y + z.image.startY;\n        \n                if (z.image.currentX < z.image.minX) {\n                    z.image.currentX =  z.image.minX + 1 - Math.pow((z.image.minX - z.image.currentX + 1), 0.8);\n                }\n                if (z.image.currentX > z.image.maxX) {\n                    z.image.currentX = z.image.maxX - 1 + Math.pow((z.image.currentX - z.image.maxX + 1), 0.8);\n                }\n        \n                if (z.image.currentY < z.image.minY) {\n                    z.image.currentY =  z.image.minY + 1 - Math.pow((z.image.minY - z.image.currentY + 1), 0.8);\n                }\n                if (z.image.currentY > z.image.maxY) {\n                    z.image.currentY = z.image.maxY - 1 + Math.pow((z.image.currentY - z.image.maxY + 1), 0.8);\n                }\n        \n                //Velocity\n                if (!z.velocity.prevPositionX) z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                if (!z.velocity.prevPositionY) z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                if (!z.velocity.prevTime) z.velocity.prevTime = Date.now();\n                z.velocity.x = (z.image.touchesCurrent.x - z.velocity.prevPositionX) / (Date.now() - z.velocity.prevTime) / 2;\n                z.velocity.y = (z.image.touchesCurrent.y - z.velocity.prevPositionY) / (Date.now() - z.velocity.prevTime) / 2;\n                if (Math.abs(z.image.touchesCurrent.x - z.velocity.prevPositionX) < 2) z.velocity.x = 0;\n                if (Math.abs(z.image.touchesCurrent.y - z.velocity.prevPositionY) < 2) z.velocity.y = 0;\n                z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                z.velocity.prevTime = Date.now();\n        \n                z.gesture.imageWrap.transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTouchEnd: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (!z.image.isTouched || !z.image.isMoved) {\n                    z.image.isTouched = false;\n                    z.image.isMoved = false;\n                    return;\n                }\n                z.image.isTouched = false;\n                z.image.isMoved = false;\n                var momentumDurationX = 300;\n                var momentumDurationY = 300;\n                var momentumDistanceX = z.velocity.x * momentumDurationX;\n                var newPositionX = z.image.currentX + momentumDistanceX;\n                var momentumDistanceY = z.velocity.y * momentumDurationY;\n                var newPositionY = z.image.currentY + momentumDistanceY;\n        \n                //Fix duration\n                if (z.velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - z.image.currentX) / z.velocity.x);\n                if (z.velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - z.image.currentY) / z.velocity.y);\n                var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n        \n                z.image.currentX = newPositionX;\n                z.image.currentY = newPositionY;\n        \n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n                z.image.currentX = Math.max(Math.min(z.image.currentX, z.image.maxX), z.image.minX);\n                z.image.currentY = Math.max(Math.min(z.image.currentY, z.image.maxY), z.image.minY);\n        \n                z.gesture.imageWrap.transition(momentumDuration).transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTransitionEnd: function (s) {\n                var z = s.zoom;\n                if (z.gesture.slide && s.previousIndex !== s.activeIndex) {\n                    z.gesture.image.transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.imageWrap.transform('translate3d(0,0,0)');\n                    z.gesture.slide = z.gesture.image = z.gesture.imageWrap = undefined;\n                    z.scale = z.currentScale = 1;\n                }\n            },\n            // Toggle Zoom\n            toggleZoom: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.slide) {\n                    z.gesture.slide = s.clickedSlide ? $(s.clickedSlide) : s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n        \n                var touchX, touchY, offsetX, offsetY, diffX, diffY, translateX, translateY, imageWidth, imageHeight, scaledWidth, scaledHeight, translateMinX, translateMinY, translateMaxX, translateMaxY, slideWidth, slideHeight;\n        \n                if (typeof z.image.touchesStart.x === 'undefined' && e) {\n                    touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n                    touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n                }\n                else {\n                    touchX = z.image.touchesStart.x;\n                    touchY = z.image.touchesStart.y;\n                }\n        \n                if (z.scale && z.scale !== 1) {\n                    // Zoom Out\n                    z.scale = z.currentScale = 1;\n                    z.gesture.imageWrap.transition(300).transform('translate3d(0,0,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.slide = undefined;\n                }\n                else {\n                    // Zoom In\n                    z.scale = z.currentScale = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax;\n                    if (e) {\n                        slideWidth = z.gesture.slide[0].offsetWidth;\n                        slideHeight = z.gesture.slide[0].offsetHeight;\n                        offsetX = z.gesture.slide.offset().left;\n                        offsetY = z.gesture.slide.offset().top;\n                        diffX = offsetX + slideWidth/2 - touchX;\n                        diffY = offsetY + slideHeight/2 - touchY;\n        \n                        imageWidth = z.gesture.image[0].offsetWidth;\n                        imageHeight = z.gesture.image[0].offsetHeight;\n                        scaledWidth = imageWidth * z.scale;\n                        scaledHeight = imageHeight * z.scale;\n        \n                        translateMinX = Math.min((slideWidth / 2 - scaledWidth / 2), 0);\n                        translateMinY = Math.min((slideHeight / 2 - scaledHeight / 2), 0);\n                        translateMaxX = -translateMinX;\n                        translateMaxY = -translateMinY;\n        \n                        translateX = diffX * z.scale;\n                        translateY = diffY * z.scale;\n        \n                        if (translateX < translateMinX) {\n                            translateX =  translateMinX;\n                        }\n                        if (translateX > translateMaxX) {\n                            translateX = translateMaxX;\n                        }\n        \n                        if (translateY < translateMinY) {\n                            translateY =  translateMinY;\n                        }\n                        if (translateY > translateMaxY) {\n                            translateY = translateMaxY;\n                        }\n                    }\n                    else {\n                        translateX = 0;\n                        translateY = 0;\n                    }\n                    z.gesture.imageWrap.transition(300).transform('translate3d(' + translateX + 'px, ' + translateY + 'px,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                }\n            },\n            // Attach/Detach Events\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n        \n                if (s.params.zoom) {\n                    var target = s.slides;\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    // Scale image\n                    if (s.support.gestures) {\n                        s.slides[action]('gesturestart', s.zoom.onGestureStart, passiveListener);\n                        s.slides[action]('gesturechange', s.zoom.onGestureChange, passiveListener);\n                        s.slides[action]('gestureend', s.zoom.onGestureEnd, passiveListener);\n                    }\n                    else if (s.touchEvents.start === 'touchstart') {\n                        s.slides[action](s.touchEvents.start, s.zoom.onGestureStart, passiveListener);\n                        s.slides[action](s.touchEvents.move, s.zoom.onGestureChange, passiveListener);\n                        s.slides[action](s.touchEvents.end, s.zoom.onGestureEnd, passiveListener);\n                    }\n        \n                    // Move image\n                    s[action]('touchStart', s.zoom.onTouchStart);\n                    s.slides.each(function (index, slide){\n                        if ($(slide).find('.' + s.params.zoomContainerClass).length > 0) {\n                            $(slide)[action](s.touchEvents.move, s.zoom.onTouchMove);\n                        }\n                    });\n                    s[action]('touchEnd', s.zoom.onTouchEnd);\n        \n                    // Scale Out\n                    s[action]('transitionEnd', s.zoom.onTransitionEnd);\n                    if (s.params.zoomToggle) {\n                        s.on('doubleTap', s.zoom.toggleZoom);\n                    }\n                }\n            },\n            init: function () {\n                s.zoom.attachEvents();\n            },\n            destroy: function () {\n                s.zoom.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          Plugins API. Collect all and init all plugins\n          ===========================*/\n        s._plugins = [];\n        for (var plugin in s.plugins) {\n            var p = s.plugins[plugin](s, s.params[plugin]);\n            if (p) s._plugins.push(p);\n        }\n        // Method to call all plugins event/method\n        s.callPlugins = function (eventName) {\n            for (var i = 0; i < s._plugins.length; i++) {\n                if (eventName in s._plugins[i]) {\n                    s._plugins[i][eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n        };\n\n        /*=========================\n          Events/Callbacks/Plugins Emitter\n          ===========================*/\n        function normalizeEventName (eventName) {\n            if (eventName.indexOf('on') !== 0) {\n                if (eventName[0] !== eventName[0].toUpperCase()) {\n                    eventName = 'on' + eventName[0].toUpperCase() + eventName.substring(1);\n                }\n                else {\n                    eventName = 'on' + eventName;\n                }\n            }\n            return eventName;\n        }\n        s.emitterEventListeners = {\n        \n        };\n        s.emit = function (eventName) {\n            // Trigger callbacks\n            if (s.params[eventName]) {\n                s.params[eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n            }\n            var i;\n            // Trigger events\n            if (s.emitterEventListeners[eventName]) {\n                for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                    s.emitterEventListeners[eventName][i](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n            // Trigger plugins\n            if (s.callPlugins) s.callPlugins(eventName, arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n        };\n        s.on = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            if (!s.emitterEventListeners[eventName]) s.emitterEventListeners[eventName] = [];\n            s.emitterEventListeners[eventName].push(handler);\n            return s;\n        };\n        s.off = function (eventName, handler) {\n            var i;\n            eventName = normalizeEventName(eventName);\n            if (typeof handler === 'undefined') {\n                // Remove all handlers for such event\n                s.emitterEventListeners[eventName] = [];\n                return s;\n            }\n            if (!s.emitterEventListeners[eventName] || s.emitterEventListeners[eventName].length === 0) return;\n            for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                if(s.emitterEventListeners[eventName][i] === handler) s.emitterEventListeners[eventName].splice(i, 1);\n            }\n            return s;\n        };\n        s.once = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            var _handler = function () {\n                handler(arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);\n                s.off(eventName, _handler);\n            };\n            s.on(eventName, _handler);\n            return s;\n        };\n\n        // Accessibility tools\n        s.a11y = {\n            makeFocusable: function ($el) {\n                $el.attr('tabIndex', '0');\n                return $el;\n            },\n            addRole: function ($el, role) {\n                $el.attr('role', role);\n                return $el;\n            },\n        \n            addLabel: function ($el, label) {\n                $el.attr('aria-label', label);\n                return $el;\n            },\n        \n            disable: function ($el) {\n                $el.attr('aria-disabled', true);\n                return $el;\n            },\n        \n            enable: function ($el) {\n                $el.attr('aria-disabled', false);\n                return $el;\n            },\n        \n            onEnterKey: function (event) {\n                if (event.keyCode !== 13) return;\n                if ($(event.target).is(s.params.nextButton)) {\n                    s.onClickNext(event);\n                    if (s.isEnd) {\n                        s.a11y.notify(s.params.lastSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.nextSlideMessage);\n                    }\n                }\n                else if ($(event.target).is(s.params.prevButton)) {\n                    s.onClickPrev(event);\n                    if (s.isBeginning) {\n                        s.a11y.notify(s.params.firstSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.prevSlideMessage);\n                    }\n                }\n                if ($(event.target).is('.' + s.params.bulletClass)) {\n                    $(event.target)[0].click();\n                }\n            },\n        \n            liveRegion: $('<span class=\"' + s.params.notificationClass + '\" aria-live=\"assertive\" aria-atomic=\"true\"></span>'),\n        \n            notify: function (message) {\n                var notification = s.a11y.liveRegion;\n                if (notification.length === 0) return;\n                notification.html('');\n                notification.html(message);\n            },\n            init: function () {\n                // Setup accessibility\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    s.a11y.makeFocusable(s.nextButton);\n                    s.a11y.addRole(s.nextButton, 'button');\n                    s.a11y.addLabel(s.nextButton, s.params.nextSlideMessage);\n                }\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    s.a11y.makeFocusable(s.prevButton);\n                    s.a11y.addRole(s.prevButton, 'button');\n                    s.a11y.addLabel(s.prevButton, s.params.prevSlideMessage);\n                }\n        \n                $(s.container).append(s.a11y.liveRegion);\n            },\n            initPagination: function () {\n                if (s.params.pagination && s.params.paginationClickable && s.bullets && s.bullets.length) {\n                    s.bullets.each(function () {\n                        var bullet = $(this);\n                        s.a11y.makeFocusable(bullet);\n                        s.a11y.addRole(bullet, 'button');\n                        s.a11y.addLabel(bullet, s.params.paginationBulletMessage.replace(/{{index}}/, bullet.index() + 1));\n                    });\n                }\n            },\n            destroy: function () {\n                if (s.a11y.liveRegion && s.a11y.liveRegion.length > 0) s.a11y.liveRegion.remove();\n            }\n        };\n        \n\n        /*=========================\n          Init/Destroy\n          ===========================*/\n        s.init = function () {\n            if (s.params.loop) s.createLoop();\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.enableDraggable();\n                }\n            }\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                if (!s.params.loop) s.updateProgress();\n                s.effects[s.params.effect].setTranslate();\n            }\n            if (s.params.loop) {\n                s.slideTo(s.params.initialSlide + s.loopedSlides, 0, s.params.runCallbacksOnInit);\n            }\n            else {\n                s.slideTo(s.params.initialSlide, 0, s.params.runCallbacksOnInit);\n                if (s.params.initialSlide === 0) {\n                    if (s.parallax && s.params.parallax) s.parallax.setTranslate();\n                    if (s.lazy && s.params.lazyLoading) {\n                        s.lazy.load();\n                        s.lazy.initialImageLoaded = true;\n                    }\n                }\n            }\n            s.attachEvents();\n            if (s.params.observer && s.support.observer) {\n                s.initObservers();\n            }\n            if (s.params.preloadImages && !s.params.lazyLoading) {\n                s.preloadImages();\n            }\n            if (s.params.zoom && s.zoom) {\n                s.zoom.init();\n            }\n            if (s.params.autoplay) {\n                s.startAutoplay();\n            }\n            if (s.params.keyboardControl) {\n                if (s.enableKeyboardControl) s.enableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.enableMousewheelControl) s.enableMousewheelControl();\n            }\n            // Deprecated hashnavReplaceState changed to replaceState for use in hashnav and history\n            if (s.params.hashnavReplaceState) {\n                s.params.replaceState = s.params.hashnavReplaceState;\n            }\n            if (s.params.history) {\n                if (s.history) s.history.init();\n            }\n            if (s.params.hashnav) {\n                if (s.hashnav) s.hashnav.init();\n            }\n            if (s.params.a11y && s.a11y) s.a11y.init();\n            s.emit('onInit', s);\n        };\n        \n        // Cleanup dynamic styles\n        s.cleanupStyles = function () {\n            // Container\n            s.container.removeClass(s.classNames.join(' ')).removeAttr('style');\n        \n            // Wrapper\n            s.wrapper.removeAttr('style');\n        \n            // Slides\n            if (s.slides && s.slides.length) {\n                s.slides\n                    .removeClass([\n                      s.params.slideVisibleClass,\n                      s.params.slideActiveClass,\n                      s.params.slideNextClass,\n                      s.params.slidePrevClass\n                    ].join(' '))\n                    .removeAttr('style')\n                    .removeAttr('data-swiper-column')\n                    .removeAttr('data-swiper-row');\n            }\n        \n            // Pagination/Bullets\n            if (s.paginationContainer && s.paginationContainer.length) {\n                s.paginationContainer.removeClass(s.params.paginationHiddenClass);\n            }\n            if (s.bullets && s.bullets.length) {\n                s.bullets.removeClass(s.params.bulletActiveClass);\n            }\n        \n            // Buttons\n            if (s.params.prevButton) $(s.params.prevButton).removeClass(s.params.buttonDisabledClass);\n            if (s.params.nextButton) $(s.params.nextButton).removeClass(s.params.buttonDisabledClass);\n        \n            // Scrollbar\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.scrollbar.track && s.scrollbar.track.length) s.scrollbar.track.removeAttr('style');\n                if (s.scrollbar.drag && s.scrollbar.drag.length) s.scrollbar.drag.removeAttr('style');\n            }\n        };\n        \n        // Destroy\n        s.destroy = function (deleteInstance, cleanupStyles) {\n            // Detach evebts\n            s.detachEvents();\n            // Stop autoplay\n            s.stopAutoplay();\n            // Disable draggable\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.disableDraggable();\n                }\n            }\n            // Destroy loop\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            // Cleanup styles\n            if (cleanupStyles) {\n                s.cleanupStyles();\n            }\n            // Disconnect observer\n            s.disconnectObservers();\n        \n            // Destroy zoom\n            if (s.params.zoom && s.zoom) {\n                s.zoom.destroy();\n            }\n            // Disable keyboard/mousewheel\n            if (s.params.keyboardControl) {\n                if (s.disableKeyboardControl) s.disableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.disableMousewheelControl) s.disableMousewheelControl();\n            }\n            // Disable a11y\n            if (s.params.a11y && s.a11y) s.a11y.destroy();\n            // Delete history popstate\n            if (s.params.history && !s.params.replaceState) {\n                window.removeEventListener('popstate', s.history.setHistoryPopState);\n            }\n            if (s.params.hashnav && s.hashnav)  {\n                s.hashnav.destroy();\n            }\n            // Destroy callback\n            s.emit('onDestroy');\n            // Delete instance\n            if (deleteInstance !== false) s = null;\n        };\n        \n        s.init();\n        \n\n    \n        // Return swiper instance\n        return s;\n    };\n    \n\n    /*==================================================\n        Prototype\n    ====================================================*/\n    Swiper.prototype = {\n        isSafari: (function () {\n            var ua = window.navigator.userAgent.toLowerCase();\n            return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n        })(),\n        isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent),\n        isArray: function (arr) {\n            return Object.prototype.toString.apply(arr) === '[object Array]';\n        },\n        /*==================================================\n        Browser\n        ====================================================*/\n        browser: {\n            ie: window.navigator.pointerEnabled || window.navigator.msPointerEnabled,\n            ieTouch: (window.navigator.msPointerEnabled && window.navigator.msMaxTouchPoints > 1) || (window.navigator.pointerEnabled && window.navigator.maxTouchPoints > 1),\n            lteIE9: (function() {\n                // create temporary DIV\n                var div = document.createElement('div');\n                // add content to tmp DIV which is wrapped into the IE HTML conditional statement\n                div.innerHTML = '<!--[if lte IE 9]><i></i><![endif]-->';\n                // return true / false value based on what will browser render\n                return div.getElementsByTagName('i').length === 1;\n            })()\n        },\n        /*==================================================\n        Devices\n        ====================================================*/\n        device: (function () {\n            var ua = window.navigator.userAgent;\n            var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/);\n            var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n            var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n            var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n            return {\n                ios: ipad || iphone || ipod,\n                android: android\n            };\n        })(),\n        /*==================================================\n        Feature Detection\n        ====================================================*/\n        support: {\n            touch : (window.Modernizr && Modernizr.touch === true) || (function () {\n                return !!(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch);\n            })(),\n    \n            transforms3d : (window.Modernizr && Modernizr.csstransforms3d === true) || (function () {\n                var div = document.createElement('div').style;\n                return ('webkitPerspective' in div || 'MozPerspective' in div || 'OPerspective' in div || 'MsPerspective' in div || 'perspective' in div);\n            })(),\n    \n            flexbox: (function () {\n                var div = document.createElement('div').style;\n                var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n                for (var i = 0; i < styles.length; i++) {\n                    if (styles[i] in div) return true;\n                }\n            })(),\n    \n            observer: (function () {\n                return ('MutationObserver' in window || 'WebkitMutationObserver' in window);\n            })(),\n    \n            passiveListener: (function () {\n                var supportsPassive = false;\n                try {\n                    var opts = Object.defineProperty({}, 'passive', {\n                        get: function() {\n                            supportsPassive = true;\n                        }\n                    });\n                    window.addEventListener('testPassiveListener', null, opts);\n                } catch (e) {}\n                return supportsPassive;\n            })(),\n    \n            gestures: (function () {\n                return 'ongesturestart' in window;\n            })()\n        },\n        /*==================================================\n        Plugins\n        ====================================================*/\n        plugins: {}\n    };\n    \n\n    /*===========================\n     Get jQuery\n     ===========================*/\n    \n    addLibraryPlugin($);\n    \n    var domLib = $;\n\n    /*===========================\n    Add .swiper plugin from Dom libraries\n    ===========================*/\n    function addLibraryPlugin(lib) {\n        lib.fn.swiper = function (params) {\n            var firstInstance;\n            lib(this).each(function () {\n                var s = new Swiper(this, params);\n                if (!firstInstance) firstInstance = s;\n            });\n            return firstInstance;\n        };\n    }\n    \n    if (domLib) {\n        if (!('transitionEnd' in domLib.fn)) {\n            domLib.fn.transitionEnd = function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            };\n        }\n        if (!('transform' in domLib.fn)) {\n            domLib.fn.transform = function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            };\n        }\n        if (!('transition' in domLib.fn)) {\n            domLib.fn.transition = function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            };\n        }\n        if (!('outerWidth' in domLib.fn)) {\n            domLib.fn.outerWidth = function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'));\n                    else\n                        return this[0].offsetWidth;\n                }\n                else return null;\n            };\n        }\n    }\n\n\treturn Swiper;\n}));\n//# sourceMappingURL=maps/swiper.jquery.umd.js.map\n"]}