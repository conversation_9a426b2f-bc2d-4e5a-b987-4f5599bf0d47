/* Malaysian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.ms = {};

flatpickr.l10ns.ms.weekdays = {
	shorthand: ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
	longhand: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sabtu"]
};

flatpickr.l10ns.ms.months = {
	shorthand: ["Jan", "Feb", "<PERSON>", "Apr", "<PERSON>", "<PERSON>", "Jul", "<PERSON>go", "Sep", "Okt", "Nov", "Dis"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "April", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ogos", "September", "Oktober", "November", "Disember"]
};

flatpickr.l10ns.ms.firstDayOfWeek = 1;

flatpickr.l10ns.ms.ordinal = function () {
	return "";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;