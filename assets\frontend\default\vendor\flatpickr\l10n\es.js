/* Spanish locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.es = {};

flatpickr.l10ns.es.weekdays = {
	shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
	longhand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>rc<PERSON><PERSON>", "<PERSON><PERSON>", "Viernes", "Sábado"]
};

flatpickr.l10ns.es.months = {
	shorthand: ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"],
	longhand: ["<PERSON><PERSON>", "<PERSON>rer<PERSON>", "<PERSON><PERSON>", "A<PERSON><PERSON>", "Mayo", "Jun<PERSON>", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"]
};

flatpickr.l10ns.es.ordinal = function () {
	return "º";
};

flatpickr.l10ns.es.firstDayOfWeek = 1;
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;