[{"id": 1, "label": "Brand", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 2, "label": "Acura", "inode": false, "checkbox": true, "radio": false}, {"id": 3, "label": "Audi", "inode": false, "checkbox": true, "radio": false}, {"id": 4, "label": "BMW", "inode": false, "checkbox": true, "radio": false}, {"id": 5, "label": "Buick", "inode": false, "checkbox": true, "radio": false}, {"id": 6, "label": "Cadillac", "inode": false, "checkbox": true, "radio": false}, {"id": 7, "label": "Chevrolet", "inode": false, "checkbox": true, "radio": false}, {"id": 8, "label": "Chrysler", "inode": false, "checkbox": true, "radio": false}, {"id": 9, "label": "Dodge", "inode": false, "checkbox": true, "radio": false}, {"id": 10, "label": "Fiat", "inode": false, "checkbox": true, "radio": false}, {"id": 11, "label": "<PERSON><PERSON><PERSON>", "inode": false, "checkbox": true, "radio": false}, {"id": 12, "label": "Ford", "inode": false, "checkbox": true, "radio": false}, {"id": 13, "label": "GMC", "inode": false, "checkbox": true, "radio": false}, {"id": 14, "label": "Honda", "inode": false, "checkbox": true, "radio": false}, {"id": 15, "label": "Hyundai", "inode": false, "checkbox": true, "radio": false}, {"id": 16, "label": "Infiniti", "inode": false, "checkbox": true, "radio": false}, {"id": 17, "label": "Jaguar", "inode": false, "checkbox": true, "radio": false}, {"id": 18, "label": "Jeep", "inode": false, "checkbox": true, "radio": false}, {"id": 19, "label": "<PERSON><PERSON>", "inode": false, "checkbox": true, "radio": false}, {"id": 20, "label": "Land Rover", "inode": false, "checkbox": true, "radio": false}, {"id": 21, "label": "<PERSON>us", "inode": false, "checkbox": true, "radio": false}, {"id": 22, "label": "Lincoln", "inode": false, "checkbox": true, "radio": false}, {"id": 23, "label": "Mazda", "inode": false, "checkbox": true, "radio": false}, {"id": 24, "label": "Mercedes", "inode": false, "checkbox": true, "radio": false}, {"id": 25, "label": "MINI", "inode": false, "checkbox": true, "radio": false}, {"id": 26, "label": "Mitsubishi", "inode": false, "checkbox": true, "radio": false}, {"id": 27, "label": "Nissan", "inode": false, "checkbox": true, "radio": false}, {"id": 28, "label": "Porsche", "inode": false, "checkbox": true, "radio": false}, {"id": 29, "label": "Ram", "inode": false, "checkbox": true, "radio": false}, {"id": 30, "label": "<PERSON><PERSON>", "inode": false, "checkbox": true, "radio": false}, {"id": 31, "label": "Smart", "inode": false, "checkbox": true, "radio": false}, {"id": 32, "label": "Subaru", "inode": false, "checkbox": true, "radio": false}, {"id": 33, "label": "Tesla", "inode": false, "checkbox": true, "radio": false}, {"id": 34, "label": "Toyota", "inode": false, "checkbox": true, "radio": false}, {"id": 35, "label": "Volkswagen", "inode": false, "checkbox": true, "radio": false}, {"id": 36, "label": "Volvo", "inode": false, "checkbox": true, "radio": false}]}, {"id": 37, "label": "Body Type & Size", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 38, "label": "Sports Car", "inode": false, "checkbox": false, "radio": true}, {"id": 39, "label": "Coupe/3-Door (Hatchback)", "inode": false, "checkbox": false, "radio": true}, {"id": 40, "label": "Sedan", "inode": false, "checkbox": false, "radio": true}, {"id": 41, "label": "Regular Pickup", "inode": false, "checkbox": false, "radio": true}, {"id": 42, "label": "Extended/Crew Pickup", "inode": false, "checkbox": false, "radio": true}, {"id": 43, "label": "Van/Multi-Purpose Vehicle (MPV)", "inode": false, "checkbox": false, "radio": true}, {"id": 44, "label": "Traditional SUV", "inode": false, "checkbox": false, "radio": true}, {"id": 45, "label": "Crossover", "inode": false, "checkbox": false, "radio": true}, {"id": 46, "label": "Wagon/5-door (Hatchback)", "inode": false, "checkbox": false, "radio": true}]}, {"id": 47, "label": "Comfort Convenience Entertainment", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 48, "label": "Convenient Control", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 49, "label": "Gesture control", "inode": false, "checkbox": true, "radio": false}, {"id": 50, "label": "Voice command system", "inode": false, "checkbox": true, "radio": false}, {"id": 51, "label": "Fully reconfigurable gauge cluster", "inode": false, "checkbox": true, "radio": false}, {"id": 52, "label": "Electronic parking brake", "inode": false, "checkbox": true, "radio": false}, {"id": 53, "label": "Shift by wire transmission", "inode": false, "checkbox": true, "radio": false}, {"id": 54, "label": "Push-button keyless ignition", "inode": false, "checkbox": true, "radio": false}, {"id": 55, "label": "Remote starter", "inode": false, "checkbox": true, "radio": false}, {"id": 56, "label": "Driver information system", "inode": false, "checkbox": true, "radio": false}]}, {"id": 57, "label": "Comfort", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 58, "label": "Automatic climate control", "inode": false, "checkbox": true, "radio": false}, {"id": 59, "label": "Smart climate control", "inode": false, "checkbox": true, "radio": false}, {"id": 60, "label": "Dual/multi zone climate control", "inode": false, "checkbox": true, "radio": false}, {"id": 61, "label": "Ambient interior lighting", "inode": false, "checkbox": true, "radio": false}, {"id": 62, "label": "Active glass", "inode": false, "checkbox": true, "radio": false}, {"id": 63, "label": "Sunroof", "inode": false, "checkbox": true, "radio": false}]}, {"id": 64, "label": "Connected Car", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 65, "label": "Built-in navigation aid", "inode": false, "checkbox": true, "radio": false}, {"id": 66, "label": "Navigation with path guide", "inode": false, "checkbox": true, "radio": false}, {"id": 67, "label": "Healthcare monitoring", "inode": false, "checkbox": true, "radio": false}, {"id": 68, "label": "Wireless phone charging mat", "inode": false, "checkbox": true, "radio": false}, {"id": 69, "label": "Remote diagnostics", "inode": false, "checkbox": true, "radio": false}, {"id": 70, "label": "Access and control with mobile device", "inode": false, "checkbox": true, "radio": false}, {"id": 71, "label": "Bluetooth", "inode": false, "checkbox": true, "radio": false}, {"id": 72, "label": "Dashboard display runs mobile apps", "inode": false, "checkbox": true, "radio": false}, {"id": 73, "label": "Live advisor/concierge service", "inode": false, "checkbox": true, "radio": false}, {"id": 74, "label": "Wi-Fi hotspot", "inode": false, "checkbox": true, "radio": false}]}, {"id": 75, "label": "Entertainment", "inode": true, "checkbox": true, "radio": false, "branch": [{"id": 76, "label": "Passenger display screen", "inode": false, "checkbox": true, "radio": false}, {"id": 77, "label": "CD player", "inode": false, "checkbox": true, "radio": false}, {"id": 78, "label": "HD radio", "inode": false, "checkbox": true, "radio": false}, {"id": 79, "label": "Satellite radio", "inode": false, "checkbox": true, "radio": false}, {"id": 80, "label": "Smartphone/MP3 player connection port", "inode": false, "checkbox": true, "radio": false}, {"id": 81, "label": "Premium audio system", "inode": false, "checkbox": true, "radio": false}, {"id": 82, "label": "Rear seat displays for entertainment system", "inode": false, "checkbox": true, "radio": false}, {"id": 83, "label": "Rear seat USB charging", "inode": false, "checkbox": true, "radio": false}]}]}, {"id": 84, "label": "Fuel Hybrid Electric", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 85, "label": "Fuel Type (Gas/Hybrid/Electric/Diesel)", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 86, "label": "Gasoline", "inode": false, "checkbox": false, "radio": true}, {"id": 87, "label": "Gasoline/Flex fuel (E85)", "inode": false, "checkbox": false, "radio": true}, {"id": 88, "label": "Hybrid (not a plug-in)", "inode": false, "checkbox": false, "radio": true}, {"id": 89, "label": "Plug-in hybrid", "inode": false, "checkbox": false, "radio": true}, {"id": 90, "label": "All-electric", "inode": false, "checkbox": false, "radio": true}, {"id": 91, "label": "Diesel", "inode": false, "checkbox": false, "radio": true}]}, {"id": 92, "label": "Fuel Economy", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 93, "label": "15 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 94, "label": "25 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 95, "label": "35 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 96, "label": "45 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 97, "label": "55 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 98, "label": "65 miles per gallon", "inode": false, "checkbox": false, "radio": true}, {"id": 99, "label": "Fuel-Saving Features", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 100, "label": "Start-stop system", "inode": false, "checkbox": true, "radio": false}]}]}, {"id": 101, "label": "Total Vehicle Range", "inode": true, "checkbox": false, "radio": false, "branch": [{"id": 102, "label": "75 miles", "inode": false, "checkbox": false, "radio": true}, {"id": 103, "label": "300 miles", "inode": false, "checkbox": false, "radio": true}, {"id": 104, "label": "500 miles", "inode": false, "checkbox": false, "radio": true}, {"id": 105, "label": "700 miles", "inode": false, "checkbox": false, "radio": true}]}]}]