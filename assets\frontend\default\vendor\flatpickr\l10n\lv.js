/* Latvian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.lv = {};

flatpickr.l10ns.lv.firstDayOfWeek = 1;

flatpickr.l10ns.lv.weekdays = {
	shorthand: ["Sv", "P", "Ot", "Tr", "Ce", "Pk", "Se"],
	longhand: ["Svētdiena", "Pirmdie<PERSON>", "Otrdie<PERSON>", "Trešdiena", "Ceturtdiena", "Piektdiena", "Sestdiena"]
};

flatpickr.l10ns.lv.months = {
	shorthand: ["Jan", "Feb", "<PERSON>", "<PERSON>", "Apr", "<PERSON><PERSON>n", "<PERSON><PERSON><PERSON>", "Aug", "Sep", "Okt", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Nov<PERSON><PERSON><PERSON>", "Decembris"]
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;