/* Welsh locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.cy = {};

flatpickr.l10ns.cy.weekdays = {
	shorthand: ["<PERSON>", "<PERSON>lu<PERSON>", "<PERSON>w", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
	longhand: ["<PERSON>yd<PERSON> Sul", "<PERSON><PERSON><PERSON>lu<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>rch<PERSON>", "<PERSON><PERSON><PERSON> Iau", "<PERSON><PERSON><PERSON> Gwen<PERSON>", "Dydd Sadwrn"]
};

flatpickr.l10ns.cy.months = {
	shorthand: ["<PERSON>", "<PERSON>wef", "<PERSON>w", "<PERSON><PERSON>r", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ta<PERSON>", "Rhag"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON>we<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "E<PERSON><PERSON>", "<PERSON>", "Mehe<PERSON>", "Gorf<PERSON>na<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ha<PERSON><PERSON><PERSON>"]
};

flatpickr.l10ns.cy.firstDayOfWeek = 1;

flatpickr.l10ns.cy.ordinal = function (nth) {
	if (nth === 1) return "af";

	if (nth === 2) return "ail";

	if (nth === 3 || nth === 4) return "ydd";

	if (nth === 5 || nth === 6) return "ed";

	if (nth >= 7 && nth <= 10 || nth == 12 || nth == 15 || nth == 18 || nth == 20) return "fed";

	if (nth == 11 || nth == 13 || nth == 14 || nth == 16 || nth == 17 || nth == 19) return "eg";

	if (nth >= 21 && nth <= 39) return "ain";

	// Inconclusive.
	return "";
};

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;