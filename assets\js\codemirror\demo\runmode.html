<!doctype html>

<title>CodeMirror: Mode Runner Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/runmode/runmode.js"></script>
<script src="../mode/xml/xml.js"></script>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Mode Runner</a>
  </ul>
</div>

<article>
<h2>Mode Runner Demo</h2>


    <textarea id="code" style="width: 90%; height: 7em; border: 1px solid black; padding: .2em .4em;">
<foobar>
  <blah>Enter your xml here and press the button below to display
    it as highlighted by the CodeMirror XML mode</blah>
  <tag2 foo="2" bar="&amp;quot;bar&amp;quot;"/>
</foobar></textarea><br>
    <button onclick="doHighlight();">Highlight!</button>
    <pre id="output" class="cm-s-default"></pre>

    <script>
function doHighlight() {
  CodeMirror.runMode(document.getElementById("code").value, "application/xml",
                     document.getElementById("output"));
}
</script>

    <p>Running a CodeMirror mode outside of the editor.
    The <code>CodeMirror.runMode</code> function, defined
    in <code><a href="../addon/runmode/runmode.js">lib/runmode.js</a></code> takes the following arguments:</p>

    <dl>
      <dt><code>text (string)</code></dt>
      <dd>The document to run through the highlighter.</dd>
      <dt><code>mode (<a href="../doc/manual.html#option_mode">mode spec</a>)</code></dt>
      <dd>The mode to use (must be loaded as normal).</dd>
      <dt><code>output (function or DOM node)</code></dt>
      <dd>If this is a function, it will be called for each token with
      two arguments, the token's text and the token's style class (may
      be <code>null</code> for unstyled tokens). If it is a DOM node,
      the tokens will be converted to <code>span</code> elements as in
      an editor, and inserted into the node
      (through <code>innerHTML</code>).</dd>
    </dl>

  </article>
