/* Slovenian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.sl = {};

flatpickr.l10ns.sl.weekdays = {
	shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON>", "Ponedeljek", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>bot<PERSON>"]
};

flatpickr.l10ns.sl.months = {
	shorthand: ["Jan", "Feb", "Mar", "Apr", "<PERSON>", "<PERSON>", "Jul", "Avg", "Sep", "Okt", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON>ij", "Julij", "Avgust", "September", "Oktober", "November", "December"]
};

flatpickr.l10ns.sl.firstDayOfWeek = 1;
flatpickr.l10ns.sl.rangeSeparator = " do ";
flatpickr.l10ns.sl.ordinal = function () {
	return ".";
};

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;