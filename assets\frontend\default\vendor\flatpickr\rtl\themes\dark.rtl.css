.flatpickr-calendar {
  background: transparent;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  visibility: hidden;
  text-align: center;
  padding: 0;
  animation: none;
  direction: rtl;
  border: 0;
  font-size: 14px;
  line-height: 24px;
  border-radius: 5px;
  position: absolute;
  width: 315px;
  box-sizing: border-box;
  background: rgba(63,68,88,0.95);
  box-shadow: -1px 0 0 #3f4458, 1px 0 0 #3f4458, 0 1px 0 #3f4458, 0 -1px 0 #3f4458, 0 3px 13px rgba(0,0,0,0.08);
}
.flatpickr-calendar.open,
.flatpickr-calendar.inline {
  opacity: 1;
  visibility: visible;
  overflow: visible;
  max-height: 640px;
}
.flatpickr-calendar.open {
  display: inline-block;
  z-index: 99999;
}
.flatpickr-calendar.animate.open {
  animation: flatpickrFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px;
}
.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px);
}
.flatpickr-calendar.static.open {
  z-index: 999;
  display: block;
}
.flatpickr-calendar.hasWeeks {
  width: auto;
}
.flatpickr-calendar .hasWeeks .dayContainer,
.flatpickr-calendar .hasTime .dayContainer {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.flatpickr-calendar .hasWeeks .dayContainer {
  border-right: 0;
}
.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
  height: 40px;
  border-top: 1px solid #3f4458;
}
.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto;
}
.flatpickr-calendar:before,
.flatpickr-calendar:after {
  position: absolute;
  display: block;
  pointer-events: none;
  border: solid transparent;
  content: '';
  height: 0;
  width: 0;
  right: 22px;
}
.flatpickr-calendar.rightMost:before,
.flatpickr-calendar.rightMost:after {
  right: auto;
  left: 22px;
}
.flatpickr-calendar:before {
  border-width: 5px;
  margin: 0 -5px;
}
.flatpickr-calendar:after {
  border-width: 4px;
  margin: 0 -4px;
}
.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  bottom: 100%;
}
.flatpickr-calendar.arrowTop:before {
  border-bottom-color: #3f4458;
}
.flatpickr-calendar.arrowTop:after {
  border-bottom-color: rgba(63,68,88,0.95);
}
.flatpickr-calendar.arrowBottom:before,
.flatpickr-calendar.arrowBottom:after {
  top: 100%;
}
.flatpickr-calendar.arrowBottom:before {
  border-top-color: #3f4458;
}
.flatpickr-calendar.arrowBottom:after {
  border-top-color: rgba(63,68,88,0.95);
}
.flatpickr-calendar:focus {
  outline: 0;
}
.flatpickr-wrapper {
  position: relative;
  display: inline-block;
}
.flatpickr-month {
  background: transparent;
  color: #fff;
  fill: #fff;
  height: 28px;
  line-height: 24px;
  text-align: center;
  position: relative;
  user-select: none;
  overflow: hidden;
}
.flatpickr-prev-month,
.flatpickr-next-month {
  text-decoration: none;
  cursor: pointer;
  position: absolute;
  top: 0px;
  height: 28px;
  line-height: 16px;
  padding: 10px calc(3.57% - 1.5px);
}
.flatpickr-prev-month i,
.flatpickr-next-month i {
  position: relative;
}
.flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-next-month.flatpickr-prev-month {
/*
        /*rtl:begin:ignore*/
/*
        */
  right: 0;
/*
        /*rtl:end:ignore*/
/*
        */
}
/*
        /*rtl:begin:ignore*/
/*
        /*rtl:end:ignore*/
.flatpickr-prev-month.flatpickr-next-month,
.flatpickr-next-month.flatpickr-next-month {
/*
        /*rtl:begin:ignore*/
/*
        */
  left: 0;
/*
        /*rtl:end:ignore*/
/*
        */
}
/*
        /*rtl:begin:ignore*/
/*
        /*rtl:end:ignore*/
.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
  color: #eee;
}
.flatpickr-prev-month:hover svg,
.flatpickr-next-month:hover svg {
  fill: #f64747;
}
.flatpickr-prev-month svg,
.flatpickr-next-month svg {
  width: 14px;
}
.flatpickr-prev-month svg path,
.flatpickr-next-month svg path {
  transition: fill 0.1s;
  fill: inherit;
}
.numInputWrapper {
  position: relative;
  height: auto;
}
.numInputWrapper input,
.numInputWrapper span {
  display: inline-block;
}
.numInputWrapper input {
  width: 100%;
}
.numInputWrapper span {
  position: absolute;
  left: 0;
  width: 14px;
  padding: 0 2px 0 4px;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  cursor: pointer;
  border: 1px solid rgba(255,255,255,0.05);
  box-sizing: border-box;
}
.numInputWrapper span:hover {
  background: rgba(192,187,167,0.1);
}
.numInputWrapper span:active {
  background: rgba(192,187,167,0.2);
}
.numInputWrapper span:after {
  display: block;
  content: "";
  position: absolute;
  top: 33%;
}
.numInputWrapper span.arrowUp {
  top: 0;
  border-bottom: 0;
}
.numInputWrapper span.arrowUp:after {
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-bottom: 4px solid rgba(255,255,255,0.6);
}
.numInputWrapper span.arrowDown {
  top: 50%;
}
.numInputWrapper span.arrowDown:after {
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-top: 4px solid rgba(255,255,255,0.6);
}
.numInputWrapper span svg {
  width: inherit;
  height: auto;
}
.numInputWrapper span svg path {
  fill: rgba(255,255,255,0.5);
}
.numInputWrapper:hover {
  background: rgba(192,187,167,0.05);
}
.numInputWrapper:hover span {
  opacity: 1;
}
.flatpickr-current-month {
  font-size: 135%;
  line-height: inherit;
  font-weight: 300;
  color: inherit;
  position: absolute;
  width: 75%;
  right: 12.5%;
  top: 5px;
  display: inline-block;
  text-align: center;
  transform: translate(0px, 0px);
}
.flatpickr-current-month.slideLeft {
  transform: translate(100%, 0px);
  animation: fadeOut 400ms ease, slideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-current-month.slideLeftNew {
  transform: translate(-100%, 0px);
  animation: fadeIn 400ms ease, slideLeftNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-current-month.slideRight {
  transform: translate(-100%, 0px);
  animation: fadeOut 400ms ease, slideRight 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-current-month.slideRightNew {
  transform: translate(0, 0);
  animation: fadeIn 400ms ease, slideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-current-month span.cur-month {
  font-family: inherit;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  margin-right: 7px;
  padding: 0;
}
.flatpickr-current-month span.cur-month:hover {
  background: rgba(192,187,167,0.05);
}
.flatpickr-current-month .numInputWrapper {
  width: 6ch;
  width: 7ch\0;
  display: inline-block;
}
.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: #fff;
}
.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: #fff;
}
.flatpickr-current-month input.cur-year {
  background: transparent;
  box-sizing: border-box;
  color: inherit;
  cursor: default;
  padding: 0 0.5ch 0 0;
  margin: 0;
  display: inline;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  line-height: inherit;
  height: initial;
  border: 0;
  border-radius: 0;
  vertical-align: initial;
}
.flatpickr-current-month input.cur-year:focus {
  outline: 0;
}
.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  font-size: 100%;
  color: rgba(255,255,255,0.5);
  background: transparent;
  pointer-events: none;
}
.flatpickr-weekdays {
  background: transparent;
  text-align: center;
  overflow: hidden;
  width: 315px;
  display: flex;
  align-items: center;
  height: 28px;
}
span.flatpickr-weekday {
  cursor: default;
  font-size: 90%;
  color: rgba(255,255,255,0.54);
  line-height: 1;
  margin: 0;
  background: transparent;
  text-align: center;
  display: block;
  flex: 1;
  font-weight: bolder;
  margin: 0;
}
.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0 0;
}
.flatpickr-days {
  position: relative;
  overflow: hidden;
  display: flex;
  width: 315px;
}
.flatpickr-days:focus {
  outline: 0;
}
.dayContainer {
  padding: 0;
  outline: 0;
  text-align: right;
  width: 315px;
  min-width: 315px;
  max-width: 315px;
  box-sizing: border-box;
  display: inline-block;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -ms-flex-pack: justify;
  justify-content: space-around;
  transform: translate(0px, 0px);
  opacity: 1;
}
.flatpickr-calendar.animate .dayContainer.slideLeft {
  animation: fadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), slideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.animate .dayContainer.slideLeft,
.flatpickr-calendar.animate .dayContainer.slideLeftNew {
  transform: translate(100%, 0px);
}
.flatpickr-calendar.animate .dayContainer.slideLeftNew {
  animation: fadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), slideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.animate .dayContainer.slideRight {
  animation: fadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), slideRight 400ms cubic-bezier(0.23, 1, 0.32, 1);
  transform: translate(-100%, 0px);
}
.flatpickr-calendar.animate .dayContainer.slideRightNew {
  animation: fadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), slideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  box-sizing: border-box;
  color: rgba(255,255,255,0.95);
  cursor: pointer;
  font-weight: 400;
  width: 14.2857143%;
  flex-basis: 14.2857143%;
  max-width: 40px;
  height: 40px;
  line-height: 40px;
  margin: 0;
  display: inline-block;
  position: relative;
  justify-content: center;
  text-align: center;
}
.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  cursor: pointer;
  outline: 0;
  background: rgba(100,108,140,0.95);
  border-color: rgba(100,108,140,0.95);
}
.flatpickr-day.today {
  border-color: #eee;
}
.flatpickr-day.today:hover,
.flatpickr-day.today:focus {
  border-color: #eee;
  background: #eee;
  color: #3f4458;
}
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #80cbc4;
  box-shadow: none;
  color: #fff;
  border-color: #80cbc4;
}
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange,
.flatpickr-day.endRange.startRange {
  border-radius: 0 50px 50px 0;
}
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange,
.flatpickr-day.endRange.endRange {
  border-radius: 50px 0 0 50px;
}
.flatpickr-day.selected.startRange + .endRange,
.flatpickr-day.startRange.startRange + .endRange,
.flatpickr-day.endRange.startRange + .endRange {
  box-shadow: 10px 0 0 #80cbc4;
}
.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange,
.flatpickr-day.endRange.startRange.endRange {
  border-radius: 50px;
}
.flatpickr-day.inRange {
  border-radius: 0;
  box-shadow: 5px 0 0 rgba(100,108,140,0.95), -5px 0 0 rgba(100,108,140,0.95);
}
.flatpickr-day.disabled,
.flatpickr-day.disabled:hover {
  pointer-events: none;
}
.flatpickr-day.disabled,
.flatpickr-day.disabled:hover,
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.notAllowed.nextMonthDay {
  color: rgba(255,255,255,0.3);
  background: transparent;
  border-color: transparent;
  cursor: default;
}
.rangeMode .flatpickr-day {
  margin-top: 1px;
}
.flatpickr-weekwrapper {
  display: inline-block;
  float: right;
}
.flatpickr-weekwrapper .flatpickr-weeks {
  padding: 1px 12px 0 12px;
  box-shadow: -1px 0 0 #3f4458;
}
.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  width: 100%;
}
.flatpickr-weekwrapper span.flatpickr-day {
  display: block;
  width: 100%;
  max-width: none;
}
.flatpickr-innerContainer {
  display: block;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
}
.flatpickr-rContainer {
  display: inline-block;
  padding: 0;
  box-sizing: border-box;
}
.flatpickr-time {
  text-align: center;
  outline: 0;
  display: block;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
}
.flatpickr-time:after {
  content: "";
  display: table;
  clear: both;
}
.flatpickr-time .numInputWrapper {
  flex: 1;
  width: 40%;
  height: 40px;
  float: right;
}
.flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: rgba(255,255,255,0.95);
}
.flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: rgba(255,255,255,0.95);
}
.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%;
}
.flatpickr-time.time24hr .numInputWrapper {
  width: 49%;
}
.flatpickr-time input {
  background: transparent;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  text-align: center;
  margin: 0;
  padding: 0;
  height: inherit;
  line-height: inherit;
  cursor: pointer;
  color: rgba(255,255,255,0.95);
  font-size: 14px;
  position: relative;
  box-sizing: border-box;
}
.flatpickr-time input.flatpickr-hour {
  font-weight: bold;
}
.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
  font-weight: 400;
}
.flatpickr-time input:focus {
  outline: 0;
  border: 0;
}
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  height: inherit;
  display: inline-block;
  float: right;
  line-height: inherit;
  color: rgba(255,255,255,0.95);
  font-weight: bold;
  width: 2%;
  user-select: none;
}
.flatpickr-time .flatpickr-am-pm {
  outline: 0;
  width: 18%;
  cursor: pointer;
  text-align: center;
  font-weight: 400;
}
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time .flatpickr-am-pm:focus {
  background: rgba(109,118,151,0.95);
}
@media all and (-ms-high-contrast: none) {
  .flatpickr-month {
    padding: 0;
  }
  .flatpickr-month svg {
    top: 0 !important;
  }
}
.flatpickr-input[readonly] {
  cursor: pointer;
}
@-moz-keyframes flatpickrFadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes flatpickrFadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-o-keyframes flatpickrFadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@keyframes flatpickrFadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
@-moz-keyframes slideLeft {
  from {
    transform: translate(0px, 0px);
  }
  to {
    transform: translate(100%, 0px);
  }
}
@-webkit-keyframes slideLeft {
  from {
    transform: translate(0px, 0px);
  }
  to {
    transform: translate(100%, 0px);
  }
}
@-o-keyframes slideLeft {
  from {
    transform: translate(0px, 0px);
  }
  to {
    transform: translate(100%, 0px);
  }
}
@keyframes slideLeft {
  from {
    transform: translate(0px, 0px);
  }
  to {
    transform: translate(100%, 0px);
  }
}
@-moz-keyframes slideLeftNew {
  from {
    transform: translate(-100%, 0px);
  }
  to {
    transform: translate(0px, 0px);
  }
}
@-webkit-keyframes slideLeftNew {
  from {
    transform: translate(-100%, 0px);
  }
  to {
    transform: translate(0px, 0px);
  }
}
@-o-keyframes slideLeftNew {
  from {
    transform: translate(-100%, 0px);
  }
  to {
    transform: translate(0px, 0px);
  }
}
@keyframes slideLeftNew {
  from {
    transform: translate(-100%, 0px);
  }
  to {
    transform: translate(0px, 0px);
  }
}
@-moz-keyframes slideRight {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(-100%, 0px);
  }
}
@-webkit-keyframes slideRight {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(-100%, 0px);
  }
}
@-o-keyframes slideRight {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(-100%, 0px);
  }
}
@keyframes slideRight {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(-100%, 0px);
  }
}
@-moz-keyframes slideRightNew {
  from {
    transform: translate(100%, 0);
  }
  to {
    transform: translate(0, 0);
  }
}
@-webkit-keyframes slideRightNew {
  from {
    transform: translate(100%, 0);
  }
  to {
    transform: translate(0, 0);
  }
}
@-o-keyframes slideRightNew {
  from {
    transform: translate(100%, 0);
  }
  to {
    transform: translate(0, 0);
  }
}
@keyframes slideRightNew {
  from {
    transform: translate(100%, 0);
  }
  to {
    transform: translate(0, 0);
  }
}
@-moz-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-o-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-o-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
