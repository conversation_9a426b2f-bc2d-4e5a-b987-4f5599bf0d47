<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Simple Impersonation Helper Functions
 * 
 * Provides simple helper functions for session-based impersonation
 */

if (!function_exists('is_impersonating')) {
    /**
     * Check if currently impersonating a user
     * 
     * @return bool True if impersonating
     */
    function is_impersonating()
    {
        $CI =& get_instance();
        return $CI->session->userdata('is_impersonating') === true;
    }
}

if (!function_exists('get_impersonation_info')) {
    /**
     * Get impersonation information
     * 
     * @return array|null Impersonation info or null if not impersonating
     */
    function get_impersonation_info()
    {
        $CI =& get_instance();
        
        if (!is_impersonating()) {
            return null;
        }
        
        return array(
            'original_admin_id' => $CI->session->userdata('original_admin_id'),
            'original_admin_name' => $CI->session->userdata('original_admin_name'),
            'original_admin_email' => $CI->session->userdata('original_admin_email'),
            'impersonated_user_type' => $CI->session->userdata('impersonated_user_type'),
            'impersonated_user_id' => $CI->session->userdata('impersonated_user_id'),
            'impersonation_started' => $CI->session->userdata('impersonation_started')
        );
    }
}

if (!function_exists('get_impersonation_banner')) {
    /**
     * Get HTML for impersonation banner
     * 
     * @return string HTML banner or empty string if not impersonating
     */
    function get_impersonation_banner()
    {
        if (!is_impersonating()) {
            return '';
        }
        
        $info = get_impersonation_info();
        $user_type = ucfirst($info['impersonated_user_type']);
        $admin_name = $info['original_admin_name'];
        
        $banner = '
        <div style="
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 10px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        ">
            <div style="margin-bottom: 8px;">
                <i class="fa fa-eye"></i> <strong>IMPERSONATION MODE</strong>
            </div>
            <div style="font-size: 12px; margin-bottom: 8px;">
                Admin <strong>' . htmlspecialchars($admin_name) . '</strong> is viewing as <strong>' . $user_type . '</strong>
            </div>
            <a href="' . site_url('admin/view_as_user/return_to_admin') . '" 
               class="btn btn-light btn-sm" 
               style="background: rgba(255,255,255,0.9); color: #333; border: none;">
                <i class="fa fa-arrow-left"></i> Return to Admin
            </a>
        </div>';
        
        return $banner;
    }
}

if (!function_exists('render_view_as_button')) {
    /**
     * Render view as button for user management tables
     * 
     * @param string $user_type User type
     * @param int $user_id User ID
     * @param string $user_name User name
     * @param string $css_class Additional CSS classes
     * @return string HTML button
     */
    function render_view_as_button($user_type, $user_id, $user_name, $css_class = 'btn btn-warning btn-sm')
    {
        $escaped_name = htmlspecialchars($user_name);
        
        return '
        <a onclick="viewAsUser(\'' . $user_type . '\', ' . $user_id . ', \'' . $escaped_name . '\')"
           class="' . $css_class . '" 
           title="View as this user">
            <i class="fa fa-eye"></i> View As
        </a>';
    }
}

if (!function_exists('get_view_as_javascript')) {
    /**
     * Get JavaScript function for view as functionality
     * 
     * @return string JavaScript function
     */
    function get_view_as_javascript()
    {
        return '
        <script type="text/javascript">
        function viewAsUser(userType, userId, userName) {
            if (confirm("Are you sure you want to view the system as " + userName + "?\\n\\nThis will switch you to their account view. You can return to admin using the banner that will appear.")) {
                window.location.href = "' . site_url('admin/view_as_user/switch') . '/" + userType + "/" + userId;
            }
        }
        </script>';
    }
}

if (!function_exists('log_impersonation_activity')) {
    /**
     * Log impersonation activity
     * 
     * @param string $action Action performed
     * @param string $user_type Target user type
     * @param int $user_id Target user ID
     * @param string $details Additional details
     */
    function log_impersonation_activity($action, $user_type = '', $user_id = '', $details = '')
    {
        $CI =& get_instance();
        $CI->load->database();
        
        $admin_id = $CI->session->userdata('original_admin_id') ?: 
                   $CI->session->userdata('login_user_id');
        
        $log_data = array(
            'user_id' => $admin_id,
            'action' => 'impersonation_' . $action,
            'details' => $details ?: "Impersonation {$action}: {$user_type} ID {$user_id}",
            'target_user_type' => $user_type,
            'target_user_id' => $user_id,
            'timestamp' => time(),
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent()
        );
        
        $CI->db->insert('activity_log', $log_data);
    }
}
