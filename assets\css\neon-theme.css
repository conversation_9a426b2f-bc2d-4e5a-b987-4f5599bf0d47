*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 62.5%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  /*font-family: "Helvetica Neue", Helvetica, "Noto Sans", sans-serif, Arial, sans-serif;*/
  font-family: 'Open Sans', sans-serif;
  font-size: 12px;
  line-height: 1.42857143;
  color: #949494;
  background-color: #ffffff;
}
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #373e4a;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #818da2;
}
a:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
img {
  vertical-align: middle;
}
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 3px;
}
.img-thumbnail {
  padding: 2px;
  line-height: 1.42857143;
  background-color: #ffffff;
  border: 1px solid #ededf0;
  border-radius: 3px;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 17px;
  margin-bottom: 17px;
  border: 0;
  border-top: 1px solid #eeeeee;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
figure {
  margin: 0;
}
p {
  margin: 0 0 8.5px;
  font-size: 12px;
}
.lead {
  margin-bottom: 17px;
  font-size: 13px;
  font-weight: 200;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 18px;
  }
}
small,
.small {
  font-size: 85%;
}
cite {
  font-style: normal;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-muted {
  color: #999999;
}
.text-primary {
  color: #949494;
}
a.text-primary:hover {
  color: #7b7b7b;
}
.text-success {
  color: #045702;
}
a.text-success:hover {
  color: #022501;
}
.text-info {
  color: #2c7ea1;
}
a.text-info:hover {
  color: #215f79;
}
.text-warning {
  color: #574802;
}
a.text-warning:hover {
  color: #251f01;
}
.text-danger {
  color: #ac1818;
}
a.text-danger:hover {
  color: #7f1212;
}
.bg-primary {
  color: #fff;
  background-color: #949494;
}
a.bg-primary:hover {
  background-color: #7b7b7b;
}
.bg-success {
  background-color: #bdedbc;
}
a.bg-success:hover {
  background-color: #95e294;
}
.bg-info {
  background-color: #c5e8f7;
}
a.bg-info:hover {
  background-color: #98d6f1;
}
.bg-warning {
  background-color: #ffefa4;
}
a.bg-warning:hover {
  background-color: #ffe671;
}
.bg-danger {
  background-color: #ffc9c9;
}
a.bg-danger:hover {
  background-color: #ff9696;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: #373e4a;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #999999;
}
h1,
h2,
h3 {
  margin-top: 17px;
  margin-bottom: 8.5px;
}
h1 small,
h2 small,
h3 small,
h1 .small,
h2 .small,
h3 .small {
  font-size: 65%;
}
h4,
h5,
h6 {
  margin-top: 8.5px;
  margin-bottom: 8.5px;
}
h4 small,
h5 small,
h6 small,
h4 .small,
h5 .small,
h6 .small {
  font-size: 75%;
}
h1,
.h1 {
  font-size: 31px;
}
h2,
.h2 {
  font-size: 25px;
}
h3,
.h3 {
  font-size: 21px;
}
h4,
.h4 {
  font-size: 15px;
}
h5,
.h5 {
  font-size: 12px;
}
h6,
.h6 {
  font-size: 11px;
}
.bg-primary {
  color: #fff;
  background-color: #949494;
}
a.bg-primary:hover {
  background-color: #7b7b7b;
}
.bg-warning {
  background-color: #ffefa4;
}
a.bg-warning:hover {
  background-color: #ffe671;
}
.bg-danger {
  background-color: #ffc9c9;
}
a.bg-danger:hover {
  background-color: #ff9696;
}
.bg-success {
  background-color: #bdedbc;
}
a.bg-success:hover {
  background-color: #95e294;
}
.bg-info {
  background-color: #c5e8f7;
}
a.bg-info:hover {
  background-color: #98d6f1;
}
.page-header {
  padding-bottom: 7.5px;
  margin: 34px 0 17px;
  border-bottom: 1px solid #eeeeee;
}
ul,
ol {
  margin-top: 0;
  margin-bottom: 8.5px;
}
ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
}
.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
.list-inline > li:first-child {
  padding-left: 0;
}
dl {
  margin-bottom: 17px;
}
dt,
dd {
  line-height: 1.42857143;
}
dt {
  font-weight: bold;
}
dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
  .dl-horizontal dd:before,
  .dl-horizontal dd:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */
  }
  .dl-horizontal dd:after {
    clear: both;
  }
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999;
}
abbr.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 8.5px 17px;
  margin: 0 0 17px;
  border-left: 5px solid #eeeeee;
}
blockquote p {
  font-size: 15px;
  font-weight: 300;
  line-height: 1.25;
}
blockquote p:last-child {
  margin-bottom: 0;
}
blockquote small {
  display: block;
  line-height: 1.42857143;
  color: #999999;
}
blockquote small:before {
  content: '\2014 \00A0';
}
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
}
blockquote.pull-right p,
blockquote.pull-right small,
blockquote.pull-right .small {
  text-align: right;
}
blockquote.pull-right small:before,
blockquote.pull-right .small:before {
  content: '';
}
blockquote.pull-right small:after,
blockquote.pull-right .small:after {
  content: '\00A0 \2014';
}
blockquote:before,
blockquote:after {
  content: "";
}
address {
  margin-bottom: 17px;
  font-style: normal;
  line-height: 1.42857143;
}
table {
  max-width: 100%;
  background-color: transparent;
}
th {
  text-align: left;
  font-weight: 400;
  color: #303641;
}
.table {
  width: 100%;
  margin-bottom: 17px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ebebeb;
}
.table > thead > tr > th .progress,
.table > tbody > tr > th .progress,
.table > tfoot > tr > th .progress,
.table > thead > tr > td .progress,
.table > tbody > tr > td .progress,
.table > tfoot > tr > td .progress {
  margin-bottom: 0;
}
.table > thead > tr > th .label,
.table > tbody > tr > th .label,
.table > tfoot > tr > th .label,
.table > thead > tr > td .label,
.table > tbody > tr > td .label,
.table > tfoot > tr > td .label {
  margin-left: 5px;
  margin-right: 5px;
  padding-left: 10px;
  padding-right: 10px;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ebebeb;
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #ebebeb;
}
.table .table {
  background-color: #ffffff;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 5px;
}
.table-bordered {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  background-color: #f5f5f6;
  border-bottom-width: 1px;
  color: #a6a7aa;
}
.table-bordered > tfoot > tr > th,
.table-bordered > tfoot > tr > td {
  background-color: #f5f5f6;
  border-top-width: 1px;
  color: #a6a7aa;
}
.table-striped > tbody > tr:nth-child(odd) > td,
.table-striped > tbody > tr:nth-child(odd) > th {
  background-color: #f8f8f8;
}
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
  background-color: #f2f2f4;
}
table col[class*="col-"] {
  float: none;
  display: table-column;
}
table td[class*="col-"],
table th[class*="col-"] {
  float: none;
  display: table-cell;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #f2f2f4;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #f2f2f4;
}
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr.active:hover > th {
  background-color: #e5e5e8;
}
.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
  background-color: #bdedbc;
}
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover,
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr.success:hover > th {
  background-color: #a9e8a8;
}
.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
  background-color: #c5e8f7;
}
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover,
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr.info:hover > th {
  background-color: #afdff4;
}
.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
  background-color: #ffefa4;
}
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover,
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr.warning:hover > th {
  background-color: #ffeb8a;
}
.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
  background-color: #ffc9c9;
}
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover,
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr.danger:hover > th {
  background-color: #ffafaf;
}
@media (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 12.75px;
    overflow-y: hidden;
    overflow-x: scroll;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ebebeb;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
table > tbody > tr.highlight > td,
table > tbody > tr.highlight > th {
  background-color: #f1f2f4 !important;
  color: #303641;
}
.table > thead > tr > .middle-align,
.table > tbody > tr > .middle-align,
.table > tfoot > tr > .middle-align {
  vertical-align: middle;
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 17px;
  font-size: 18px;
  line-height: inherit;
  color: #7d8086;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  margin-bottom: 5px;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 7px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #555555;
}
.form-control {
  display: block;
  width: 100%;
  height: 31px;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #555555;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  -moz-transition: border-color ease-in-out .15s, -moz-box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
  border-color: #c8cdd7;
  outline: 0;
  -moz-box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
  -webkit-box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
  box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
}
.form-control::-webkit-input-placeholder {
  color: #aaaaaa;
}
.form-control:-moz-placeholder {
  color: #aaaaaa;
}
.form-control::-moz-placeholder {
  color: #aaaaaa;
}
.form-control:-ms-input-placeholder {
  color: #aaaaaa;
}
.form-control:-moz-placeholder {
  color: #aaaaaa;
}
.form-control::-moz-placeholder {
  color: #aaaaaa;
}
.form-control:-ms-input-placeholder {
  color: #aaaaaa;
}
.form-control::-webkit-input-placeholder {
  color: #aaaaaa;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  cursor: not-allowed;
  background-color: #eeeeee;
}
textarea.form-control {
  height: auto;
}
input[type="date"] {
  line-height: 31px;
}
.form-group {
  margin-bottom: 15px;
}
.radio,
.checkbox {
  display: block;
  min-height: 17px;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
}
.radio label,
.checkbox label {
  display: inline;
  font-weight: normal;
  cursor: pointer;
}
.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  float: left;
  margin-left: -20px;
  margin-top: 1px;
}
.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline,
.checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
.radio[disabled],
.radio-inline[disabled],
.checkbox[disabled],
.checkbox-inline[disabled],
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"],
fieldset[disabled] .radio,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.input-sm {
  height: 28px;
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
select.input-sm {
  height: 28px;
  line-height: 28px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
.input-lg {
  height: 41px;
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
select.input-lg {
  height: 41px;
  line-height: 41px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 38.75px;
}
.has-feedback .form-control-feedback {
  position: absolute;
  top: 22px;
  right: 0;
  display: block;
  width: 31px;
  height: 31px;
  line-height: 31px;
  text-align: center;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline {
  color: #ffd40b;
}
.has-warning .form-control {
  border-color: #ffd78a;
}
.has-warning .form-control:focus {
  border-color: #ffc658;
}
.has-warning .input-group-addon {
  color: #ffd40b;
  border-color: #ffd78a;
  background-color: #ffefa4;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline {
  color: #ff3030;
}
.has-error .form-control {
  border-color: #ffafbd;
}
.has-error .form-control:focus {
  border-color: #ff7c92;
}
.has-error .input-group-addon {
  color: #ff3030;
  border-color: #ffafbd;
  background-color: #ffc9c9;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline {
  color: #46cd43;
}
.has-success .form-control {
  border-color: #b4e8a8;
}
.has-success .form-control:focus {
  border-color: #91dd80;
}
.has-success .input-group-addon {
  color: #46cd43;
  border-color: #b4e8a8;
  background-color: #bdedbc;
}
.form-control-static {
  margin-bottom: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #d4d4d4;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    vertical-align: middle;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .control-label,
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
}
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 24px;
}
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
.form-horizontal .form-group:before,
.form-horizontal .form-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.form-horizontal .form-group:after {
  clear: both;
}
.form-horizontal .form-control-static {
  padding-top: 7px;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  top: 0;
  right: 15px;
}
.bs-example > .btn,
.bs-example > .make-switch,
.bs-example > .btn-group {
  margin-top: 4px;
  margin-bottom: 4px;
  margin-right: 9px;
}
.bs-example.bs-baseline-top .btn,
.bs-example.bs-baseline-top .btn-group {
  vertical-align: top !important;
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.btn:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover,
.btn:focus {
  color: #303641;
  text-decoration: none;
  outline: none;
}
.btn:active,
.btn.active {
  outline: none;
  background-image: none;
  -moz-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -webkit-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -moz-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  pointer-events: none;
  -webkit-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn.btn-icon {
  position: relative;
}
.btn.btn-icon i {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
}
.btn-default {
  color: #303641;
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  color: #303641;
  background-color: #dbdbdd;
  border-color: #d0d0d3;
}
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default .badge {
  color: #f0f0f1;
  background-color: #303641;
}
.btn-default > .caret {
  border-top-color: #303641;
  border-bottom-color: #303641 !important;
}
.btn-default.dropdown-toggle {
  border-left-color: #dedee0;
}
.btn-default.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-default.btn-icon i {
  background-color: #dbdbdd;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-default.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-default.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-default.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-default.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-default.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-default.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-default.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-default.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-default.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-default.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-default.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary {
  color: #ffffff;
  background-color: #303641;
  border-color: #303641;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  color: #ffffff;
  background-color: #1f232a;
  border-color: #16191e;
}
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #303641;
  border-color: #303641;
}
.btn-primary .badge {
  color: #303641;
  background-color: #ffffff;
}
.btn-primary > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-primary.dropdown-toggle {
  border-left-color: #21252c;
}
.btn-primary.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-primary.btn-icon i {
  background-color: #1f232a;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-primary.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-primary.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-primary.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-primary.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-primary.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-primary.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-primary.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-primary.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-primary.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-primary.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue {
  color: #ffffff;
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue:hover,
.btn-blue:focus,
.btn-blue:active,
.btn-blue.active,
.open .dropdown-toggle.btn-blue {
  color: #ffffff;
  background-color: #005993;
  border-color: #004d7f;
}
.btn-blue:active,
.btn-blue.active,
.open .dropdown-toggle.btn-blue {
  background-image: none;
}
.btn-blue.disabled,
.btn-blue[disabled],
fieldset[disabled] .btn-blue,
.btn-blue.disabled:hover,
.btn-blue[disabled]:hover,
fieldset[disabled] .btn-blue:hover,
.btn-blue.disabled:focus,
.btn-blue[disabled]:focus,
fieldset[disabled] .btn-blue:focus,
.btn-blue.disabled:active,
.btn-blue[disabled]:active,
fieldset[disabled] .btn-blue:active,
.btn-blue.disabled.active,
.btn-blue[disabled].active,
fieldset[disabled] .btn-blue.active {
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue .badge {
  color: #0072bc;
  background-color: #ffffff;
}
.btn-blue > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-blue.dropdown-toggle {
  border-left-color: #005c98;
}
.btn-blue.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-blue.btn-icon i {
  background-color: #005993;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-blue.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-blue.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-blue.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-blue.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-blue.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-blue.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-blue.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-blue.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-blue.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-blue.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red {
  color: #ffffff;
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red:hover,
.btn-red:focus,
.btn-red:active,
.btn-red.active,
.open .dropdown-toggle.btn-red {
  color: #ffffff;
  background-color: #b11b1b;
  border-color: #9f1818;
}
.btn-red:active,
.btn-red.active,
.open .dropdown-toggle.btn-red {
  background-image: none;
}
.btn-red.disabled,
.btn-red[disabled],
fieldset[disabled] .btn-red,
.btn-red.disabled:hover,
.btn-red[disabled]:hover,
fieldset[disabled] .btn-red:hover,
.btn-red.disabled:focus,
.btn-red[disabled]:focus,
fieldset[disabled] .btn-red:focus,
.btn-red.disabled:active,
.btn-red[disabled]:active,
fieldset[disabled] .btn-red:active,
.btn-red.disabled.active,
.btn-red[disabled].active,
fieldset[disabled] .btn-red.active {
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red .badge {
  color: #d42020;
  background-color: #ffffff;
}
.btn-red > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-red.dropdown-toggle {
  border-left-color: #b51b1b;
}
.btn-red.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-red.btn-icon i {
  background-color: #b11b1b;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-red.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-red.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-red.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-red.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-red.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-red.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-red.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-red.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-red.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-red.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange {
  color: #ffffff;
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange:active,
.btn-orange.active,
.open .dropdown-toggle.btn-orange {
  color: #ffffff;
  background-color: #d67e00;
  border-color: #c27200;
}
.btn-orange:active,
.btn-orange.active,
.open .dropdown-toggle.btn-orange {
  background-image: none;
}
.btn-orange.disabled,
.btn-orange[disabled],
fieldset[disabled] .btn-orange,
.btn-orange.disabled:hover,
.btn-orange[disabled]:hover,
fieldset[disabled] .btn-orange:hover,
.btn-orange.disabled:focus,
.btn-orange[disabled]:focus,
fieldset[disabled] .btn-orange:focus,
.btn-orange.disabled:active,
.btn-orange[disabled]:active,
fieldset[disabled] .btn-orange:active,
.btn-orange.disabled.active,
.btn-orange[disabled].active,
fieldset[disabled] .btn-orange.active {
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange .badge {
  color: #ff9600;
  background-color: #ffffff;
}
.btn-orange > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-orange.dropdown-toggle {
  border-left-color: #db8100;
}
.btn-orange.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-orange.btn-icon i {
  background-color: #d67e00;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-orange.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-orange.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-orange.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-orange.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-orange.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-orange.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-orange.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-orange.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-orange.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-orange.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold {
  color: #846e20;
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold:hover,
.btn-gold:focus,
.btn-gold:active,
.btn-gold.active,
.open .dropdown-toggle.btn-gold {
  color: #846e20;
  background-color: #fbc70e;
  border-color: #f1bc04;
}
.btn-gold:active,
.btn-gold.active,
.open .dropdown-toggle.btn-gold {
  background-image: none;
}
.btn-gold.disabled,
.btn-gold[disabled],
fieldset[disabled] .btn-gold,
.btn-gold.disabled:hover,
.btn-gold[disabled]:hover,
fieldset[disabled] .btn-gold:hover,
.btn-gold.disabled:focus,
.btn-gold[disabled]:focus,
fieldset[disabled] .btn-gold:focus,
.btn-gold.disabled:active,
.btn-gold[disabled]:active,
fieldset[disabled] .btn-gold:active,
.btn-gold.disabled.active,
.btn-gold[disabled].active,
fieldset[disabled] .btn-gold.active {
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold .badge {
  color: #fcd036;
  background-color: #846e20;
}
.btn-gold > .caret {
  border-top-color: #846e20;
  border-bottom-color: #846e20 !important;
}
.btn-gold.dropdown-toggle {
  border-left-color: #fbc813;
}
.btn-gold.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-gold.btn-icon i {
  background-color: #fbc70e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-gold.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-gold.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-gold.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-gold.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-gold.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-gold.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-gold.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-gold.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-gold.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-gold.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black {
  color: #ffffff;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:hover,
.btn-black:focus,
.btn-black:active,
.btn-black.active,
.open .dropdown-toggle.btn-black {
  color: #ffffff;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:active,
.btn-black.active,
.open .dropdown-toggle.btn-black {
  background-image: none;
}
.btn-black.disabled,
.btn-black[disabled],
fieldset[disabled] .btn-black,
.btn-black.disabled:hover,
.btn-black[disabled]:hover,
fieldset[disabled] .btn-black:hover,
.btn-black.disabled:focus,
.btn-black[disabled]:focus,
fieldset[disabled] .btn-black:focus,
.btn-black.disabled:active,
.btn-black[disabled]:active,
fieldset[disabled] .btn-black:active,
.btn-black.disabled.active,
.btn-black[disabled].active,
fieldset[disabled] .btn-black.active {
  background-color: #000000;
  border-color: #000000;
}
.btn-black .badge {
  color: #000000;
  background-color: #ffffff;
}
.btn-black > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-black.dropdown-toggle {
  border-left-color: #000000;
}
.btn-black.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-black.btn-icon i {
  background-color: #000000;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-black.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-black.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-black.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-black.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-black.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-black.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-black.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-black.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-black.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-black.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white {
  color: #303641;
  background-color: #ffffff;
  border-color: #ffffff;
  border-color: #ebebeb;
}
.btn-white:hover,
.btn-white:focus,
.btn-white:active,
.btn-white.active,
.open .dropdown-toggle.btn-white {
  color: #303641;
  background-color: #ebebeb;
  border-color: #e0e0e0;
}
.btn-white:active,
.btn-white.active,
.open .dropdown-toggle.btn-white {
  background-image: none;
}
.btn-white.disabled,
.btn-white[disabled],
fieldset[disabled] .btn-white,
.btn-white.disabled:hover,
.btn-white[disabled]:hover,
fieldset[disabled] .btn-white:hover,
.btn-white.disabled:focus,
.btn-white[disabled]:focus,
fieldset[disabled] .btn-white:focus,
.btn-white.disabled:active,
.btn-white[disabled]:active,
fieldset[disabled] .btn-white:active,
.btn-white.disabled.active,
.btn-white[disabled].active,
fieldset[disabled] .btn-white.active {
  background-color: #ffffff;
  border-color: #ffffff;
}
.btn-white .badge {
  color: #ffffff;
  background-color: #303641;
}
.btn-white > .caret {
  border-top-color: #303641;
  border-bottom-color: #303641 !important;
}
.btn-white.dropdown-toggle {
  border-left-color: #ededed;
}
.btn-white.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-white.btn-icon i {
  background-color: #ebebeb;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-white.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-white.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-white.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-white.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-white.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-white.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-white.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-white.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-white.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-white.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning {
  color: #ffffff;
  background-color: #fad839;
  border-color: #fad839;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  color: #ffffff;
  background-color: #f9d011;
  border-color: #f0c706;
}
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  background-image: none;
}
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #fad839;
  border-color: #fad839;
}
.btn-warning .badge {
  color: #fad839;
  background-color: #ffffff;
}
.btn-warning > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-warning.dropdown-toggle {
  border-left-color: #f9d116;
}
.btn-warning.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-warning.btn-icon i {
  background-color: #f9d011;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-warning.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-warning.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-warning.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-warning.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-warning.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-warning.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-warning.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-warning.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-warning.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-warning.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger {
  color: #ffffff;
  background-color: #cc2424;
  border-color: #cc2424;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  color: #ffffff;
  background-color: #a91e1e;
  border-color: #981b1b;
}
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  background-image: none;
}
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #cc2424;
  border-color: #cc2424;
}
.btn-danger .badge {
  color: #cc2424;
  background-color: #ffffff;
}
.btn-danger > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-danger.dropdown-toggle {
  border-left-color: #ae1f1f;
}
.btn-danger.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-danger.btn-icon i {
  background-color: #a91e1e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-danger.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-danger.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-danger.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-danger.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-danger.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-danger.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-danger.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-danger.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-danger.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-danger.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success,
.btn-green {
  color: #ffffff;
  background-color: #00a651;
  border-color: #00a651;
}
.btn-success:hover,
.btn-green:hover,
.btn-success:focus,
.btn-green:focus,
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open .dropdown-toggle.btn-success,
.open .dropdown-toggle.btn-green {
  color: #ffffff;
  background-color: #007d3d;
  border-color: #006933;
}
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open .dropdown-toggle.btn-success,
.open .dropdown-toggle.btn-green {
  background-image: none;
}
.btn-success.disabled,
.btn-green.disabled,
.btn-success[disabled],
.btn-green[disabled],
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-green,
.btn-success.disabled:hover,
.btn-green.disabled:hover,
.btn-success[disabled]:hover,
.btn-green[disabled]:hover,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-green:hover,
.btn-success.disabled:focus,
.btn-green.disabled:focus,
.btn-success[disabled]:focus,
.btn-green[disabled]:focus,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-green:focus,
.btn-success.disabled:active,
.btn-green.disabled:active,
.btn-success[disabled]:active,
.btn-green[disabled]:active,
fieldset[disabled] .btn-success:active,
fieldset[disabled] .btn-green:active,
.btn-success.disabled.active,
.btn-green.disabled.active,
.btn-success[disabled].active,
.btn-green[disabled].active,
fieldset[disabled] .btn-success.active,
fieldset[disabled] .btn-green.active {
  background-color: #00a651;
  border-color: #00a651;
}
.btn-success .badge,
.btn-green .badge {
  color: #00a651;
  background-color: #ffffff;
}
.btn-success > .caret,
.btn-green > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-success.dropdown-toggle,
.btn-green.dropdown-toggle {
  border-left-color: #008240;
}
.btn-success.btn-icon,
.btn-green.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-success.btn-icon i,
.btn-green.btn-icon i {
  background-color: #007d3d;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-success.btn-icon.icon-left,
.btn-green.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-success.btn-icon.icon-left i,
.btn-green.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-success.btn-icon.btn-lg,
.btn-green.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-success.btn-icon.btn-lg.icon-left,
.btn-green.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-success.btn-icon.btn-lg i,
.btn-green.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-success.btn-icon.btn-sm,
.btn-green.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-success.btn-icon.btn-sm.icon-left,
.btn-green.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-success.btn-icon.btn-sm i,
.btn-green.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success.btn-icon.btn-xs,
.btn-green.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-success.btn-icon.btn-xs.icon-left,
.btn-green.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-success.btn-icon.btn-xs i,
.btn-green.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info {
  color: #ffffff;
  background-color: #21a9e1;
  border-color: #21a9e1;
}
.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  color: #ffffff;
  background-color: #1a8fbf;
  border-color: #1782ad;
}
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  background-image: none;
}
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #21a9e1;
  border-color: #21a9e1;
}
.btn-info .badge {
  color: #21a9e1;
  background-color: #ffffff;
}
.btn-info > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-info.dropdown-toggle {
  border-left-color: #1a92c4;
}
.btn-info.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-info.btn-icon i {
  background-color: #1a8fbf;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-info.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-info.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-info.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-info.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-info.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-info.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-info.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-info.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-info.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-info.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-link {
  color: #373e4a;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
}
.btn-link,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent;
}
.btn-link:hover,
.btn-link:focus {
  color: #818da2;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #999999;
  text-decoration: none;
}
.btn-lg {
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-sm,
.btn-xs {
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-xs {
  padding: 1px 5px;
}
.btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.invoice {
  margin: 0px 0;
  font-size: 14px;
}
.invoice .invoice-left strong,
.invoice .invoice-right strong {
  color: #303641;
}
.invoice .invoice-left > h3,
.invoice .invoice-right > h3 {
  margin-top: 0;
}
.invoice .invoice-right {
  text-align: right;
}
.invoice .margin {
  margin: 40px 0;
}
.invoice h4 {
  font-weight: bold;
}
@media print {
  .invoice-left {
    float: left !important;
  }
  .invoice-right {
    float: right !important;
    text-align: right !important;
    top: -20px;
    position: relative;
  }
}
@media (max-width: 768px) {
  .invoice .invoice-right {
    text-align: left;
    margin-top: 20px;
  }
  .invoice .margin {
    margin: 15px 0;
  }
  .invoice table {
    margin: 20px 0;
  }
}
.mail-env {
  position: relative;
}
.mail-env:before,
.mail-env:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.mail-env:after {
  clear: both;
}
hr + .mail-env {
  margin-top: -18px;
  border-top: 1px solid #ebebeb;
  margin-left: -20px;
  margin-right: -20px;
}
.mail-env + hr {
  margin-top: 0px;
  position: relative;
  margin-left: -20px;
  margin-right: -20px;
}
.mail-env .mail-sidebar,
.mail-env .mail-body {
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.mail-env .mail-sidebar:before,
.mail-env .mail-body:before,
.mail-env .mail-sidebar:after,
.mail-env .mail-body:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.mail-env .mail-sidebar:after,
.mail-env .mail-body:after {
  clear: both;
}
.mail-env .mail-sidebar-row {
  padding: 20px;
}
.mail-env > .mail-sidebar-row.visible-xs {
  padding-bottom: 0;
}
.mail-env .mail-sidebar {
  width: 22%;
  background: #f9f9f9;
  border-right: 1px solid #ebebeb;
  position: relative;
}
.mail-env .mail-sidebar > h4 {
  padding: 20px;
}
.mail-env .mail-sidebar .mail-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  border-top: 1px solid #ebebeb;
}
.mail-env .mail-sidebar .mail-menu > li {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #ebebeb;
}
.mail-env .mail-sidebar .mail-menu > li a {
  display: block;
  padding: 20px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.mail-env .mail-sidebar .mail-menu > li a .badge {
  font-size: 13px;
}
.mail-env .mail-sidebar .mail-menu > li a .badge-gray {
  background: transparent;
  border: 1px solid #ebebeb;
}
.mail-env .mail-sidebar .mail-menu > li a .badge-roundless {
  display: inline-block;
  width: 14px;
  height: 14px;
  -webkit-border-radius: 2px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 2px;
  -moz-background-clip: padding;
  border-radius: 2px;
  background-clip: padding-box;
}
.mail-env .mail-sidebar .mail-menu > li:hover a {
  background: rgba(255, 255, 255, 0.8);
}
.mail-env .mail-sidebar .mail-menu > li.active a {
  background: #fff;
  font-weight: bold;
}
.mail-env .mail-sidebar .mail-distancer {
  height: 40px;
}
.mail-env .mail-body {
  width: 78%;
  float: right;
  overflow: hidden;
}
.mail-env .mail-body .mail-header {
  padding: 20px;
  border-bottom: 1px solid #ebebeb;
}
.mail-env .mail-body .mail-header:before,
.mail-env .mail-body .mail-header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.mail-env .mail-body .mail-header:after {
  clear: both;
}
.mail-env .mail-body .mail-header .mail-title {
  margin: 0;
  padding: 0;
  font-size: 20px;
  float: left;
  width: 75%;
  padding-right: 20px;
  color: #303641;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mail-env .mail-body .mail-header .mail-title span.count {
  font-weight: normal;
  -webkit-opacity: 0.3;
  -moz-opacity: 0.3;
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.mail-env .mail-body .mail-header .mail-title .label {
  font-size: 9px;
  position: relative;
  top: -4px;
}
.mail-env .mail-body .mail-header .mail-search,
.mail-env .mail-body .mail-header .mail-links {
  float: left;
  text-align: right;
  width: 25%;
}
.mail-env .mail-body .mail-header .mail-search.mail-links > .btn,
.mail-env .mail-body .mail-header .mail-links.mail-links > .btn {
  margin-left: 5px;
  font-size: 11px;
}
.mail-env .mail-body .mail-header .mail-search.mail-links > .btn:first-child,
.mail-env .mail-body .mail-header .mail-links.mail-links > .btn:first-child {
  margin-left: 0;
}
.mail-env .mail-body .mail-header .mail-search .form-control[type="text"],
.mail-env .mail-body .mail-header .mail-links .form-control[type="text"] {
  height: 29px;
}
.mail-env .mail-body .mail-info {
  background: #f3f4f4;
  display: table;
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  border-bottom: 1px solid #ebebeb;
}
.mail-env .mail-body .mail-info:before,
.mail-env .mail-body .mail-info:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.mail-env .mail-body .mail-info:after {
  clear: both;
}
.mail-env .mail-body .mail-info .mail-sender,
.mail-env .mail-body .mail-info .mail-date {
  display: table-cell;
  width: 50%;
  color: #a6a6a6;
  padding: 20px;
}
.mail-env .mail-body .mail-info .mail-sender.mail-sender span,
.mail-env .mail-body .mail-info .mail-date.mail-sender span {
  font-weight: bold;
  color: #ec5956;
}
.mail-env .mail-body .mail-info .mail-sender.mail-sender img,
.mail-env .mail-body .mail-info .mail-date.mail-sender img {
  margin-right: 5px;
  border: 2px solid #ebebeb;
}
.mail-env .mail-body .mail-info .mail-sender.mail-sender .dropdown-menu:after,
.mail-env .mail-body .mail-info .mail-date.mail-sender .dropdown-menu:after {
  position: absolute;
  content: '';
  display: block;
  top: -5px;
  left: 17%;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 0 3px 4px 3px;
  border-color: transparent transparent #cc2424 transparent;
}
.mail-env .mail-body .mail-info .mail-sender.mail-date,
.mail-env .mail-body .mail-info .mail-date.mail-date {
  text-align: right;
}
.mail-env .mail-body .mail-text {
  border-bottom: 1px solid #ebebeb;
  padding: 20px;
}
.mail-env .mail-body .mail-attachments {
  padding: 20px;
  border-bottom: 1px solid #ebebeb;
}
.mail-env .mail-body .mail-attachments h4 {
  margin-bottom: 30px;
  line-height: 1;
}
.mail-env .mail-body .mail-attachments h4 span {
  -webkit-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
  filter: alpha(opacity=40);
}
.mail-env .mail-body .mail-attachments ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.mail-env .mail-body .mail-attachments ul:before,
.mail-env .mail-body .mail-attachments ul:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.mail-env .mail-body .mail-attachments ul:after {
  clear: both;
}
.mail-env .mail-body .mail-attachments ul li {
  float: left;
  margin: 0;
  padding: 0;
  margin-right: 30px;
  margin-bottom: 20px;
}
.mail-env .mail-body .mail-attachments ul li > a {
  display: block;
}
.mail-env .mail-body .mail-attachments ul li > a img {
  position: relative;
  display: block;
  line-height: 1;
  margin-bottom: 10px;
}
.mail-env .mail-body .mail-attachments ul li > a.thumb {
  position: relative;
}
.mail-env .mail-body .mail-attachments ul li > a.thumb:after {
  font-family: 'Entypo';
  content: '\e826';
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  background: #fff;
  width: 40px;
  height: 40px;
  margin-left: -20px;
  margin-top: -20px;
  font-size: 28px;
  text-align: center;
  line-height: 40px;
  vertical-align: text-top;
  color: #fff;
  background: rgba(0, 0, 0, 0.25);
  -webkit-border-radius: 4px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 4px;
  -moz-background-clip: padding;
  border-radius: 4px;
  background-clip: padding-box;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.mail-env .mail-body .mail-attachments ul li > a.thumb:hover:after {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.mail-env .mail-body .mail-attachments ul li > a.thumb.download:after {
  content: '\e82d';
}
.mail-env .mail-body .mail-attachments ul li > a.name span {
  color: #666666;
  float: right;
}
.mail-env .mail-body .mail-attachments ul li .links {
  display: block;
  font-size: 11px;
  color: #666666;
  margin-top: 6px;
}
.mail-env .mail-body .mail-attachments ul li .links a {
  color: #666666;
}
.mail-env .mail-body .mail-reply {
  border-bottom: 1px solid #ebebeb;
  padding: 20px;
}
.mail-env .mail-body .mail-reply .fake-form {
  padding: 20px;
  border: 2px solid #ebebeb;
  min-height: 100px;
  -webkit-border-radius: 4px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 4px;
  -moz-background-clip: padding;
  border-radius: 4px;
  background-clip: padding-box;
}
.mail-env .mail-body .mail-reply .fake-form > div a {
  font-weight: bold;
  color: #ec5956;
}
.mail-env .mail-body .mail-compose {
  padding: 20px;
}
.mail-env .mail-body .mail-compose .form-group {
  position: relative;
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 15px;
}
.mail-env .mail-body .mail-compose .form-group label {
  position: absolute;
  left: 10px;
  top: 7px;
  z-index: 10;
}
.mail-env .mail-body .mail-compose .form-group input {
  border-color: transparent;
  position: relative;
  z-index: 5;
  padding-left: 100px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.mail-env .mail-body .mail-compose .form-group input:focus {
  background: #f9f9f9;
  border-color: #f4f4f4;
}
.mail-env .mail-body .mail-compose .form-group .field-options {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 12;
}
.mail-env .mail-body .mail-compose .form-group .field-options a {
  display: inline-block;
  background: #f3f4f4;
  color: #7e8186;
  padding: 2px 6px;
  margin-left: 4px;
  text-align: center;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.mail-env .mail-body .mail-compose .compose-message-editor {
  padding-top: 5px;
}
.mail-env .mail-body .mail-compose .compose-message-editor textarea {
  height: 400px;
}
.mail-env .mail-body > div:last-child {
  border-bottom: 0;
}
.mail-env .mail-body .mail-table {
  margin-bottom: 0;
}
.mail-env .mail-body .mail-table thead tr th,
.mail-env .mail-body .mail-table tfoot tr th {
  background: #ebebeb;
  border: 0;
  color: #666666;
  vertical-align: middle;
  border-bottom: 0;
}
.mail-env .mail-body .mail-table thead tr th a,
.mail-env .mail-body .mail-table tfoot tr th a {
  color: #666666;
}
.mail-env .mail-body .mail-table thead tr th .mail-select-options,
.mail-env .mail-body .mail-table tfoot tr th .mail-select-options {
  float: left;
  padding-top: 5px;
}
.mail-env .mail-body .mail-table thead tr th .mail-pagination,
.mail-env .mail-body .mail-table tfoot tr th .mail-pagination {
  float: right;
}
.mail-env .mail-body .mail-table thead tr th .mail-pagination span,
.mail-env .mail-body .mail-table tfoot tr th .mail-pagination span {
  color: rgba(102, 102, 102, 0.5);
}
.mail-env .mail-body .mail-table thead tr th .mail-pagination .btn-group,
.mail-env .mail-body .mail-table tfoot tr th .mail-pagination .btn-group {
  margin-left: 5px;
}
.mail-env .mail-body .mail-table thead tr th .mail-pagination .btn-group .btn-sm,
.mail-env .mail-body .mail-table tfoot tr th .mail-pagination .btn-group .btn-sm {
  padding-left: 7px;
  padding-right: 7px;
}
.mail-env .mail-body .mail-table tbody tr td {
  border-top: 0;
  border-bottom: 1px solid #ebebeb;
  height: 50px;
  vertical-align: middle;
  background: #f9f9f9;
}
.mail-env .mail-body .mail-table tbody tr td.col-name,
.mail-env .mail-body .mail-table tbody tr td.col-subject {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mail-env .mail-body .mail-table tbody tr td.col-name.col-subject a,
.mail-env .mail-body .mail-table tbody tr td.col-subject.col-subject a {
  color: #8c8c8c;
}
.mail-env .mail-body .mail-table tbody tr td.col-name.col-subject a.label,
.mail-env .mail-body .mail-table tbody tr td.col-subject.col-subject a.label {
  color: #fff;
}
.mail-env .mail-body .mail-table tbody tr td.col-name {
  width: 25%;
}
.mail-env .mail-body .mail-table tbody tr td.col-subject a {
  display: inline-block;
  max-width: 400px;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mail-env .mail-body .mail-table tbody tr td.col-options {
  text-align: right;
}
.mail-env .mail-body .mail-table tbody tr td.col-options a {
  color: #999999;
}
.mail-env .mail-body .mail-table tbody tr td.col-time {
  width: 12%;
  text-align: right;
  color: rgba(102, 102, 102, 0.5);
}
.mail-env .mail-body .mail-table tbody tr td .star {
  display: inline-block;
  color: #dddddd;
  margin-right: 5px;
  font-size: 14px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.mail-env .mail-body .mail-table tbody tr td .star.stared,
.mail-env .mail-body .mail-table tbody tr td .star.starred {
  color: #ff9600;
}
.mail-env .mail-body .mail-table tbody tr td > .label:first-child {
  margin-left: 0;
}
.mail-env .mail-body .mail-table tbody tr.unread > td {
  background-color: #fff !important;
}
.mail-env .mail-body .mail-table tbody tr.unread > td.col-name a {
  font-weight: bold;
}
.mail-env .mail-body .mail-table tbody tr.highlight > td {
  background-color: #ffffcc !important;
}
.mail-env .mail-body .mail-table > thead > tr > td:first-child,
.mail-env .mail-body .mail-table > tbody > tr > td:first-child,
.mail-env .mail-body .mail-table > tfoot > tr > td:first-child,
.mail-env .mail-body .mail-table > thead > tr > th:first-child,
.mail-env .mail-body .mail-table > tbody > tr > th:first-child,
.mail-env .mail-body .mail-table > tfoot > tr > th:first-child {
  padding-left: 20px;
}
.mail-env .mail-body .mail-table > thead > tr > td:last-child,
.mail-env .mail-body .mail-table > tbody > tr > td:last-child,
.mail-env .mail-body .mail-table > tfoot > tr > td:last-child,
.mail-env .mail-body .mail-table > thead > tr > th:last-child,
.mail-env .mail-body .mail-table > tbody > tr > th:last-child,
.mail-env .mail-body .mail-table > tfoot > tr > th:last-child {
  padding-right: 20px;
}
.mail-env .mail-body .mail-table > thead > tr > thX:first-child,
.mail-env .mail-body .mail-table > tbody > tr > thX:first-child,
.mail-env .mail-body .mail-table > tfoot > tr > thX:first-child {
  position: relative;
}
.mail-env .mail-body .mail-table > thead > tr > thX:first-child:before,
.mail-env .mail-body .mail-table > tbody > tr > thX:first-child:before,
.mail-env .mail-body .mail-table > tfoot > tr > thX:first-child:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  bottom: 0;
  width: 20px;
  background: #FFF;
}
.mail-env .mail-body .mail-table > thead > tr > thX:last-child,
.mail-env .mail-body .mail-table > tbody > tr > thX:last-child,
.mail-env .mail-body .mail-table > tfoot > tr > thX:last-child {
  position: relative;
}
.mail-env .mail-body .mail-table > thead > tr > thX:last-child:before,
.mail-env .mail-body .mail-table > tbody > tr > thX:last-child:before,
.mail-env .mail-body .mail-table > tfoot > tr > thX:last-child:before {
  position: absolute;
  content: '';
  top: 0;
  right: 0;
  bottom: 0;
  width: 20px;
  background: #FFF;
}
.mail-env .mail-body .mail-table > tbody > tr:nth-child(odd) > td {
  background: #fbfbfb;
}
.mail-env .mail-body .mail-table .neon-cb-replacement {
  top: 2px;
}
.mail-env .mail-body .mail-table .neon-cb-replacement .cb-wrapper {
  background: #fff;
  border-color: #dfdfdf;
}
.mail-env .mail-body .mail-table .neon-cb-replacement .cb-wrapper .checked {
  background: #dfdfdf;
}
.mail-env.right-sidebar .mail-sidebar {
  border-left: 1px solid #ebebeb;
  border-right: 0;
}
.mail-env.right-sidebar .mail-body {
  float: left;
}
@media (max-width: 999px) {
  .mail-env .mail-body .mail-table tbody tr td.col-subject a {
    max-width: 280px;
  }
}
@media (max-width: 959px) {
  .mail-env .mail-body .mail-header div.mail-title {
    width: 100%;
    white-space: normal;
  }
  .mail-env .mail-body .mail-header .mail-links {
    float: none;
    width: 100%;
    text-align: left;
    clear: left;
    padding-top: 10px;
  }
  .mail-env .mail-body .mail-info {
    display: block;
  }
  .mail-env .mail-body .mail-info .mail-sender,
  .mail-env .mail-body .mail-info .mail-date {
    display: block;
    width: 100%;
  }
  .mail-env .mail-body .mail-info .mail-sender.mail-sender,
  .mail-env .mail-body .mail-info .mail-date.mail-sender {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebebeb;
  }
  .mail-env .mail-body .mail-info .mail-sender.mail-date,
  .mail-env .mail-body .mail-info .mail-date.mail-date {
    text-align: left;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .mail-env .mail-body .mail-compose .compose-message-editor textarea {
    height: 300px;
  }
}
@media (max-width: 768px) {
  .mail-env .mail-sidebar {
    width: 30.8%;
  }
  .mail-env .mail-body {
    width: 69.2%;
  }
  .mail-env .mail-body .mail-table tbody tr td.col-options {
    display: none;
  }
  .mail-env .mail-body .mail-table tbody tr td.col-subject a {
    max-width: 160px;
  }
  .mail-env .mail-body .mail-compose .compose-message-editor textarea {
    height: 240px;
  }
}
@media (max-width: 767px) {
  .mail-env .mail-sidebar,
  .mail-env .mail-body {
    width: 100%;
    float: none;
  }
  .mail-env .mail-body .mail-header .mail-title,
  .mail-env .mail-body .mail-header .mail-search,
  .mail-env .mail-body .mail-header .mail-links {
    float: none;
    width: 100%;
  }
  .mail-env .mail-body .mail-header .mail-title.mail-search,
  .mail-env .mail-body .mail-header .mail-search.mail-search,
  .mail-env .mail-body .mail-header .mail-links.mail-search,
  .mail-env .mail-body .mail-header .mail-title.mail-links,
  .mail-env .mail-body .mail-header .mail-search.mail-links,
  .mail-env .mail-body .mail-header .mail-links.mail-links {
    margin-top: 20px;
  }
  .mail-env .mail-body .mail-header .mail-links {
    padding-top: 0;
  }
}
@media (max-width: 449px) {
  .mail-env .mail-body .mail-table tbody tr td.col-subject a {
    max-width: 40px;
  }
  .mail-env .mail-body .mail-table tbody tr > td.col-name a.star {
    display: none;
  }
  .mail-env .mail-body .mail-table > thead > tr > td:last-child,
  .mail-env .mail-body .mail-table > tbody > tr > td:last-child,
  .mail-env .mail-body .mail-table > tfoot > tr > td:last-child,
  .mail-env .mail-body .mail-table > thead > tr > th:last-child,
  .mail-env .mail-body .mail-table > tbody > tr > th:last-child,
  .mail-env .mail-body .mail-table > tfoot > tr > th:last-child {
    padding-right: 10px;
  }
  .mail-env .mail-body .mail-table > thead > tr > td:first-child,
  .mail-env .mail-body .mail-table > tbody > tr > td:first-child,
  .mail-env .mail-body .mail-table > tfoot > tr > td:first-child,
  .mail-env .mail-body .mail-table > thead > tr > th:first-child,
  .mail-env .mail-body .mail-table > tbody > tr > th:first-child,
  .mail-env .mail-body .mail-table > tfoot > tr > th:first-child {
    padding-left: 10px;
  }
}
.login-page {
  background: #303641;
}
.login-page .login-content {
  position: relative;
  width: 320px;
  margin: 0 auto;
  text-align: center;
  padding: 20px 0;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-content a {
  color: #949494;
}
.login-page .login-header {
  position: relative;
  background: #373e4a;
  padding: 100px 0;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-header .description {
  font-size: 13px;
  margin-top: 20px;
  margin-bottom: 0;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-header.login-caret:after {
  position: absolute;
  content: '';
  left: 50%;
  bottom: 0;
  margin-left: -12.5px;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 13px 12.5px 0 12.5px;
  border-color: #373e4a transparent transparent transparent;
  bottom: -13px;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-form {
  position: relative;
  padding-top: 60px;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-form .form-group {
  margin-bottom: 20px;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-form .form-group .input-group {
  background: #373e4a;
  border: 1px solid #373e4a;
  padding-top: 6px;
  padding-bottom: 6px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.login-page .login-form .form-group .input-group.focused {
  border-color: #626f85;
  border-color: rgba(98, 111, 133, 0.5);
}
.login-page .login-form .form-group .input-group.validate-has-error {
  border-color: #ec5956;
  border-color: rgba(236, 89, 86, 0.5);
}
.login-page .login-form .form-group .input-group.validate-has-error .error {
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -8px;
  font-size: 10px;
}
.login-page .login-form .form-group .input-group .input-group-addon,
.login-page .login-form .form-group .input-group .form-control {
  background: transparent;
  border: 0;
}
.login-page .login-form .form-group .input-group .input-group-addon {
  position: relative;
}
.login-page .login-form .form-group .input-group .input-group-addon:after {
  position: absolute;
  display: block;
  content: '';
  right: 0;
  top: 0;
  height: 100%;
  width: 1px;
  background: #454a54;
  -webkit-transform: scaleY(.56);
  -moz-transform: scaleY(.56);
  -o-transform: scaleY(.56);
  -ms-transform: scaleY(.56);
  transform: scaleY(.56);
}
.login-page .login-form .form-group .input-group .form-control {
  color: #ffffff;
}
.login-page .login-form .form-group .input-group .form-control:focus {
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.login-page .login-form .form-group.lockscreen-input {
  margin-top: -155px;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb {
  position: relative;
  display: inline-block;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb img {
  border: 5px solid #373e4a;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb .lockscreen-progress-indicator {
  display: block;
  position: absolute;
  margin: 5px;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  color: #ffffff;
  font-size: 19px;
  text-align: center;
  line-height: 145px;
  background: rgba(0, 0, 0, 0.3);
  visibility: hidden;
  -webkit-border-radius: 50%;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 50%;
  -moz-background-clip: padding;
  border-radius: 50%;
  background-clip: padding-box;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb canvas {
  position: absolute;
  left: 0;
  top: 0;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-details {
  position: relative;
  padding-top: 5px;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-details h4 {
  color: #ffffff;
}
.login-page .login-form .form-group.lockscreen-input .lockscreen-details span {
  display: block;
  padding-bottom: 5px;
}
.login-page .login-form .form-group .btn-login {
  border: 1px solid #454a54;
  text-align: left;
  padding: 15px 20px;
  font-size: 14px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.login-page .login-form .form-group .btn-login i {
  float: right;
}
.login-page .login-form .form-group .btn-login:hover,
.login-page .login-form .form-group .btn-login:active {
  background: #373e4a;
}
.login-page .login-form .form-group .facebook-button,
.login-page .login-form .form-group .twitter-button,
.login-page .login-form .form-group .google-button {
  text-align: left;
  color: #fff;
  background-color: #3b5998;
  font-size: 12px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.login-page .login-form .form-group .facebook-button i,
.login-page .login-form .form-group .twitter-button i,
.login-page .login-form .form-group .google-button i {
  background-color: #385490;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.login-page .login-form .form-group .facebook-button:hover,
.login-page .login-form .form-group .twitter-button:hover,
.login-page .login-form .form-group .google-button:hover {
  background-color: rgba(59, 89, 152, 0.8);
}
.login-page .login-form .form-group .facebook-button:hover i,
.login-page .login-form .form-group .twitter-button:hover i,
.login-page .login-form .form-group .google-button:hover i {
  background-color: #31497e;
}
.login-page .login-form .form-group .facebook-button.twitter-button,
.login-page .login-form .form-group .twitter-button.twitter-button,
.login-page .login-form .form-group .google-button.twitter-button {
  background-color: #4099ff;
}
.login-page .login-form .form-group .facebook-button.twitter-button i,
.login-page .login-form .form-group .twitter-button.twitter-button i,
.login-page .login-form .form-group .google-button.twitter-button i {
  background-color: #0d7eff;
}
.login-page .login-form .form-group .facebook-button.twitter-button:hover,
.login-page .login-form .form-group .twitter-button.twitter-button:hover,
.login-page .login-form .form-group .google-button.twitter-button:hover {
  background-color: rgba(64, 153, 255, 0.8);
}
.login-page .login-form .form-group .facebook-button.twitter-button:hover i,
.login-page .login-form .form-group .twitter-button.twitter-button:hover i,
.login-page .login-form .form-group .google-button.twitter-button:hover i {
  background-color: #0071f3;
}
.login-page .login-form .form-group .facebook-button.google-button,
.login-page .login-form .form-group .twitter-button.google-button,
.login-page .login-form .form-group .google-button.google-button {
  background-color: #d34836;
}
.login-page .login-form .form-group .facebook-button.google-button i,
.login-page .login-form .form-group .twitter-button.google-button i,
.login-page .login-form .form-group .google-button.google-button i {
  background-color: #b03626;
}
.login-page .login-form .form-group .facebook-button.google-button:hover,
.login-page .login-form .form-group .twitter-button.google-button:hover,
.login-page .login-form .form-group .google-button.google-button:hover {
  background-color: rgba(211, 72, 54, 0.8);
}
.login-page .login-form .form-group .facebook-button.google-button:hover i,
.login-page .login-form .form-group .twitter-button.google-button:hover i,
.login-page .login-form .form-group .google-button.google-button:hover i {
  background-color: #9b3022;
}
.login-page .login-bottom-links {
  padding-top: 40px;
  padding-bottom: 30px;
}
.login-page .login-bottom-links a {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.login-page .login-bottom-links a:hover {
  color: #aeaeae;
}
.login-page .login-bottom-links .link {
  font-size: 14px;
  display: inline-block;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.7);
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-bottom-links .link:hover {
  color: #ffffff;
}
.login-page .login-progressbar {
  height: 0px;
  width: 100%;
  overflow: hidden;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-progressbar div {
  width: 0%;
  -moz-transition: 700ms all cubic-bezier(0.770, 0.000, 0.175, 1.000);
  -o-transition: 700ms all cubic-bezier(0.770, 0.000, 0.175, 1.000);
  -webkit-transition: 700ms all cubic-bezier(0.770, 0.000, 0.175, 1.000);
  transition: 700ms all cubic-bezier(0.770, 0.000, 0.175, 1.000);
}
.login-page .login-progressbar-indicator {
  position: absolute;
  width: 100%;
  text-align: center;
  top: 100%;
  margin-top: 80px;
  visibility: hidden;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(100px) scale(0.2);
  -moz-transform: translateY(100px) scale(0.2);
  -o-transform: translateY(100px) scale(0.2);
  -ms-transform: translateY(100px) scale(0.2);
  transform: translateY(100px) scale(0.2);
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page .login-progressbar-indicator h3 {
  color: #fff;
  margin: 0;
  margin-bottom: 10px;
  font-size: 20px;
}
.login-page.logging-in {
  overflow: hidden;
}
.login-page.logging-in .login-header {
  padding-top: 170px;
  padding-bottom: 30px;
}
.login-page.logging-in .login-header.login-caret:after {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.login-page.logging-in .login-header .description {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.login-page.logging-in .login-form form,
.login-page.logging-in .login-form .link {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(-200px) scale(0.8);
  -moz-transform: translateY(-200px) scale(0.8);
  -o-transform: translateY(-200px) scale(0.8);
  -ms-transform: translateY(-200px) scale(0.8);
  transform: translateY(-200px) scale(0.8);
}
.login-page.logging-in .login-progressbar {
  background: #515b6d;
  height: 2px;
}
.login-page.logging-in .login-progressbar div {
  background: #de6c65;
  height: 2px;
}
.login-page.logging-in .login-progressbar-indicator {
  visibility: visible;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px) scale(1);
  -moz-transform: translateY(0px) scale(1);
  -o-transform: translateY(0px) scale(1);
  -ms-transform: translateY(0px) scale(1);
  transform: translateY(0px) scale(1);
}
.login-page.logging-in-lockscreen .login-form .form-group.lockscreen-input .lockscreen-thumb .lockscreen-progress-indicator {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}
.login-page.logging-in-lockscreen .login-form .form-group:nth-child(n + 2),
.login-page.logging-in-lockscreen .login-form .link {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateY(-50px) scale(0.5);
  -moz-transform: translateY(-50px) scale(0.5);
  -o-transform: translateY(-50px) scale(0.5);
  -ms-transform: translateY(-50px) scale(0.5);
  transform: translateY(-50px) scale(0.5);
}
.login-page.login-form-fall .login-form {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  top: -100px;
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
}
.login-page.login-form-fall .login-form .form-group {
  -moz-transition: all 550ms ease-in-out;
  -o-transition: all 550ms ease-in-out;
  -webkit-transition: all 550ms ease-in-out;
  transition: all 550ms ease-in-out;
  -moz-transition-delay: 250ms;
  -o-transition-delay: 250ms;
  -webkit-transition-delay: 250ms;
  transition-delay: 250ms;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.login-page.login-form-fall .login-form .form-group:nth-child(2) {
  -moz-transition-delay: 350ms;
  -o-transition-delay: 350ms;
  -webkit-transition-delay: 350ms;
  transition-delay: 350ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(3) {
  -moz-transition-delay: 450ms;
  -o-transition-delay: 450ms;
  -webkit-transition-delay: 450ms;
  transition-delay: 450ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(4) {
  -moz-transition-delay: 550ms;
  -o-transition-delay: 550ms;
  -webkit-transition-delay: 550ms;
  transition-delay: 550ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(5) {
  -moz-transition-delay: 650ms;
  -o-transition-delay: 650ms;
  -webkit-transition-delay: 650ms;
  transition-delay: 650ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(6) {
  -moz-transition-delay: 750ms;
  -o-transition-delay: 750ms;
  -webkit-transition-delay: 750ms;
  transition-delay: 750ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(8) {
  -moz-transition-delay: 850ms;
  -o-transition-delay: 850ms;
  -webkit-transition-delay: 850ms;
  transition-delay: 850ms;
}
.login-page.login-form-fall .login-form .form-group:nth-child(9) {
  -moz-transition-delay: 950ms;
  -o-transition-delay: 950ms;
  -webkit-transition-delay: 950ms;
  transition-delay: 950ms;
}
.login-page.login-form-fall-init .login-form {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
  top: 0;
}
.login-page.login-form-fall-init .login-form .form-group {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.login-page .form-steps .step {
  display: none;
}
.login-page .form-steps .step.current {
  display: block;
}
.login-page .form-register-success,
.login-page .form-forgotpassword-success,
.login-page .form-login-error {
  display: none;
  background: #00a651;
  color: #ffffff;
  padding: 10px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  margin-bottom: 30px;
  overflow: hidden;
}
.login-page .form-register-success.visible,
.login-page .form-forgotpassword-success.visible,
.login-page .form-login-error.visible {
  display: block;
}
.login-page .form-register-success i,
.login-page .form-forgotpassword-success i,
.login-page .form-login-error i {
  position: relative;
  font-size: 25px;
  background: #008d45;
  display: block;
  text-align: center;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-top: -10px;
  margin-left: -10px;
  margin-right: -10px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.login-page .form-register-success h3,
.login-page .form-forgotpassword-success h3,
.login-page .form-login-error h3 {
  font-size: 15px;
  color: #ffffff;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 5px;
}
.login-page .form-register-success p,
.login-page .form-forgotpassword-success p,
.login-page .form-login-error p {
  font-size: 11px;
  margin: 0;
}
.login-page .form-register-success.form-login-error,
.login-page .form-forgotpassword-success.form-login-error,
.login-page .form-login-error.form-login-error {
  padding: 0;
  background: #cc2424;
}
.login-page .form-register-success.form-login-error h3,
.login-page .form-forgotpassword-success.form-login-error h3,
.login-page .form-login-error.form-login-error h3 {
  background: #b62020;
  padding: 10px;
  margin: 0;
  margin-bottom: 5px;
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.login-page .form-register-success.form-login-error p,
.login-page .form-forgotpassword-success.form-login-error p,
.login-page .form-login-error.form-login-error p {
  padding: 10px;
}
@media (max-width: 991px) {
  .login-page .login-header {
    padding: 20px 0;
  }
  .login-page .login-form {
    padding-top: 30px;
  }
  .login-page .login-form .form-group.lockscreen-input {
    margin-top: 0;
  }
  .login-page.logging-in .login-header {
    padding-top: 34px;
    padding-bottom: 6px;
  }
  .login-page.logging-in .login-progressbar-indicator {
    margin-top: 60px;
  }
  .login-page.logging-in .login-form form,
  .login-page.logging-in .login-form .link {
    -webkit-transform: translateY(-100px) scale(0.6);
    -moz-transform: translateY(-100px) scale(0.6);
    -o-transform: translateY(-100px) scale(0.6);
    -ms-transform: translateY(-100px) scale(0.6);
    transform: translateY(-100px) scale(0.6);
  }
}
@media (max-width: 350px) {
  .login-page .login-content {
    width: 280px;
  }
}
.tocify {
  border: 1px solid #f2f2f4;
  background: #fff;
  overflow: hidden;
  margin-top: 20px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.tocify.fixed {
  position: fixed;
  top: 10px;
}
.tocify li a:hover {
  background: #fafafb;
  color: #303641;
}
.tocify > ul {
  border-bottom: 1px solid #f2f2f4;
}
.tocify > ul ul li a {
  padding-left: 30px;
}
.tocify > ul ul {
  border-top: 1px solid #f2f2f4;
  background: #fdfdfd;
}
.tocify > ul:last-child {
  border-bottom: 0;
}
.tocify .tocify-item.active > a {
  background: #f8f8f8;
  font-weight: bold;
}
.tocify-extend-page {
  display: none !important;
}
@media (max-width: 991px) {
  .tocify.fixed {
    position: static;
    top: 0px;
  }
}
.calendar-env {
  position: relative;
}
.calendar-env:before,
.calendar-env:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.calendar-env:after {
  clear: both;
}
hr + .calendar-env {
  margin-top: -18px;
  border-top: 1px solid #ebebeb;
  margin-left: -20px;
  margin-right: -20px;
}
.calendar-env + hr {
  margin-top: 0px;
  position: relative;
  margin-left: -20px;
  margin-right: -20px;
}
.calendar-env .calendar-sidebar,
.calendar-env .calendar-body {
  float: left;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.calendar-env .calendar-sidebar:before,
.calendar-env .calendar-body:before,
.calendar-env .calendar-sidebar:after,
.calendar-env .calendar-body:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.calendar-env .calendar-sidebar:after,
.calendar-env .calendar-body:after {
  clear: both;
}
.calendar-env .calendar-sidebar-row {
  padding: 20px;
}
.calendar-env > .calendar-sidebar-row.visible-xs {
  padding-bottom: 0;
}
.calendar-env .calendar-sidebar {
  width: 22%;
  background: #f9f9f9;
  border-right: 1px solid #ebebeb;
  position: relative;
}
.calendar-env .calendar-sidebar > h4 {
  padding: 20px;
}
.calendar-env .calendar-sidebar #add_event_form .input-group {
  background: #fff;
}
.calendar-env .calendar-sidebar .calendar-distancer {
  height: 40px;
}
.calendar-env .calendar-sidebar .events-list {
  border-top: 1px solid #ebebeb;
  padding-top: 20px;
  list-style: none;
  margin: 0;
  padding: 20px;
}
.calendar-env .calendar-sidebar .events-list li a {
  display: block;
  padding: 6px 8px;
  margin-bottom: 4px;
  -moz-transition: background 250ms ease-in-out, color 250ms ease-in-out;
  -o-transition: background 250ms ease-in-out, color 250ms ease-in-out;
  -webkit-transition: background 250ms ease-in-out, color 250ms ease-in-out;
  transition: background 250ms ease-in-out, color 250ms ease-in-out;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  background: #ee4749;
  color: #ffffff;
}
.calendar-env .calendar-sidebar .events-list li a:hover {
  background: #ec3032;
}
.calendar-env .calendar-sidebar .events-list li a.color-blue {
  background: #21a9e1;
  color: #ffffff;
}
.calendar-env .calendar-sidebar .events-list li a.color-blue:hover {
  background: #1c99cd;
}
.calendar-env .calendar-sidebar .events-list li a.color-green {
  background: #00a651;
  color: #ffffff;
}
.calendar-env .calendar-sidebar .events-list li a.color-green:hover {
  background: #008d45;
}
.calendar-env .calendar-sidebar .events-list li a.color-primary {
  background: #303641;
  color: #ffffff;
}
.calendar-env .calendar-sidebar .events-list li a.color-primary:hover {
  background: #252a32;
}
.calendar-env .calendar-sidebar .events-list li a.color-orange {
  background: #ffae2f;
  color: #ffffff;
}
.calendar-env .calendar-sidebar .events-list li a.color-orange:hover {
  background: #ffa416;
}
.calendar-env .calendar-body {
  width: 100%;
  float: right;
}
.calendar-env .calendar-body .fc-header {
  border-bottom: 1px solid #ebebeb;
}
.calendar-env .calendar-body .fc-header h2,
.calendar-env .calendar-body .fc-header h3 {
  margin: 0;
  padding: 0;
}
.calendar-env .calendar-body .fc-header .fc-header-left {
  padding: 20px;
}
.calendar-env .calendar-body .fc-header .fc-header-center {
  display: none;
}
.calendar-env .calendar-body .fc-header .fc-header-right {
  padding: 20px;
  text-align: right;
}
.calendar-env .calendar-body .fc-header .fc-button {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  color: #303641;
  background-color: #ffffff;
  border-color: #ffffff;
  border-color: #ebebeb;
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
  border-right-width: 0;
}
.calendar-env .calendar-body .fc-header .fc-button:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.calendar-env .calendar-body .fc-header .fc-button:hover,
.calendar-env .calendar-body .fc-header .fc-button:focus {
  color: #303641;
  text-decoration: none;
  outline: none;
}
.calendar-env .calendar-body .fc-header .fc-button:active,
.calendar-env .calendar-body .fc-header .fc-button.active {
  outline: none;
  background-image: none;
  -moz-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -webkit-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -moz-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
}
.calendar-env .calendar-body .fc-header .fc-button.disabled,
.calendar-env .calendar-body .fc-header .fc-button[disabled],
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button {
  cursor: not-allowed;
  pointer-events: none;
  -webkit-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon {
  position: relative;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon i {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
}
.calendar-env .calendar-body .fc-header .fc-button:hover,
.calendar-env .calendar-body .fc-header .fc-button:focus,
.calendar-env .calendar-body .fc-header .fc-button:active,
.calendar-env .calendar-body .fc-header .fc-button.active,
.open .dropdown-toggle.calendar-env .calendar-body .fc-header .fc-button {
  color: #303641;
  background-color: #ebebeb;
  border-color: #e0e0e0;
}
.calendar-env .calendar-body .fc-header .fc-button:active,
.calendar-env .calendar-body .fc-header .fc-button.active,
.open .dropdown-toggle.calendar-env .calendar-body .fc-header .fc-button {
  background-image: none;
}
.calendar-env .calendar-body .fc-header .fc-button.disabled,
.calendar-env .calendar-body .fc-header .fc-button[disabled],
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button,
.calendar-env .calendar-body .fc-header .fc-button.disabled:hover,
.calendar-env .calendar-body .fc-header .fc-button[disabled]:hover,
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button:hover,
.calendar-env .calendar-body .fc-header .fc-button.disabled:focus,
.calendar-env .calendar-body .fc-header .fc-button[disabled]:focus,
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button:focus,
.calendar-env .calendar-body .fc-header .fc-button.disabled:active,
.calendar-env .calendar-body .fc-header .fc-button[disabled]:active,
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button:active,
.calendar-env .calendar-body .fc-header .fc-button.disabled.active,
.calendar-env .calendar-body .fc-header .fc-button[disabled].active,
fieldset[disabled] .calendar-env .calendar-body .fc-header .fc-button.active {
  background-color: #ffffff;
  border-color: #ffffff;
}
.calendar-env .calendar-body .fc-header .fc-button .badge {
  color: #ffffff;
  background-color: #303641;
}
.calendar-env .calendar-body .fc-header .fc-button > .caret {
  border-top-color: #303641;
  border-bottom-color: #303641 !important;
}
.calendar-env .calendar-body .fc-header .fc-button.dropdown-toggle {
  border-left-color: #ededed;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon i {
  background-color: #ebebeb;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-lg {
  padding-right: 55px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-sm {
  padding-right: 36px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-xs {
  padding-right: 32px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.calendar-env .calendar-body .fc-header .fc-button.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.calendar-env .calendar-body .fc-header .fc-button.fc-corner-left {
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-header .fc-button.fc-corner-right {
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
  border-right-width: 1px;
}
.calendar-env .calendar-body .fc-header .fc-button.fc-corner-left.fc-corner-right {
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-header .fc-button.fc-state-active {
  background: #f5f5f6;
}
.calendar-env .calendar-body .fc-header .fc-header-space {
  width: 10px;
  display: inline-block;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-cell-overlay {
  background: rgba(255, 255, 204, 0.5);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event {
  background: #000;
  padding: 2px 4px;
  margin-top: 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  background: #ee4749;
  color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event:hover {
  background: #ec3032;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-blue {
  background: #21a9e1;
  color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-blue:hover {
  background: #1c99cd;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-green {
  background: #00a651;
  color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-green:hover {
  background: #008d45;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-primary {
  background: #303641;
  color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-primary:hover {
  background: #252a32;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-orange {
  background: #ffae2f;
  color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view .fc-event.color-orange:hover {
  background: #ffa416;
}
.calendar-env .calendar-body .fc-content .fc-view table thead tr th {
  text-align: center;
  padding: 5px 0;
  border-bottom: 1px solid #ebebeb;
  background: #f5f5f6;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day {
  vertical-align: text-top;
  text-align: right;
  border-bottom: 1px solid #ebebeb;
  padding-right: 10px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day .fc-day-number {
  margin-top: 5px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day:hover {
  background-color: rgba(250, 250, 250, 0.68);
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number {
  color: #ffffff;
  background-color: #21a9e1;
  border-color: #21a9e1;
  display: inline-block;
  padding: 5px 8px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:hover,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:focus,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:active,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.active,
.open .dropdown-toggle.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number {
  color: #ffffff;
  background-color: #1a8fbf;
  border-color: #1782ad;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:active,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.active,
.open .dropdown-toggle.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number {
  background-image: none;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.disabled,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number[disabled],
fieldset[disabled] .calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.disabled:hover,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number[disabled]:hover,
fieldset[disabled] .calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:hover,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.disabled:focus,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number[disabled]:focus,
fieldset[disabled] .calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:focus,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.disabled:active,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number[disabled]:active,
fieldset[disabled] .calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number:active,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.disabled.active,
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number[disabled].active,
fieldset[disabled] .calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.active {
  background-color: #21a9e1;
  border-color: #21a9e1;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number .badge {
  color: #21a9e1;
  background-color: #ffffff;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.dropdown-toggle {
  border-left-color: #1a92c4;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon i {
  background-color: #1a8fbf;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-lg {
  padding-right: 55px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-sm {
  padding-right: 36px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-xs {
  padding-right: 32px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.calendar-env .calendar-body .fc-content .fc-view table tbody tr td.fc-day.fc-today .fc-day-number.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-days,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-days {
  border-bottom: 1px solid #e6e6e6;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-days + div,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-days + div {
  margin-top: 1px;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-days th,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-days th,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-days td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-days td {
  width: 1% !important;
  color: #666666;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-allday,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-allday {
  background: #fafafa;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-allday td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-allday td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-allday th,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-allday th {
  padding-top: 6px;
  padding-bottom: 6px;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-allday tbody tr .fc-agenda-axis,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-allday tbody tr .fc-agenda-axis {
  width: 60px !important;
  vertical-align: middle;
  text-align: right;
  color: #666666;
  border-right: 1px solid #e8e8e8;
  padding-right: 6px;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-divider,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-divider {
  height: 2px;
  background: #ebebeb;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr th,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr th {
  border-bottom: 1px dotted #ebebeb;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr td.fc-agenda-axis,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr td.fc-agenda-axis,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr th.fc-agenda-axis,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr th.fc-agenda-axis {
  width: 60px !important;
  text-align: right;
  color: #666666;
  border-right: 1px solid #e8e8e8;
  padding-right: 6px;
}
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr.fc-minor td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr.fc-minor td,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaWeek .fc-agenda-slots tr.fc-minor th,
.calendar-env .calendar-body .fc-content .fc-view.fc-view-agendaDay .fc-agenda-slots tr.fc-minor th {
  border-bottom-color: #e6e6e6;
}
.calendar-env .calendar-body > div:last-child {
  border-bottom: 0;
}
.calendar-env.right-sidebar .calendar-sidebar {
  border-left: 1px solid #ebebeb;
  border-right: 0;
}
.calendar-env.right-sidebar .calendar-body {
  float: left;
}
@media (max-width: 959px) {
  .calendar-env .calendar-body .calendar-header div.calendar-title {
    width: 100%;
    white-space: normal;
  }
  .calendar-env .calendar-body .calendar-header .calendar-links {
    float: none;
    width: 100%;
    text-align: left;
    clear: left;
    padding-top: 10px;
  }
  .calendar-env .calendar-body .calendar-info {
    display: block;
  }
  .calendar-env .calendar-body .calendar-info .calendar-sender,
  .calendar-env .calendar-body .calendar-info .calendar-date {
    display: block;
    width: 100%;
  }
  .calendar-env .calendar-body .calendar-info .calendar-sender.calendar-sender,
  .calendar-env .calendar-body .calendar-info .calendar-date.calendar-sender {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebebeb;
  }
  .calendar-env .calendar-body .calendar-info .calendar-sender.calendar-date,
  .calendar-env .calendar-body .calendar-info .calendar-date.calendar-date {
    text-align: left;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .calendar-env .calendar-body .calendar-compose .compose-message-editor textarea {
    height: 300px;
  }
}
@media (max-width: 768px) {
  .calendar-env .calendar-sidebar {
    width: 30.8%;
  }
  .calendar-env .calendar-body {
    width: 69.2%;
  }
  .calendar-env .calendar-body .calendar-compose .compose-message-editor textarea {
    height: 240px;
  }
}
@media (max-width: 767px) {
  .calendar-env .calendar-sidebar,
  .calendar-env .calendar-body {
    width: 100%;
    float: none;
  }
  .calendar-env .calendar-body .calendar-header .calendar-title,
  .calendar-env .calendar-body .calendar-header .calendar-search,
  .calendar-env .calendar-body .calendar-header .calendar-links {
    float: none;
    width: 100%;
  }
  .calendar-env .calendar-body .calendar-header .calendar-title.calendar-search,
  .calendar-env .calendar-body .calendar-header .calendar-search.calendar-search,
  .calendar-env .calendar-body .calendar-header .calendar-links.calendar-search,
  .calendar-env .calendar-body .calendar-header .calendar-title.calendar-links,
  .calendar-env .calendar-body .calendar-header .calendar-search.calendar-links,
  .calendar-env .calendar-body .calendar-header .calendar-links.calendar-links {
    margin-top: 20px;
  }
  .calendar-env .calendar-body .calendar-header .calendar-links {
    padding-top: 0;
  }
  .fc-header {
    display: block;
  }
  .fc-header .fc-header-left,
  .fc-header .fc-header-center,
  .fc-header .fc-header-right,
  .fc-header tr,
  .fc-header tbody {
    display: block;
    text-align: center !important;
  }
  .fc-header .fc-header-right {
    text-align: center !important;
    padding-bottom: 10px;
  }
}
.notes-env {
  position: relative;
}
.notes-env .notes-header {
  display: table;
  width: 100%;
  vertical-align: middle;
}
.notes-env .notes-header:before,
.notes-env .notes-header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.notes-env .notes-header:after {
  clear: both;
}
.notes-env .notes-header > h2,
.notes-env .notes-header > .right {
  display: table-cell;
  vertical-align: middle;
  width: 50%;
  margin: 0;
  padding-bottom: 20px;
}
.notes-env .notes-header > .right {
  text-align: right;
}
.notes-env .notes-list {
  position: relative;
  background: #f0ecdb;
  margin-left: -20px;
  margin-right: -20px;
  border: 1px solid #ebebeb;
  border-left: 0;
  border-right: 0;
}
.notes-env .notes-list:before,
.notes-env .notes-list:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.notes-env .notes-list:after {
  clear: both;
}
.notes-env .notes-list .write-pad,
.notes-env .notes-list .list-of-notes {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.notes-env .notes-list .list-of-notes {
  float: right;
  width: 26%;
  list-style: none;
  margin: 0;
  padding: 0;
  padding-bottom: 30px;
}
.notes-env .notes-list .list-of-notes li {
  position: relative;
  padding: 30px;
  padding-bottom: 0;
}
.notes-env .notes-list .list-of-notes li a {
  display: block;
  border: 1px solid #e9e4ca;
  background: #fffced;
  padding: 20px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.notes-env .notes-list .list-of-notes li a strong,
.notes-env .notes-list .list-of-notes li a span {
  display: block;
}
.notes-env .notes-list .list-of-notes li a strong + strong,
.notes-env .notes-list .list-of-notes li a span + strong,
.notes-env .notes-list .list-of-notes li a strong + span,
.notes-env .notes-list .list-of-notes li a span + span {
  padding-top: 8px;
}
.notes-env .notes-list .list-of-notes li a strong {
  word-break: break-all;
}
.notes-env .notes-list .list-of-notes li a span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.notes-env .notes-list .list-of-notes li a em {
  float: right;
}
.notes-env .notes-list .list-of-notes li a:hover {
  background: #eae6ce;
  border-color: #dad2a6;
}
.notes-env .notes-list .list-of-notes li a:hover span,
.notes-env .notes-list .list-of-notes li a:hover strong {
  color: #737881;
}
.notes-env .notes-list .list-of-notes li .note-close {
  position: absolute;
  right: 30px;
  top: 30px;
  border: none;
  background: none;
  outline: none;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.notes-env .notes-list .list-of-notes li:hover .note-close {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.notes-env .notes-list .list-of-notes li .content {
  display: none;
}
.notes-env .notes-list .list-of-notes li.current a {
  background: #fff;
}
.notes-env .notes-list .list-of-notes li + .no-notes {
  display: none;
}
.notes-env .notes-list .write-pad {
  float: left;
  width: 74%;
  background: #fffced;
  position: relative;
}
.notes-env .notes-list .write-pad:after {
  display: block;
  content: '';
  position: absolute;
  left: 95px;
  top: 0;
  bottom: 0;
  background: #f9d4d1;
  width: 1px;
}
.notes-env .notes-list .write-pad textarea {
  background: transparent;
  border: none;
  background: url(../images/notes-lines.png) left top;
  background-attachment: local;
  min-height: 780px;
  font: 14px/52px "Noto Sans", sans-serif, serif;
  max-height: 1500px;
  padding-left: 125px;
  padding-right: 50px;
}
.notes-env .notes-list .write-pad textarea::-webkit-scrollbar {
  width: 5px;
}
.notes-env .notes-list .write-pad textarea::-webkit-scrollbar-track {
  width: 5px;
  background-color: #e8e3c9;
}
.notes-env .notes-list .write-pad textarea::-webkit-scrollbar-thumb {
  background-color: #bdbdbd;
}
.notes-env + footer.main {
  border-top: 0;
  padding-top: 10px;
}
@media (max-width: 992px) {
  .notes-env .notes-list .list-of-notes {
    width: 35%;
  }
  .notes-env .notes-list .write-pad {
    width: 65%;
  }
  .notes-env .notes-list .write-pad textarea {
    padding-left: 50px;
  }
  .notes-env .notes-list .write-pad:after {
    left: 35px;
  }
}
@media (max-width: 768px) {
  body .notes-env .notes-list .list-of-notes,
  body .notes-env .notes-list .write-pad {
    width: 100%;
    float: none;
  }
  body .notes-env .notes-list .list-of-notes {
    padding-bottom: 10px;
  }
  body .notes-env .notes-list .list-of-notes li {
    padding: 15px;
    padding-top: 10px;
    padding-bottom: 0;
  }
  body .notes-env .notes-list .list-of-notes li a span {
    display: none;
  }
  body .notes-env .notes-list .write-pad textarea {
    min-height: 400px;
    max-height: 600px;
  }
}
.gallery-env:before,
.gallery-env:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.gallery-env:after {
  clear: both;
}
.gallery-env article.album {
  border: 1px solid #ebebeb;
  margin-bottom: 30px;
  -moz-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.04);
  -webkit-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.04);
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.04);
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.gallery-env article.album header {
  position: relative;
}
.gallery-env article.album header img {
  line-height: 1;
  margin: 0;
  width: 100%;
  display: block;
  max-width: 100%;
  height: auto;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.gallery-env article.album header .album-options {
  position: absolute;
  display: block;
  right: 10px;
  bottom: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5px 7px;
  font-size: 11px;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.gallery-env article.album header:hover .album-options {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.gallery-env article.album .album-info {
  padding: 20px;
}
.gallery-env article.album .album-info h3 {
  font-size: 18px;
  margin: 0;
}
.gallery-env article.album .album-info p {
  margin: 0;
  margin-top: 10px;
  color: #80858e;
}
.gallery-env article.album footer {
  border-top: 1px solid #f0f0f0;
}
.gallery-env article.album footer:before,
.gallery-env article.album footer:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.gallery-env article.album footer:after {
  clear: both;
}
.gallery-env article.album footer .album-images-count,
.gallery-env article.album footer .album-options {
  padding: 12px 15px;
  float: left;
  color: #8d929a;
}
.gallery-env article.album footer .album-images-count a,
.gallery-env article.album footer .album-options a {
  color: #8d929a;
  display: inline-block;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.gallery-env article.album footer .album-images-count a + a,
.gallery-env article.album footer .album-options a + a {
  margin-left: 10px;
}
.gallery-env article.album footer .album-options {
  float: right;
  border-left: 1px solid #f0f0f0;
  padding: 7px 10px;
}
.gallery-env article.album footer .album-options a {
  padding: 4px 5px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.gallery-env article.album footer .album-options a:hover {
  background: #f5f5f5;
  color: #43464b;
}
.gallery-env article.image-thumb {
  margin-bottom: 20px;
}
.gallery-env article.image-thumb .image {
  position: relative;
}
.gallery-env article.image-thumb .image img {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 3px;
  width: 100%;
}
.gallery-env article.image-thumb .image-options {
  position: absolute;
  top: -10px;
  right: 8px;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transform: scale(0, );
  -ms-transform: scale(0, );
  transform: scale(0, );
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -o-transform-origin: 50% 0%;
  -ms-transform-origin: 50% 0%;
  -webkit-transform-origin: 50% 0%;
  -moz-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.gallery-env article.image-thumb .image-options a {
  display: inline-block;
  line-height: 1;
  margin-left: 2px;
  background: #737881;
  color: #FFF;
  width: 24px;
  height: 24px;
  line-height: 24px;
  -webkit-border-radius: 12px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 12px;
  -moz-background-clip: padding;
  border-radius: 12px;
  background-clip: padding-box;
  text-align: center;
  -moz-box-shadow: 0 2px 5px rgba(0,0,0,.2);
  -webkit-box-shadow: 0 2px 5px rgba(0,0,0,.2);
  box-shadow: 0 2px 5px rgba(0,0,0,.2);
}
.gallery-env article.image-thumb .image-options a.delete {
  background: #dd1f26;
}
.gallery-env article.image-thumb:hover .image-options {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transform: scale(1, );
  -ms-transform: scale(1, );
  transform: scale(1, );
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.gallery-env div[data-tag] {
  -moz-transition: opacity 350ms ease-in-out;
  -o-transition: opacity 350ms ease-in-out;
  -webkit-transition: opacity 350ms ease-in-out;
  transition: opacity 350ms ease-in-out;
}
.gallery-env div[data-tag].not-in-filter {
  -webkit-opacity: 0.3;
  -moz-opacity: 0.3;
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.gallery-env div[data-tag].no-animation {
  -moz-transition: none;
  -o-transition: none;
  -webkit-transition: none;
  transition: none;
}
.gallery-env .image-categories {
  margin-bottom: 20px;
  background: #fafafb;
  position: relative;
  margin-top: -17px;
  padding: 10px;
}
.gallery-env .image-categories span {
  color: #80858e;
}
.gallery-env .image-categories a {
  display: inline-block;
  margin: 0 5px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.gallery-env .image-categories a.active {
  font-weight: bold;
  color: #4f5259;
}
.modal .croppable-image {
  border: 4px solid rgba(235, 235, 235, 0.5);
}
.modal h4 + .croppable-image {
  margin-top: 5px;
}
.jcrop-keymgr {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.gallery-image-edit-env {
  border-bottom: 1px solid #ebebeb;
  position: relative;
  margin-left: -1px;
  margin-right: -1px;
  margin-top: -1px;
}
.gallery-image-edit-env img {
  width: 100%;
}
.gallery-image-edit-env .close {
  position: absolute;
  right: 10px;
  top: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-weight: bold;
  padding: 5px 10px;
  display: block;
  z-index: 1000;
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  filter: alpha(opacity=70);
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.gallery-image-edit-env .close:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.gallery-image-edit-env .jcrop-holder {
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.profile-env > header {
  position: relative;
  z-index: 20;
  margin-top: 30px;
}
.profile-env > header .profile-picture {
  position: relative;
}
.profile-env > header .profile-picture img {
  float: right;
  -moz-box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.9);
  -webkit-box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.9);
  box-shadow: 0px 0px 0px 5px rgba(255, 255, 255, 0.9);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env > header .profile-picture:hover img {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.profile-env > header .profile-info-sections {
  margin: 0;
  padding: 0;
  margin-top: 15px;
  padding-left: 0;
  list-style: none;
}
.profile-env > header .profile-info-sections > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
.profile-env > header .profile-info-sections > li:first-child {
  padding-left: 0;
}
.profile-env > header .profile-info-sections .profile-name strong,
.profile-env > header .profile-info-sections .profile-name span {
  display: block;
}
.profile-env > header .profile-info-sections .profile-name strong {
  font-size: 18px;
  font-weight: normal;
}
.profile-env > header .profile-info-sections .profile-name span {
  font-size: 12px;
  color: #bbbbbb;
}
.profile-env > header .profile-info-sections .profile-name span a {
  color: #bbbbbb;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env > header .profile-info-sections .profile-name span a:hover {
  color: #888888;
}
.profile-env > header .profile-info-sections .profile-name .user-status {
  position: relative;
  display: inline-block;
  background: #575d67;
  top: -2px;
  margin-left: 5px;
  width: 6px;
  height: 6px;
  -webkit-border-radius: 6px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 6px;
  -moz-background-clip: padding;
  border-radius: 6px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env > header .profile-info-sections .profile-name .user-status.is-online {
  background-color: #06b53c;
}
.profile-env > header .profile-info-sections .profile-name .user-status.is-offline {
  background-color: #575d67;
}
.profile-env > header .profile-info-sections .profile-name .user-status.is-idle {
  background-color: #f7d227;
}
.profile-env > header .profile-info-sections .profile-name .user-status.is-busy {
  background-color: #ee4749;
}
.profile-env > header .profile-info-sections .profile-stat h3 {
  font-size: 18px;
  margin-bottom: 5px;
}
.profile-env > header .profile-info-sections .profile-stat span {
  color: #bbbbbb;
}
.profile-env > header .profile-info-sections .profile-stat span a {
  color: #bbbbbb;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env > header .profile-info-sections .profile-stat span a:hover {
  color: #888888;
}
.profile-env > header .profile-info-sections > li {
  padding: 0 40px;
  position: relative;
}
.profile-env > header .profile-info-sections > li + li:after {
  content: '';
  display: block;
  position: absolute;
  top: 15px;
  bottom: 0;
  left: 0;
  width: 1px;
  background: #ebebeb;
  margin: 8px 0;
}
.profile-env > header .profile-info-sections > li:first-child {
  padding-left: 0;
}
.profile-env > header .profile-buttons {
  margin-top: 35px;
}
.profile-env > header .profile-buttons a {
  margin: 0 4px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section {
  position: relative;
  z-index: 10;
}
.profile-env section.profile-info-tabs {
  position: relative;
  background: #f1f1f1;
  margin-left: -20px;
  margin-right: -20px;
  padding: 20px;
  margin-top: -20px;
  margin-bottom: 30px;
}
.profile-env section.profile-info-tabs .user-details {
  padding-left: 0;
  list-style: none;
}
.profile-env section.profile-info-tabs .user-details li {
  margin-bottom: 10px;
}
.profile-env section.profile-info-tabs .user-details li a {
  color: #a0a0a0;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-info-tabs .user-details li a:hover {
  color: #606060;
}
.profile-env section.profile-info-tabs .user-details li a:hover span {
  color: #e72c28;
}
.profile-env section.profile-info-tabs .user-details li a i {
  margin-right: 5px;
}
.profile-env section.profile-info-tabs .user-details li a span {
  color: #ec5956;
  font-weight: normal;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-info-tabs .nav-tabs {
  position: relative;
  margin-bottom: -20px;
  border-bottom: 0;
}
.profile-env section.profile-info-tabs .nav-tabs > li:first-child a {
  margin-left: 0;
}
.profile-env section.profile-info-tabs .nav-tabs li {
  margin-bottom: 0;
}
.profile-env section.profile-info-tabs .nav-tabs li a {
  border: none;
  padding: 10px 35px;
  font-size: 13px;
  background: #e1e1e1;
  margin-right: 10px;
}
.profile-env section.profile-info-tabs .nav-tabs li.active a {
  background: #fff;
}
.profile-env section.profile-feed {
  margin-bottom: 15px;
  padding-left: 20px;
  padding-right: 20px;
}
.profile-env section.profile-feed .profile-post-form {
  border: 1px solid #ebebeb;
  margin-bottom: 30px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.profile-env section.profile-feed .profile-post-form .form-control {
  border: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin: 0;
  background: #fff;
  min-height: 80px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.profile-env section.profile-feed .profile-post-form .form-options {
  background: #f3f3f3;
  border-top: 1px solid #ebebeb;
  padding: 10px;
}
.profile-env section.profile-feed .profile-post-form .form-options:before,
.profile-env section.profile-feed .profile-post-form .form-options:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.profile-env section.profile-feed .profile-post-form .form-options:after {
  clear: both;
}
.profile-env section.profile-feed .profile-post-form .form-options .post-type {
  float: left;
  padding-top: 6px;
}
.profile-env section.profile-feed .profile-post-form .form-options .post-type a {
  margin-left: 10px;
  font-size: 13px;
  color: #aaaaaa;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-feed .profile-post-form .form-options .post-type a:hover {
  color: #303641;
}
.profile-env section.profile-feed .profile-post-form .form-options .post-submit {
  float: right;
}
.profile-env section.profile-feed .profile-post-form .form-options .post-submit .btn {
  padding-left: 20px;
  padding-right: 20px;
}
.profile-env section.profile-feed .profile-stories article.story {
  margin: 30px 0;
}
.profile-env section.profile-feed .profile-stories article.story:before,
.profile-env section.profile-feed .profile-stories article.story:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.profile-env section.profile-feed .profile-stories article.story:after {
  clear: both;
}
.profile-env section.profile-feed .profile-stories article.story .user-thumb {
  float: left;
  width: 8%;
}
.profile-env section.profile-feed .profile-stories article.story .user-thumb a img {
  -moz-box-shadow: 0px 0px 0px 3px rgba(0, 0, 0, 0.04);
  -webkit-box-shadow: 0px 0px 0px 3px rgba(0, 0, 0, 0.04);
  box-shadow: 0px 0px 0px 3px rgba(0, 0, 0, 0.04);
}
.profile-env section.profile-feed .profile-stories article.story .story-content {
  float: right;
  width: 92%;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header {
  display: block;
  margin-bottom: 10px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header:before,
.profile-env section.profile-feed .profile-stories article.story .story-content header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.profile-env section.profile-feed .profile-stories article.story .story-content header:after {
  clear: both;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header .publisher {
  float: left;
  color: #9b9fa6;
  font-size: 14px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header .publisher a {
  color: #303641;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header .publisher em {
  display: block;
  font-style: normal;
  font-size: 12px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content header .story-type {
  float: right;
}
.profile-env section.profile-feed .profile-stories article.story .story-content .story-main-content {
  font-size: 13px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content .story-main-content p {
  font-size: 13px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer {
  margin-top: 15px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .liked i {
  color: #ff4e50;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer > a {
  margin-right: 30px;
  display: inline-block;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer > a span {
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 30px;
  border-top: 1px solid #ebebeb;
  padding-top: 20px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li {
  display: table;
  vertical-align: top;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li:before,
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li:after {
  clear: both;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li + li {
  margin-top: 15px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-thumb,
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content {
  display: table-cell;
  vertical-align: top;
  width: 100%;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-thumb {
  width: 1%;
  padding-right: 20px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content {
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 15px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-name {
  font-weight: bold;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta {
  font-size: 11px;
  margin-top: 15px;
  color: #9b9fa6;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta a {
  color: #8d929a;
  margin-right: 5px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta a + a {
  margin-left: 5px;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta a i {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta a:hover {
  color: #737881;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li .user-comment-content .user-comment-meta a:hover i {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li.comment-form .user-comment-content {
  position: relative;
  border-bottom: 0;
  padding-bottom: 0;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li.comment-form .user-comment-content .form-control {
  background: #eeeeee;
  border: 0;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li.comment-form .user-comment-content .btn {
  position: absolute;
  right: 5px;
  top: 5px;
  border: 0;
  background: transparent;
  color: #737881;
  font-size: 13px;
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  filter: alpha(opacity=70);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-env section.profile-feed .profile-stories article.story .story-content footer .comments li.comment-form .user-comment-content .btn:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.profile-env section.profile-feed .profile-stories article.story .story-content hr {
  margin-top: 40px;
}
@media (max-width: 992px) {
  .profile-env > header .profile-picture img {
    width: 90%;
  }
  .profile-env > header .profile-buttons {
    margin-top: 18px;
  }
  .profile-env > header .profile-info-sections .profile-name strong,
  .profile-env > header .profile-info-sections .profile-stat h3 {
    font-size: 16px;
  }
  .profile-env > header .profile-info-sections {
    margin-top: 0;
  }
  .profile-env > header .profile-info-sections > li {
    padding: 0 20px;
  }
  .profile-env section.profile-info-tabs .nav-tabs li a {
    padding-left: 25px;
    padding-right: 25px;
  }
  .profile-env section.profile-feed .profile-stories article.story .user-thumb {
    width: 10%;
  }
  .profile-env section.profile-feed .profile-stories article.story .story-content {
    width: 90%;
  }
}
@media (max-width: 768px) {
  .profile-env section.profile-info-tabs {
    margin-top: 30px;
  }
  .profile-env > header .profile-picture img {
    float: none;
  }
  .profile-env > header .profile-buttons a {
    margin-bottom: 5px;
  }
}
@media (max-width: 601px) {
  .profile-env > header .profile-info-sections {
    margin-bottom: 10px;
  }
  .profile-env > header .profile-info-sections li {
    padding: 15px;
  }
  .profile-env > header .profile-info-sections > li:first-child {
    padding-left: 0;
  }
  .profile-env > header .profile-buttons {
    margin-top: 0;
  }
  .profile-env > header .profile-picture {
    text-align: center;
    display: block;
  }
  .profile-env > header .profile-picture img {
    width: auto;
    float: none;
    display: inline-block;
    margin-bottom: 15px;
  }
  .profile-env section.profile-feed .profile-stories article.story .user-thumb {
    width: 18%;
  }
  .profile-env section.profile-feed .profile-stories article.story .story-content {
    width: 82%;
  }
  .profile-env section.profile-info-tabs .nav-tabs li a {
    padding-left: 15px;
    padding-right: 15px;
    margin-right: 5px;
    font-size: 12px;
  }
  .profile-env section.profile-feed {
    padding: 0;
  }
  .profile-env .col-sm-7,
  .profile-env .col-sm-3 {
    text-align: center;
  }
  .profile-env .col-sm-7 .profile-info-sections,
  .profile-env .col-sm-3 .profile-info-sections,
  .profile-env .col-sm-7 .profile-buttons,
  .profile-env .col-sm-3 .profile-buttons {
    display: inline-block;
  }
  .profile-env > header .profile-info-sections > li + li:after {
    margin: 18px 0;
  }
}
.map-checkin {
  border: 5px solid rgba(235, 235, 235, 0.2);
  -moz-box-shadow: 0px 0px 0px 1px #ebebeb;
  -webkit-box-shadow: 0px 0px 0px 1px #ebebeb;
  box-shadow: 0px 0px 0px 1px #ebebeb;
  background: #fff !important;
}
.page-body .main-content .cbp_tmtimeline:before {
  background: #f5f5f6;
  width: 5px;
  margin-left: -6px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmtime > span {
  color: #111;
  font-size: 15px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmtime > span:first-child {
  font-weight: bold;
  margin-bottom: 2px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmtime > span:last-child {
  color: #303641;
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  filter: alpha(opacity=80);
  font-size: 12px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmtime > span.large {
  font-size: 17px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmtime > span.hidden + span {
  margin-top: 8px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon {
  background: #ffffff;
  color: #d2d2d2;
  -moz-box-shadow: 0px 0px 0px 5px #f5f5f6;
  -webkit-box-shadow: 0px 0px 0px 5px #f5f5f6;
  box-shadow: 0px 0px 0px 5px #f5f5f6;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-primary {
  background-color: #303641;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-secondary {
  background-color: #ee4749;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-success {
  background-color: #00a651;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-info {
  background-color: #21a9e1;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-warning {
  background-color: #fad839;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmicon.bg-danger {
  background-color: #cc2424;
  color: #ffffff;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel {
  background: #f5f5f6;
  color: #737881;
  margin-bottom: 10px;
  padding: 1.7em;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel h2,
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel p {
  color: #737881;
  /*font-family: "Noto Sans", sans-serif;*/
  font-family: 'Open Sans', sans-serif;
  font-size: 12px;
  margin: 0;
  line-height: 1.42857143;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel p + p {
  margin-top: 15px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel h2 {
  font-size: 16px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel h2 a {
  color: #303641;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel h2 span {
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel:after {
  border-right-color: #f5f5f6;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel.empty {
  background: 0;
  padding: 9px 0;
  margin-bottom: 70px;
}
.page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel.empty:after {
  visibility: hidden;
}
@media screen and (max-width: 47.2em) {
  .page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel:after {
    border-right-color: transparent;
    border-bottom-color: #f5f5f6;
    left: 10px;
  }
  .page-body .main-content .cbp_tmtimeline > li .large {
    font-weight: bold;
    font-size: 16px !important;
  }
  .page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel.empty {
    background: #f5f5f6;
    padding: 1.7em;
  }
  .page-body .main-content .cbp_tmtimeline > li .cbp_tmlabel.empty:after {
    visibility: visible;
  }
}
.timeline-centered {
  position: relative;
  margin-bottom: 30px;
}
.timeline-centered:before,
.timeline-centered:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.timeline-centered:after {
  clear: both;
}
.timeline-centered:before {
  content: '';
  position: absolute;
  display: block;
  width: 4px;
  background: #f5f5f6;
  left: 50%;
  top: 20px;
  bottom: 20px;
  margin-left: -4px;
}
.timeline-centered .timeline-entry {
  position: relative;
  width: 50%;
  float: right;
  margin-bottom: 70px;
  clear: both;
}
.timeline-centered .timeline-entry:before,
.timeline-centered .timeline-entry:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.timeline-centered .timeline-entry:after {
  clear: both;
}
.timeline-centered .timeline-entry.begin {
  margin-bottom: 0;
}
.timeline-centered .timeline-entry.left-aligned {
  float: left;
}
.timeline-centered .timeline-entry.left-aligned .timeline-entry-inner {
  margin-left: 0;
  margin-right: -18px;
}
.timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-time {
  left: auto;
  right: -100px;
  text-align: left;
}
.timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-icon {
  float: right;
}
.timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label {
  margin-left: 0;
  margin-right: 70px;
}
.timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label:after {
  left: auto;
  right: 0;
  margin-left: 0;
  margin-right: -9px;
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.timeline-centered .timeline-entry .timeline-entry-inner {
  position: relative;
  margin-left: -22px;
}
.timeline-centered .timeline-entry .timeline-entry-inner:before,
.timeline-centered .timeline-entry .timeline-entry-inner:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.timeline-centered .timeline-entry .timeline-entry-inner:after {
  clear: both;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-time {
  position: absolute;
  left: -100px;
  text-align: right;
  padding: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-time > span {
  display: block;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-time > span:first-child {
  font-size: 15px;
  font-weight: bold;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-time > span:last-child {
  font-size: 12px;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon {
  background: #fff;
  color: #737881;
  display: block;
  width: 40px;
  height: 40px;
  -webkit-border-radius: 20px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 20px;
  -moz-background-clip: padding;
  border-radius: 20px;
  background-clip: padding-box;
  text-align: center;
  -moz-box-shadow: 0px 0px 0px 5px #f5f5f6;
  -webkit-box-shadow: 0px 0px 0px 5px #f5f5f6;
  box-shadow: 0px 0px 0px 5px #f5f5f6;
  line-height: 40px;
  font-size: 15px;
  float: left;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-primary {
  background-color: #303641;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-secondary {
  background-color: #ee4749;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-success {
  background-color: #00a651;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-info {
  background-color: #21a9e1;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-warning {
  background-color: #fad839;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-danger {
  background-color: #cc2424;
  color: #ffffff;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label {
  position: relative;
  background: #f5f5f6;
  padding: 1.7em;
  margin-left: 70px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label:after {
  content: '';
  display: block;
  position: absolute;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 9px 9px 9px 0;
  border-color: transparent #f5f5f6 transparent transparent;
  left: 0;
  top: 10px;
  margin-left: -9px;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label h2,
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label p {
  color: #737881;
  /*font-family: "Noto Sans", sans-serif;*/
  font-family: 'Open Sans', sans-serif;
  font-size: 12px;
  margin: 0;
  line-height: 1.42857143;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label p + p {
  margin-top: 15px;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label h2 {
  font-size: 16px;
  margin-bottom: 10px;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label h2 a {
  color: #303641;
}
.timeline-centered .timeline-entry .timeline-entry-inner .timeline-label h2 span {
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
@media screen and (max-width: 47.2em) {
  .timeline-centered {
    margin-top: 20px;
  }
  .timeline-centered:before {
    left: 22px;
  }
  .timeline-centered .timeline-entry,
  .timeline-centered .timeline-entry.left-aligned {
    width: 100%;
    float: none;
  }
  .timeline-centered .timeline-entry .timeline-entry-inner,
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner {
    margin-left: 0;
    margin-right: 0;
  }
  .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time,
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-time {
    left: 60px;
    right: auto;
    top: -40px;
    width: auto;
  }
  .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time span,
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-time span {
    display: inline-block;
  }
  .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time span + span,
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-time span + span {
    margin-left: 10px;
  }
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-icon {
    float: left;
  }
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label {
    margin-left: 70px;
    margin-right: 0;
  }
  .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label:after {
    left: 0;
    right: auto;
    margin-left: -9px;
    margin-right: 0;
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.member-entry {
  border: 1px solid #ebebeb;
  padding: 15px;
  margin-top: 15px;
  margin-bottom: 30px;
  -moz-box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.02);
  -webkit-box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.02);
  box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.02);
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.member-entry:before,
.member-entry:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.member-entry:after {
  clear: both;
}
.member-entry:hover {
  background: rgba(235, 235, 235, 0.3);
  -moz-box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.06);
  -webkit-box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.06);
  box-shadow: 1px 1px 1px rgba(0, 1, 1, 0.06);
}
.member-entry .member-img,
.member-entry .member-details {
  float: left;
}
.member-entry .member-img {
  position: relative;
  display: block;
  width: 10%;
}
.member-entry .member-img img {
  width: 100%;
  display: block;
  max-width: 100%;
  height: auto;
}
.member-entry .member-img i {
  position: absolute;
  display: block;
  left: 50%;
  top: 50%;
  margin-top: -12.5px;
  margin-left: -12.5px;
  color: #FFF;
  font-size: 25px;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: scale(0.5);
  -moz-transform: scale(0.5);
  -o-transform: scale(0.5);
  -ms-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform: scale(0.5, );
  -ms-transform: scale(0.5, );
  transform: scale(0.5, );
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.member-entry .member-img:hover i {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transform: scale(1, );
  -ms-transform: scale(1, );
  transform: scale(1, );
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.member-entry .member-details {
  width: 89.9%;
}
.member-entry .member-details h4 {
  font-size: 18px;
  margin-left: 20px;
}
.member-entry .member-details h4 a {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.member-entry .member-details .info-list {
  margin-left: 5px;
}
.member-entry .member-details .info-list > div {
  margin-top: 5px;
  font-size: 13px;
}
.member-entry .member-details .info-list > div a {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.member-entry .member-details .info-list > div i {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.member-entry .member-details .info-list > div:hover i {
  color: #4f5259;
}
@media screen and (max-width: 768px) {
  .member-entry .member-img {
    width: 18%;
  }
  .member-entry .member-details {
    width: 81.9%;
  }
  .member-entry .member-details h4 {
    margin-top: 0;
  }
}
@media screen and (max-width: 480px) {
  .member-entry .member-img {
    width: 100%;
    float: none;
    text-align: center;
    position: relative;
    background: #f8f8f8;
    margin-bottom: 15px;
    -webkit-border-radius: 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px;
    -moz-background-clip: padding;
    border-radius: 3px;
    background-clip: padding-box;
  }
  .member-entry .member-img img {
    width: auto;
    display: inline-block;
    -webkit-border-radius: 0;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 0;
    -moz-background-clip: padding;
    border-radius: 0;
    background-clip: padding-box;
  }
  .member-entry .member-details {
    width: 100%;
    float: none;
  }
  .member-entry .member-details h4,
  .member-entry .member-details .info-list {
    margin-left: 0;
  }
  .member-entry .member-details h4 > div,
  .member-entry .member-details .info-list > div {
    padding: 0;
  }
  .member-entry .member-details .info-list > div {
    margin-top: 10px;
  }
}
.comments-env .comment-filters {
  margin: 15px 0;
  margin-bottom: 30px;
}
.comments-env .comment-filters > a {
  position: relative;
  display: inline-block;
  color: #93979e;
  margin: 0 15px;
  font-size: 13px;
  font-weight: 400;
}
.comments-env .comment-filters > a.current {
  font-weight: bold;
}
.comments-env .comment-filters > a:after {
  content: '';
  display: block;
  position: absolute;
  right: -18px;
  width: 1px;
  height: 12px;
  background: #ebebeb;
  top: 3px;
}
.comments-env .comment-filters > a:first-child {
  margin-left: 0;
}
.comments-env .comment-filters > a:last-child {
  margin-right: 0;
}
.comments-env .comment-filters > a:last-child:after {
  display: none;
}
.comments-env .filtering .selectboxit-btn {
  height: 38px;
}
.comments-env .filtering .selectboxit-btn span {
  height: 38px;
  line-height: 38px;
}
.comments-env .filtering .search-form-full .form-control {
  margin-top: 0;
}
.comments-env .filtering .search-form-contaner {
  max-width: 300px;
  width: 32%;
  float: right;
}
.comments-env .filtering .search-form-contaner + .pagination-container {
  margin-right: 20px;
}
.comments-env .filtering .pagination-container {
  float: right;
}
.comments-env .filtering .pagination-container .pagination {
  margin: 0;
}
.comments-env .filtering .pagination-container .pagination a {
  position: relative;
  padding-top: 9px;
  padding-bottom: 10px;
  margin-top: -2px;
}
.comments-env .filtering .pagination-container + .search-form-contaner {
  margin-right: 20px;
}
.comments-env .panel-title h4 {
  padding-top: 5px;
  padding-bottom: 5px;
}
.comments-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: block;
  position: relative;
}
.comments-list > li {
  border-bottom: 1px solid #ebebeb;
  padding: 15px;
}
.comments-list > li:before,
.comments-list > li:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.comments-list > li:after {
  clear: both;
}
.comments-list > li:nth-child(even) {
  background: #f5f5f6;
}
.comments-list > li .comment-checkbox {
  float: left;
  width: 3%;
  padding-top: 3px;
}
.comments-list > li .comment-details {
  float: right;
  width: 96.9%;
}
.comments-list > li .comment-details a {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.comments-list > li .comment-details .comment-head {
  font-size: 13px;
}
.comments-list > li .comment-details .comment-head a {
  font-weight: bold;
}
.comments-list > li .comment-details .comment-text {
  margin-top: 15px;
}
.comments-list > li .comment-details .comment-footer {
  border-top: 1px solid #ebebeb;
  margin-top: 15px;
  padding-top: 15px;
}
.comments-list > li .comment-details .comment-footer:before,
.comments-list > li .comment-details .comment-footer:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.comments-list > li .comment-details .comment-footer:after {
  clear: both;
}
.comments-list > li .comment-details .comment-footer .comment-time {
  float: right;
  color: #80858e;
}
.comments-list > li .comment-details .comment-footer .action-links a {
  font-weight: bold;
  display: inline-block;
  margin-right: 10px;
}
.comments-list > li .comment-details .comment-footer .action-links .approve {
  color: #00a651;
}
.comments-list > li .comment-details .comment-footer .action-links .delete {
  color: #cc2424;
}
.comments-list > li:last-child {
  border-bottom: 0;
}
@media screen and (max-width: 768px) {
  .comments-list > li .comment-checkbox {
    width: 5%;
  }
  .comments-list > li .comment-details {
    width: 94.9%;
  }
  .comments-env .filtering .search-form-contaner {
    float: left;
  }
  .search-and-pagination {
    margin-top: 20px;
  }
}
@media screen and (max-width: 480px) {
  .comments-env .search-and-pagination .pagination-container,
  .comments-env .search-and-pagination .search-form-contaner {
    float: none !important;
    text-align: left;
  }
  .comments-env .search-and-pagination .search-form-contaner {
    margin-right: 0;
    width: 100%;
    max-width: 1000px;
  }
  .comments-env .search-and-pagination .search-form-contaner .search-form-full {
    margin-top: 10px;
  }
  .comments-list > li .comment-checkbox {
    width: 8%;
  }
  .comments-list > li .comment-details {
    width: 91.9%;
  }
  .comments-list > li .comment-details .comment-footer .action-links {
    float: none;
    width: 100%;
  }
  .comments-list > li .comment-details .comment-footer .action-links a {
    display: block;
  }
}
/**
 * Nestable
 */
.dd {
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
  list-style: none;
}
.dd-list {
  display: block;
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.dd-list .dd-list {
  padding-left: 30px;
}
.dd-collapsed .dd-list {
  display: none;
}
.dd-item,
.dd-empty,
.dd-placeholder {
  display: block;
  position: relative;
  margin: 0;
  padding: 0;
  min-height: 20px;
  font-size: 13px;
  line-height: 20px;
}
.dd-handle,
.dd-content {
  display: block;
  margin: 0px 0;
  padding: 5px 10px;
  background: #fafafa;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #ebebeb;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
dd-content,
.dd-content:hover {
  background: #fff;
}
.dd-item > button {
  display: block;
  position: relative;
  cursor: pointer;
  float: left;
  width: 25px;
  height: 20px;
  margin: 5px 0;
  padding: 0;
  text-indent: 100%;
  white-space: nowrap;
  overflow: visible;
  border: 0;
  background: transparent;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  font-weight: bold;
  margin-right: 10px;
  outline: 0;
}
.dd-item > button:after {
  content: '';
  display: block;
  position: absolute;
  right: 0px;
  top: -5px;
  bottom: -6px;
  width: 1px;
  background: #ebebeb;
}
.dd-item > button:before {
  content: '+';
  display: block;
  position: absolute;
  width: 100%;
  text-align: center;
  text-indent: 0;
}
.dd-item > button[data-action="collapse"]:before {
  content: '-';
}
.dd-placeholder,
.dd-empty {
  margin: 0px 0;
  padding: 0;
  min-height: 30px;
  background: rgba(245, 245, 246, 0.3);
  border: 1px dotted #ebebeb;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.dd-empty {
  border: 1px dashed #bbb;
  min-height: 100px;
  background-color: #e5e5e5;
}
.dd-dragel {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
}
.dd-dragel > .dd-item .dd-handle,
.dd-dragel > .dd-item .dd-content {
  margin-top: 0;
}
.dd-dragel .dd-handle,
.dd-dragel .dd-content {
  -moz-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);
}
.nested-list.with-margins .dd-item .dd-handle,
.nested-list.with-margins .dd-item .dd-content {
  margin: 5px 0;
}
.nested-list.custom-drag-button .dd-handle {
  position: absolute !important;
  margin: 0 !important;
  width: 15px;
  height: 32px;
  padding-left: 5px;
  padding-right: 4px;
  cursor: pointer;
  padding-top: 7px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.nested-list.custom-drag-button .dd-handle span {
  display: block;
  line-height: 3px;
  color: rgba(115, 120, 129, 0.7);
}
.nested-list.custom-drag-button .dd-content {
  padding-left: 25px;
}
.nested-list.custom-drag-button button + .dd-handle {
  left: 24px;
}
.nested-list.custom-drag-button button + .dd-handle + .dd-content {
  padding-left: 50px;
}
.nested-list.custom-drag-button.drag-button-on-hover .dd-item > .dd-handle {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.nested-list.custom-drag-button.drag-button-on-hover .dd-item:hover > .dd-handle {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.custom-handler .dd-item .dd-handle {
  position: absolute !important;
  margin: 0 !important;
  width: 15px;
  height: 32px;
  padding-left: 5px;
  padding-right: 4px;
  cursor: pointer;
  padding-top: 7px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.custom-handler .dd-item .dd-handle span {
  display: block;
  line-height: 3px;
  color: rgba(115, 120, 129, 0.7);
}
.custom-handler .dd-item .dd-content {
  padding-left: 25px;
}
.custom-handler .dd-item button + .dd-handle {
  left: 24px;
}
.custom-handler .dd-item button + .dd-handle + .dd-content {
  padding-left: 50px;
}
