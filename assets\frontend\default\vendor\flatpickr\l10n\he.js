/* Hebrew locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.he = {};

flatpickr.l10ns.he.weekdays = {
	shorthand: ["א", "ב", "ג", "ד", "ה", "ו", "ז"],
	longhand: ["ראשון", "שני", "שלישי", "רביעי", "חמישי", "שישי", "שבת"]
};

flatpickr.l10ns.he.months = {
	shorthand: ["ינו׳", "פבר׳", "מרץ", "אפר׳", "מאי", "יוני", "יולי", "אוג׳", "ספט׳", "אוק׳", "נוב׳", "דצמ׳"],
	longhand: ["ינואר", "פברואר", "מרץ", "אפריל", "מאי", "יוני", "יולי", "אוגוסט", "ספט<PERSON>בר", "אוקטובר", "נובמבר", "דצמבר"]
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;