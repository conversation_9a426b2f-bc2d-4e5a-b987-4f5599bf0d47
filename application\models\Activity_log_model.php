<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Activity_log_model extends CI_Model {

    function __construct()
    {
        parent::__construct();
    }

    // Log user activity
    public function log_activity($user_type, $user_id, $activity, $details = '', $ip_address = '', $user_agent = '')
    {
        $data = array(
            'user_type' => $user_type,
            'user_id' => $user_id,
            'activity' => $activity,
            'details' => $details,
            'ip_address' => $ip_address ? $ip_address : $this->input->ip_address(),
            'user_agent' => $user_agent ? $user_agent : $this->input->user_agent(),
            'timestamp' => time()
        );

        $this->db->insert('user_activity_log', $data);
        return $this->db->insert_id();
    }

    // Get user activity logs with filters
    public function get_user_activity_logs($user_type = '', $user_id = '', $date_from = '', $date_to = '', $activity_type = '', $limit = 1000)
    {
        $this->db->select('ual.*, u.name as user_name');
        $this->db->from('user_activity_log ual');
        
        // Join with appropriate user table based on user_type
        if ($user_type) {
            $this->db->join($user_type . ' u', 'u.' . $user_type . '_id = ual.user_id', 'left');
            $this->db->where('ual.user_type', $user_type);
        } else {
            // Complex join for all user types - we'll handle this differently
            $this->db->select('ual.*, "" as user_name', false);
        }

        if ($user_id) {
            $this->db->where('ual.user_id', $user_id);
        }

        if ($date_from) {
            $this->db->where('ual.timestamp >=', strtotime($date_from));
        }

        if ($date_to) {
            $this->db->where('ual.timestamp <=', strtotime($date_to . ' 23:59:59'));
        }

        if ($activity_type) {
            $this->db->where('ual.activity', $activity_type);
        }

        $this->db->order_by('ual.timestamp', 'DESC');
        $this->db->limit($limit);

        $logs = $this->db->get()->result_array();

        // If no specific user_type, get user names separately
        if (!$user_type) {
            foreach ($logs as &$log) {
                $user = $this->get_user_name($log['user_type'], $log['user_id']);
                $log['user_name'] = $user ? $user['name'] : 'Unknown User';
            }
        }

        return $logs;
    }

    // Get user name from any user type
    private function get_user_name($user_type, $user_id)
    {
        $valid_types = ['admin', 'doctor', 'patient', 'nurse', 'pharmacist', 'laboratorist', 'accountant', 'receptionist'];
        
        if (!in_array($user_type, $valid_types)) {
            return null;
        }

        return $this->db->select('name')
                       ->where($user_type . '_id', $user_id)
                       ->get($user_type)
                       ->row_array();
    }

    // Search users across all user types
    public function search_users($search_term, $user_type = '')
    {
        $users = array();
        $user_types = $user_type ? [$user_type] : ['admin', 'doctor', 'patient', 'nurse', 'pharmacist', 'laboratorist', 'accountant', 'receptionist'];

        foreach ($user_types as $type) {
            $this->db->select($type . '_id as user_id, name, email, "' . $type . '" as user_type');
            $this->db->from($type);
            $this->db->group_start();
            $this->db->like('name', $search_term);
            $this->db->or_like('email', $search_term);
            $this->db->group_end();
            $this->db->limit(10);

            $results = $this->db->get()->result_array();
            $users = array_merge($users, $results);
        }

        return $users;
    }

    // Get activity statistics
    public function get_activity_statistics()
    {
        $stats = array();

        // Total activities today
        $today_start = strtotime('today');
        $today_end = strtotime('tomorrow') - 1;
        
        $stats['today_activities'] = $this->db->where('timestamp >=', $today_start)
                                             ->where('timestamp <=', $today_end)
                                             ->count_all_results('user_activity_log');

        // Total activities this week
        $week_start = strtotime('monday this week');
        $stats['week_activities'] = $this->db->where('timestamp >=', $week_start)
                                            ->count_all_results('user_activity_log');

        // Total activities this month
        $month_start = strtotime('first day of this month');
        $stats['month_activities'] = $this->db->where('timestamp >=', $month_start)
                                             ->count_all_results('user_activity_log');

        // Most active users today
        $this->db->select('user_type, user_id, COUNT(*) as activity_count');
        $this->db->where('timestamp >=', $today_start);
        $this->db->where('timestamp <=', $today_end);
        $this->db->group_by(['user_type', 'user_id']);
        $this->db->order_by('activity_count', 'DESC');
        $this->db->limit(5);
        
        $active_users = $this->db->get('user_activity_log')->result_array();
        
        // Get user names for active users
        foreach ($active_users as &$user) {
            $user_data = $this->get_user_name($user['user_type'], $user['user_id']);
            $user['user_name'] = $user_data ? $user_data['name'] : 'Unknown User';
        }
        
        $stats['most_active_users'] = $active_users;

        // Activity types breakdown
        $this->db->select('activity, COUNT(*) as count');
        $this->db->where('timestamp >=', $today_start);
        $this->db->group_by('activity');
        $this->db->order_by('count', 'DESC');
        
        $stats['activity_breakdown'] = $this->db->get('user_activity_log')->result_array();

        return $stats;
    }

    // Get recent activities for dashboard
    public function get_recent_activities($limit = 20)
    {
        return $this->get_user_activity_logs('', '', '', '', '', $limit);
    }

    // Clean old logs (for maintenance)
    public function clean_old_logs($days_to_keep = 90)
    {
        $cutoff_time = strtotime("-{$days_to_keep} days");
        $this->db->where('timestamp <', $cutoff_time);
        return $this->db->delete('user_activity_log');
    }
}
