/* Czech locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.cs = {};

flatpickr.l10ns.cs.weekdays = {
	shorthand: ["<PERSON>e", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>í", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Čtvrtek", "Pátek", "Sobota"]
};

flatpickr.l10ns.cs.months = {
	shorthand: ["Led", "Ún", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>vc", "Srp", "<PERSON><PERSON><PERSON>", "Říj", "Lis", "<PERSON>"],
	longhand: ["<PERSON><PERSON>", "<PERSON><PERSON>", "Březen", "Duben", "K<PERSON>ě<PERSON>", "Červen", "Červenec", "Srpen", "Z<PERSON>ř<PERSON>", "Říjen", "Listopad", "Prosinec"]
};

flatpickr.l10ns.cs.firstDayOfWeek = 1;

flatpickr.l10ns.cs.ordinal = function () {
	return ".";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;