<!doctype html>

<title>CodeMirror: Placeholder demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/display/placeholder.js"></script>
<style type="text/css">
      .CodeMirror { border: 1px solid silver; }
      .CodeMirror-empty { outline: 1px solid #c22; }
      .CodeMirror-empty.CodeMirror-focused { outline: none; }
      .CodeMirror pre.CodeMirror-placeholder { color: #999; }
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Placeholder</a>
  </ul>
</div>

<article>
<h2>Placeholder demo</h2>
<form><textarea id="code" name="code" placeholder="Code goes here..."></textarea></form>

    <p>The <a href="../doc/manual.html#addon_placeholder">placeholder</a>
    plug-in adds an option <code>placeholder</code> that can be set to
    make text appear in the editor when it is empty and not focused.
    If the source textarea has a <code>placeholder</code> attribute,
    it will automatically be inherited.</p>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true
      });
    </script>

  </article>
