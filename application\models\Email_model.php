<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Email_model extends CI_Model {

    function __construct() {
        parent::__construct();
    }

    function account_opening_email($account_type = '', $email = '', $password = '') {
        $system_name = $this->db->get_where('settings', array('type' => 'system_name'))->row()->description;

        $email_msg = "Welcome to " . $system_name . "<br />";
        $email_msg .= "Your account type : " . $account_type . "<br />";
        $email_msg .= "Your login password : " . $password . "<br />";
        $email_msg .= "Login Here : " . base_url() . "<br />";

        $email_sub = "Account opening email";
        $email_to = $email;

        $this->do_email($email_msg, $email_sub, $email_to);
    }

    function password_reset_email($account_type = '', $email = '', $password = '') {
        //$query = $this->db->get_where($account_type, array('email' => $email));
        if ($account_type != '') {
            $email_msg = "Your account type is : " . $account_type . "<br />";
            $email_msg .= "Your password is : " . $password . "<br />";

            $email_sub = "Password reset request";
            $email_to = $email;
            $this->do_email($email_msg, $email_sub, $email_to);
            return true;
        } else {
            return false;
        }
    }

    function contact_email($message = '', $name = '', $email = '', $phone = '' , $address = '') {
        $subject = 'Contact Email';

        $msg = $name . '<br />';
        $msg = $email . '<br />';
        $msg .= $phone . '<br />';
        $msg .= $address . '<br />';
        $msg .= $message;

        $to = $this->frontend_model->get_frontend_settings('email');

        $this->do_email($msg, $subject, $to, $email);
    }

    /*     * *custom email sender*** */

    function do_email($msg = NULL, $sub = NULL, $to = NULL, $from = NULL) {

        $config = array();
        $config['useragent'] = "CodeIgniter";
        $config['mailpath'] = "/usr/bin/sendmail"; // or "/usr/sbin/sendmail"
        $config['protocol'] = "smtp";
        $config['smtp_host'] = "localhost";
        $config['smtp_port'] = "25";
        $config['mailtype'] = 'html';
        $config['charset'] = 'utf-8';
        $config['newline'] = "\r\n";
        $config['wordwrap'] = TRUE;

        $this->load->library('email');

        $this->email->initialize($config);

        $system_name = $this->db->get_where('settings', array('type' => 'system_name'))->row()->description;
        if ($from == NULL)
            $from = $this->db->get_where('settings', array('type' => 'system_email'))->row()->description;

        $this->email->from($from, $system_name);
        $this->email->from($from, $system_name);
        $this->email->to($to);
        $this->email->subject($sub);

        //$msg = $msg . "<br /><br /><br /><br /><br /><br /><br /><hr /><center><a href=\"http://codecanyon.net/item/ekattor-school-management-system-pro/6087521?ref=joyontaroy\">&copy; 2013 Ekattor School Management System Pro</a></center>";
        $this->email->message($msg);

        $this->email->send();

        //echo $this->email->print_debugger();
    }

}