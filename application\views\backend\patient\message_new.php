<div class="mail-header" style="padding-bottom: 27px ;">
    <!-- title -->
    <h3 class="mail-title">
        <?php echo get_phrase('write_new_message'); ?>
    </h3>
</div>

<div class="mail-compose">

    <?php echo form_open(site_url('patient/message/send_new'), array('class' => 'form', 'enctype' => 'multipart/form-data')); ?>


    <div class="form-group">
        <label for="subject"><?php echo get_phrase('recipient'); ?>:</label>
        <br><br>
        <select class="form-control select2" name="reciever" required>

            <option value=""><?php echo get_phrase('select_a_user'); ?></option>
            <optgroup label="<?php echo get_phrase('doctor'); ?>">
                <?php
                $appointments = $this->crud_model->select_appointments_between_loggedin_patient_and_doctor();
                foreach($appointments as $appointment) {
                    $doctor_info = $this->db->get_where('doctor', array('doctor_id' => $appointment['doctor_id']))->result_array();
                    foreach ($doctor_info as $row) { ?>

                        <option value="doctor-<?php echo $row['doctor_id']; ?>">
                            - <?php echo $row['name']; ?></option>

                <?php } } ?>
            </optgroup>
        </select>
    </div>


    <div class="compose-message-editor">
        <textarea row="5" class="form-control wysihtml5" data-stylesheet-url="<?php echo base_url('assets/css/wysihtml5-color.css');?>"
            name="message" placeholder="<?php echo get_phrase('write_your_message'); ?>"
            id="sample_wysiwyg" required></textarea>
    </div>

    <hr>

    <button type="submit" class="btn btn-success btn-icon pull-right">
        <?php echo get_phrase('send'); ?>
        <i class="entypo-mail"></i>

    </button>
</form>

</div>
