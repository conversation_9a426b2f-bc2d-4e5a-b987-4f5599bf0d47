@charset "UTF-8";

@font-face {
  font-family: "medical-and-health";
  src:url("fonts/medical-and-health.eot");
  src:url("fonts/medical-and-health.eot?#iefix") format("embedded-opentype"),
    url("fonts/medical-and-health.woff") format("woff"),
    url("fonts/medical-and-health.ttf") format("truetype"),
    url("fonts/medical-and-health.svg#medical-and-health") format("svg");
  font-weight: normal;
  font-style: normal;

}

[data-icon]:before {
  font-family: "medical-and-health" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "medical-and-health" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-001:before {
  content: "a";
}
.icon-002:before {
  content: "b";
}
.icon-015:before {
  content: "c";
}
.icon-014:before {
  content: "d";
}
.icon-027:before {
  content: "e";
}
.icon-028:before {
  content: "f";
}
.icon-029:before {
  content: "g";
}
.icon-016:before {
  content: "h";
}
.icon-003:before {
  content: "i";
}
.icon-004:before {
  content: "j";
}
.icon-017:before {
  content: "k";
}
.icon-030:before {
  content: "l";
}
.icon-031:before {
  content: "m";
}
.icon-018:before {
  content: "n";
}
.icon-005:before {
  content: "o";
}
.icon-006:before {
  content: "p";
}
.icon-019:before {
  content: "q";
}
.icon-032:before {
  content: "r";
}
.icon-033:before {
  content: "s";
}
.icon-020:before {
  content: "t";
}
.icon-007:before {
  content: "u";
}
.icon-008:before {
  content: "v";
}
.icon-021:before {
  content: "w";
}
.icon-034:before {
  content: "x";
}
.icon-035:before {
  content: "y";
}
.icon-022:before {
  content: "z";
}
.icon-009:before {
  content: "A";
}
.icon-010:before {
  content: "B";
}
.icon-023:before {
  content: "C";
}
.icon-036:before {
  content: "D";
}
.icon-037:before {
  content: "E";
}
.icon-024:before {
  content: "F";
}
.icon-011:before {
  content: "G";
}
.icon-012:before {
  content: "H";
}
.icon-025:before {
  content: "I";
}
.icon-038:before {
  content: "J";
}
.icon-039:before {
  content: "K";
}
.icon-026:before {
  content: "L";
}
.icon-013:before {
  content: "M";
}
.icon-040:before {
  content: "N";
}
.icon-053:before {
  content: "O";
}
.icon-066:before {
  content: "P";
}
.icon-079:before {
  content: "Q";
}
.icon-092:before {
  content: "R";
}
.icon-093:before {
  content: "S";
}
.icon-080:before {
  content: "T";
}
.icon-067:before {
  content: "U";
}
.icon-054:before {
  content: "V";
}
.icon-041:before {
  content: "W";
}
.icon-042:before {
  content: "X";
}
.icon-055:before {
  content: "Y";
}
.icon-068:before {
  content: "Z";
}
.icon-081:before {
  content: "0";
}
.icon-094:before {
  content: "1";
}
.icon-096:before {
  content: "2";
}
.icon-082:before {
  content: "3";
}
.icon-095:before {
  content: "4";
}
.icon-069:before {
  content: "5";
}
.icon-056:before {
  content: "6";
}
.icon-043:before {
  content: "7";
}
.icon-044:before {
  content: "8";
}
.icon-057:before {
  content: "9";
}
.icon-070:before {
  content: "!";
}
.icon-083:before {
  content: "\"";
}
.icon-084:before {
  content: "#";
}
.icon-071:before {
  content: "$";
}
.icon-058:before {
  content: "%";
}
.icon-045:before {
  content: "&";
}
.icon-046:before {
  content: "'";
}
.icon-059:before {
  content: "(";
}
.icon-098:before {
  content: ")";
}
.icon-097:before {
  content: "*";
}
.icon-085:before {
  content: "+";
}
.icon-072:before {
  content: ",";
}
.icon-073:before {
  content: "-";
}
.icon-086:before {
  content: ".";
}
.icon-099:before {
  content: "/";
}
.icon-100:before {
  content: ":";
}
.icon-087:before {
  content: ";";
}
.icon-074:before {
  content: "<";
}
.icon-060:before {
  content: "=";
}
.icon-061:before {
  content: ">";
}
.icon-047:before {
  content: "?";
}
.icon-048:before {
  content: "@";
}
.icon-049:before {
  content: "[";
}
.icon-062:before {
  content: "]";
}
.icon-075:before {
  content: "^";
}
.icon-088:before {
  content: "_";
}
.icon-089:before {
  content: "`";
}
.icon-076:before {
  content: "{";
}
.icon-063:before {
  content: "|";
}
.icon-050:before {
  content: "}";
}
.icon-051:before {
  content: "~";
}
.icon-064:before {
  content: "\\";
}
.icon-077:before {
  content: "\e000";
}
.icon-090:before {
  content: "\e001";
}
.icon-091:before {
  content: "\e002";
}
.icon-078:before {
  content: "\e003";
}
.icon-065:before {
  content: "\e004";
}
.icon-052:before {
  content: "\e005";
}
