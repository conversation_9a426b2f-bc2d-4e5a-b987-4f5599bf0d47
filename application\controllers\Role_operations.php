<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Role_operations extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->library('session');
        $this->load->model('crud_model');
        $this->load->model('email_model');
        $this->load->model('sms_model');
        $this->load->model('activity_log_model');
        /*cache control*/
        $this->output->set_header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
        $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        $this->output->set_header('Pragma: no-cache');
        $this->output->set_header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
    }

    /***default function, redirects to login page if no admin logged in yet***/
    public function index()
    {
        if ($this->session->userdata('admin_login') != 1) {
            $this->session->set_userdata('last_page', current_url());
            redirect(site_url(), 'refresh');
        }
        
        $page_data['page_name']  = 'role_operations_dashboard';
        $page_data['page_title'] = get_phrase('role_operations');
        $this->load->view('backend/index', $page_data);
    }

    // ==================== DOCTOR OPERATIONS ====================
    
    // Doctor - Manage Patients
    public function doctor_patients($task = "", $patient_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_patient_info();
            $this->log_activity('doctor_operation', 'Admin created patient via doctor operations');
            $this->session->set_flashdata('message', get_phrase('patient_info_saved_successfuly'));
            redirect(site_url('role_operations/doctor_patients'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_patient_info($patient_id);
            $this->log_activity('doctor_operation', 'Admin updated patient via doctor operations');
            redirect(site_url('role_operations/doctor_patients'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_patient_info($patient_id);
            $this->log_activity('doctor_operation', 'Admin deleted patient via doctor operations');
            redirect(site_url('role_operations/doctor_patients'), 'refresh');
        }
        
        $data['patient_info'] = $this->crud_model->select_patient_info();
        $data['page_name']    = 'doctor_patients';
        $data['page_title']   = get_phrase('doctor') . ' - ' . get_phrase('manage_patient');
        $this->load->view('backend/index', $data);
    }

    // Doctor - Manage Appointments
    public function doctor_appointments($task = "", $appointment_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_appointment_info();
            $this->log_activity('doctor_operation', 'Admin created appointment via doctor operations');
            $this->session->set_flashdata('message', get_phrase('appointment_info_saved_successfuly'));
            redirect(site_url('role_operations/doctor_appointments'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_appointment_info($appointment_id);
            $this->log_activity('doctor_operation', 'Admin updated appointment via doctor operations');
            redirect(site_url('role_operations/doctor_appointments'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_appointment_info($appointment_id);
            $this->log_activity('doctor_operation', 'Admin deleted appointment via doctor operations');
            redirect(site_url('role_operations/doctor_appointments'), 'refresh');
        }
        
        $data['appointment_info'] = $this->crud_model->select_appointment_info();
        $data['page_name']        = 'doctor_appointments';
        $data['page_title']       = get_phrase('doctor') . ' - ' . get_phrase('manage_appointment');
        $this->load->view('backend/index', $data);
    }

    // Doctor - Manage Prescriptions
    public function doctor_prescriptions($task = "", $prescription_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_prescription_info();
            $this->log_activity('doctor_operation', 'Admin created prescription via doctor operations');
            $this->session->set_flashdata('message', get_phrase('prescription_info_saved_successfuly'));
            redirect(site_url('role_operations/doctor_prescriptions'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_prescription_info($prescription_id);
            $this->log_activity('doctor_operation', 'Admin updated prescription via doctor operations');
            redirect(site_url('role_operations/doctor_prescriptions'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_prescription_info($prescription_id);
            $this->log_activity('doctor_operation', 'Admin deleted prescription via doctor operations');
            redirect(site_url('role_operations/doctor_prescriptions'), 'refresh');
        }
        
        $data['prescription_info'] = $this->crud_model->select_prescription_info();
        $data['page_name']         = 'doctor_prescriptions';
        $data['page_title']        = get_phrase('doctor') . ' - ' . get_phrase('manage_prescription');
        $this->load->view('backend/index', $data);
    }

    // Doctor - Manage Reports
    public function doctor_reports($task = "", $report_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_report_info();
            $this->log_activity('doctor_operation', 'Admin created report via doctor operations');
            $this->session->set_flashdata('message', get_phrase('report_info_saved_successfuly'));
            redirect(site_url('role_operations/doctor_reports'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_report_info($report_id);
            $this->log_activity('doctor_operation', 'Admin updated report via doctor operations');
            redirect(site_url('role_operations/doctor_reports'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_report_info($report_id);
            $this->log_activity('doctor_operation', 'Admin deleted report via doctor operations');
            redirect(site_url('role_operations/doctor_reports'), 'refresh');
        }
        
        $data['report_info'] = $this->crud_model->select_report_info();
        $data['page_name']   = 'doctor_reports';
        $data['page_title']  = get_phrase('doctor') . ' - ' . get_phrase('manage_report');
        $this->load->view('backend/index', $data);
    }

    // ==================== NURSE OPERATIONS ====================
    
    // Nurse - Manage Beds
    public function nurse_beds($task = "", $bed_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_bed_info();
            $this->log_activity('nurse_operation', 'Admin created bed via nurse operations');
            $this->session->set_flashdata('message', get_phrase('bed_info_saved_successfuly'));
            redirect(site_url('role_operations/nurse_beds'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_bed_info($bed_id);
            $this->log_activity('nurse_operation', 'Admin updated bed via nurse operations');
            redirect(site_url('role_operations/nurse_beds'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_bed_info($bed_id);
            $this->log_activity('nurse_operation', 'Admin deleted bed via nurse operations');
            redirect(site_url('role_operations/nurse_beds'), 'refresh');
        }
        
        $data['bed_info']   = $this->crud_model->select_bed_info();
        $data['page_name']  = 'nurse_beds';
        $data['page_title'] = get_phrase('nurse') . ' - ' . get_phrase('manage_bed');
        $this->load->view('backend/index', $data);
    }

    // Nurse - Manage Bed Allotments
    public function nurse_bed_allotments($task = "", $bed_allotment_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }
        
        if ($task == "create") {
            $this->crud_model->save_bed_allotment_info();
            $this->log_activity('nurse_operation', 'Admin created bed allotment via nurse operations');
            $this->session->set_flashdata('message', get_phrase('bed_allotment_info_saved_successfuly'));
            redirect(site_url('role_operations/nurse_bed_allotments'), 'refresh');
        }
        
        if ($task == "update") {
            $this->crud_model->update_bed_allotment_info($bed_allotment_id);
            $this->log_activity('nurse_operation', 'Admin updated bed allotment via nurse operations');
            redirect(site_url('role_operations/nurse_bed_allotments'), 'refresh');
        }
        
        if ($task == "delete") {
            $this->crud_model->delete_bed_allotment_info($bed_allotment_id);
            $this->log_activity('nurse_operation', 'Admin deleted bed allotment via nurse operations');
            redirect(site_url('role_operations/nurse_bed_allotments'), 'refresh');
        }
        
        $data['bed_allotment_info'] = $this->crud_model->select_bed_allotment_info();
        $data['page_name']          = 'nurse_bed_allotments';
        $data['page_title']         = get_phrase('nurse') . ' - ' . get_phrase('manage_bed_allotment');
        $this->load->view('backend/index', $data);
    }

    // ==================== PHARMACIST OPERATIONS ====================

    // Pharmacist - Manage Medicines
    public function pharmacist_medicines($task = "", $medicine_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "create") {
            $this->crud_model->save_medicine_info();
            $this->log_activity('pharmacist_operation', 'Admin created medicine via pharmacist operations');
            $this->session->set_flashdata('message', get_phrase('medicine_info_saved_successfuly'));
            redirect(site_url('role_operations/pharmacist_medicines'), 'refresh');
        }

        if ($task == "update") {
            $this->crud_model->update_medicine_info($medicine_id);
            $this->log_activity('pharmacist_operation', 'Admin updated medicine via pharmacist operations');
            redirect(site_url('role_operations/pharmacist_medicines'), 'refresh');
        }

        if ($task == "delete") {
            $this->crud_model->delete_medicine_info($medicine_id);
            $this->log_activity('pharmacist_operation', 'Admin deleted medicine via pharmacist operations');
            redirect(site_url('role_operations/pharmacist_medicines'), 'refresh');
        }

        $data['medicine_info'] = $this->crud_model->select_medicine_info();
        $data['page_name']     = 'pharmacist_medicines';
        $data['page_title']    = get_phrase('pharmacist') . ' - ' . get_phrase('manage_medicine');
        $this->load->view('backend/index', $data);
    }

    // ==================== LABORATORIST OPERATIONS ====================

    // Laboratorist - Manage Blood Bank
    public function laboratorist_blood_bank($task = "", $blood_group_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "update") {
            $this->crud_model->update_blood_bank_info($blood_group_id);
            $this->log_activity('laboratorist_operation', 'Admin updated blood bank via laboratorist operations');
            $this->session->set_flashdata('message', get_phrase('blood_bank_info_updated_successfuly'));
            redirect(site_url('role_operations/laboratorist_blood_bank'), 'refresh');
        }

        $data['blood_bank_info'] = $this->crud_model->select_blood_bank_info();
        $data['page_name']       = 'laboratorist_blood_bank';
        $data['page_title']      = get_phrase('laboratorist') . ' - ' . get_phrase('manage_blood_bank');
        $this->load->view('backend/index', $data);
    }

    // Laboratorist - Manage Blood Donors
    public function laboratorist_blood_donors($task = "", $blood_donor_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "create") {
            $this->crud_model->save_blood_donor_info();
            $this->log_activity('laboratorist_operation', 'Admin created blood donor via laboratorist operations');
            $this->session->set_flashdata('message', get_phrase('blood_donor_info_saved_successfuly'));
            redirect(site_url('role_operations/laboratorist_blood_donors'), 'refresh');
        }

        if ($task == "update") {
            $this->crud_model->update_blood_donor_info($blood_donor_id);
            $this->log_activity('laboratorist_operation', 'Admin updated blood donor via laboratorist operations');
            redirect(site_url('role_operations/laboratorist_blood_donors'), 'refresh');
        }

        if ($task == "delete") {
            $this->crud_model->delete_blood_donor_info($blood_donor_id);
            $this->log_activity('laboratorist_operation', 'Admin deleted blood donor via laboratorist operations');
            redirect(site_url('role_operations/laboratorist_blood_donors'), 'refresh');
        }

        $data['blood_donor_info'] = $this->crud_model->select_blood_donor_info();
        $data['page_name']        = 'laboratorist_blood_donors';
        $data['page_title']       = get_phrase('laboratorist') . ' - ' . get_phrase('manage_blood_donor');
        $this->load->view('backend/index', $data);
    }

    // ==================== ACCOUNTANT OPERATIONS ====================

    // Accountant - Manage Invoices
    public function accountant_invoices($task = "", $invoice_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "create") {
            $this->crud_model->create_invoice();
            $this->log_activity('accountant_operation', 'Admin created invoice via accountant operations');
            $this->session->set_flashdata('message', get_phrase('invoice_info_saved_successfuly'));
            redirect(site_url('role_operations/accountant_invoices'), 'refresh');
        }

        if ($task == "update") {
            $this->crud_model->update_invoice($invoice_id);
            $this->log_activity('accountant_operation', 'Admin updated invoice via accountant operations');
            redirect(site_url('role_operations/accountant_invoices'), 'refresh');
        }

        if ($task == "delete") {
            $this->crud_model->delete_invoice($invoice_id);
            $this->log_activity('accountant_operation', 'Admin deleted invoice via accountant operations');
            redirect(site_url('role_operations/accountant_invoices'), 'refresh');
        }

        $data['invoice_info'] = $this->crud_model->select_invoice_info();
        $data['page_name']    = 'accountant_invoices';
        $data['page_title']   = get_phrase('accountant') . ' - ' . get_phrase('manage_invoice');
        $this->load->view('backend/index', $data);
    }

    // Accountant - Add Invoice
    public function accountant_add_invoice($task = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "create") {
            $this->crud_model->create_invoice();
            $this->log_activity('accountant_operation', 'Admin created invoice via accountant operations');
            $this->session->set_flashdata('message', get_phrase('invoice_info_saved_successfuly'));
            redirect(site_url('role_operations/accountant_invoices'), 'refresh');
        }

        $data['page_name']  = 'accountant_add_invoice';
        $data['page_title'] = get_phrase('accountant') . ' - ' . get_phrase('add_invoice');
        $this->load->view('backend/index', $data);
    }

    // ==================== RECEPTIONIST OPERATIONS ====================

    // Receptionist - Manage Patients (same as doctor but different context)
    public function receptionist_patients($task = "", $patient_id = "")
    {
        if ($this->session->userdata('admin_login') != 1) {
            redirect(site_url(), 'refresh');
        }

        if ($task == "create") {
            $this->crud_model->save_patient_info();
            $this->log_activity('receptionist_operation', 'Admin created patient via receptionist operations');
            $this->session->set_flashdata('message', get_phrase('patient_info_saved_successfuly'));
            redirect(site_url('role_operations/receptionist_patients'), 'refresh');
        }

        if ($task == "update") {
            $this->crud_model->update_patient_info($patient_id);
            $this->log_activity('receptionist_operation', 'Admin updated patient via receptionist operations');
            redirect(site_url('role_operations/receptionist_patients'), 'refresh');
        }

        if ($task == "delete") {
            $this->crud_model->delete_patient_info($patient_id);
            $this->log_activity('receptionist_operation', 'Admin deleted patient via receptionist operations');
            redirect(site_url('role_operations/receptionist_patients'), 'refresh');
        }

        $data['patient_info'] = $this->crud_model->select_patient_info();
        $data['page_name']    = 'receptionist_patients';
        $data['page_title']   = get_phrase('receptionist') . ' - ' . get_phrase('manage_patient');
        $this->load->view('backend/index', $data);
    }

    // Helper method to log activities
    private function log_activity($activity, $details = '')
    {
        if ($this->session->userdata('admin_login') == 1) {
            $this->activity_log_model->log_activity(
                'admin',
                $this->session->userdata('login_user_id'),
                $activity,
                $details
            );
        }
    }
}
