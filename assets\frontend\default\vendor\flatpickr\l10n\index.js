var ar = require("./ar")["ar"];
var bg = require("./bg")["bg"];
var bn = require("./bn")["bn"];
var cat = require("./cat")["cat"];
var cs = require("./cs")["cs"];
var cy = require("./cy")["cy"];
var da = require("./da")["da"];
var de = require("./de")["de"];
var eo = require("./eo")["eo"];
var es = require("./es")["es"];
var et = require("./et")["et"];
var fa = require("./fa")["fa"];
var fi = require("./fi")["fi"];
var fr = require("./fr")["fr"];
var gr = require("./gr")["gr"];
var he = require("./he")["he"];
var hi = require("./hi")["hi"];
var hr = require("./hr")["hr"];
var hu = require("./hu")["hu"];
var id = require("./id")["id"];
var it = require("./it")["it"];
var ja = require("./ja")["ja"];
var ko = require("./ko")["ko"];
var lt = require("./lt")["lt"];
var lv = require("./lv")["lv"];
var mk = require("./mk")["mk"];
var ms = require("./ms")["ms"];
var my = require("./my")["my"];
var nl = require("./nl")["nl"];
var no = require("./no")["no"];
var pa = require("./pa")["pa"];
var pl = require("./pl")["pl"];
var pt = require("./pt")["pt"];
var ro = require("./ro")["ro"];
var ru = require("./ru")["ru"];
var si = require("./si")["si"];
var sk = require("./sk")["sk"];
var sl = require("./sl")["sl"];
var sq = require("./sq")["sq"];
var sr = require("./sr")["sr"];
var sv = require("./sv")["sv"];
var th = require("./th")["th"];
var tr = require("./tr")["tr"];
var uk = require("./uk")["uk"];
var vn = require("./vn")["vn"];
var zh = require("./zh")["zh"];

var l10n = {
	ar: ar,
	bg: bg,
	bn: bn,
	cat: cat,
	cs: cs,
	cy: cy,
	da: da,
	de: de,
	eo: eo,
	es: es,
	et: et,
	fa: fa,
	fi: fi,
	fr: fr,
	gr: gr,
	he: he,
	hi: hi,
	hr: hr,
	hu: hu,
	id: id,
	it: it,
	ja: ja,
	ko: ko,
	lt: lt,
	lv: lv,
	mk: mk,
	ms: ms,
	my: my,
	nl: nl,
	no: no,
	pa: pa,
	pl: pl,
	pt: pt,
	ro: ro,
	ru: ru,
	si: si,
	sk: sk,
	sl: sl,
	sq: sq,
	sr: sr,
	sv: sv,
	th: th,
	tr: tr,
	uk: uk,
	vn: vn,
	zh: zh
};

if (typeof module !== "undefined") module.exports = l10n;