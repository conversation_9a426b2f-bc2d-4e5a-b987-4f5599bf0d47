/*
Theme: Boomerang - Multipurpose Template
Website URI: https://wrapbootstrap.com/theme/boomerang-multipurpose-template-*********
Author: Webpixels
Author URI: https://www.webpixels.io
*/
.dropdown-menu-right .dropdown-menu,
.navbar-right .dropdown-menu .dropdown-menu,
.pull-right .dropdown-menu .dropdown-menu {
  left: auto;
  right: 100%; }

.dropdown-menu.animated {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s; }

.dropdown-menu.animated:before {
  content: " ";
  display: block;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 99; }

.dropdownhover-top .dropdown-menu {
  margin-bottom: 2px;
  margin-top: 0; }

.navbar-fixed-bottom .dropdown-menu .dropdown-menu,
.dropdownhover-top .dropdown-menu {
  bottom: -1px;
  top: auto; }

.dropdownhover-bottom .dropdown-menu {
  -webkit-transform-origin: 50% 0;
  transform-origin: 50% 0; }

.dropdownhover-left .dropdown-menu {
  -webkit-transform-origin: 100% 50%;
  transform-origin: 100% 50%; }

.dropdownhover-right .dropdown-menu {
  -webkit-transform-origin: 0 50%;
  transform-origin: 0 50%; }

.dropdownhover-top .dropdown-menu {
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%; }

.global-search {
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 600;
  display: none;
  opacity: 0;
  -webkit-transition: all 0.8s ease-out;
  -moz-transition: all 0.8s ease-out;
  transition: all 0.8s ease-out; }

.header-inner + .container > .global-search {
  position: absolute;
  border-top: 1px solid #f1f1f1; }

.header-affix.affix .global-search {
  display: none; }

.header-affix.affix [data-toggle="global-search"] {
  display: none; }

.global-search.in {
  display: block; }

.global-search.animated {
  opacity: 1; }

.global-search.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s; }

.global-search.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s; }

.navbar.global-search-active {
  padding: 30px 0; }

.navbar.global-search-active * {
  opacity: 0 !important; }

.global-search .form-global-search {
  height: 100%;
  position: relative;
  z-index: 700;
  background: white; }

.top-navbar + .header-1 .global-search {
  margin-top: 48px; }

.navbar--inverse + .global-search .form-global-search {
  background: black; }

.global-search .form-global-search .search-input {
  display: block;
  width: 100%;
  margin: 54px 0;
  height: 40px;
  padding: 0 50px 0 0;
  font-size: 22px;
  color: #555;
  font-family: "Lato", sans-serif;
  border: 0;
  background-color: transparent;
  background-image: none; }

.navbar--inverse + .global-search .form-global-search .search-input {
  color: rgba(255, 255, 255, 0.7); }

.navbar--sm + .global-search .form-global-search .search-input {
  margin: 42px 0; }

.global-search .form-global-search .search-input:focus {
  outline: 0;
  font-weight: 500; }

.global-search .form-global-search .search-input::-moz-placeholder {
  color: #999;
  opacity: 1;
  font-weight: 400; }

.global-search .form-global-search .search-input::-moz-placeholder:focus {
  color: #999;
  font-weight: 400; }

.global-search .form-global-search .search-input:-ms-input-placeholder {
  color: #999;
  font-weight: 400; }

.global-search .form-global-search .search-input::-webkit-input-placeholder {
  color: #999;
  font-weight: 400; }

.global-search .form-global-search .search-input:focus::-webkit-input-placeholder {
  color: transparent; }

.global-search .form-global-search .btn {
  display: block;
  width: 100%;
  height: 40px;
  margin-top: 20px;
  line-height: 40px;
  padding: 0; }

.global-search-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95); }

.global-search.in .global-search-backdrop {
  display: block; }

.global-search-close-btn {
  position: absolute;
  top: 50%;
  margin-top: -18px;
  right: 15px;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
  background: transparent;
  border: 0;
  z-index: 900; }

.global-search-fullscreen {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 500px;
  height: 100%;
  z-index: 1500;
  background: rgba(255, 255, 255, 0.95); }

.global-search-fullscreen .form-global-search {
  width: 80%;
  margin-left: 10%;
  position: absolute;
  top: 50%;
  margin-top: -75px; }

.global-search-fullscreen .form-global-search .search-input {
  display: block;
  width: 100%;
  margin-top: 25px;
  height: 50px;
  padding: 0 0 3px;
  font-size: 28px;
  line-height: 1.42857143;
  color: #111;
  border: 0;
  border-bottom: 1px solid #111;
  background-color: transparent;
  background-image: none; }

.global-search-fullscreen .form-global-search .search-input:focus {
  border-color: #111;
  outline: 0; }

.global-search-fullscreen .form-global-search .search-input::-moz-placeholder {
  color: #111;
  opacity: 1; }

.global-search-fullscreen .form-global-search .search-input::-moz-placeholder:focus {
  color: #111; }

.global-search-fullscreen .form-global-search .search-input:-ms-input-placeholder {
  color: #111; }

.global-search-fullscreen .form-global-search .search-input::-webkit-input-placeholder {
  color: #111; }

.global-search-fullscreen .form-global-search .search-input:focus::-webkit-input-placeholder {
  color: transparent; }

.global-search-fullscreen .form-global-search .btn {
  height: 50px;
  margin-top: 25px;
  padding: 14px 36px;
  float: right;
  font-size: 18px;
  line-height: 0 !important; }

.global-search-fullscreen .close-search {
  position: relative;
  z-index: 300;
  padding: 20px;
  background-color: #111;
  float: right;
  cursor: pointer;
  border-bottom-left-radius: 4px; }

.global-search-fullscreen .close-search:before {
  content: "\f00d";
  font-family: FontAwesome;
  color: #fff; }

.global-search-overlay {
  padding-top: 30px;
  height: auto;
  background: transparent; }

.global-search-overlay .form-global-search {
  background: #fff;
  border-radius: 0.25rem; }

.global-search-overlay .form-global-search .search-input {
  display: block;
  width: 100%;
  margin: 0;
  padding: 30px 0;
  height: auto;
  font-size: 20px;
  line-height: 1.42857143;
  color: #111;
  border: 0;
  background-color: transparent;
  background-image: none; }

.global-search-overlay .close-search {
  position: absolute;
  top: 50%;
  right: 20px;
  z-index: 300;
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-top: -16px;
  text-align: center;
  color: #ddd;
  float: right;
  cursor: pointer;
  border-bottom-left-radius: 4px; }

.global-search-overlay .close-search:before {
  content: "\f00d";
  font-family: FontAwesome;
  color: #ddd; }

/* DROPDOWN CART */
.top-navbar {
  background: white;
  border-bottom: 1px solid #f1f1f1;
  position: relative;
  z-index: 710; }

.header-affix.sps--blw .top-navbar {
  display: none; }

.top-navbar .aux-text {
  padding: 14px 0;
  color: rgba(0, 0, 0, 0.3);
  font-size: 11px;
  float: left; }

.top-navbar .aux-text ul li a,
.top-navbar .aux-text ul li a span {
  font-size: 0.75rem;
  font-family: "Lato", sans-serif; }

.top-navbar .top-navbar-menu {
  float: right; }

.top-navbar .top-navbar-menu > ul {
  list-style: none;
  margin: 0;
  padding: 0; }

.top-navbar .top-navbar-menu > ul.top-menu > li {
  position: relative;
  float: left;
  display: inline-block;
  font-size: 0.75rem;
  font-family: "Lato", sans-serif; }

.top-navbar .top-navbar-menu > ul.top-menu > li > a {
  display: block;
  padding: 14px 15px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75rem; }

.top-navbar .top-navbar-menu > ul.top-menu > li > a:hover {
  color: #0087be; }

.top-navbar .top-navbar-menu > ul.top-menu > li.dropdown > a:after {
  content: "\f107";
  margin-left: 6px;
  font-family: "FontAwesome";
  position: relative;
  float: right; }

.top-navbar .top-navbar-menu ul.top-menu > li > a > i {
  margin-right: 6px; }

.top-navbar .social-media.social-media--style-1-v4 > li > a {
  color: #818a91;
  font-size: 0.75rem; }

.top-navbar .top-navbar-menu > ul.top-menu-sm > li > a:not(.btn) {
  padding: 0.5rem 0.75rem; }

.top-navbar.top-navbar--caps .top-navbar-menu > ul.top-menu > li > a:not(.btn) {
  text-transform: uppercase; }

.top-navbar.top-navbar--caps .top-navbar-menu > ul.top-menu-sm > li > a:not(.btn) {
  font-size: 0.75rem; }

.top-navbar .top-navbar-menu > ul.top-menu--style-2 > li {
  border-right: 0; }

.top-navbar .top-navbar-menu > ul.top-menu--style-2 > li > a:not(.btn) {
  padding-left: 1.25rem;
  padding-right: 1.25rem; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu {
  display: none;
  min-width: 160px;
  position: absolute;
  right: -1px;
  z-index: 1100;
  margin: 0;
  padding: 0;
  list-style: none;
  background: #FFF;
  border: 0;
  opacity: 0;
  -moz-opacity: 0;
  filter: alpha(opacity=0);
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  -moz-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear;
  border-radius: 3px; }

.top-navbar .top-navbar-menu ul.top-menu > li:hover ul.sub-menu {
  opacity: 1;
  display: block; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu > li {
  font-size: 0.75rem; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu > li:last-child {
  border: 0; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu > li > a:not(.btn) {
  display: block;
  padding: 6px 15px;
  color: #666; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu > li > a:hover {
  background: transparent;
  color: #0087be; }

.top-navbar .top-navbar-menu ul.top-menu > li ul.sub-menu > li > .language-active {
  display: block;
  padding: 6px 15px;
  background: #0087be;
  color: #FFF;
  cursor: default; }

.top-navbar .top-navbar-menu ul.top-menu > li.dropdown:hover .sub-menu {
  display: block; }

@media (max-width: 991px) {
  .top-navbar .top-navbar-menu {
    float: none; }

  .top-navbar .top-navbar-menu > ul.top-menu {
    display: table;
    width: 100%; }

  .top-navbar .top-navbar-menu > ul.top-menu > li {
    float: none;
    display: table-cell; } }
.top-navbar--inverse {
  border-bottom: 1px solid black;
  background: black; }

.top-navbar--inverse .aux-text {
  color: rgba(255, 255, 255, 0.6); }

.top-navbar--inverse .top-navbar-menu > ul.top-menu > li {
  border-right: 1px solid transparent; }

.top-navbar--inverse .top-navbar-menu > ul.top-menu > li > a {
  color: rgba(255, 255, 255, 0.7); }

.top-navbar--inverse .top-navbar-menu > ul.top-menu > li > a:hover {
  color: #0087be; }

.top-navbar .top-navbar-links {
  margin: 0;
  padding: 0; }

.top-navbar .top-navbar-links > li {
  display: inline-block;
  position: relative;
  width: auto;
  font-size: 12px;
  line-height: 43px;
  padding: 0; }

.top-navbar .top-navbar-links i {
  margin-right: 5px;
  color: #5c5c5c; }

.top-navbar .top-navbar-links a {
  color: #a1a1a1; }

.top-navbar .top-navbar-links span {
  color: #a1a1a1; }

.top-navbar .top-navbar-links .top-navbar-email {
  padding: 0 20px 0 0; }

.top-navbar-currency-language > li {
  display: inline-block;
  position: relative !important;
  line-height: 44px !important;
  font-size: 12px !important;
  width: auto !important;
  padding: 0 !important; }

.top-navbar-currency-language > li > a {
  color: #fff !important; }

.top-navbar-currency-language > li > a i {
  margin-left: 5px; }

.top-navbar-currency-language > li:hover .currency-dropdown,
.top-navbar-currency-language > li:hover .language-dropdown {
  visibility: visible;
  opacity: 1; }

.top-navbar-currency-language li {
  font-family: 'Lato', sans-serif;
  color: #a1a1a1; }

.top-navbar-currency-language li.language {
  margin-left: 10px; }

.top-navbar-currency-language .currency-dropdown,
.top-navbar-currency-language .language-dropdown {
  background-color: #fff;
  padding: 0 10px;
  position: absolute;
  border: 1px solid #f2f2f2;
  z-index: 1201;
  right: 0;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }

.top-navbar-currency-language .currency-dropdown ul,
.top-navbar-currency-language .language-dropdown ul {
  padding: 0; }

.top-navbar-currency-language .currency-dropdown ul li,
.top-navbar-currency-language .language-dropdown ul li {
  line-height: 26px;
  border-top: 1px solid #f2f2f2;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0;
  font-size: 12px; }

.top-navbar-currency-language .currency-dropdown ul li > a,
.top-navbar-currency-language .language-dropdown ul li > a {
  color: #7a7a7a; }

.top-navbar .top-navbar-links .top-bar-date a,
.top-navbar .top-navbar-links .top-bar-date span,
.top-navbar .top-navbar-links .top-bar-link a,
.top-navbar .top-navbar-links .top-bar-link span {
  font-family: "Lato", sans-serif;
  font-size: 0.625rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #818a91; }

.top-navbar .top-navbar-links .top-bar-date a:hover,
.top-navbar .top-navbar-links .top-bar-link a:hover {
  color: #0087be; }

.top-navbar .top-navbar-links .top-bar-date {
  margin-right: 25px; }

.top-navbar .top-navbar-links .top-bar-link a:after {
  content: "";
  border-right: 1px solid #606060;
  margin: 0 7px 0 10px; }

.top-navbar .top-navbar-links .top-bar-link:last-child a:after {
  display: none; }

.top-navbar .topbar-login > li {
  display: inline-block;
  width: auto;
  padding: 0; }

.top-navbar .topbar-login > li a {
  font-family: "Lato", sans-serif;
  font-size: 0.625rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #818a91; }

.top-navbar .topbar-login > li a:after {
  content: "";
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  margin: 0 7px 0 10px; }

.top-navbar .topbar-login > li a:hover {
  color: #0087be; }

.top-navbar .topbar-login > li:last-child a:after {
  display: none; }

.top-navbar-widget {
  display: inline-block; }

.navbar {
  z-index: 100; }

.navbar-container {
  position: relative; }

@media (max-width: 991px) {
  .navbar-container {
    width: 100%;
    max-width: 100%;
    padding-left: 1rem !important;
    padding-right: 1rem !important; } }
.navbar-floating {
  position: absolute;
  padding: 2rem;
  width: 100%;
  left: 0;
  z-index: 100; }

.navbar-floating .navbar-container {
  padding-left: 20px;
  padding-right: 20px; }

.navbar-floating .navbar {
  border-radius: 0.25rem; }

@media (min-width: 992px) {
  .nav-logo-item .navbar-brand {
    padding: 0;
    line-height: 1;
    margin: 0 3rem; }

  .navbar-brand--centered {
    position: absolute;
    left: calc(50% - 70px); } }
@media (max-width: 767px) {
  .navbar-container {
    margin-left: 0;
    margin-right: 0; } }
.navbar-nav .nav-link {
  font-size: 0.875rem;
  font-family: "Lato", sans-serif;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  color: #555;
  background: transparent; }

.navbar-nav .nav-link,
.navbar-nav .nav-link * {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.navbar-nav .nav-link:focus,
.navbar-nav .nav-link:hover {
  color: #FFF;
  background: #0087be; }

.navbar-nav .show .nav-link,
.navbar-nav .show .nav-link:focus,
.navbar-nav .show .nav-link:hover {
  color: #FFF;
  background: #0087be; }

.navbar-nav .nav-item:not(.nav-item-icon) .nav-link i {
  margin-right: 0.625rem; }

@media (min-width: 992px) {
  .navbar {
    padding: 0; }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding-top: 2rem;
    padding-bottom: 2rem;
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    border-radius: 0; } }
.navbar-light .navbar-nav .nav-link {
  color: #666;
  background: transparent; }

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: #FFF;
  background: #0087be; }

.navbar-light .navbar-nav .show .nav-link,
.navbar-light .navbar-nav .show .nav-link:focus,
.navbar-light .navbar-nav .show .nav-link:hover {
  color: #FFF;
  background: #0087be; }

.navbar-inverse .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.7);
  background: transparent; }

.navbar-inverse .navbar-nav .nav-link:focus,
.navbar-inverse .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.8);
  background: transparent; }

.navbar-inverse .navbar-nav .show .nav-link,
.navbar-inverse .navbar-nav .show .nav-link:focus,
.navbar-inverse .navbar-nav .show .nav-link:hover {
  color: rgba(255, 255, 255, 0.9);
  background: transparent; }

.navbar.bg-default {
  background: white; }

.navbar.bg-light {
  background-color: #f2f2f2; }

.navbar.bg-dark {
  background-color: black; }

.navbar.bg-base-1 {
  background-color: #0087be; }

.navbar.bg-base-2 {
  background-color: #292f36; }

.navbar.bg-gradient-1, .navbar.mask-gradient-1--style-1, .navbar.mask-gradient-1--style-2 {
  background: #FC5C7D;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #6A82FB, #FC5C7D);
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #6A82FB, #FC5C7D);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */ }

.navbar.bg-gradient-2 {
  background: #ec008c;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #fc6767, #ec008c);
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #fc6767, #ec008c);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */ }

.navbar-opaque {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100; }

.navbar-floating .navbar-opaque {
  position: static;
  width: auto; }

.navbar-opaque .navbar {
  background-color: rgba(255, 255, 255, 0.5);
  border: 0; }

.navbar-opaque.bg-light {
  background-color: rgba(255, 255, 255, 0.5) !important; }

.navbar-opaque.bg-light {
  background-color: rgba(242, 242, 242, 0.5) !important; }

.navbar-opaque.bg-dark {
  background-color: rgba(0, 0, 0, 0.5) !important; }

.navbar-opaque.bg-base-1 {
  background-color: rgba(222, 27, 27, 0.5) !important; }

.navbar-opaque.sps--blw {
  background-color: rgba(255, 255, 255, 0.7); }

.navbar-opaque.navbar-inverse.sps--blw {
  background-color: rgba(0, 0, 0, 0.7); }

.navbar-opaque .navbar-nav .nav-link,
.navbar-opaque .navbar-nav .active .nav-link::before,
.navbar-opaque .navbar-nav .nav-link:focus::before,
.navbar-opaque .navbar-nav .nav-link:hover::before,
.navbar-opaque .navbar-nav .show .nav-link::before {
  background-color: transparent; }

.navbar-opaque .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.6); }

.navbar-opaque .navbar-nav .nav-link:focus,
.navbar-opaque .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, 0.9); }

.navbar-opaque.navbar-inverse .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.7); }

.navbar-opaque.navbar-inverse .navbar-nav .nav-link:focus,
.navbar-opaque.navbar-inverse .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.9); }

@media (min-width: 992px) {
  .navbar.navbar-transparent {
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 100;
    background-color: transparent !important;
    border: 0;
    box-shadow: none; }

  .navbar-transparent .navbar-nav .nav-link {
    color: rgba(0, 0, 0, 0.7); }

  .navbar-transparent .navbar-nav .nav-link:focus,
  .navbar-transparent .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, 0.7);
    background-color: transparent; }

  .navbar-transparent .navbar-nav .show .nav-link,
  .navbar-transparent .navbar-nav .show .nav-link:focus,
  .navbar-transparent .navbar-nav .show .nav-link:hover {
    color: rgba(0, 0, 0, 0.9);
    background-color: transparent; }

  .navbar-transparent.navbar-inverse .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.7); }

  .navbar-transparent.navbar-inverse .navbar-nav .nav-link:focus,
  .navbar-transparent.navbar-inverse .navbar-nav .nav-link:hover {
    color: white; }

  .navbar-transparent.navbar-inverse .navbar-nav .show .nav-link,
  .navbar-transparent.navbar-inverse .navbar-nav .show .nav-link:focus,
  .navbar-transparent.navbar-inverse .navbar-nav .show .nav-link:hover {
    color: white; }

  .navbar-transparent.navbar-transparent-bb {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); }

  .navbar-transparent.navbar-transparent-bb.navbar-inverse {
    border-bottom: 1px solid rgba(255, 255, 255, 0.25); } }
.navbar--shadow {
  z-index: 100;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); }

.navbar--bb-1px {
  border-bottom: 1px solid #f1f1f1 !important; }

.navbar--bb-2px {
  border-bottom: 2px solid #f1f1f1 !important; }

.navbar--bb-3px {
  border-bottom: 3px solid #f1f1f1 !important; }

.navbar-inverse.navbar--bb-1px {
  border-bottom: 1px solid black !important; }

.navbar-inverse.navbar--bb-2px {
  border-bottom: 2px solid black !important; }

.navbar-inverse.navbar--bb-3px {
  border-bottom: 3px solid black !important; }

.navbar--bt-1px {
  border-top: 1px solid #f1f1f1 !important; }

.navbar--bt-2px {
  border-top: 2px solid #f1f1f1 !important; }

.navbar--bt-3px {
  border-top: 3px solid #f1f1f1 !important; }

.navbar-inverse.navbar--bt-1px {
  border-top: 1px solid black !important; }

.navbar-inverse.navbar--bt-2px {
  border-top: 2px solid black !important; }

.navbar-inverse.navbar--bt-3px {
  border-top: 3px solid black !important; }

.navbar--uppercase .navbar-nav .nav-link {
  text-transform: uppercase; }

.navbar--bold .navbar-nav .nav-link {
  font-weight: bolder !important; }

.navbar-nav .nav-item-icon .nav-link {
  background: transparent;
  color: #555;
  padding-top: 1rem;
  padding-bottom: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem; }

.navbar-nav .nav-item-icon .nav-link:focus,
.navbar-nav .nav-item-icon .nav-link:hover,
.navbar-nav .nav-item-icon.show .nav-link,
.navbar-nav .nav-item-icon.show .nav-link,
.navbar-nav .nav-item-icon.show .nav-link:focus {
  background: transparent !important;
  color: #0087be !important; }

.navbar-nav .nav-item-icon .nav-link::after {
  border: 0; }

.navbar-inverse .navbar-nav .nav-item-icon .nav-link {
  color: rgba(255, 255, 255, 0.7); }

.navbar-inverse .navbar-nav .nav-item-icon .nav-link:hover {
  color: rgba(255, 255, 255, 0.8); }

@media (min-width: 992px) {
  .navbar--style-1 .navbar-nav .nav-link {
    position: relative;
    font-size: 0.875rem;
    font-family: 0.9375rem;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0;
    color: rgba(0, 0, 0, 0.6);
    background: transparent;
    padding: 2rem 1.25rem;
    border-radius: 0; }

  .navbar--style-1 .navbar-nav .nav-item:first-child .nav-link {
    padding-left: 0; }

  .navbar--style-1 .navbar-nav .nav-link:focus,
  .navbar--style-1 .navbar-nav .nav-link:hover {
    color: #0087be;
    background: transparent; }

  .navbar--style-1 .navbar-nav .show .nav-link,
  .navbar--style-1 .navbar-nav .show .nav-link:focus,
  .navbar--style-1 .navbar-nav .show .nav-link:hover {
    color: #0087be;
    background: transparent; }

  .navbar--style-1 .navbar-nav .show .nav-link::after,
  .navbar--style-1 .navbar-nav .nav-link:hover::after,
  .navbar--style-1 .navbar-nav .nav-link:focus::after {
    content: '\f111';
    font-family: 'FontAwesome';
    font-size: 8px;
    color: #0087be;
    position: absolute;
    left: 50%;
    top: 10px;
    transform: translateX(-50%); } }
.navbar-nav .dropdown-menu {
  margin: 0;
  background-color: #FFF;
  border: 0;
  border-radius: 0; }

@media (min-width: 992px) {
  .navbar-nav .dropdown-menu {
    min-width: 220px;
    padding: 0 0.5rem;
    margin: 0;
    text-align: left;
    border: 0;
    border-top: 0;
    border-radius: 3px;
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.08);
    -webkit-box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.08); }

  .navbar-nav .dropdown-menu-sm {
    min-width: 360px !important; }

  .navbar-nav .dropdown-menu-lg {
    min-width: 500px !important; }

  .navbar-nav .dropdown-menu--left {
    left: 0;
    right: auto; }

  .navbar-nav .dropdown-menu--right {
    left: auto;
    right: 0; } }
@media (max-width: 991px) {
  .navbar-transparent .dropdown-menu {
    background-color: transparent; } }
.navbar-nav .dropdown-menu .dropdown-item {
  padding: 0.875rem 1.5rem;
  font-size: 0.8rem;
  font-weight: 400;
  text-transform: capitalize;
  color: #666; }

.navbar-nav .dropdown-menu .dropdown-item .badge {
  position: absolute;
  right: 30px; }

.navbar-nav .dropdown-menu .dropdown-item:focus,
.navbar-nav .dropdown-menu .dropdown-item:hover {
  color: #0087be;
  background-color: transparent; }

.navbar-nav .dropdown-menu .divider {
  background-color: rgba(0, 0, 0, 0.5); }

.navbar-nav .dropdown-menu .open .dropdown-item {
  color: #0087be;
  background-color: transparent; }

@media (min-width: 992px) {
  .navbar-dropdown--inverse .dropdown-menu {
    background-color: #292f36;
    border-top: 0; }

  .navbar-dropdown--inverse .dropdown-menu:after {
    border-bottom-color: #292f36 !important; }

  .navbar-dropdown--inverse .dropdown-menu .dropdown-item {
    color: rgba(255, 255, 255, 0.7);
    border-bottom-color: rgba(30, 30, 30, 0.7); }

  .navbar-main.navbar-dropdown--inverse .dropdown-menu .dropdown-item {
    color: rgba(255, 255, 255, 0.7); }

  .navbar-dropdown--inverse .dropdown-menu .dropdown-item:focus,
  .navbar-dropdown--inverse .dropdown-menu .dropdown-item:hover {
    color: #0087be;
    background-color: transparent; }

  .navbar-dropdown--inverse .dropdown-menu .open .dropdown-item {
    color: #0087be;
    background-color: transparent; } }
@media (min-width: 992px) {
  .navbar-nav .dropdown-menu {
    margin-top: 0; }

  .navbar-nav .dropdown-menu::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    background: transparent;
    margin-top: 0; }

  .navbar-dropdown--arrow .navbar-nav .dropdown-menu {
    margin-top: 12px; }

  .navbar-dropdown--arrow .navbar-nav .dropdown-menu::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 12px;
    background: transparent;
    margin-top: -12px; }

  .navbar-dropdown--arrow .navbar-nav .dropdown-menu::after {
    bottom: 100%;
    right: 12px;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(221, 221, 221, 0);
    border-bottom-color: #FFF;
    border-width: 8px;
    margin-left: -8px; }

  .navbar-dropdown--arrow .dropdown-menu.mega-dropdown-menu::after {
    border: 0; }

  .navbar--link-arrow .navbar-nav .nav-link {
    position: relative; }

  .navbar--link-arrow .navbar-nav .nav-item.show:not(.nav-item-icon) .nav-link::after {
    content: "";
    position: absolute;
    left: 50%;
    border: solid transparent;
    height: 0;
    width: 0;
    border-color: transparent;
    border-width: 10px;
    margin-left: -10px;
    z-index: 1100; }

  .navbar--link-arrow .navbar-nav .dropdown-top.show:not(.nav-item-icon) .nav-link::after {
    top: 0;
    border-top-color: #FFF; }

  .navbar--link-arrow .navbar-nav .dropdown-bottom.show:not(.nav-item-icon) .nav-link::after {
    bottom: 0;
    border-bottom-color: #FFF; }

  .navbar--link-arrow.navbar-dropdown--inverse .navbar-nav .dropdown-top.show:not(.nav-item-icon) .nav-link::after {
    border-top-color: #292f36; }

  .navbar--link-arrow.navbar-dropdown--inverse .navbar-nav .dropdown-bottom.show:not(.nav-item-icon) .nav-link::after {
    border-bottom-color: #292f36; } }
.navbar-dropdown--uppercase .dropdown-menu .dropdown-item {
  font-size: 0.7rem;
  text-transform: uppercase !important; }

.navbar-dropdown--bold .navbar-nav .nav-link {
  font-weight: bolder !important; }

.dropdown-menu .dropdown-menu {
  left: 100%;
  margin: 0;
  right: auto;
  top: -1px;
  border-radius: 3px; }

.navbar-nav .dropdown-submenu > .dropdown-menu::after {
  border: 0 !important; }

.navbar-nav .dropdown-submenu > a:before {
  content: "\f105";
  position: absolute;
  right: 15px;
  font-family: 'FontAwesome';
  font-size: 12px; }

.dropdown-menu-search {
  padding: 20px !important;
  min-width: 420px !important;
  position: absolute; }

.dropdown-form {
  padding: 0 !important;
  min-width: 340px !important; }

.dropdown-form-inner {
  padding: 1rem 1.5rem !important; }

.dropdown-form .btn-close-dropdown {
  display: none; }

@media (max-width: 991px) {
  .dropdown-form {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100%; }

  .dropdown-form .btn-close-dropdown {
    display: inline-block !important;
    width: auto !important;
    position: relative;
    z-index: 100;
    right: 0;
    float: right;
    font-size: 20px !important;
    color: #818a91; }

  .dropdown-form .btn-close-dropdown:hover,
  .dropdown-form .btn-close-dropdown:focus {
    color: #2b2b2c !important; } }
@media screen and (min-width: 992px) {
  .megamenu {
    position: static; }

  .megamenu ul {
    padding-bottom: 15px; }

  .megamenu > .dropdown-menu {
    left: auto;
    right: 0;
    min-width: 100%; }

  .megamenu > .dropdown-menu-sm {
    min-width: 600px; } }
.navbar .dropdown-md .dropdown-menu {
  min-width: 780px; }

.navbar .dropdown-lg .dropdown-menu {
  width: 850px; }

.mega-dropdown-menu {
  padding: 1.5rem; }

.mega-dropdown-menu > li {
  border: 0; }

.mega-dropdown-menu .megadropdown-links {
  padding: 0;
  margin: 0;
  list-style: none; }

.mega-dropdown-menu .megadropdown-links .dropdown-item {
  display: block;
  padding: 6px 0;
  clear: both;
  line-height: 1.42857143;
  font-size: 0.8rem;
  text-transform: capitalize;
  font-weight: 400; }

.navbar-dropdown--caps .mega-dropdown-menu > li > ul > li > a {
  font-size: 0.7rem;
  text-transform: uppercase; }

.mega-dropdown-menu > li > ul > li > a:hover {
  color: #0087be; }

.mega-dropdown-menu .dropdown-header {
  padding: 14px 0; }

.mega-dropdown-menu .meganav-section-title {
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.7);
  font-size: 0.875rem;
  font-weight: bolder;
  text-transform: none; }

.navbar-dropdown--inverse .mega-dropdown-menu .meganav-section-title {
  color: rgba(255, 255, 255, 0.7); }

.mega-dropdown-menu .meganav-section-title.text-uppercase {
  font-size: 0.75rem; }

.mega-dropdown-menu .meganav-section-title > a {
  color: #666; }

.mega-dropdown-menu .meganav-section-title > a:hover {
  color: #0087be; }

.mega-dropdown-col-cover {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow: hidden; }

.mega-dropdown-col-cover--left {
  border-radius: 3px 0 0 3px; }

.mega-dropdown-col-inner {
  height: 100%;
  padding: 2rem; }

@media (min-width: 992px) {
  .navbar-main .mega-dropdown-menu > .mega-dropdown-col {
    border-right: 1px solid #f9f9f9;
    padding: 0; }

  .navbar-main .mega-dropdown-menu > li > ul {
    padding-left: 2rem;
    padding-right: 2rem; }

  .mega-dropdown-menu .mega-dropdown-col-icon > a {
    position: relative;
    display: block;
    text-align: center;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #f9f9f9; }

  .mega-dropdown-menu .mega-dropdown-col-icon > a:hover {
    color: #0087be; }

  .mega-dropdown-menu .mega-dropdown-col-icon > a > i {
    font-size: 90px; }

  .navbar-dropdown--inverse .mega-dropdown-menu .meganav-section-title {
    color: rgba(255, 255, 255, 0.7) !important; }

  .navbar-dropdown--inverse .mega-dropdown-menu .meganav-section-title > a {
    color: rgba(255, 255, 255, 0.7); }

  .navbar-dropdown--inverse .mega-dropdown-menu .meganav-section-title > a:hover {
    color: #0087be; }

  .mega-dropdown-menu .mega-dropdown-col:last-child > ul {
    border-right: 0; } }
@media (max-width: 991px) {
  .mega-dropdown-menu {
    padding: 0 !important; }

  .mega-dropdown-menu .dropdown-header {
    padding: 0.75rem 2rem !important;
    border-bottom: 1px solid #f9f9f9; }

  .mega-dropdown-menu .mega-dropdown-col-icon {
    display: none; }

  .mega-dropdown-menu > li {
    padding: 0; }

  .mega-dropdown-menu > li > ul > li {
    border-bottom: 1px solid #f9f9f9; }

  .mega-dropdown-menu > li > ul > li > a {
    padding: 14px 30px;
    padding-left: 50px !important; }

  .mega-dropdown-menu > li > ul > li > a:before {
    content: "\f178";
    font-family: "FontAwesome";
    position: relative;
    left: -6px; }

  .navbar-inverse .mega-dropdown-menu .meganav-section-title {
    color: rgba(255, 255, 255, 0.7); }

  .navbar-inverse .mega-dropdown-menu .dropdown-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); }

  .navbar-dropdown--inverse .mega-dropdown-menu > li > ul > li {
    border-bottom: 1px solid rgba(30, 30, 30, 0.7); } }
.navbar.sps--blw {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000; }

@media (min-width: 992px) {
  .navbar.sps--blw .navbar-nav .nav-link {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem; } }
.global-search-toggler {
  display: none; }

@media (max-width: 991px) {
  .navbar {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem; }

  .navbar-collapse {
    margin-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1); }

  .navbar.sps--blw {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    max-height: 450px;
    overflow-y: scroll; }

  .navbar-nav .nav-link {
    color: #666;
    padding: 0.875rem 1rem;
    margin-right: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); }

  .navbar-nav .nav-link:focus,
  .navbar-nav .nav-link:hover,
  .navbar-nav .show .nav-link,
  .navbar-nav .show .nav-link:focus,
  .navbar-nav .show .nav-link:hover {
    color: #0087be;
    background: transparent;
    border-radius: 0 !important; }

  .navbar-search-widget {
    padding: 1rem 0; }

  .navbar-search-widget.b-xs-bottom {
    border-color: rgba(0, 0, 0, 0.1); }

  .navbar-nav .nav-item > .dropdown-menu {
    border-radius: 0;
    padding: 0;
    box-shadow: none; }

  .navbar-inverse .navbar-nav .dropdown-menu {
    background: transparent; }

  .navbar-nav .dropdown-menu .dropdown-item {
    padding: 0.75rem 2rem;
    line-height: 1.42857143;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); }

  .navbar-inverse .dropdown-menu .dropdown-item {
    color: rgba(255, 255, 255, 0.7); }

  .navbar-inverse .dropdown-menu .dropdown-item:focus,
  .navbar-inverse .dropdown-menu .dropdown-item:hover {
    color: #0087be; }

  .navbar-nav .dropdown-submenu .dropdown-menu {
    padding: 0;
    box-shadow: none; }

  .navbar-nav .dropdown-submenu .dropdown-menu .dropdown-item {
    padding-left: 3rem; }

  .navbar-toggler {
    font-size: 30px;
    cursor: pointer; }

  .navbar-toggler:hover,
  .navbar-toggler[aria-expanded=true] {
    background-color: transparent;
    border-color: transparent;
    color: rgba(0, 0, 0, 0.8);
    outline: none; }

  .navbar-toggler:focus {
    outline: none; }

  .global-search-toggler {
    display: inline-block;
    background-color: transparent;
    border-color: transparent;
    color: rgba(0, 0, 0, 0.8);
    padding: .625rem .75rem;
    cursor: pointer;
    line-height: 1;
    background: 0 0;
    border-radius: .25rem;
    outline: 0; }

  .global-search-toggler > i {
    color: rgba(0, 0, 0, 0.5);
    font-size: 20px; }

  .global-search-toggler:hover i {
    color: rgba(0, 0, 0, 0.8); }

  .navbar--inverse .navbar-toggle {
    background-color: transparent; }

  .navbar--inverse .navbar-toggle:focus,
  .navbar--inverse .navbar-toggle:hover {
    background-color: transparent;
    border-color: transparent; }

  .navbar--inverse .navbar-toggle .icon-bar {
    background-color: rgba(255, 255, 255, 0.5); }

  .navbar--inverse .navbar-toggle:focus .icon-bar,
  .navbar--inverse .navbar-toggle:hover .icon-bar {
    background-color: rgba(255, 255, 255, 0.7); }

  .navbar--inverse .navbar-icon-btn {
    background-color: transparent;
    border: 1px solid transparent; }

  .navbar--inverse .navbar-icon-btn i {
    color: rgba(255, 255, 255, 0.5); }

  .navbar--inverse .navbar-icon-btn:hover i {
    color: rgba(255, 255, 255, 0.7); } }
@media (min-width: 992px) {
  .body-wrap.has-navbar-aside {
    margin-left: 18.125rem; }

  .navbar-aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 18.125rem;
    z-index: 500; }

  .navbar-aside .container {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column; }

  .navbar-aside .navbar {
    height: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column; }

  .navbar-aside .navbar .navbar-collapse {
    width: 100%;
    padding: 0 4rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column; }

  .navbar-aside .navbar .navbar-nav {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-left: 0 !important;
    margin-right: 0 !important; }

  .navbar-aside .navbar-brand {
    width: 100%;
    padding: 0 4rem;
    margin: 2rem 0; } }
@media (min-width: 992px) and (min-width: 992px) {
  .navbar-aside .navbar-expand-lg .navbar-nav .nav-link {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 0;
    padding-right: 0; }

  .navbar-aside .navbar-expand-lg .navbar-nav .nav-link::after {
    content: "\f3d3";
    font-family: "Ionicons";
    font-size: 10px;
    margin-left: 0.875rem; } }
@media (min-width: 992px) {
  .navbar-aside .navbar .dropdown .dropdown-menu {
    margin-left: 12rem;
    top: 1rem; }

  .navbar-aside .navbar .dropdown-submenu .dropdown-menu {
    margin-left: 0; }

  .navbar-aside .megamenu {
    position: relative; }

  .navbar-aside .navbar-expand-lg .navbar-nav .megamenu .dropdown-menu {
    min-width: 900px;
    left: 0;
    right: auto; } }
.navbar-header {
  float: none; }

.modal-fullscreen-menu .close {
  opacity: 1;
  padding: 0.875rem;
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 1;
  font-size: 1rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.5); }
.modal-fullscreen-menu .close:hover {
  color: rgba(255, 255, 255, 0.8); }
.modal-fullscreen-menu .modal-dialog {
  margin: 0 auto;
  width: 100%;
  max-width: 768px;
  display: flex;
  height: 100%;
  align-items: center;
  z-index: 100; }
.modal-fullscreen-menu .modal-content {
  background-color: transparent;
  box-shadow: none;
  border: none; }
.modal-fullscreen-menu .list-group {
  text-align: center;
  margin: 0 auto;
  width: 100%; }
  .modal-fullscreen-menu .list-group a {
    font-size: 200%;
    font-weight: 200;
    letter-spacing: 0.05em;
    border: none;
    transition: all 0.25s ease;
    background-color: transparent;
    color: white;
    padding: 7.5vh 0;
    height: 5vh;
    font-size: 5vh;
    line-height: 0; }
    .modal-fullscreen-menu .list-group a:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: white;
      z-index: -1;
      opacity: 0;
      transform: scale3d(0.7, 1, 1);
      transition: transform 0.4s, opacity 0.4s; }
    .modal-fullscreen-menu .list-group a:hover {
      color: black; }
      .modal-fullscreen-menu .list-group a:hover:before {
        transform: translate3d(0, 0, 0);
        opacity: 1; }

.modal-fullscreen-menu::before {
  background: rgba(0, 0, 0, 0.9);
  content: '';
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1; }

html {
  box-sizing: border-box; }

@-ms-viewport {
  width: device-width; }
html {
  font-size: 16px;
  -ms-overflow-style: scrollbar; }

body {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: #2b2b2c;
  background-color: #fff; }

iframe {
  border: 0; }

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: relative;
  margin-left: 0; }

a:hover {
  text-decoration: none; }

.slice {
  padding-top: 4rem;
  padding-bottom: 4rem;
  position: relative; }

.slice-xs {
  padding-top: 1rem;
  padding-bottom: 1rem;
  position: relative; }

.slice-sm {
  padding-top: 2rem;
  padding-bottom: 2rem;
  position: relative; }

.slice-lg {
  padding-top: 6rem;
  padding-bottom: 6rem;
  position: relative; }

.slice-xl {
  padding-top: 8rem;
  padding-bottom: 8rem;
  position: relative; }

.slice--offset-top {
  padding-top: 10rem !important; }

.slice--offset-bottom {
  padding-bottom: 10rem !important; }

.slice--arrow:before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(255, 255, 255, 0);
  border-width: 30px;
  margin-left: -30px;
  z-index: 600; }

.slice--arrow.slice--arrow-white:before {
  border-top-color: #fff; }

.slice--arrow.slice--arrow.bg-base-1:before {
  border-top-color: #0087be; }

.slice--arrow.slice--arrow.bg-base-2:before {
  border-top-color: #292f36; }

.slice--arrow.slice--arrow.bg-base-3:before {
  border-top-color: #818a91; }

.slice--arrow.slice--arrow.bg-base-4:before {
  border-top-color: #2B2B2B; }

.slice--arrow.slice--arrow.bg-base-5:before {
  border-top-color: #FFF; }

@media (min-width: 768px) {
  .container-xs {
    width: 600px; }

  .container-sm {
    width: 750px; } }
@media (min-width: 992px) {
  .container-xs {
    width: 750px; }

  .container-sm {
    width: 870px; } }
@media (min-width: 1200px) {
  .container-xs {
    width: 750px; }

  .container-sm {
    width: 900px; } }
.row-no-padding {
  margin: 0; }

.row-no-padding > [class*="col-"] {
  padding-left: 0 !important;
  padding-right: 0 !important; }

.row-sm-padding > [class*="col-"] {
  padding-left: 5px !important;
  padding-right: 5px !important; }

.cols-space > div[class*='col-']:not(:last-child) {
  margin-bottom: 2rem; }

@media (max-width: 575px) {
  .cols-xs-space > div[class*='col-']:not(:last-child) {
    margin-bottom: 2.5rem; } }
@media (max-width: 767px) {
  .cols-sm-space > div[class*='col-']:not(:last-child) {
    margin-bottom: 2rem; } }
@media (max-width: 991px) {
  .cols-md-space > div[class*='col-']:not(:last-child) {
    margin-bottom: 2rem; } }
@media (max-width: 1199px) {
  .cols-lg-space > div[class*='col-']:not(:last-child) {
    margin-bottom: 2rem; } }
@media (max-width: 575px) {
  .row-wrapper > .row .col-12:not(:last-child),
  .row-wrapper > .row:not(:last-child) {
    margin-bottom: 2rem; } }
@media (min-width: 576px) and (max-width: 767px) {
  .row-wrapper > .row .col-sm-12:not(:last-child),
  .row-wrapper > .row:not(:last-child) {
    margin-bottom: 2rem; } }
@media (min-width: 768px) and (max-width: 991px) {
  .row-wrapper .row .col-md-12:not(:last-child),
  .row-wrapper .row:not(:last-child) {
    margin-bottom: 2rem; } }
@media (min-width: 992px) {
  .row-wrapper > .row:not(:last-child) {
    margin-bottom: 2rem; } }
@media (min-width: 992px) {
  .container-over-top {
    min-height: 200px;
    max-width: 1200px;
    margin: -150px auto 0;
    z-index: 10;
    position: relative;
    padding-top: 0; }

  .container-over-top .container-inner {
    border-radius: 0.25rem;
    padding: 2rem; }

  .container-over-top--style-2 {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
    border-bottom: medium double #ccc; } }
.col-wrapper--text {
  padding: 3rem; }

@media (min-width: 992px) {
  .col-wrapper--text {
    padding: 3rem 7rem; } }
.col-wrapper--spaced {
  padding: 3rem; }

.col-wrapper--spaced-x {
  padding-left: 3rem;
  padding-right: 3rem; }

.col-wrapper--spaced-y {
  padding-top: 3rem;
  padding-bottom: 3rem; }

/* Pace: Page Progress Bar */
.pace .pace-progress {
  background: #0087be !important; }

/* Hamburgers */
.hamburger {
  padding: 0; }

.hamburger-box {
  width: 20px;
  height: 18px; }

.hamburger-inner,
.hamburger-inner:after,
.hamburger-inner:before {
  width: 20px;
  height: 2px;
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.5); }

.hamburger:hover:not(.is-active) .hamburger-inner,
.hamburger:hover:not(.is-active) .hamburger-inner:after,
.hamburger:hover:not(.is-active) .hamburger-inner:before {
  background-color: rgba(0, 0, 0, 0.8); }

.hamburger.is-active .hamburger-inner:after,
.hamburger.is-active .hamburger-inner:before {
  background-color: #0087be; }

.hamburger:not(.is-active) .hamburger-inner:before {
  top: 6px !important; }

.hamburger:not(.is-active) .hamburger-inner:after {
  top: 12px !important; }

.navbar-inverse .hamburger-inner,
.navbar-inverse .hamburger-inner:after,
.navbar-inverse .hamburger-inner:before {
  background-color: rgba(255, 255, 255, 0.5); }

.navbar-inverse .hamburger:hover:not(.is-active) .hamburger-inner,
.navbar-inverse .hamburger:hover:not(.is-active) .hamburger-inner:after,
.navbar-inverse .hamburger:hover:not(.is-active) .hamburger-inner:before {
  background-color: rgba(255, 255, 255, 0.7); }

.navbar-inverse .hamburger.is-active .hamburger-inner:after,
.navbar-inverse .hamburger.is-active .hamburger-inner:before {
  background-color: #0087be; }

/* Light Gallery */
.lightbox-item {
  cursor: pointer; }

/* Masonry - Isotope */
.masonry-item {
  margin-bottom: 30px; }

.masonry-item .block {
  margin-bottom: 0 !important; }

/* No UI Slider */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.noUi-target {
  position: relative;
  direction: ltr; }

.noUi-base {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1; }

.noUi-connect {
  position: absolute;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0; }

.noUi-origin {
  position: absolute;
  height: 0;
  width: 0; }

.noUi-handle {
  position: relative;
  z-index: 1; }

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  -webkit-transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
  transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s; }

.noUi-state-drag * {
  cursor: inherit !important; }

.noUi-base,
.noUi-handle {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0); }

.noUi-horizontal {
  height: 18px; }

.noUi-horizontal .noUi-handle {
  width: 34px;
  height: 28px;
  left: -17px;
  top: -6px; }

.noUi-vertical {
  width: 18px; }

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  left: -6px;
  top: -17px; }

.noUi-target {
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #D3D3D3;
  box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB; }

.noUi-connect {
  background: #3FB8AF;
  box-shadow: inset 0 0 3px rgba(51, 51, 51, 0.45);
  -webkit-transition: background 450ms;
  transition: background 450ms; }

.noUi-draggable {
  cursor: ew-resize; }

.noUi-vertical .noUi-draggable {
  cursor: ns-resize; }

.noUi-handle {
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB; }

.noUi-active {
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB; }

.noUi-handle:after,
.noUi-handle:before {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #E8E7E6;
  left: 14px;
  top: 6px; }

.noUi-handle:after {
  left: 17px; }

.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px; }

.noUi-vertical .noUi-handle:after {
  top: 17px; }

[disabled] .noUi-connect {
  background: #B8B8B8; }

[disabled] .noUi-handle,
[disabled].noUi-handle,
[disabled].noUi-target {
  cursor: not-allowed; }

.noUi-pips,
.noUi-pips * {
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.noUi-pips {
  position: absolute;
  color: #999; }

.noUi-value {
  position: absolute;
  text-align: center; }

.noUi-value-sub {
  color: #ccc;
  font-size: 10px; }

.noUi-marker {
  position: absolute;
  background: #CCC; }

.noUi-marker-large,
.noUi-marker-sub {
  background: #AAA; }

.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%; }

.noUi-value-horizontal {
  -webkit-transform: translate3d(-50%, 50%, 0);
  transform: translate3d(-50%, 50%, 0); }

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px; }

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px; }

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px; }

.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%; }

.noUi-value-vertical {
  -webkit-transform: translate3d(0, 50%, 0);
  transform: translate3d(0, 50%, 0);
  padding-left: 25px; }

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px; }

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px; }

.noUi-marker-vertical.noUi-marker-large {
  width: 15px; }

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center; }

.noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%; }

.noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%; }

.noUi-target {
  border-radius: 0;
  box-shadow: none;
  border: 0;
  background: #e8e8e8;
  margin: 15px 0; }

.noUi-horizontal {
  height: 2px; }

.noUi-horizontal .noUi-handle {
  top: -5px;
  left: -1px; }

.noUi-vertical {
  width: 3px; }

.noUi-connect {
  background: #0087be;
  box-shadow: none; }

.noUi-horizontal .noUi-handle,
.noUi-vertical .noUi-handle {
  width: 12px;
  height: 12px;
  border: 0;
  border-radius: 100%;
  box-shadow: none;
  cursor: pointer;
  position: relative;
  background-color: #0087be;
  transition: box-shadow 0.2s,-webkit-transform 0.2s;
  transition: box-shadow 0.2s,transform 0.2s;
  transition: box-shadow 0.2s,transform 0.2s,-webkit-transform 0.2s; }

.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before,
.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  display: none; }

.noUi-horizontal .noUi-handle.noUi-active,
.noUi-vertical .noUi-handle.noUi-active {
  -webkit-transform: scale(1.3);
  transform: scale(1.3); }

.noUi-horizontal .noUi-active,
.noUi-vertical .noUi-active {
  box-shadow: 0 0 0 10px rgba(0, 0, 0, 0.04); }

.input-slider--blue .noUi-connect {
  background: #03A9F4; }

.input-slider--blue.noUi-horizontal .noUi-handle,
.input-slider--blue.noUi-vertical .noUi-handle {
  background-color: #03A9F4; }

.input-slider--red .noUi-connect {
  background: #ff5652; }

.input-slider--red.noUi-horizontal .noUi-handle,
.input-slider--red.noUi-vertical .noUi-handle {
  background-color: #ff5652; }

.input-slider--amber .noUi-connect {
  background: #ffc107; }

.input-slider--amber.noUi-horizontal .noUi-handle,
.input-slider--amber.noUi-vertical .noUi-handle {
  background-color: #ffc107; }

.input-slider--green .noUi-connect {
  background: #32c787; }

.input-slider--green.noUi-horizontal .noUi-handle,
.input-slider--green.noUi-vertical .noUi-handle {
  background-color: #32c787; }

/* Disabled state */
[disabled] .noUi-connect,
[disabled].noUi-connect {
  background: #b2b2b2; }

[disabled] .noUi-handle,
[disabled].noUi-origin {
  cursor: not-allowed; }

/* Range slider value labels */
.range-slider-value {
  font-size: 0.875rem;
  font-weight: 500; }

.range-slider-wrapper .upper-info {
  font-weight: 400;
  margin-bottom: 5px; }

.input-slider-value-output {
  background: #333;
  color: #fff;
  padding: 4px 8px;
  position: relative;
  top: 12px;
  font-size: 11px;
  border-radius: 2px; }

.input-slider-value-output:after {
  bottom: 100%;
  left: 10px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #333;
  border-width: 4px;
  margin-left: -4px; }

.input-slider-value-output.left:after {
  left: 10px;
  right: auto; }

.input-slider-value-output.right:after {
  right: 10px;
  left: auto; }

.swiper-wrapper {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.swiper-pagination--style-1 {
  display: inline-block;
  position: relative; }

.swiper-pagination--style-1 .swiper-pagination-bullet {
  width: 14px;
  height: 14px;
  display: inline-block;
  margin-right: 4px;
  border-radius: 100%;
  background: #000;
  opacity: 0.2; }

.swiper-pagination--style-1 .swiper-pagination-bullet-active {
  background: #0087be; }

.swiper-pagination-bullet-active {
  background: #0087be; }

.swiper-container {
  height: 100%; }


.swiper-wrapper .swiper-slide:active,
.swiper-wrapper .swiper-slide:active *:not(.btn):not(.link) {
  cursor: grabbing !important;
  cursor: -webkit-grabbing !important;
  cursor: -moz-grabbing !important; }

.swiper-container .swiper-slide .animated {
  opacity: 0; }

.swiper-container .swiper-slide .animated.animation-ended {
  opacity: 1; }

@media (max-width: 767px) {
  .swiper-slide {
    height: auto !important; } }
.swiper-wrapper .swiper-slide .btn {
  cursor: pointer !important; }

.swiper-container-centered {
  margin: 20px auto; }

.swiper-container-centered .swiper-slide {
  width: 50%;
  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center; }

.swiper-container-vertical .swiper-slide {
  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center; }

.swiper-container-vertical .swiper-slide-inner-right {
  padding-left: 50px; }

.swiper-container-vertical .swiper-pagination {
  right: auto;
  left: 10px; }

.swiper-container-vertical .swiper-pagination .swiper-pagination-bullet {
  margin: 8px 0;
  width: 6px;
  height: 6px; }

.swiper-container-vertical .swiper-pagination .swiper-pagination-bullet-active {
  width: 10px;
  height: 10px;
  margin-left: -2px; }

.swiper-button-next,
.swiper-button-prev {
  position: absolute;
  top: 50%;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  margin-top: -22px;
  border-radius: 100%;
  z-index: 10;
  cursor: pointer;
  background: none;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.5); }

.swiper-button-prev:after {
  content: "\f3d2";
  font-family: "Ionicons"; }

.swiper-button-next:after {
  content: "\f3d3";
  font-family: "Ionicons"; }

.background-image-holder .swiper-button-next,
.background-image-holder .swiper-button-prev,
.swiper-button--hover-only.swiper-button-next,
.swiper-button--hover-only.swiper-button-prev {
  opacity: 0; }

.background-image-holder:hover .swiper-button-next,
.background-image-holder:hover .swiper-button-prev,
.swiper-button--hover-only.swiper-button-next,
.swiper-button--hover-only.swiper-button-prev {
  opacity: 1; }

.swiper-button--style-1 {
  width: auto;
  margin: 0;
  top: 0;
  left: 0;
  right: 0;
  display: inline-block;
  position: static;
  background: transparent;
  color: #333;
  font-size: 24px; }

.swiper-button--style-1:active,
.swiper-button--style-1:focus,
.swiper-button--style-1:hover {
  background: transparent;
  color: #0087be;
  opacity: 1; }

.swiper-button--style-1.swiper-button-next {
  margin-left: 10px; }

.swiper-button--style-2 {
  position: absolute;
  top: 50%;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  margin-top: -22px;
  border-radius: 100%;
  z-index: 10;
  cursor: pointer;
  background: none;
  background: white;
  color: #999;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.swiper-button--style-2.swiper-button-prev {
  left: -10px; }

.swiper-button--style-2.swiper-button-next {
  right: -10px; }

.swiper-button--style-2.swiper-button-disabled {
  opacity: 0; }

.swiper-button--style-2:hover {
  background: white;
  color: #0087be; }

.swiper-container .swiper-caption {
  text-shadow: 0;
  text-align: left;
  background: rgba(0, 0, 0, 0.5);
  background: -moz-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  background: -webkit-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=  '#00000000', endColorstr='#a6000000',GradientType=0 );
  color: #fff; }

.swiper-container .swiper-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px 30px; }

.swiper-container .swiper-caption .caption-title {
  display: block;
  margin: 10px 0 0;
  padding: 0;
  font-size: 22px;
  line-height: 1.1;
  color: #fff;
  font-weight: 500; }

.swiper-container .swiper-caption .caption-subtitle {
  display: block;
  margin: 10px 0 0;
  padding: 0;
  font-size: 16px;
  line-height: 1.1;
  color: #ddd;
  font-weight: 400; }

.swiper-container .swiper-caption .meta-info-cells li,
.swiper-container .swiper-caption .meta-info-cells li a,
.swiper-container .swiper-caption .meta-info-cells li i {
  color: #ddd; }

.gallery-container {
  position: relative; }

.gallery-container .gallery-top {
  width: 100%;
  padding: 1rem;
  background: #eceeef;
  border-radius: 0.25rem; }

.gallery-container .gallery-top .swiper-button-next,
.gallery-container .gallery-top .swiper-button-prev {
  opacity: 0; }

.gallery-container .gallery-top:hover .swiper-button-next,
.gallery-container .gallery-top:hover .swiper-button-prev {
  opacity: 1; }

.gallery-container .gallery-top .swiper-slide img {
  max-width: 100%; }

.gallery-container .gallery-thumbs {
  box-sizing: border-box;
  padding: 1rem 0; }

.gallery-container .gallery-thumbs .swiper-slide {
  width: 25%;
  opacity: 0.4;
  background: transparent; }

.gallery-container .gallery-thumbs .swiper-slide-active {
  opacity: 1; }

.gallery-container .gallery-thumbs .swiper-slide img {
  max-width: 100%; }

.gallery-container.gallery-container--style-2 .gallery-top {
  background: transparent; }

.milestone-counter {
  margin: 30px 0; }

.milestone-counter .milestone-count {
  font-size: 60px;
  font-family: "Lato", sans-serif;
  padding: 0;
  margin: 0;
  font-weight: 600;
  text-transform: uppercase; }

.milestone-counter .milestone-count.milestone-count-sm {
  font-size: 52px !important; }

.milestone-counter .milestone-count.milestone-count-xs {
  font-size: 32px !important; }

.milestone-counter .milestone-info {
  font-family: "Lato", sans-serif;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase; }

.milestone-counter .milestone-icon {
  display: block;
  line-height: 1; }

.milestone-counter .milestone-icon .fa,
.milestone-counter .milestone-icon .icon {
  font-size: 60px;
  line-height: 1;
  margin-bottom: 30px; }

.milestone-counter .milestone-icon .icon:before {
  line-height: 0; }

.milestone-counter .milestone-delimiter {
  display: block;
  width: 60px;
  margin-bottom: 14px;
  border-bottom: 3px solid #0087be;
  border-bottom: 3px solid #0087be; }

.milestone-counter .milestone-delimiter:after,
.milestone-counter .milestone-delimiter:before {
  display: table;
  content: "";
  width: 100%; }

.milestone-counter .milestone-delimiter:after {
  clear: both; }

@media (max-width: 767px) {
  .milestone-counter {
    margin-bottom: 60px; } }
.morphext > .animated {
  display: inline-block; }

.easy-pie-chart {
  display: inline-block;
  position: relative; }

.easy-pie-chart-value {
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
  height: 100%; }

.easy-pie-chart-value:after {
  content: "%";
  font-size: 12px; }

.easy-pie-chart-title {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.5rem; }

.rev-slider-outer-wrapper {
  position: relative;
  overflow: hidden;
  background: #FFF; }

.rev_slider_wrapper {
  margin: 0 auto;
  padding: 0;
  background-color: transparent; }

.rev_slider_wrapper .play-video {
  position: relative;
  top: auto;
  left: auto; }

.rev_slider_wrapper .title--style-1,
.rev_slider_wrapper .tp-caption.title--style-1 {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0 0 0 0;
  border-style: none;
  border-width: 0;
  color: white;
  font-family: "Lato", sans-serif;
  font-size: 70px;
  font-style: normal;
  font-weight: 800;
  line-height: 70px;
  padding: 10px 0;
  text-decoration: none; }

.rev_slider_wrapper .subtitle--style-1,
.rev_slider_wrapper .tp-caption.subtitle--style-1 {
  font-size: 1rem; }

.rev_slider_wrapper .content--style-1,
.rev_slider_wrapper .tp-caption.content--style-1 {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0 0 0 0;
  border-style: none;
  border-width: 0;
  color: #999999;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding: 0;
  text-decoration: none; }

.title--style-2,
.tp-caption.title--style-2 {
  color: white;
  font-size: 65px;
  line-height: 70px;
  font-weight: 700;
  font-style: normal;
  font-family: "Lato", sans-serif;
  padding: 21px 30px 16px;
  text-decoration: none;
  text-align: left;
  background-color: rgba(17, 17, 17, 0.9);
  border-color: transparent;
  border-style: none;
  border-width: 0;
  border-radius: 0 0 0 0; }

.category-title--style-2,
.tp-caption.category-title--style-2 {
  color: #111111;
  font-size: 20px;
  line-height: 20px;
  font-weight: 700;
  font-style: normal;
  font-family: "Lato", sans-serif;
  padding: 21px 30px 16px;
  text-decoration: none;
  text-align: left;
  background-color: rgba(255, 255, 255, 0.9);
  border-color: transparent;
  border-style: none;
  border-width: 0;
  border-radius: 0 0 0 0;
  letter-spacing: 3px; }

.non-generic-icon,
.tp-caption.non-generic-icon {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0);
  border-radius: 0 0 0 0;
  border-style: solid;
  border-width: 0;
  color: white;
  font-family: "Lato", sans-serif;
  font-size: 70px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 3px;
  line-height: 30px;
  padding: 0;
  text-align: left;
  text-decoration: none; }

.rev_slider_wrapper .handwritten-title,
.rev_slider_wrapper .handwritten-title {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0 0 0 0;
  border-style: none;
  border-width: 0;
  color: white;
  font-family: Pacifico;
  font-size: 70px;
  font-style: normal;
  font-weight: 800;
  line-height: 70px;
  padding: 10px 0;
  text-decoration: none; }

.rev_slider_wrapper .handwritten-subtitle,
.rev_slider_wrapper .tp-caption.handwritten-subtitle {
  background-color: transparent;
  border-color: transparent;
  border-radius: 0 0 0 0;
  border-style: none;
  border-width: 0;
  color: white;
  font-family: Pacifico;
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px;
  padding: 10px 0;
  text-decoration: none; }

@media (min-width: 768px) {
  .rev_slider_wrapper .rev-slider-search-wrapper {
    width: 650px; } }
@media (max-width: 767px) {
  .rev_slider_wrapper .rev-slider-search-wrapper {
    width: 450px; } }
@media (max-width: 440px) {
  .rev_slider_wrapper .rev-slider-search-wrapper {
    width: 300px; } }
.tp-tab-title {
  font-family: "Lato", sans-serif !important; }

/* COUNTDOWN TIMER */
.countdown .countdown-item {
  display: inline-block; }

.countdown .countdown-digit,
.countdown .countdown-label {
  font-size: 2rem;
  font-weight: 300;
  font-family: "Lato", sans-serif; }

.countdown .countdown-label {
  font-size: 1.2rem;
  padding: 0 10px; }

.countdown-sm .countdown-digit,
.countdown-sm .countdown-label {
  font-size: 1.4rem; }

.countdown-sm .countdown-label {
  font-size: 0.875rem;
  padding: 0 10px; }

[data-countdown-label="hide"] .countdown-label:not(.countdown-days) {
  display: none; }

[data-countdown-label="show"] .countdown-separator {
  display: none; }

.countdown--style-1 .countdown-item {
  margin-right: 10px; }

.countdown--style-1 .countdown-item:last-child {
  margin-right: 0; }

.countdown--style-1 .countdown-digit {
  display: block;
  width: 60px;
  height: 60px;
  background: #f3f3f3;
  color: #333;
  font-size: 22px;
  font-weight: 400;
  text-align: center;
  line-height: 60px;
  font-family: "Lato", sans-serif; }

.countdown--style-1 .countdown-label {
  display: block;
  margin-top: 5px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  font-family: "Lato", sans-serif;
  text-transform: uppercase; }

.countdown--style-1-v1 .countdown-digit {
  background: rgba(0, 0, 0, 0.5);
  color: #fff; }

.btn-back-to-top {
  display: inline-block;
  height: 40px;
  width: 40px;
  position: fixed;
  bottom: 40px;
  right: 10px;
  text-align: center;
  line-height: 40px;
  background: #0087be;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  border-radius: 100%;
  overflow: hidden;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s 0s, visibility 0s 0.3s;
  -moz-transition: opacity 0.3s 0s, visibility 0s 0.3s;
  transition: opacity 0.3s 0s, visibility 0s 0.3s; }

.btn-back-to-top:before {
  font-family: "Ionicons";
  content: "\f3d8";
  display: block;
  font-size: 1rem;
  color: #FFF; }

.btn-back-to-top.back-to-top-fade-out,
.btn-back-to-top.back-to-top-is-visible,
.no-touch .btn-back-to-top:hover {
  -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
  -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
  transition: opacity 0.3s 0s, visibility 0s 0s; }

.btn-back-to-top.back-to-top-is-visible {
  visibility: visible;
  opacity: 1; }

.btn-back-to-top.back-to-top-fade-out {
  opacity: 0.8; }

.no-touch .btn-back-to-top:hover {
  background-color: #e86256;
  opacity: 1; }

@media only screen and (min-width: 768px) {
  .btn-back-to-top {
    right: 20px;
    bottom: 20px; } }
@media only screen and (min-width: 1024px) {
  .btn-back-to-top {
    height: 40px;
    width: 40px;
    right: 30px;
    bottom: 30px;
    line-height: 40px; } }
.instafeed [class^=col-] {
  padding-left: 10px;
  padding-right: 10px; }

.instafeed img {
  margin-bottom: 20px; }

.instafeed.row-no-padding img {
  margin-bottom: 0; }

.paraxify {
  background-attachment: fixed;
  background-position: center center;
  background-size: cover; }

/* ==========  Blzee  ========== */
@font-face {
  font-family: 'Blzee';
  src: url("../fonts/blzee/blzee.eot");
  src: url("../fonts/blzee/blzee.eot?#iefix") format("embedded-opentype"), url("../fonts/blzee/blzee.woff") format("woff"), url("../fonts/blzee/blzee.ttf") format("truetype");
  font-weight: 400;
  font-style: normal; }
@font-face {
  font-family: 'Sue Ellen Francisco';
  src: url("../fonts/other/sue-ellen-francisco.ttf");
  src: url("../fonts/other/sue-ellen-francisco.ttf") format("truetype");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: 'Pacifico';
  src: url("../fonts/other/pacifico.ttf");
  src: url("../fonts/other/pacifico.ttf") format("truetype");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: 'YellowTail';
  src: url("../fonts/other/yellowtail-regular.ttf");
  src: url("../fonts/other/yellowtail-regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal; }
.st-container,
.st-content,
.st-pusher {
  height: 100%; }

.st-content {
  background: transparent; }

.st-content,
.st-content-inner {
  position: relative; }

.st-container {
  position: relative;
  overflow: hidden; }

.st-pusher {
  position: relative;
  right: 0;
  z-index: 99;
  height: 100%;
  -webkit-transition: -webkit-transform 0.5s;
  transition: transform 0.5s; }

.st-pusher::after {
  z-index: 1100;
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  background: rgba(0, 0, 0, 0.2);
  content: '';
  opacity: 0;
  -webkit-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s; }

.st-menu-open .st-pusher::after {
  width: 100%;
  height: 100%;
  opacity: 1;
  -webkit-transition: opacity 0.5s;
  transition: opacity 0.5s; }

.st-menu {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 100;
  visibility: hidden;
  width: 300px;
  height: 100%;
  overflow-y: scroll;
  background: #1f1f1f;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  border-left: 1px solid transparent; }

.st-menu::after {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  content: '';
  opacity: 1;
  -webkit-transition: opacity 0.5s;
  transition: opacity 0.5s; }

.st-menu-open .st-menu::after {
  width: 0;
  height: 0;
  opacity: 0;
  -webkit-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
  transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s; }

.st-menu-open {
  overflow: hidden; }

.st-menu::-webkit-scrollbar {
  display: none !important;
  width: 0 !important; }

.st-menu-title {
  font-size: 1rem;
  color: #818a91;
  font-weight: 600;
  margin: 10px 0 0;
  padding: 15px 20px 10px; }

.st-profile {
  background-image: url("../images/patterns/pattern-1.png");
  background-repeat: repeat;
  padding: 2rem; }

.st-profile-user-wrapper {
  display: table;
  width: 100%;
  vertical-align: middle; }

.profile-user-image {
  display: table-cell;
  padding-right: 1rem;
  width: 72px; }

.profile-user-image > img {
  max-width: 100%; }

.profile-user-info {
  display: inline-block;
  vertical-align: middle; }

.st-profile .profile-user-name {
  color: #0087be;
  font-size: 1rem;
  font-weight: 600; }

.st-profile .profile-user-email {
  font-weight: 400;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5); }

.st-menu-title {
  font-size: 0.75rem;
  text-transform: uppercase;
  color: #818a91; }

.st-menu-list > ul {
  margin: 0;
  padding: 0;
  list-style: none; }

.st-menu-list > ul > li {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500; }

.st-menu-list > ul > li > a {
  display: block;
  padding: 0.3125rem 2rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500;
  -webkit-transition: background 0.3s, box-shadow 0.3s;
  transition: background 0.3s, box-shadow 0.3s; }

.st-menu-list > ul > li > a:hover {
  color: rgba(255, 255, 255, 0.7); }

.st-menu-list > ul > li > a > i {
  margin-right: 1rem;
  display: inline-block;
  font-size: 20px;
  width: 20px;
  position: relative;
  top: 4px; }

.st-effect-1.st-menu-open .st-pusher {
  -webkit-transform: translate3d(-300px, 0, 0);
  transform: translate3d(-300px, 0, 0); }

.st-effect-1.st-menu-open .st-pusher > .st-content {
  overflow: hidden; }

.st-effect-1.st-menu {
  z-index: 1; }

.st-effect-1.st-menu-open .st-effect-1.st-menu {
  visibility: visible;
  -webkit-transition: -webkit-transform 0.5s;
  transition: transform 0.5s; }

.st-effect-1.st-menu::after {
  display: none; }

body {
  font-size: 1rem;
  font-family: "Roboto", sans-serif;
  color: #55595c; }

p:not(.lead) {
  font-size: 0.875rem;
  line-height: 1.5rem; }

a.link {
  color: #0087be;
  display: inline-block;
  text-decoration: none; }

a.link > i {
  margin-right: 5px; }

a.link:hover {
  color: #9a1313;
  text-decoration: none;
  text-decoration: underline; }

a.link--style-1 {
  color: #2b2b2c; }

a.link--style-1:hover {
  color: #0087be; }

a.link-lg {
  font-size: 1.25rem; }

a.link-sm {
  font-size: 0.875rem; }

a.link-xs {
  font-size: 0.75rem; }

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Lato", sans-serif;
  line-height: 1.46; }

.heading {
  margin: 0 0 6px;
  padding: 0;
  text-transform: none;
  font-family: "Lato", sans-serif;
  font-weight: 600;
  color: #111111;
  line-height: 1.46; }

.heading--base {
  font-family: "Roboto", sans-serif; }

.heading-light {
  color: rgba(0, 0, 0, 0.6); }

.heading > a {
  color: #111111; }

.heading:hover > a {
  color: #0087be !important; }

.heading-inverse {
  color: white; }

.heading-inverse > a {
  color: white; }

.heading-inverse:hover > a {
  color: #0087be !important; }

.heading > .icon,
.heading > a > .icon {
  display: inline-block;
  margin-right: 4px; }

.heading-1 {
  font-size: 2.5rem !important;
  line-height: 1.3; }

.heading-2 {
  font-size: 2rem !important;
  line-height: 1.3; }

.heading-3 {
  font-size: 1.5rem !important;
  line-height: 1.3; }

.heading-4 {
  font-size: 1.25rem !important; }

.heading-5 {
  font-size: 1.125rem !important; }

.heading-6 {
  font-size: 1rem !important; }

@media (max-width: 991px) {
  .heading-responsive.heading-1 {
    font-size: 1.66667rem !important;
    line-height: 1.3; }

  .heading-responsive.heading-2 {
    font-size: 2rem !important;
    line-height: 1.3; }

  .heading-responsive.heading-3 {
    font-size: 1.5rem !important;
    line-height: 1.3; }

  .heading-responsive.heading-4 {
    font-size: 1.25rem !important; }

  .heading-responsive.heading-5 {
    font-size: 1.125rem !important; }

  .heading-responsive.heading-6 {
    font-size: 1rem !important; } }
.heading-xs {
  font-size: 0.75rem !important; }

.heading-sm {
  font-size: 0.875rem !important; }

.heading-lg {
  font-size: 2.5rem !important; }

.heading-xl {
  font-size: 3.75rem !important; }

.heading-xl-x2 {
  font-size: 7.5rem !important; }

.heading-xxl {
  font-size: 5rem !important; }

@media (max-width: 767px) {
  .heading-xl-x2 {
    font-size: 3.75rem !important; } }
.fluid-paragraph {
  font-size: 1rem;
  width: 680px;
  margin: auto;
  padding: 0 20px;
  position: relative; }

.fluid-paragraph h1:not(.heading),
.fluid-paragraph h2:not(.heading),
.fluid-paragraph h3:not(.heading),
.fluid-paragraph h4:not(.heading),
.fluid-paragraph h5:not(.heading),
.fluid-paragraph h6:not(.heading) {
  text-transform: none !important; }

.fluid-paragraph-sm {
  width: 580px; }

.fluid-paragraph-md {
  width: 780px; }

.fluid-paragraph-lg {
  width: 880px; }

.paragraph-xs > p {
  font-size: 0.8rem;
  line-height: 1.4; }

.paragraph-sm > p {
  font-size: 0.875rem; }

.paragraph-lg > p {
  font-size: 1.25rem; }

.paragraph-excerpt {
  line-height: 1.6; }

@media (max-width: 767px) {
  .fluid-paragraph {
    width: 100% !important; } }
.quote-block-lg {
  line-height: 2.4rem; }

.quote-icon-lg {
  font-size: 2rem;
  font-weight: 600;
  display: inline-block;
  margin: 0 5px; }

small,
.small {
  font-size: 70%; }

strong {
  font-weight: 600; }

blockquote .blockquote-source {
  font-size: 0.875rem; }

blockquote .blockquote-source::before {
  content: '\2014 \00A0'; }

.blockquote-custom {
  border: 0;
  position: relative; }

.blockquote--style-1 {
  border: 0;
  font-family: 'Pacifico', sans-serif;
  font-size: 1.5rem; }

.blockquote--style-2 {
  border: 0;
  font-size: 1.3rem;
  color: #818a91; }

.blockquote--style-2:before {
  content: '\201C';
  position: absolute;
  left: -82px;
  top: -52px;
  font-size: 9em;
  opacity: 0.2;
  font-family: Georgia, "Times New Roman", Times, seri; }

.blockquote--style-2:after {
  content: '\201D';
  position: absolute;
  right: -82px;
  top: -52px;
  font-size: 9em;
  opacity: 0.2;
  font-family: Georgia, "Times New Roman", Times, serif; }

.icon-pic--style-1 {
  color: #0087be; }

.icon-pic-lg > i {
  font-size: 10rem; }

.icon-pic-lg > img {
  width: 10rem; }

.icon-pic-xl > i {
  font-size: 20rem; }

.icon-pic-xl > img {
  width: 20rem; }

.alert {
  font-size: 0.875rem; }

.alert strong {
  color: inherit !important;
  font-weight: 500; }

.badge {
  font-size: 0.625rem;
  font-weight: 400; }

.badge-inline {
  margin-right: 0.625rem; }

.badge-inline + span {
  top: 2px;
  position: relative;
  font-size: 0.875rem; }

.badge-inline + span > a {
  text-decoration: underline; }

.badge-md {
  padding: 0.5em 1em; }

.badge-lg {
  padding: 0.8em 1.2em; }

.badge a {
  color: #FFF;
  font-weight: 500; }

.badge-duration {
  color: #0087be;
  font-weight: 500; }

.badge-corner:empty {
  display: inline-block; }

.badge-corner {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-top: 66px solid #888;
  border-top-color: rgba(0, 0, 0, 0.3);
  border-left: 66px solid transparent;
  padding: 0;
  background-color: transparent;
  border-radius: 0; }

.badge-corner span {
  position: absolute;
  top: -52px;
  left: -28px;
  font-size: 16px;
  color: #fff; }

.badge-corner-base-1 {
  border-top-color: #0087be; }

.badge-corner-blue {
  border-top-color: #007aff; }

.badge-corner-green {
  border-top-color: #4cd964; }

.badge-corner-red {
  border-top-color: #ff3b30; }

.badge-corner-orange {
  border-top-color: #ff9500; }

.badge-corner-pink {
  border-top-color: #ff2d55; }

.badge-corner-yellow {
  border-top-color: #ffcc00; }

.badge-corner-purple {
  border-top-color: #5856d6; }

.badge-corner-black {
  border-top-color: #000000; }

.ribbon {
  position: absolute;
  top: 20px;
  right: -5px;
  padding: 0.875rem; }

.ribbon:after,
.ribbon:before {
  content: '';
  position: absolute;
  left: -9px;
  border-left: 10px solid transparent; }

.ribbon:before {
  top: 0; }

.ribbon:after {
  bottom: 0; }

.ribbon span {
  display: block;
  font-size: 0.875rem;
  font-weight: 600; }

.ribbon.bg-base-1 {
  border-right: 5px solid #eb5b5b; }

.ribbon.bg-base-1:before {
  border-top: 27px solid #0087be; }

.ribbon.bg-base-1:after {
  border-bottom: 27px solid #0087be; }

.ribbon.bg-base-2 {
  border-right: 5px solid #4a5561; }

.ribbon.bg-base-2:before {
  border-top: 27px solid #292f36; }

.ribbon.bg-base-2:after {
  border-bottom: 27px solid #292f36; }

.ribbon.bg-gray-dark {
  border-right: 5px solid #515153; }

.ribbon.bg-gray-dark:before {
  border-top: 27px solid #2b2b2c; }

.ribbon.bg-gray-dark:after {
  border-bottom: 27px solid #2b2b2c; }

.ribbon.bg-red {
  border-right: 5px solid #ff837d; }

.ribbon.bg-red:before {
  border-top: 27px solid #ff3b30; }

.ribbon.bg-red:after {
  border-bottom: 27px solid #ff3b30; }

.ribbon.bg-orange {
  border-right: 5px solid #ffb54d; }

.ribbon.bg-orange:before {
  border-top: 27px solid #ff9500; }

.ribbon.bg-orange:after {
  border-bottom: 27px solid #ff9500; }

.ribbon.bg-yellow {
  border-right: 5px solid #ffdb4d; }

.ribbon.bg-yellow:before {
  border-top: 27px solid #ffcc00; }

.ribbon.bg-yellow:after {
  border-bottom: 27px solid #ffcc00; }

.ribbon.bg-blue {
  border-right: 5px solid #4da2ff; }

.ribbon.bg-blue:before {
  border-top: 27px solid #007aff; }

.ribbon.bg-blue:after {
  border-bottom: 27px solid #007aff; }

.ribbon.bg-green {
  border-right: 5px solid #8be69b; }

.ribbon.bg-green:before {
  border-top: 27px solid #4cd964; }

.ribbon.bg-green:after {
  border-bottom: 27px solid #4cd964; }

.ribbon.bg-purple {
  border-right: 5px solid #9594e5; }

.ribbon.bg-purple:before {
  border-top: 27px solid #5856d6; }

.ribbon.bg-purple:after {
  border-bottom: 27px solid #5856d6; }

.ribbon.bg-pink {
  border-right: 5px solid #ff7a93; }

.ribbon.bg-pink:before {
  border-top: 27px solid #ff2d55; }

.ribbon.bg-pink:after {
  border-bottom: 27px solid #ff2d55; }

.block-wrapper > .block:not(:last-child) {
  margin-bottom: 2rem; }

.block {
  margin: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
  cursor: default;
  border-radius: 0.25rem 0.25rem 0.25rem;
  -moz-border-radius: 0.25rem 0.25rem 0.25rem; }

.block:after,
.block:before {
  display: table;
  content: ""; }

.block:after {
  clear: both; }

a > .block {
  cursor: pointer; }

.block.no-radius > .block-image img {
  border-radius: 0 !important; }

.block .lead {
  margin-bottom: 0; }

.block-stack-wrapper {
  border-radius: 0.25rem; }

.block-stack-wrapper .row {
  background: #fff; }

@media (max-width: 991px) {
  .block-stack-wrapper .row {
    margin-bottom: 20px; } }
.block .block-body {
  padding: 1.5rem 1.5rem; }

.block .block-body > p {
  margin-bottom: 0; }

.block .block-image {
  position: relative; }

.block .block-image > img {
  max-width: 100%; }

.has-solid-shadow-left,
.has-solid-shadow-right {
  position: relative;
  z-index: 1; }

.has-solid-shadow-left::before,
.has-solid-shadow-right::before {
  content: "";
  position: absolute;
  width: 100%;
  height: calc(100% - 35px);
  background: #f9f9f9;
  border: 1px solid #e0eded;
  z-index: -1;
  border-radius: 0.25rem; }

.has-solid-shadow-left::before {
  top: 55px;
  left: -20px; }

.has-solid-shadow-right::before {
  top: 55px;
  right: -20px; }

.overlay-label {
  position: absolute;
  left: 0;
  bottom: 20px;
  padding: 6px 8px;
  font-weight: 500;
  font-size: 0.75rem;
  font-family: "Lato", sans-serif; }

.overlay-label.image-title--top {
  top: 15px;
  bottom: auto; }

.overlay-label.image-title--bottom {
  top: auto;
  bottom: 15px; }

.animate-this {
  position: relative;
  overflow: hidden; }

.animate-this,
.animate-this * {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  transition: all 0.5s ease; }

.animate--hover-zoom:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
  z-index: 100; }

.animate--hover-show-btn:hover .animate-translate--up {
  transform: translateY(-150%); }

.animate--hover-button {
  position: absolute;
  left: 0;
  bottom: -100%;
  width: 100%;
  padding: 1.5rem;
  text-align: center;
  opacity: 0;
  z-index: 10; }

.animate--hover-show-btn:hover .animate--hover-button {
  opacity: 1;
  bottom: 0; }

.block-author {
  display: table; }

.block-author:after,
.block-author:before {
  content: "";
  display: table; }

.block-author:after {
  clear: both; }

.block-author .author-image {
  width: 60px;
  display: table-cell; }

.block-author .author-image-lg {
  width: 80px; }

.block-author .author-image-sm {
  width: 40px; }

.block-author .author-image-xs {
  width: 30px; }

.block-author .author-image img {
  width: 100%;
  border-radius: 100%; }

.block-author .author-info {
  display: table-cell;
  vertical-align: middle;
  font-size: 0.875rem;
  color: #818a91; }

.block-author .author-image + .author-info {
  padding-left: 10px; }

.block-author .author-image-xs + .author-info {
  font-size: 0.75rem; }

.block-author .author-info .author-name {
  display: inline-block;
  font-weight: 400; }

.block-author .author-info .author-name > span {
  margin-right: 3px;
  color: #2b2b2c; }

.block-author .author-info .author-name > a {
  color: #818a91; }

.block-author .author-info .author-name > a:hover {
  color: #0087be; }

.block-author .author-info .author-desc {
  font-weight: 400;
  font-size: 0.875rem; }

.block-date-over {
  position: absolute;
  top: 14px;
  right: 14px;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.8);
  color: #2b2b2c;
  border-radius: 0.1rem;
  font-size: 0.75rem;
  text-align: center;
  font-weight: 500; }

.block-price-over {
  position: absolute;
  bottom: -20px;
  left: 50%;
  margin-left: -75px;
  width: 150px;
  height: 40px;
  border-radius: 20px;
  font-size: 1rem;
  text-align: center;
  font-weight: 600;
  line-height: 40px; }

.block-ribbon {
  position: absolute;
  top: 15px;
  z-index: 10; }

.block-ribbon-square {
  width: 64px; }

.block-ribbon-fixed {
  width: 74px;
  padding: 8px;
  text-align: center;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  border-radius: 0.25rem; }

.block-ribbon-left {
  left: 15px; }

.block-ribbon-right {
  right: 15px; }

.block-caption-over {
  position: absolute;
  width: 100%;
  height: 80px;
  bottom: 0;
  left: 0;
  padding: 1rem; }

.block-caption-half-over {
  position: absolute;
  width: 50%;
  height: 100%;
  top: 0;
  padding: 30px 40px; }

.block-caption-half-over--left {
  left: 0; }

.block-caption-half-over--right {
  right: 0; }

.block-image-holder > .mask {
  border-radius: 0.25rem; }

.block-image-holder > .block-image img {
  max-width: 100%; }

.block-image-holder > .block-image-sm {
  width: 60%;
  margin: auto; }

.block-image-holder:not(.no-radius) > .block-image img {
  border-radius: 0.25rem; }

.block-image-holder .block-info.block-info-over {
  overflow: hidden;
  display: flex;
  justify-content: center;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 3;
  padding: 15px 0;
  background: #FFF;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 0.25rem 0.25rem;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out; }

.block-image-holder .block-info.block-info-over > .block-info-inner {
  padding: 0 1.5rem;
  width: 100%;
  align-self: center; }

.block-image-holder .block-info.block-info-over--animated {
  height: 80px; }

.block-image-holder:hover .block-info.block-info-over--animated {
  height: 90px; }

.block-image-holder .block-info.block-info-over--animated:hover {
  height: 100%;
  border-radius: 0.25rem; }

.block-image-holder .block-info.block-info-over > .block-info-inner > .block-info-hidden-content {
  display: none;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out; }

.block-image-holder .block-info.block-info-over--animated:hover > .block-info-inner > .block-info-hidden-content {
  display: block;
  opacity: 1; }

.block-image-holder .block-info-table {
  width: 100%;
  margin: 20px 0 0;
  color: #2b2b2c;
  font-size: 0.7em; }

.block-image-holder .block-info-over.block-info-over .heading {
  margin: 0;
  color: #2b2b2c; }

.block-image-holder .block-info-over.block-info-over .heading > a {
  color: #2b2b2c; }

.block-image-holder .block-info-over.block-info-over:hover .info-title > a {
  color: #2b2b2c; }

.block-image-holder .block-info-over.block-info-over--style-2 {
  height: 60px;
  width: calc(100% - 1.6rem);
  margin: 0.8rem;
  background: #FFF;
  border-radius: 0.25rem; }

.block-image-holder .block-info-over--style-2.block-info-over--animated:hover {
  height: calc(100% - 1.6rem); }

.block-image-holder .block-info-over.block-info-over--style-3 {
  height: 80px;
  width: 100%;
  background: #FFF;
  background: rgba(255, 255, 255, 0.8); }

.block-image-holder .block-info-over.block-info-over--dark {
  background: rgba(0, 0, 0, 0.8);
  color: #787878; }

.block-image-holder .block-info-over.block-info-over--dark-gradient {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#000000+0,000000+100&0+0,0.65+100 */
  background: -moz-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=  '#00000000', endColorstr='#a6000000',GradientType=0 );
  /* IE6-9 */ }

.block-image-holder:hover .block-info-over.block-info-over--dark-gradient {
  background: -moz-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=  '#00000000', endColorstr='#a6000000',GradientType=0 );
  /* IE6-9 */ }

.block-image-holder .block-info-over.block-info-over--dark .heading,
.block-image-holder .block-info-over.block-info-over--dark .heading > a {
  color: #eceeef; }

.block-image-holder .block-info-over.block-info-over--dark .heading > a:hover,
.block-image-holder .block-info-over.block-info-over--dark .heading:hover a {
  color: #eceeef !important; }

.block-image-holder .block-info-over.block-info-over--dark .heading > a:hover {
  text-decoration: underline; }

.block-image-holder .block-info-over--dark .block-info-table tr {
  border-bottom: 1px dotted rgba(255, 255, 255, 0.1); }

.block-image-holder .block-info-over--dark .block-info-table td {
  color: #eceeef;
  padding: 8px 0;
  border: 0; }

.block-image-holder .block-info {
  padding: 1rem 0 !important;
  width: 100%;
  position: absolute;
  bottom: 30px;
  left: 0;
  z-index: 3;
  background: transparent; }

.block-image-holder .image-mask {
  background-color: #282B30;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  opacity: 0.45;
  transition: opacity 0.3s ease 0s; }

.block-image-holder:hover .image-mask {
  opacity: 0; }

.block--style-1 {
  border: 1px solid rgba(0, 0, 0, 0.05); }

.block.block--style-1 .block-subtitle {
  margin: 0;
  padding: 0;
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 15px;
  padding: 0;
  text-transform: none;
  color: #818a91; }

.block.block--style-1 img {
  max-width: 100%; }

.block.block--style-1 .meta-tags a {
  display: inline-block;
  color: #0087be; }

.block.block--style-1.grid .block-image {
  position: relative; }

.block.block--style-1.grid .block-body {
  padding: 1.5rem 0; }

.block.block--style-1.grid .image-title {
  position: absolute;
  left: 0;
  bottom: 10px;
  padding: 6px 8px;
  font-weight: 500; }

.block.block--style-1.grid .block-category {
  font-size: 11px;
  color: #0087be;
  text-transform: uppercase; }

.block.block--style-1.list {
  display: flex; }

.block.block--style-1.list .block-image {
  max-width: 40%; }

.block.block--style-1.list .block-image-sm {
  max-width: 25%; }

.block.block--style-1.list .block-header {
  padding: 10px 15px;
  font-weight: 500;
  text-transform: uppercase; }

.block.block--style-1.list .block-content {
  display: table-cell;
  vertical-align: top;
  position: relative; }

.block.block--style-1.list .block-body {
  padding: 1rem 0 1rem 2rem; }

.block.block--style-1.list .block-content .block-body {
  padding-bottom: 15px; }

.block.block--style-1.list .block-label {
  display: inline-block;
  padding: 6px 8px;
  font-size: 0.875rem;
  font-weight: 500; }

.block.block--style-1.list .info {
  display: block;
  margin-bottom: 4px;
  font-size: 11px;
  text-transform: uppercase;
  color: #818a91; }

.block.block--style-1.list .block-footer {
  padding: 0.5rem 2rem;
  display: table;
  width: 100%; }

.block.block--style-1.list .block-footer-fixed-bottom {
  position: absolute;
  bottom: 0; }

.block.block--style-1.list .block-footer .meta-info span {
  float: left;
  margin-right: 8px;
  font-size: 11px; }

.block.block--style-1.list .block-footer .meta-info span i {
  margin-right: 4px;
  font-size: 13px; }

.block.block--style-1.list .image-title {
  position: absolute;
  left: 0;
  bottom: 10px;
  padding: 10px;
  font-size: 1rem;
  font-weight: 500; }

@media (max-width: 767px) {
  .block.block--style-1.list {
    display: block; }

  .block.block--style-1.list .block-image {
    display: block;
    max-width: 100%;
    position: relative;
    margin-bottom: 1rem; }

  .block.block--style-1.list .block-body {
    display: block;
    padding: 1rem 2rem; }

  .block.block--style-1.list .block-footer-fixed-bottom {
    position: relative; } }
.block--style-2 {
  display: flex;
  align-items: center;
  width: 100%; }

.block--style-2 .heading {
  margin-bottom: 8px; }

.block--style-2 .block-image {
  max-width: 45%;
  position: relative;
  padding: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  overflow: hidden; }

.block--style-2 .block-image-lg {
  width: 48%; }

.block--style-2 .block-image img {
  width: 100%; }

.block--style-2 .block-body {
  display: table-cell;
  vertical-align: top;
  padding: 1.5rem;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.block--style-2:hover .mask {
  opacity: 0; }

.block--style-2.v1 .block-image {
  width: 50%; }

.block--style-2 .block-image-over {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 0;
  transform: translate(0, -50%); }

.block--style-2.grid .block-image {
  display: block;
  max-width: 100%;
  border-radius: 0;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem; }

.block--style-2.grid {
  display: block; }

.block--style-2.grid .block-body {
  display: block;
  border-radius: 0.25rem; }

.block--style-2.grid .block-body-over {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  padding: 1rem; }

.row-no-padding .block--style-2 {
  border-radius: 0; }

.row-no-padding .block--style-2 .block-image {
  border-radius: 0; }

.row-no-padding .block--style-2 .block-body {
  border-radius: 0; }

@media (max-width: 767px) {
  .block--style-2 {
    display: block; }

  .block--style-2 .block-image {
    max-width: 100%; }

  .block--style-2 .block-body {
    display: block; } }
.block--style-3 {
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.05);
  background: #fff; }

.block--style-3 img {
  max-width: 100%;
  border-radius: 0.25rem 0.25rem 0 0; }

.block--style-3 .heading {
  margin-bottom: 10px; }

.block--style-3 .block-footer {
  padding: 1.5rem 1.5rem; }

.block--style-3 .block-footer h3 {
  padding: 0;
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize; }

.block--style-3.block-fixed-width {
  width: 500px; }

.text-xs-center .block--style-3.block-fixed-width {
  margin: auto; }

@media (max-width: 767px) {
  .block--style-3.block-fixed-width {
    width: 90%; } }
.block--style-3 .description {
  margin-bottom: 0; }

.block--style-3 .block-quote-bullet {
  position: absolute;
  width: 46px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  left: 50%;
  top: -23px;
  margin-left: -23px;
  background: #fff;
  font-size: 20px;
  border-radius: 100%; }

.block--style-3 .block-price-text {
  margin-left: 2px;
  color: #818a91;
  font-size: 0.7em; }

.block--style-3 .block-price {
  font-size: 18px;
  color: #0087be;
  font-weight: 600; }

.block--style-3 .capacity > i {
  font-size: 12px; }

.block--style-3 .aux-info-wrapper {
  position: relative; }

.block--style-3 ul.aux-info {
  margin: 0 auto;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center; }

.block--style-3 ul.aux-info li {
  flex-grow: 1;
  margin: 0;
  padding: 14px 15px;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.75rem;
  position: relative;
  font-family: "Lato", sans-serif;
  color: #818a91; }

.block--style-3 ul.aux-info li i {
  font-size: 18px;
  margin-right: 4px;
  position: relative;
  top: 4px; }

.block--style-3 ul.aux-info li:last-child {
  border: 0; }

.block--style-3 ul.aux-info--over {
  background: #fff;
  display: table;
  position: absolute;
  bottom: 10px;
  width: 96%;
  left: 2%;
  border-radius: 2px; }

.block--style-3-v2 {
  border: 0;
  background: transparent; }

.block--style-3-v2 img {
  border-radius: 0.25rem; }

.block--style-3-v2 .block-body {
  padding: 1.5rem 0; }

.block--style-3-v2 .block-body > p {
  margin: 0; }

.block--style-3-v2 .block-footer {
  padding-left: 0;
  padding-right: 0; }

.block--style-3 > .block-image > .block-author {
  width: 50px;
  height: 50px;
  position: absolute;
  bottom: -25px;
  right: 30px;
  border-radius: 100%;
  border: 2px solid #fff; }

.block--style-3 > .block-image > .block-author img {
  border-radius: 100%; }

.block--style-3.list .block-image {
  display: table-cell;
  vertical-align: middle;
  width: 250px;
  position: relative;
  padding: 15px; }

.block--style-3.list .block-image-sm {
  width: 180px; }

.block--style-3.list .block-image .btn {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0; }

.block--style-3.list .block-title-wrapper {
  display: table-cell;
  vertical-align: top;
  padding: 20px 20px 20px 25px;
  border-left: 1px solid rgba(0, 0, 0, 0.05); }

.block--style-3.list .block-body {
  display: table-cell;
  vertical-align: top;
  padding: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05); }

@media (max-width: 767px) {
  .block--style-3.list .block-image {
    display: block;
    width: 100%; }

  .block--style-3.list .block-title-wrapper {
    border-left: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.05); }

  .block--style-3.list .block-body {
    display: block;
    padding: 20px; } }
.block--style-3.list .block-footer {
  padding: 12px 20px; }

.block--style-4 .info-author:after,
.block--style-4 .info-author:before {
  content: "";
  display: table; }

.block--style-4 .info-author:after {
  clear: both; }

.block--style-4 .block-body {
  padding: 2rem; }

.block--style-4 .info-author .author-img {
  display: table-cell;
  width: 70px;
  height: 70px;
  margin: 0;
  padding: 0; }

.block--style-4 .info-author .author-info {
  height: 75px;
  display: table-cell;
  padding-left: 15px;
  vertical-align: middle;
  text-align: left;
  border-bottom-left-radius: 0.25rem; }

.block--style-4 .info-author .author-info .author-name {
  display: block;
  font-style: normal;
  font-weight: 500;
  color: #2b2b2c; }

.block--style-4 .info-author .author-info .author-pos {
  display: block;
  font-size: 85%; }

.block--style-4-v1 {
  position: relative;
  margin-bottom: 34px; }

.block--style-4-v1.bg-white {
  border: 1px solid rgba(0, 0, 0, 0.05); }

.block--style-4-v1 .block-body {
  padding: 2rem; }

.block--style-4-v1 .block-body p {
  padding: 0 20px; }

.block--style-4-v1 .info-author {
  width: 64px;
  height: 64px;
  position: absolute;
  left: 50%;
  top: -32px;
  margin-left: -32px;
  border-radius: 100%; }

.block--style-4-v1 .info-author .author-img {
  padding: 3px;
  border-radius: 100%;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.05); }

.block--style-4-v1 .info-author .author-img.z-depth {
  padding: 0;
  background: #fff;
  border: 0; }

.block--style-4-v1 .info-author .author-img img {
  width: 100%;
  border-radius: 100%; }

.block--style-4-v1 .block-body span.quote-icon {
  display: block;
  text-align: center;
  margin: 20px 0; }

.block--style-4-v1 .block-body span.quote-icon > i {
  font-size: 26px; }

.block--style-4-v2 .block-body {
  position: relative;
  border-radius: 0.25rem; }

.block--style-4-v2 .block-body:after {
  top: 100%;
  left: 32px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-width: 10px;
  margin-left: -10px; }

.block--style-4-v2 .block-body > p {
  margin-bottom: 0; }

.block--style-4-v2 .block-body .quote {
  display: inline-block;
  margin-right: 8px;
  font-size: 24px; }

.block--style-4-v2 .block-body.bg-base-1:after {
  border-top-color: #0087be; }

.block--style-4-v2 .block-body.bg-white:after {
  border-top-color: white; }

.block--style-4-v2 .info-author {
  margin-top: 1.25rem; }

.block--style-4-v2 .info-author .author-img img {
  width: 64px; }

.block--style-4-v2 .style-8-author {
  width: 100%;
  margin-top: 15px;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.block--style-5 .block-image img {
  max-width: 100%;
  border-radius: 0.25rem; }

.block--style-5 .block-title-upper {
  margin: 4px 0;
  padding: 0;
  font-size: 0.75rem;
  font-weight: 400; }

.block--style-5 .block-info-text {
  margin-top: 15px;
  margin-bottom: 0; }

.block--style-5 .block-caption--over {
  position: absolute;
  width: 100%;
  height: 80px;
  bottom: 0;
  left: 0;
  padding: 15px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out; }

.block--style-5:hover .block-caption--over {
  height: 130px; }

.block--style-5 .block-mask-caption--over {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: transparent;
  color: #eceeef;
  border-radius: 0.25rem;
  bottom: 0;
  left: 0;
  padding: 1.5rem;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out; }

.block--style-5:hover .block-mask-caption--over {
  opacity: 1;
  background: rgba(0, 0, 0, 0.8); }

.block--style-5 .block-mask-caption--over.in {
  background: rgba(0, 0, 0, 0.4);
  opacity: 1; }

.block--style-5 .block-mask-caption--over .heading,
.block--style-5 .block-mask-caption--over .heading a {
  color: #eceeef; }

.block--style-5 .block-mask-caption--over .heading:hover a {
  color: #eceeef !important; }

.block--style-5 .block-footer {
  padding: 15px 20px; }

.block-quote {
  position: relative; }

.block-quote .quote-icon {
  font-size: 63px;
  color: #0087be;
  float: left;
  font-family: Georgia;
  position: relative;
  top: -20px;
  margin-right: 10px; }

.block-quote .quote-body {
  width: 80%;
  float: left;
  font-size: 16px;
  line-height: 28px; }

.block-testimonial > .quote {
  position: absolute;
  top: -28px;
  left: 15px;
  font-size: 50px;
  color: #0087be;
  z-index: 100; }

.block-testimonial > .block-body {
  position: relative;
  background: white;
  border-radius: 0.25rem; }

.block-testimonial > .block-body:after {
  top: 100%;
  left: 38px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-width: 10px;
  margin-left: -15px;
  border-top-color: white; }

.block-testimonial .block-author {
  margin-top: 1.5rem; }

.block.article > p {
  margin: 0; }

.block.article .heading {
  margin: 0;
  padding: 0; }

.block.article img {
  max-width: 100%;
  border-radius: 0.25rem; }

.block.article .meta-tags a {
  display: inline-block;
  color: #0087be; }

.block.article .article-meta {
  margin-bottom: 10px; }

.block.article .article-meta .article-labels {
  float: left; }

.block.article .article-meta .article-date {
  float: right;
  font-size: 11px;
  padding: 0.2em 0 0.3em;
  text-transform: uppercase;
  text-align: right;
  color: #818a91; }

.block.article .video-hover-play {
  font-size: 100%; }

.block.article .video-hover-play a {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%; }

.block.article .video-hover-play a:before {
  content: "\f144";
  font-family: FontAwesome;
  font-size: 30px;
  color: #FFF;
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 60px;
  height: 46px;
  line-height: 46px;
  background: #2B2B2B;
  border-radius: 0.25rem;
  text-align: center; }

.block.article .video-hover-play-centered a:before {
  right: 50%;
  bottom: 50%;
  transform: translate(50%, 50%); }

.block.article .video-hover-play-inverse a:before {
  color: rgba(0, 0, 0, 0.7);
  background: #fff; }

.block.article .video-hover-play a:hover:before {
  color: #FFF;
  background: #0087be; }

.block.article .video-hover-play-sm a:before {
  font-size: 16px;
  bottom: 8px;
  left: 8px;
  width: 30px;
  height: 24px;
  line-height: 24px; }

.block.article.grid .block-image {
  position: relative;
  margin-bottom: 1rem; }

.block.article.grid .image-title {
  bottom: 10px; }

.block.article.grid .article-category {
  font-size: 11px;
  color: #0087be;
  text-transform: uppercase; }

.block.article.list {
  display: table;
  width: 100%; }

.block.article.list .block-image {
  display: table-cell;
  position: relative; }

.block.article--style-1.list .block-image {
  width: 50%; }

.block.article--style-2.list .block-image {
  width: 30%; }

.block.article--style-2.list .block-image-lg {
  width: 40%; }

.block.article.list .block-body {
  display: table-cell;
  vertical-align: top;
  padding-top: 0;
  padding-bottom: 0; }

.block.article.list .block-body > .article-date {
  color: #818a91;
  font-size: 0.875rem; }

.block.article--style-1.list .block-body.left {
  padding: 0 20px 0 0; }

.block.article--style-1.list .block-body.right {
  padding: 0 0 0 20px; }

.block.article--style-2.list .block-body.left {
  padding: 0 10px 0 0; }

.block.article--style-2.list .block-body.right {
  padding: 0 0 0 10px; }

.block.article.list .block-body p {
  margin-top: 15px; }

.block.article.list .article-label {
  display: inline-block;
  padding: 4px 8px;
  font-weight: 500;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  margin-bottom: 8px; }

.block.article.list .article-title {
  margin: 0 0 10px; }

.block.article.list .article-info {
  display: block;
  margin-bottom: 4px;
  font-size: 11px;
  text-transform: uppercase;
  color: #818a91; }

.block.article--style-1.list .article-text {
  position: relative;
  padding-bottom: 25px; }

.block.article.list .article-text:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 1px;
  background: #e0eded; }

.block.article.list .article-title-text {
  margin: 0 !important;
  line-height: 18px; }

.block.article.list .block-footer {
  display: table;
  width: 100%; }

.block.article.list .block-footer .meta-info span {
  float: left;
  margin-right: 8px;
  font-size: 11px; }

.block.article.list .block-footer .meta-info span i {
  margin-right: 4px;
  font-size: 13px; }

.block.article.list .image-title {
  position: absolute;
  left: 0;
  bottom: 10px;
  padding: 10px;
  font-weight: 500; }

.block.article.list .article-category {
  font-size: 11px;
  color: #0087be;
  text-transform: uppercase; }

@media (max-width: 767px) {
  .block.article.list .block-image {
    display: block;
    width: 100% !important; }

  .block.article.list .article-text {
    padding-bottom: 0; }

  .block.article.list .block-body {
    display: block;
    padding: 0 !important; }

  .block.article.list .block-body.left {
    margin-bottom: 20px; }

  .block.article.list .block-body.right {
    margin-top: 20px; } }
.article-wrapper {
  padding: 1rem; }

.article-wrapper--style-1 {
  background: #fcfcfc; }

.article-wrapper--style-2 {
  background: #fff; }

.article-wrapper .article-title {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: none !important;
  line-height: 1.3;
  margin: 0;
  padding: 0;
  margin-bottom: 4px !important; }

.article-wrapper .article-title > a {
  color: #2b2b2c; }

.article-wrapper .article-title > a:hover {
  color: #0087be; }

@media (max-width: 767px) {
  .article-wrapper .article-title {
    margin-top: 1rem; } }
.card > .article-wrapper {
  padding-left: 1.5rem;
  padding-right: 1.5rem; }

.card > .article-wrapper .block-image > img {
  border-radius: 0; }

.article-wrapper > .block.article {
  margin: 0;
  padding: 0; }

.block.product {
  background: #fff;
  padding: 1px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden; }

.block.product .figure {
  padding: 1px; }

.block.product .figure img {
  width: 100% !important; }

.block.product .product-title {
  margin: 10px 0;
  padding: 0;
  border-bottom: 0;
  line-height: 0.9; }

.block.product .product-title.heading {
  line-height: 1; }

.block.product .product-title a {
  font-size: 1.125rem;
  color: #2b2b2c; }

.block.product .product-title a:hover {
  color: #0087be; }

.block.product figure {
  padding-bottom: 1px;
  border-bottom: 1px solid #e0eded; }

.block.product .block-content {
  padding: 10px; }

.block.product p {
  margin: 5px 0; }

.block.product .price-wrapper {
  margin-top: 4px; }

.block.product .price {
  padding: 4px 0;
  font-size: 1.25rem;
  font-weight: 600; }

.block.product .price.discount {
  padding: 7px 0 0;
  margin-right: 4px;
  font-size: 0.875rem;
  font-weight: 400;
  color: #ff3b30 !important;
  text-decoration: line-through; }

.block.product .block-footer {
  border-top: 1px solid #e0eded;
  padding-top: 8px;
  margin-top: 10px; }

.block.product .block-footer:after {
  display: table;
  content: "";
  clear: both; }

.block.property {
  border: 1px solid rgba(0, 0, 0, 0.05); }

.block.property .block-content .content-title {
  font-size: 1rem;
  color: #0087be;
  margin-bottom: 5px; }

.block.property .block-content .price {
  font-size: 1.25rem;
  color: #292f36;
  font-weight: 600; }

.block.property .block-content .period {
  font-size: 0.75rem;
  margin-left: 5px;
  color: #818a91; }

.block.property .block-content .capacity {
  font-size: 12px;
  color: #818a91; }

.block.property .block-footer ul.aux-info {
  width: 100%;
  margin: 0;
  padding: 0;
  display: block;
  background: #f1f1f1;
  border-top: 1px solid rgba(0, 0, 0, 0.05); }

.block.property .block-footer ul.aux-info li {
  display: table-cell;
  padding: 12px 15px;
  vertical-align: middle;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.75rem; }

.block.property .block-footer ul.aux-info li:last-child {
  border: 0; }

.block.property .block-footer ul.aux-info li i {
  font-size: 18px;
  margin-right: 8px;
  color: #2b2b2c; }

.block.property .block-footer--style-2 ul.aux-info {
  display: table;
  width: 100%;
  border: 1px solid #e0eded; }

.block.property .block-footer--style-2 ul.aux-info li {
  padding: 10px;
  font-size: 11px;
  text-align: center; }

.block.property .block-footer--style-2 ul.aux-info li i {
  display: block; }

.block.property.list .block-title {
  padding: 1rem 1rem 0; }

.block.property.list .block-title h3 {
  margin: 0;
  padding: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #2b2b2c; }

.block.property.list .block-title h3 > a {
  color: #2b2b2c; }

.block.property.list .block-image {
  display: table-cell;
  width: 250px; }

.block.property.list .block-image img {
  width: 100%; }

.block.property.list .block-body {
  padding: 12px; }

.block.property.list .block-body .block-content {
  display: table-cell;
  vertical-align: top;
  padding-left: 12px; }

.block.property.list .block-content .description {
  padding-bottom: 10px;
  border-bottom: 1px solid #e0eded;
  font-size: 0.8rem; }

@media (max-width: 767px) {
  .block.property.list .block-image {
    display: block;
    width: auto; }

  .block.property.list .block-body .block-content {
    display: block;
    padding-left: 0; } }
.block.property.grid {
  border: 1px solid #e0eded; }

.block.property.grid .block-title {
  padding: 15px 15px 0; }

.block.property.grid .block-title h3 {
  margin: 0;
  padding: 0;
  font-size: 16px;
  font-weight: 400; }

.block.property.grid .block-body .block-image img {
  width: 100%; }

.block.property.grid .block-body .block-content {
  padding-top: 15px; }

.block.property.grid .block-body .block-content .description {
  padding-bottom: 10px;
  border-bottom: 1px solid #e0eded; }

.block-post .block-post-body {
  padding: 0; }

.block-post .block-post-body > .heading {
  margin-bottom: 0 !important;
  margin-top: 3rem !important; }

.block-post > .block-post-body > p {
  margin-top: 2rem !important; }

.block-post > .block-post-body > .tagcloud {
  margin-top: 2rem; }

.block-post-share {
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid #e0eded;
  border-bottom: 1px solid #e0eded; }

.block-post-comment-action {
  padding: 1rem 0; }

.block-post-comment-action .heading {
  margin-bottom: 0; }

.block-post-comments {
  margin: 0;
  padding: 0;
  list-style: none; }

.block-comment {
  display: table;
  width: 100%;
  margin-bottom: 2rem; }

.block-comment:last-child {
  border-bottom: 1px solid rgba(243, 243, 243, 0.7); }

@media (min-width: 768px) {
  .block-comment-reply {
    padding-left: 72px; } }
.block-comment .block-image {
  display: table-cell;
  width: 72px;
  height: 72px; }

.block-comment .block-body {
  display: table-cell;
  vertical-align: top;
  position: relative;
  padding: 0 0 0 2rem; }

.block-comment .block-body > .block-body-inner {
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(243, 243, 243, 0.7); }

.block-comment:last-child .block-body > .block-body-inner {
  border: 0; }

.block-body-inner .heading {
  margin: 0; }

.block-comment .block-body .comment-date {
  font-size: 0.875rem;
  color: #818a91; }

.block-comment .block-body .comment-text {
  margin-top: 1rem; }

.block-comment .block-body .comment-options > a {
  font-size: 0.875rem;
  color: #818a91;
  margin-right: 0.75rem; }

.block-comment .block-body .comment-options > a:hover {
  color: #0087be; }

.block-post-comments--style-1 {
  border: 1px solid #e0eded;
  border-radius: 0.25rem; }

.block-post-comments--style-1 .block-comment {
  margin: 0;
  border-radius: 0;
  border: 0; }

.block-post-comments--style-1 .block-comment-reply {
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid #e0eded; }

@media (min-width: 768px) {
  .block-comment-reply {
    padding-left: 100px; } }
.block-post-comments--style-1 > li {
  border-bottom: 1px solid #e0eded;
  padding: 2rem; }

.block-post-comments--style-1 > li:last-child {
  border: 0; }

.block-post-comments--style-1 li:nth-child(odd) {
  background: #fcfcfc; }

.block-post-comments--style-1 li:nth-child(even) {
  background: #fafafa; }

.block-post-comments--style-1 .block-comment-reply:last-child {
  border-bottom: 0; }

.block-comment .block-image {
  display: table-cell;
  width: 60px;
  height: 60px; }

.block-comment .block-body {
  padding-left: 40px; }

.block-post-comments--style-1 .block-comment .block-body > .block-body-inner {
  border: 0;
  padding: 0; }

.post-prev-next {
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid rgba(243, 243, 243, 0.7);
  border-bottom: 1px solid rgba(243, 243, 243, 0.7); }

.post-prev-next h5 {
  padding: 0;
  margin: 0;
  color: #818a91; }

.post-prev-next h3 {
  padding: 0;
  margin: 5px 0 0; }

.post-prev-next .post-next,
.post-prev-next .post-prev {
  position: relative; }

.post-prev-next .post-prev {
  padding: 0 20px 0 40px; }

.post-prev-next .post-next {
  text-align: right;
  padding-right: 40px; }

.post-prev-next .post-next:before,
.post-prev-next .post-prev:before {
  position: absolute;
  top: 50%;
  margin-top: -15px;
  font-family: "Ionicons";
  font-size: 20px;
  color: #2b2b2c; }

.post-prev-next .post-prev:before {
  content: "\f108";
  left: 0; }

.post-prev-next .post-next:before {
  content: "\f10b";
  right: 0; }

@media (max-width: 767px) {
  .post-prev-next .post-next {
    margin-top: 20px; } }
.animate-hover-slide .figure {
  position: relative;
  overflow: hidden; }

.animate-hover-slide .figure img {
  max-width: 100%;
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide .figure .figcaption {
  height: 100%;
  padding: 0;
  width: 100%;
  position: absolute;
  left: 0;
  top: auto;
  bottom: 0;
  opacity: 0;
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -o-transform: translateY(100%);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide .figure:hover .figcaption {
  opacity: 0.8;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s;
  -o-transition: -o-transform 0.4s, opacity 0.1s;
  transition: transform 0.4s, opacity 0.1s; }

.animate-hover-slide .figure .figcaption {
  text-align: center; }

.animate-hover-slide .figure .figcaption-btn {
  width: 100%;
  height: 50%;
  position: absolute;
  top: 0;
  opacity: 0;
  padding-left: 20px;
  text-align: center;
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide .figure:hover .figcaption-btn {
  opacity: 1;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s;
  -o-transition: -o-transform 0.4s, opacity 0.1s;
  transition: transform 0.4s, opacity 0.1s; }

.animate-hover-slide .figure .figcaption-txt {
  width: 100%;
  height: 50%;
  position: absolute;
  bottom: 0;
  opacity: 0;
  padding-left: 20px;
  text-align: center;
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -o-transform: translateY(100%);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide .figure:hover .figcaption-txt {
  opacity: 1;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s;
  -o-transition: -o-transform 0.4s, opacity 0.1s;
  transition: transform 0.4s, opacity 0.1s; }

.animate-hover-slide .figure .figcaption-txt .title {
  padding: 0;
  margin: 30px 0 0;
  color: #fff;
  font-size: 18px;
  text-transform: capitalize; }

.animate-hover-slide .figure .figcaption-txt .subtitle {
  padding: 0;
  margin: 0;
  color: #fff;
  font-size: 12px; }

.animate-hover-slide .figure a {
  position: relative;
  top: 94%;
  margin-top: -11px; }

.animate-hover-slide .figure .figcaption h3 {
  padding-bottom: 5px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #f2f2f2; }

.animate-hover-slide-2 .figure {
  position: relative;
  overflow: hidden; }

.animate-hover-slide-2 .figure img {
  max-width: 100%;
  position: relative;
  z-index: 2;
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide-2 .figure:hover img {
  -webkit-transform: scale(0.4);
  -moz-transform: scale(0.4);
  -ms-transform: scale(0.4);
  transform: scale(0.4); }

.animate-hover-slide-2 .figure .figcaption {
  height: 100%;
  z-index: 1;
  position: absolute;
  top: 0;
  bottom: auto;
  background: #f3f3f3, 10%;
  color: #333 !important;
  padding: 0 15px;
  width: 100%;
  opacity: 1;
  -webkit-transform: scale(0.4);
  -moz-transform: scale(0.4);
  -ms-transform: scale(0.4);
  transform: scale(0.4);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide-2 .figure:hover .figcaption {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1; }

.animate-hover-slide-2 .figure .figcaption h2 {
  text-align: center;
  margin-top: 15px; }

.animate-hover-slide-2 .figure .figcaption .social-icons {
  width: 100%;
  position: absolute;
  bottom: 15px;
  text-align: center; }

.animate-hover-slide-3 .figure {
  position: relative;
  overflow: hidden; }

.animate-hover-slide-3 .figure img {
  max-width: 100%;
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide-3 .figure .figcaption {
  height: 32px;
  background: #f3f3f3;
  color: #333;
  padding: 0 15px;
  width: 100%;
  position: absolute;
  left: 0;
  top: auto;
  bottom: 0;
  opacity: 0;
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -o-transform: translateY(100%);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  -o-transition: -o-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s; }

.animate-hover-slide-3 .figure:hover .figcaption {
  opacity: 1;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s;
  -o-transition: -o-transform 0.4s, opacity 0.1s;
  transition: transform 0.4s, opacity 0.1s; }

.animate-hover-slide-4 {
  position: relative; }

.animate-hover-slide-4 .figure {
  position: relative; }

.animate-hover-slide-4 .figure .figcaption {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  opacity: 0.75; }

.animate-hover-slide-4 .figure .figcaption {
  text-align: center; }

.animate-hover-slide-4 .figure .figcaption-btn {
  width: 100%;
  height: 50%;
  position: absolute;
  top: 0;
  opacity: 0;
  padding-left: 20px;
  text-align: center; }

.animate-hover-slide-4 .figure:hover .figcaption-btn {
  opacity: 1; }

.animate-hover-slide-4 .figure .figcaption-txt {
  width: 100%;
  height: 50%;
  position: absolute;
  bottom: 0;
  opacity: 0;
  padding-left: 20px;
  text-align: center; }

.animate-hover-slide-4 .figure:hover .figcaption-txt {
  opacity: 1; }

.animate-hover-slide-4 .figure .figcaption-txt .title {
  padding: 0;
  margin: 30px 0 0;
  color: #fff;
  font-size: 18px;
  text-transform: capitalize; }

.animate-hover-slide-4 .figure .figcaption-txt .subtitle {
  padding: 0;
  margin: 0;
  color: #fff;
  font-size: 12px; }

.animate-hover-slide-4 .figure a {
  position: relative;
  top: 94%;
  margin-top: -11px; }

.animate-hover-slide-4 .figure .figcaption h3 {
  padding-bottom: 5px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #f2f2f2; }

.block-cell {
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  position: relative; }

.block-cell .block-image {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat; }

.block-cell .block-image,
.block-cell .block-text {
  width: 50%; }

@media screen and (max-width: 992px) {
  .block-cell .block-image,
  .block-cell .block-text {
    width: 100%; }

  .block-cell .block-image {
    height: 200px; } }
.block-cell .block-text {
  padding: 40px;
  position: relative; }

.block-cell .block-text h3 {
  margin: 0 0 10px; }

.block-cell .block-text:before,
.block-cell.reverse .block-text:before {
  z-index: 22;
  top: 15%;
  content: " ";
  height: 0;
  margin-top: -12px;
  pointer-events: none; }

.block-cell .block-text p:last-child {
  margin-bottom: 0; }

.block-cell .block-text p.price {
  font-size: 1.25rem;
  font-family: "Lato", sans-serif;
  color: #0087be; }

.block-cell .block-text:before {
  right: 100%;
  border: solid transparent;
  width: 0;
  position: absolute;
  border-color: rgba(255, 225, 255, 0);
  border-right-color: #fff;
  border-width: 12px; }

.block-cell.reverse .block-image {
  position: absolute;
  right: 0;
  width: 50%;
  min-height: 100%; }

@media screen and (max-width: 992px) {
  .block-cell.reverse .block-image {
    position: relative;
    width: 100%; } }
.block-cell.reverse .block-text:before {
  left: 100%;
  border: solid transparent;
  width: 0;
  position: absolute;
  border-color: rgba(255, 225, 255, 0);
  border-left-color: #fff;
  border-width: 12px; }

@media screen and (max-width: 768px) {
  .block-cell .block-text:before,
  .block-cell.reverse .block-text:before {
    display: none; } }
.btn {
  font-size: 0.875rem;
  font-weight: 500;
  font-family: "Lato", sans-serif;
  letter-spacing: 0.1rem;
  text-transform: uppercase;
  font-style: normal;
  text-align: center;
  border: 1px solid;
  border-radius: 2px;
  outline: none; }

.btn-styled {
  padding: 0.75rem 1.5rem; }

.btn-sm {
  font-size: 0.7rem !important; }

.btn-styled.btn-xl {
  font-size: 1.125rem !important;
  padding: 1rem 2.5rem !important; }

.btn-styled.btn-lg {
  font-size: 0.9rem !important;
  padding: 1rem 2rem !important; }

.btn-styled.btn-sm {
  font-size: 0.625rem !important;
  padding: 0.625rem 1rem !important; }

.btn-styled.btn-xs {
  font-size: 0.6rem !important;
  padding: 0.5rem !important; }

@media (max-width: 767px) {
  .btn-xs-block {
    width: 100%; } }
.btn-icon-left .fa,
.btn-icon-left .icon {
  margin-right: 0.625rem; }

.btn-icon-right .fa,
.btn-icon-right .icon {
  margin-left: 0.625rem; }

.btn-square {
  border-radius: 0px !important; }

.btn-round {
  border-radius: 8px !important; }

.btn-circle {
  border-radius: 10rem !important; }

.btn-shadow {
  -webkit-box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.8) !important;
  -moz-box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.8) !important;
  box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.8) !important; }

.btn-italic {
  font-style: italic !important; }

.btn-disable-hover {
  pointer-events: none !important; }

@media (max-width: 767px) {
  .btn-container {
    margin-top: 2rem; } }
@media (max-width: 991px) {
  .btn-container > .btn {
    margin-bottom: 1rem; } }
@media (min-width: 992px) {
  .btn-container > .btn {
    margin-right: 2rem; }

  .btn-container > .btn.btn-border-animated {
    margin-right: 3rem; }

  .btn-container > .btn:last-child {
    margin-right: 0; } }
.btn-inline-links > a {
  position: relative;
  display: inline-block;
  padding: 0 10px 0 5px; }

.btn-inline-links > a:after {
  content: "-";
  position: absolute;
  right: 0;
  height: 20px;
  color: #0087be; }

.btn-inline-links > a:last-child:after {
  content: ""; }

.btn-base-1 {
  color: #FFF;
  background-color: #0087be;
  border: 1px solid #0087be; }

.btn-base-1:active,
.btn-base-1.active,
.btn-base-1:focus,
.btn-base-1:hover {
  background-color: #177196;
  color: #FFF; }

.btn-base-1.btn-outline {
  background-color: transparent !important;
  border-color: #0087be !important;
  color: #0087be !important; }

.btn-base-1.btn-outline:active,
.btn-base-1.btn-outline:focus,
.btn-base-1.btn-outline:hover {
  background-color: #0087be !important;
  border-color: #0087be !important;
  color: #FFF !important; }

.btn-base-2 {
  color: #FFF;
  background-color: #292f36;
  border: 1px solid #292f36; }

.btn-base-2:active,
.btn-base-2.active,
.btn-base-2:focus,
.btn-base-2:hover {
  background-color: #131619;
  color: #FFF; }

.btn-base-2.btn-outline {
  background-color: transparent !important;
  border-color: #292f36 !important;
  color: #292f36 !important; }

.btn-base-2.btn-outline:active,
.btn-base-2.btn-outline:focus,
.btn-base-2.btn-outline:hover {
  background-color: #292f36 !important;
  border-color: #292f36 !important;
  color: #FFF !important; }

.btn-base-3 {
  color: rgba(255, 255, 255, 0.9);
  background-color: #818a91;
  border: 1px solid #818a91; }

.btn-base-3:active,
.btn-base-3.active,
.btn-base-3:focus,
.btn-base-3:hover {
  background-color: #687077;
  color: rgba(255, 255, 255, 0.9); }

.btn-base-3.btn-outline {
  background-color: transparent !important;
  border-color: #818a91 !important;
  color: #818a91 !important; }

.btn-base-3.btn-outline:active,
.btn-base-3.btn-outline:focus,
.btn-base-3.btn-outline:hover {
  background-color: #818a91 !important;
  border-color: #818a91 !important;
  color: rgba(255, 255, 255, 0.9) !important; }

.btn-base-4 {
  color: #FFF;
  background-color: #2B2B2B;
  border: 1px solid #2B2B2B; }

.btn-base-4:active,
.btn-base-4.active,
.btn-base-4:focus,
.btn-base-4:hover {
  background-color: #121212;
  color: #FFF; }

.btn-base-4.btn-outline {
  background-color: transparent !important;
  border-color: #2B2B2B !important;
  color: #2B2B2B !important; }

.btn-base-4.btn-outline:active,
.btn-base-4.btn-outline:focus,
.btn-base-4.btn-outline:hover {
  background-color: #2B2B2B !important;
  border-color: #2B2B2B !important;
  color: #FFF !important; }

.btn-base-5 {
  color: #0A0814;
  background-color: #FFF;
  border: 1px solid #FFF; }

.btn-base-5:active,
.btn-base-5.active,
.btn-base-5:focus,
.btn-base-5:hover {
  background-color: #e6e6e6;
  color: #0A0814; }

.btn-base-5.btn-outline {
  background-color: transparent !important;
  border-color: #FFF !important;
  color: #FFF !important; }

.btn-base-5.btn-outline:active,
.btn-base-5.btn-outline:focus,
.btn-base-5.btn-outline:hover {
  background-color: #FFF !important;
  border-color: #FFF !important;
  color: #0A0814 !important; }

.btn-white {
  color: #333;
  background-color: white;
  border: 1px solid white; }

.btn-white:active,
.btn-white:focus,
.btn-white:hover {
  background-color: #e6e6e6;
  border-color: white;
  color: #333; }

.btn-white.btn-outline {
  background-color: transparent;
  border-color: white;
  color: white; }

.btn-white.btn-outline:active,
.btn-white.btn-outline:not(.btn-hover-nobg):focus,
.btn-white.btn-outline:not(.btn-hover-nobg):hover {
  background-color: white;
  border-color: white;
  color: #333; }

.btn-blue {
  color: #FFF !important;
  background-color: #007aff !important;
  border-color: #007aff !important; }

.btn-blue:active,
.btn-blue:not(.btn-hover-nobg):focus,
.btn-blue:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #007aff !important;
  color: #007aff !important; }

.btn-blue.btn-outline {
  background-color: transparent !important;
  border-color: #007aff !important;
  color: #007aff !important; }

.btn-blue.btn-outline:active,
.btn-blue.btn-outline:not(.btn-hover-nobg):focus,
.btn-blue.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #007aff !important;
  border-color: #007aff !important;
  color: #007aff !important; }

.btn-green {
  color: #FFF !important;
  background-color: #4cd964 !important;
  border-color: #4cd964 !important; }

.btn-green:active,
.btn-green:not(.btn-hover-nobg):focus,
.btn-green:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #4cd964 !important;
  color: #4cd964 !important; }

.btn-green.btn-outline {
  background-color: transparent !important;
  border-color: #4cd964 !important;
  color: #4cd964 !important; }

.btn-green.btn-outline:active,
.btn-green.btn-outline:not(.btn-hover-nobg):focus,
.btn-green.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #4cd964 !important;
  border-color: #4cd964 !important;
  color: #4cd964 !important; }

.btn-red {
  color: #FFF !important;
  background-color: #ff3b30 !important;
  border-color: #ff3b30 !important; }

.btn-red:active,
.btn-red:not(.btn-hover-nobg):focus,
.btn-red:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #ff3b30 !important;
  color: #ff3b30 !important; }

.btn-red.btn-outline {
  background-color: transparent !important;
  border-color: #ff3b30 !important;
  color: #ff3b30 !important; }

.btn-red.btn-outline:active,
.btn-red.btn-outline:not(.btn-hover-nobg):focus,
.btn-red.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #ff3b30 !important;
  border-color: #ff3b30 !important;
  color: #ff3b30 !important; }

.btn-pink {
  color: #FFF !important;
  background-color: #ff2d55 !important;
  border-color: #ff2d55 !important; }

.btn-pink:active,
.btn-pink:not(.btn-hover-nobg):focus,
.btn-pink:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #ff2d55 !important;
  color: #ff2d55 !important; }

.btn-pink.btn-outline {
  background-color: transparent !important;
  border-color: #ff2d55 !important;
  color: #ff2d55 !important; }

.btn-pink.btn-outline:active,
.btn-pink.btn-outline:not(.btn-hover-nobg):focus,
.btn-pink.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #ff2d55 !important;
  border-color: #ff2d55 !important;
  color: #ff2d55 !important; }

.btn-orange {
  color: #FFF !important;
  background-color: #ff9500 !important;
  border-color: #ff9500 !important; }

.btn-orange:active,
.btn-orange:not(.btn-hover-nobg):focus,
.btn-orange:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #ff9500 !important;
  color: #ff9500 !important; }

.btn-orange.btn-outline {
  background-color: transparent !important;
  border-color: #ff9500 !important;
  color: #ff9500 !important; }

.btn-orange.btn-outline:active,
.btn-orange.btn-outline:not(.btn-hover-nobg):focus,
.btn-orange.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #ff9500 !important;
  border-color: #ff9500 !important;
  color: #ff9500 !important; }

.btn-yellow {
  color: #FFF !important;
  background-color: #ffcc00 !important;
  border-color: #ffcc00 !important; }

.btn-yellow:active,
.btn-yellow:not(.btn-hover-nobg):focus,
.btn-yellow:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #ffcc00 !important;
  color: #ffcc00 !important; }

.btn-yellow.btn-outline {
  background-color: transparent !important;
  border-color: #ffcc00 !important;
  color: #ffcc00 !important; }

.btn-yellow.btn-outline:active,
.btn-yellow.btn-outline:not(.btn-hover-nobg):focus,
.btn-yellow.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #ffcc00 !important;
  border-color: #ffcc00 !important;
  color: #ffcc00 !important; }

.btn-purple {
  color: #FFF !important;
  background-color: #5856d6 !important;
  border-color: #5856d6 !important; }

.btn-purple:active,
.btn-purple:not(.btn-hover-nobg):focus,
.btn-purple:not(.btn-hover-nobg):hover {
  background-color: transparent !important;
  border-color: #5856d6 !important;
  color: #5856d6 !important; }

.btn-purple.btn-outline {
  background-color: transparent !important;
  border-color: #5856d6 !important;
  color: #5856d6 !important; }

.btn-purple.btn-outline:active,
.btn-purple.btn-outline:not(.btn-hover-nobg):focus,
.btn-purple.btn-outline:not(.btn-hover-nobg):hover {
  background-color: #5856d6 !important;
  border-color: #5856d6 !important;
  color: #5856d6 !important; }

.btn-color-470604 {
  color: #ffffff !important;
  background: linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%); }

.btn-color-470604:active,
.btn-color-470604:not(.btn-hover-nobg):focus,
.btn-color-470604:not(.btn-hover-nobg):hover {
  background: linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%); }

.card-wrapper > .card:not(:last-child) {
  margin-bottom: 2rem; }

.card {
  position: relative;
  z-index: 10;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
  background: #fff;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

@media (min-width: 992px) {
  .card-sticked-bottom {
    position: absolute;
    bottom: 0;
    border-radius: 0.25rem 0.25rem 0 0; } }
.card-overflow-fade {
  position: relative; }

.card-overflow-fade::after {
  position: absolute;
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  left: 0; }

.card .b-xs-top,
.card .b-xs-right,
.card .b-xs-bottom,
.card .b-xs-left {
  border-color: rgba(0, 0, 0, 0.05) !important; }

.card-body {
  padding: 1.5rem 1.5rem; }

.card-body > .lead {
  margin-bottom: 0; }

.card-title {
  padding: 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  margin: 0; }

.card-title > .heading {
  margin: 0;
  display: inline-block; }

.card-title > .btn-aux {
  float: right;
  font-size: 0.875rem;
  color: #818a91; }

.card-title > .btn-aux > .aux-text {
  display: inline-block;
  margin-left: 5px;
  color: #2b2b2c; }

.card-title > .btn-aux:hover > .aux-text {
  color: #0087be; }

.card.bg-base-1 .card-title-wrapped {
  background: #d01919;
  border: 1px solid #c71818; }

.card.bg-base-1 .card-title .heading {
  color: #FFF; }

.card-inner-title-wrapper {
  padding: 1.5rem 0; }

.card-inner-title {
  margin: 0;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111111; }

.card-inner-title > i {
  margin-right: 6px; }

.card-inner-subtitle {
  margin: 4px 0 0 0;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 300;
  color: #818a91; }

.card-inner-subtitle.subtitle-icon-aligned {
  padding-left: 1.5rem; }

.card-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
  background: inherit; }

@media (min-width: 992px) {
  .card-footer.absolute-bottom {
    position: absolute;
    bottom: 0;
    width: 100%; } }
.card-icon-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center; }

.card-icon-bg i {
  font-size: 10rem;
  color: rgba(0, 0, 0, 0.1);
  line-height: 0; }

.card-inverse {
  background: #1b1e23; }

.card-inverse *:not(.btn):not(.alert):not(.form-control):not(.heading):not(a) {
  color: rgba(255, 255, 255, 0.5); }

.card-inverse .heading,
.card-inverse .heading > a {
  color: #f3f3f3; }

.card-inverse .card-footer {
  border-top-color: rgba(0, 0, 0, 0.15); }

.card-inverse p {
  color: rgba(255, 255, 255, 0.5); }

.card-inverse .heading {
  color: white; }

.card-inverse .heading-light {
  color: rgba(255, 255, 255, 0.8); }

.card-inverse .stats-entry .stats-count {
  color: white; }

.card-inverse .stats-entry .stats-label {
  color: rgba(255, 255, 255, 0.8); }

.card-inverse .useful-links a {
  color: rgba(255, 255, 255, 0.8); }

.card-inverse .useful-links a:hover {
  color: white; }

.card-colored {
  border: 0; }

@media (min-width: 768px) {
  .row-no-padding .card-colored {
    border-radius: 0; }

  .row-no-padding [class^=col-]:first-child .card-colored {
    border-radius: 0.25rem 0 0 0.25rem; }

  .row-no-padding [class^=col-]:last-child .card-colored {
    border-radius: 0 0.25rem 0.25rem 0; } }
.card-colored .card-body {
  padding: 2.5rem; }

.card-colored .card-title + .card-body {
  padding: 1.5rem 1.5rem; }

.card-colored .card-body .heading,
.card-colored .card-body p {
  color: #FFF; }

.card-image {
  position: relative; }

.card-image img,
.card-image > .view {
  max-width: 100%;
  border-radius: 0.25rem 0.25rem 0 0; }

[class*=z-depth] .card-image img,
[class*=z-depth] .card-image > .view {
  max-width: 100% !important;
  margin-left: 0;
  margin-top: 0; }

.card-title + .card-image img,
.card-title + .card-image > .view {
  border-radius: 0; }

.card-icon {
  padding: 2.5rem; }

.card-icon-lg i,
.card-icon-lg i {
  font-size: 70px;
  line-height: 1; }

.card-image-container {
  position: relative;
  height: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
  flex: 1;
  -ms-flex-align: center;
  align-items: center;
  background-size: cover;
  background-color: #efe9e8;
  -ms-flex-pack: center;
  justify-content: center;
  overflow: hidden; }

.card-image-container > .card-image {
  max-width: none; }

@media (min-width: 768px) {
  .card > .row [class^=col-md] .card-image-container .card-img > img {
    top: 0;
    left: 0;
    height: 100% !important;
    width: 100%;
    position: absolute;
    object-fit: cover; } }
.card-horizontal .card-image img {
  border-radius: 0.25rem 0 0 0.25rem; }

.card-horizontal .card-body {
  padding: 1.5rem; }

.card-horizontal .card-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem; }

.card-blockquote {
  border-left: 0; }

.card-blockquote > p {
  font-size: 1.25rem;
  line-height: 1.7;
  color: #818a91;
  font-weight: 400;
  font-style: italic; }

.card-blockquote > p > i {
  font-size: 30px;
  color: #eceeef;
  display: inline-block; }

.card-blockquote > p > .quote-left {
  position: relative;
  margin-right: 10px;
  top: -12px; }

.card-blockquote > p > .quote-right {
  position: relative;
  margin-left: 10px;
  bottom: -12px; }

.card-blockquote > footer {
  font-size: 1rem;
  margin-top: 2rem;
  color: #0087be; }

.card-hover--animation-1,
.card-hover--animation-1 * {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.card-hover--animation-1:hover .btn {
  background: #0087be;
  color: #FFF;
  border-color: #0087be; }

/* CAROUSEL - STYLE 1 */
.carousel--style-1 .carousel-control {
  position: absolute;
  top: 50%;
  bottom: auto;
  left: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-top: -25px;
  font-size: 20px;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  filter: alpha(opacity=80);
  opacity: 0.8;
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear; }

.carousel--style-1 .carousel-control.right {
  right: 0;
  left: auto;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem; }

.carousel--style-1 .carousel-control.left {
  left: 0;
  right: auto;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.carousel--style-1 .carousel-control:hover {
  width: 70px;
  background: #0087be;
  color: #FFF;
  filter: alpha(opacity=100);
  opacity: 1; }

.carousel--style-1-v1 .carousel-control {
  opacity: 0; }

.carousel--style-1-v1:hover .carousel-control {
  opacity: 1; }

.carousel--style-2 .carousel-control {
  position: absolute;
  top: 50%;
  bottom: auto;
  left: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-top: -25px;
  font-size: 20px;
  background: rgba(37, 37, 37, 0.8);
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  filter: alpha(opacity=80);
  opacity: 0.8;
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear; }

.carousel--style-2 .carousel-control.right {
  right: 0;
  left: auto;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px; }

.carousel--style-2 .carousel-control.left {
  left: 0;
  right: auto;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px; }

.carousel--style-2 .carousel-control:hover {
  width: 70px;
  background: #1f3a93;
  color: #fff;
  filter: alpha(opacity=100);
  opacity: 1; }

.carousel--style-2 .carousel-inner .carousel-item img {
  max-width: 100%; }

.carousel--style-2 .carousel-indicators {
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 60%;
  padding-left: 0;
  margin-left: -30%;
  text-align: center;
  list-style: none; }

.carousel--style-2 .carousel-caption {
  text-shadow: 0;
  text-align: left;
  background: rgba(0, 0, 0, 0.5);
  background: -moz-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  background: -webkit-linear-gradient(top, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.65) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr= '#00000000', endColorstr='#a6000000',GradientType=0 );
  color: #fff; }

.carousel--style-2 .carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px 30px; }

.carousel--style-2 .carousel-caption .caption-title {
  display: block;
  margin: 10px 0 0;
  padding: 0;
  font-size: 22px;
  line-height: 1.1;
  color: #fff;
  font-weight: 500; }

.carousel--style-2 .carousel-caption .caption-subtitle {
  display: block;
  margin: 10px 0 0;
  padding: 0;
  font-size: 16px;
  line-height: 1.1;
  color: #ddd;
  font-weight: 400; }

.carousel--style-2 .carousel-caption .meta-info-cells li,
.carousel--style-2 .carousel-caption .meta-info-cells li a,
.carousel--style-2 .carousel-caption .meta-info-cells li i {
  color: #ddd; }

/* COLLAPSE STACKED */
.accordion-cards--stacked .card {
  border-radius: 0; }

.accordion-cards--stacked .card:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem; }

.accordion-cards--stacked .card:last-child {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.accordion-cards--stacked .card + .card {
  margin-top: 0;
  border-top: 0; }

/* COLLAPSE SPACED */
.accordion-cards--spaced .card {
  border-radius: 0.25rem; }

.accordion-cards--spaced .card + .card {
  margin-top: 1rem; }

/* STYLE 2 & STYLE 3 */
.accordion--style-1 .card,
.accordion--style-2 .card {
  border: 0;
  background: transparent; }

.accordion--style-1 .card-block,
.accordion--style-2 .card-block {
  border: 0; }

.accordion--style-1 .card-header,
.accordion--style-2 .card-header {
  color: #333;
  border: none;
  padding: 0 !important; }

.accordion--style-1 .card-title,
.accordion--style-2 .card-title {
  margin: 0;
  padding: 0;
  font-size: 0.875rem;
  color: inherit;
  background: #fff;
  border: 0; }

.accordion--style-2 .card-title {
  background: #000; }

.accordion--style-1 .card-title a,
.accordion--style-2 .card-title a {
  padding: 18px 35px 18px 22px;
  display: table;
  width: 100%;
  border: 1px solid #e0eded;
  color: black;
  font-size: 1.125rem;
  font-weight: 500;
  position: relative; }

.accordion--style-2 .card-title a {
  border: none;
  color: #fff; }

.accordion--style-1 .card-title a span,
.accordion--style-2 .card-title a span {
  position: absolute;
  top: 22px;
  right: 17px;
  color: #ccc; }

/* STYLE 3 and 4 */
.accordion--style-3 .card-header,
.accordion--style-4 .card-header {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 0; }

.accordion--style-4 .card-header {
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0 20px 0 0; }

.accordion--style-3 .card,
.accordion--style-4 .card {
  margin-bottom: 1px;
  border-radius: 0;
  background-color: transparent; }

.accordion--style-3 .card:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.05); }

.accordion--style-3 .card {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.accordion--style-3 .card-title,
.accordion--style-4 .card-title {
  overflow: hidden;
  padding: 0;
  border: 0; }

.accordion--style-3 .card-title a,
.accordion--style-4 .card-title a {
  padding: 1rem 0;
  font-weight: 400;
  font-size: 1.125rem;
  color: black;
  display: table;
  width: 100%;
  position: relative;
  overflow: hidden; }

.accordion--style-4 .card-title a {
  padding-left: 70px; }

.accordion--style-4 .card-title a > .collapse-heading-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 54px;
  height: 100%;
  text-align: center;
  line-height: 54px;
  border-right: 1px solid #e0eded; }

.accordion--style-4 .card-title a > .collapse-heading-icon > i {
  font-size: 28px;
  line-height: 54px;
  color: #818a91; }

.accordion--style-3 .card,
.accordion--style-4 .card {
  border: none; }

.accordion--style-3 .card-header > .card-title,
.accordion--style-3 [aria-expanded="true"] .card-block {
  border-bottom: 1px solid #e0eded; }

.accordion--style-3 .card-header > .card-title > a[aria-expanded="true"],
.accordion--style-4 .card-header > .card-title > a[aria-expanded="true"] {
  color: #0087be !important;
  text-decoration: none; }

/*!
 * Datepicker for Bootstrap v1.7.1 (https://github.com/uxsolutions/bootstrap-datepicker)
 *
 * Licensed under the Apache License v2.0 (http://www.apache.org/licenses/LICENSE-2.0)
 */
.datepicker {
  direction: ltr; }

.datepicker-inline {
  width: 220px; }

.datepicker-rtl {
  direction: rtl; }

.datepicker-rtl.dropdown-menu {
  left: auto; }

.datepicker-rtl table tr td span {
  float: right; }

.datepicker-dropdown {
  top: 0;
  left: 0; }

.datepicker-dropdown:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #999;
  border-top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute; }

.datepicker-dropdown:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  border-top: 0;
  position: absolute; }

.datepicker-dropdown.datepicker-orient-left:before {
  left: 6px; }

.datepicker-dropdown.datepicker-orient-left:after {
  left: 7px; }

.datepicker-dropdown.datepicker-orient-right:before {
  right: 6px; }

.datepicker-dropdown.datepicker-orient-right:after {
  right: 7px; }

.datepicker-dropdown.datepicker-orient-bottom:before {
  top: -7px; }

.datepicker-dropdown.datepicker-orient-bottom:after {
  top: -6px; }

.datepicker-dropdown.datepicker-orient-top:before {
  bottom: -7px;
  border-bottom: 0;
  border-top: 7px solid #999; }

.datepicker-dropdown.datepicker-orient-top:after {
  bottom: -6px;
  border-bottom: 0;
  border-top: 6px solid #fff; }

.datepicker table {
  margin: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.datepicker td,
.datepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: none; }

.table-striped .datepicker table tr td,
.table-striped .datepicker table tr th {
  background-color: transparent; }

.datepicker table tr td.day.focused,
.datepicker table tr td.day:hover {
  background: #eee;
  cursor: pointer; }

.datepicker table tr td.new,
.datepicker table tr td.old {
  color: #999; }

.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
  background: none;
  color: #999;
  cursor: default; }

.datepicker table tr td.highlighted {
  background: #d9edf7;
  border-radius: 0; }

.datepicker table tr td.today,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today:hover {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-image: linear-gradient(to bottom, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #000; }

.datepicker table tr td.today.active,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled.disabled,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today.disabled:hover[disabled],
.datepicker table tr td.today.disabled[disabled],
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today:hover:hover,
.datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today[disabled] {
  background-color: #fdf59a; }

.datepicker table tr td.today.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today:hover:active {
  background-color: #fbf069 \9; }

.datepicker table tr td.today:hover:hover {
  color: #000; }

.datepicker table tr td.today.active:hover {
  color: #fff; }

.datepicker table tr td.range,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range.disabled:hover,
.datepicker table tr td.range:hover {
  background: #eee;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0; }

.datepicker table tr td.range.today,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today:hover {
  background-color: #f3d17a;
  background-image: -moz-linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-image: -ms-linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f3c17a), to(#f3e97a));
  background-image: -webkit-linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-image: -o-linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-image: linear-gradient(to bottom, #f3c17a, #f3e97a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#f3c17a', endColorstr='#f3e97a', GradientType=0);
  border-color: #f3e97a #f3e97a #edde34;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0; }

.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled.disabled,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover.active,
.datepicker table tr td.range.today.disabled:hover.disabled,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today.disabled:hover[disabled],
.datepicker table tr td.range.today.disabled[disabled],
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today:hover.disabled,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today:hover:hover,
.datepicker table tr td.range.today:hover[disabled],
.datepicker table tr td.range.today[disabled] {
  background-color: #f3e97a; }

.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover.active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today:hover:active {
  background-color: #efe24b \9; }

.datepicker table tr td.selected,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected:hover {
  background-color: #9e9e9e;
  background-image: -moz-linear-gradient(to bottom, #b3b3b3, #808080);
  background-image: -ms-linear-gradient(to bottom, #b3b3b3, #808080);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b3b3b3), to(#808080));
  background-image: -webkit-linear-gradient(to bottom, #b3b3b3, #808080);
  background-image: -o-linear-gradient(to bottom, #b3b3b3, #808080);
  background-image: linear-gradient(to bottom, #b3b3b3, #808080);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#b3b3b3', endColorstr='#808080', GradientType=0);
  border-color: #808080 #808080 #595959;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); }

.datepicker table tr td.selected.active,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled.disabled,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover.active,
.datepicker table tr td.selected.disabled:hover.disabled,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected.disabled:hover[disabled],
.datepicker table tr td.selected.disabled[disabled],
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected:hover.disabled,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected:hover:hover,
.datepicker table tr td.selected:hover[disabled],
.datepicker table tr td.selected[disabled] {
  background-color: #808080; }

.datepicker table tr td.selected.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover.active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected:hover:active {
  background-color: #666666 \9; }

.datepicker table tr td.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -ms-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#08c), to(#0044cc));
  background-image: -webkit-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -o-linear-gradient(to bottom, #08c, #0044cc);
  background-image: linear-gradient(to bottom, #08c, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#08c', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); }

.datepicker table tr td.active.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active.disabled:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active[disabled] {
  background-color: #0044cc; }

.datepicker table tr td.active.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active:hover:active {
  background-color: #003399 \9; }

.datepicker table tr td span {
  display: block;
  width: 23%;
  height: 54px;
  line-height: 54px;
  float: left;
  margin: 1%;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px; }

.datepicker table tr td span.focused,
.datepicker table tr td span:hover {
  background: #eee; }

.datepicker table tr td span.disabled,
.datepicker table tr td span.disabled:hover {
  background: none;
  color: #999;
  cursor: default; }

.datepicker table tr td span.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -ms-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#08c), to(#0044cc));
  background-image: -webkit-linear-gradient(to bottom, #08c, #0044cc);
  background-image: -o-linear-gradient(to bottom, #08c, #0044cc);
  background-image: linear-gradient(to bottom, #08c, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr= '#08c', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25); }

.datepicker table tr td span.active.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active.disabled:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active[disabled] {
  background-color: #0044cc; }

.datepicker table tr td span.active.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active:hover:active {
  background-color: #003399 \9; }

.datepicker table tr td span.new,
.datepicker table tr td span.old {
  color: #999; }

.datepicker .datepicker-switch {
  width: 145px; }

.datepicker .datepicker-switch,
.datepicker .next,
.datepicker .prev,
.datepicker tfoot tr th {
  cursor: pointer; }

.datepicker .datepicker-switch:hover,
.datepicker .next:hover,
.datepicker .prev:hover,
.datepicker tfoot tr th:hover {
  background: #eee; }

.datepicker .next.disabled,
.datepicker .prev.disabled {
  visibility: hidden; }

.datepicker .cw {
  font-size: 10px;
  width: 12px;
  padding: 0 2px 0 5px;
  vertical-align: middle; }

.input-append.date .add-on,
.input-prepend.date .add-on {
  cursor: pointer; }

.input-append.date .add-on i,
.input-prepend.date .add-on i {
  margin-top: 3px; }

.input-daterange input {
  text-align: center; }

.input-daterange input:first-child {
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px; }

.input-daterange input:last-child {
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0; }

.input-daterange .add-on {
  display: inline-block;
  width: auto;
  min-width: 16px;
  height: 18px;
  padding: 4px 5px;
  font-weight: normal;
  line-height: 18px;
  text-align: center;
  text-shadow: 0 1px 0 #fff;
  vertical-align: middle;
  background-color: #eee;
  border: 1px solid #ccc;
  margin-left: -5px;
  margin-right: -5px; }

.delimiter {
  display: block;
  margin-top: 20px; }

.delimiter.delimiter--fixed-width {
  width: 130px !important;
  margin: auto; }

.delimiter--style-1 {
  border-bottom: 1px solid #e0eded;
  margin: 30px 0;
  position: relative; }

.delimiter--style-1 .delimiter-title {
  width: 200px;
  position: relative;
  top: 25px;
  background: #fff;
  border: 2px solid #e0eded;
  color: #818a91;
  margin: auto;
  height: 50px;
  text-align: center;
  z-index: 3;
  cursor: default; }

.delimiter--style-1 .delimiter-title h3 {
  margin: 0;
  padding: 0;
  line-height: 50px;
  font-size: 1rem;
  font-weight: 600; }

.delimiter--style-1 .divider-title-wrapper::before {
  content: "";
  width: 280px;
  height: 50px;
  position: absolute;
  margin-left: -140px;
  left: 50%;
  top: 50%;
  background: #fff;
  z-index: 2; }

.delimiter--style-2 {
  border-bottom: 2px solid #132222;
  position: relative; }

.delimiter--style-2:before {
  content: "";
  width: 160%;
  position: absolute;
  left: -30%;
  top: -4px;
  border-top: 2px solid #e0eded; }

.delimiter--style-2 .delimiter-icon {
  position: absolute;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  left: 50%;
  top: -18px;
  margin-left: -18px;
  background: white;
  font-size: 20px; }

.delimiter-left-thick--style-1 {
  border-left: 4px solid #0087be; }

.short-delimiter--style-1 {
  padding: 14px 0;
  display: block;
  position: relative; }

.short-delimiter--style-1:before {
  content: "";
  display: block;
  position: relative;
  top: 0;
  left: 0;
  width: 40px;
  height: 1px;
  background: #0087be; }

.short-delimiter-light:before {
  background: #818a91; }

.short-delimiter-dark:before {
  background: #2b2b2c; }

.short-delimiter-base-1:before {
  background: #0087be; }

.short-delimiter-thick {
  height: 3px; }

.short-delimiter-sm:before {
  width: 40px; }

.short-delimiter-md:before {
  width: 60px; }

.short-delimiter-lg:before {
  width: 90px; }

.short-delimiter-center.short-delimiter-sm:before {
  left: 50%;
  margin-left: -20px; }

.short-delimiter-center.short-delimiter-md:before {
  left: 50%;
  margin-left: -30px; }

.short-delimiter-center.short-delimiter-lg:before {
  left: 50%;
  margin-left: -45px; }

.vertical-delimiter--style-1 {
  height: 100px;
  position: relative; }

.vertical-delimiter--style-1:before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: #0087be; }

.dropdown-menu {
  border: 1px solid #eceff1;
  border-radius: 1px;
  line-height: 1;
  min-width: 12rem;
  box-shadow: 0 5px 25px 0 rgba(123, 123, 123, 0.15); }

.dropdown-menu {
  font-size: 0.9rem; }

@media (min-width: 992px) {
  .dropdown-menu {
    display: block;
    opacity: 0;
    visibility: hidden;
    z-index: 1; }

  .dropdown--animated .dropdown-menu {
    -moz-transform: translateY(8px);
    -o-transform: translateY(8px);
    -ms-transform: translateY(8px);
    -webkit-transform: translateY(8px);
    transform: translateY(8px);
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s; } }
.dropdown-menu .dropdown-item {
  padding: 0.875rem 1rem;
  font-size: 0.875rem; }

.dropdown-menu .dropdown-item:active,
.dropdown-menu .dropdown-item:hover {
  background-color: rgba(162, 162, 162, 0.1);
  color: inherit; }

.dropdown-menu .dropdown-item > i {
  margin-right: 1rem;
  font-size: 1.3rem;
  line-height: .75em;
  vertical-align: -17%; }

.dropdown-header {
  padding-left: 1rem;
  padding-right: 1rem; }

.dropdown-menu.dropdown-menu-sm {
  min-width: 100px;
  border: 0.1rem; }

.dropdown-menu.dropdown-menu-lg {
  min-width: 260px;
  border-radius: 0.25rem; }

.dropdown--style-2 .dropdown-toggle {
  display: block;
  padding: 0 1rem;
  text-decoration: none;
  color: inherit; }

.dropdown--style-2 .dropdown-toggle::after {
  border: 0;
  margin: 0;
  content: none; }

.dropdown--style-2 .dropdown-image {
  width: 32px;
  height: 32px;
  border-radius: 0.25rem; }

.dropdown--style-2 .dropdown-image.rounded-circle {
  border-radius: 50%; }

.dropdown--style-2 .dropdown-icon {
  font-size: 1.5rem;
  text-align: center; }

.dropdown--style-2 .has-badge .badge {
  position: absolute;
  left: 38px;
  font-weight: 300; }

.dropdown--style-2 .has-notification .badge {
  padding: 0;
  margin: 0;
  display: block;
  position: absolute;
  left: 50%;
  top: 0px;
  font-weight: 300;
  width: 6px;
  height: 6px;
  margin-left: -3px;
  border-radius: 6px; }

.dropdown--style-2 .dropdown-text {
  font-size: 0.875rem;
  margin-left: 0.5rem;
  display: inline-block; }

.dropdown-menu.dropdown-menu-inverse {
  background: #282f37 !important;
  border-color: #242a31 !important; }

.dropdown-menu.dropdown-menu-inverse .dropdown-item {
  color: #dadada !important; }

.dropdown-menu.dropdown-menu-inverse .dropdown-item:active,
.dropdown-menu.dropdown-menu-inverse .dropdown-item:focus,
.dropdown-menu.dropdown-menu-inverse .dropdown-item:hover {
  color: #fff !important;
  background: #31353e !important; }

.dropdown-menu.dropdown-menu-inverse .dropdown-divider {
  background: #191e23; }

@media (min-width: 992px) {
  .show > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    z-index: 1000;
    -moz-transform: none;
    -o-transform: none;
    -ms-transform: none;
    -webkit-transform: none;
    transform: none; } }
.navbar .dropdown-toggle::after {
  display: none; }

.dropdown-submenu .dropdown-toggle i {
  font-size: 10px;
  position: absolute;
  right: 14px;
  top: 8px;
  color: #a3a7bd; }

@media (max-width: 991px) {
  .dropdown-submenu .dropdown-menu {
    border-color: #e8e8e8; }

  .dropdown-submenu .dropdown-menu .dropdown-item {
    padding-left: 2rem; } }
@media (min-width: 992px) {
  .dropdown-submenu .dropdown-menu {
    left: 98%;
    top: -2px;
    border-radius: 0.25rem !important; } }
.dropdown-extend {
  position: initial; }

.dropdown-extend-menu {
  width: 100%; }

@media (min-width: 768px) {
  .dropdown-extend-menu {
    padding: 20px 30px; } }
@media (min-width: 768px) {
  .dropdown-extend-menu .dropdown-item {
    margin: 5px 0; } }
.dropdown-extend-menu .dropdown-item i {
  margin-right: 3px;
  font-size: 14px;
  color: #6b7386; }

@media (max-width: 767px) {
  .dropdown-extend-menu .dropdown-item i {
    display: none; } }
.flatpickr-calendar {
  background: transparent;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  visibility: hidden;
  text-align: center;
  padding: 0;
  -webkit-animation: none;
  animation: none;
  direction: ltr;
  border: 0;
  font-size: 14px;
  line-height: 24px;
  border-radius: 5px;
  position: absolute;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  background: #fff;
  -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
  box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08); }

.flatpickr-calendar.open,
.flatpickr-calendar.inline {
  opacity: 1;
  visibility: visible;
  overflow: visible;
  max-height: 640px; }

.flatpickr-calendar.open {
  display: inline-block;
  z-index: 99999; }

.flatpickr-calendar.animate.open {
  -webkit-animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px; }

.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px); }

.flatpickr-calendar.static.open {
  z-index: 999;
  display: block; }

.flatpickr-calendar.hasWeeks {
  width: auto; }

.flatpickr-calendar .hasWeeks .dayContainer,
.flatpickr-calendar .hasTime .dayContainer {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.flatpickr-calendar .hasWeeks .dayContainer {
  border-left: 0; }

.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
  height: 40px;
  border-top: 1px solid #e6e6e6; }

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto; }

.flatpickr-calendar:before,
.flatpickr-calendar:after {
  position: absolute;
  display: block;
  pointer-events: none;
  border: solid transparent;
  content: '';
  height: 0;
  width: 0;
  left: 22px; }

.flatpickr-calendar.rightMost:before,
.flatpickr-calendar.rightMost:after {
  left: auto;
  right: 22px; }

.flatpickr-calendar:before {
  border-width: 5px;
  margin: 0 -5px; }

.flatpickr-calendar:after {
  border-width: 4px;
  margin: 0 -4px; }

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  bottom: 100%; }

.flatpickr-calendar.arrowTop:before {
  border-bottom-color: #e6e6e6; }

.flatpickr-calendar.arrowTop:after {
  border-bottom-color: #fff; }

.flatpickr-calendar.arrowBottom:before,
.flatpickr-calendar.arrowBottom:after {
  top: 100%; }

.flatpickr-calendar.arrowBottom:before {
  border-top-color: #e6e6e6; }

.flatpickr-calendar.arrowBottom:after {
  border-top-color: #fff; }

.flatpickr-calendar:focus {
  outline: 0; }

.flatpickr-wrapper {
  position: relative;
  display: inline-block; }

.flatpickr-month {
  background: transparent;
  color: rgba(0, 0, 0, 0.9);
  fill: rgba(0, 0, 0, 0.9);
  height: 28px;
  line-height: 1;
  text-align: center;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: hidden; }

.flatpickr-prev-month,
.flatpickr-next-month {
  text-decoration: none;
  cursor: pointer;
  position: absolute;
  top: 0px;
  line-height: 16px;
  height: 28px;
  padding: 10px calc(3.57% - 1.5px);
  z-index: 3; }

.flatpickr-prev-month i,
.flatpickr-next-month i {
  position: relative; }

.flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-next-month.flatpickr-prev-month {
  /*
          /*rtl:begin:ignore*/
  /*
          */
  left: 0;
  /*
          /*rtl:end:ignore*/
  /*
          */ }

/*
        /*rtl:begin:ignore*/
/*
        /*rtl:end:ignore*/
.flatpickr-prev-month.flatpickr-next-month,
.flatpickr-next-month.flatpickr-next-month {
  /*
          /*rtl:begin:ignore*/
  /*
          */
  right: 0;
  /*
          /*rtl:end:ignore*/
  /*
          */ }

/*
        /*rtl:begin:ignore*/
/*
        /*rtl:end:ignore*/
.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
  color: #959ea9; }

.flatpickr-prev-month:hover svg,
.flatpickr-next-month:hover svg {
  fill: #f64747; }

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
  width: 14px; }

.flatpickr-prev-month svg path,
.flatpickr-next-month svg path {
  -webkit-transition: fill 0.1s;
  transition: fill 0.1s;
  fill: inherit; }

.numInputWrapper {
  position: relative;
  height: auto; }

.numInputWrapper input,
.numInputWrapper span {
  display: inline-block; }

.numInputWrapper input {
  width: 100%; }

.numInputWrapper span {
  position: absolute;
  right: 0;
  width: 14px;
  padding: 0 4px 0 2px;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  cursor: pointer;
  border: 1px solid rgba(57, 57, 57, 0.05);
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

.numInputWrapper span:hover {
  background: rgba(0, 0, 0, 0.1); }

.numInputWrapper span:active {
  background: rgba(0, 0, 0, 0.2); }

.numInputWrapper span:after {
  display: block;
  content: "";
  position: absolute;
  top: 33%; }

.numInputWrapper span.arrowUp {
  top: 0;
  border-bottom: 0; }

.numInputWrapper span.arrowUp:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(57, 57, 57, 0.6); }

.numInputWrapper span.arrowDown {
  top: 50%; }

.numInputWrapper span.arrowDown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(57, 57, 57, 0.6); }

.numInputWrapper span svg {
  width: inherit;
  height: auto; }

.numInputWrapper span svg path {
  fill: rgba(0, 0, 0, 0.5); }

.numInputWrapper:hover {
  background: rgba(0, 0, 0, 0.05); }

.numInputWrapper:hover span {
  opacity: 1; }

.flatpickr-current-month {
  font-size: 135%;
  line-height: inherit;
  font-weight: 300;
  color: inherit;
  position: absolute;
  width: 75%;
  left: 12.5%;
  padding: 6.16px 0 0 0;
  line-height: 1;
  height: 28px;
  display: inline-block;
  text-align: center;
  -webkit-transform: translate3d(0px, 0px, 0px);
  transform: translate3d(0px, 0px, 0px); }

.flatpickr-current-month.slideLeft {
  -webkit-transform: translate3d(-100%, 0px, 0px);
  transform: translate3d(-100%, 0px, 0px);
  -webkit-animation: fpFadeOut 400ms ease, fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeOut 400ms ease, fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-current-month.slideLeftNew {
  -webkit-transform: translate3d(100%, 0px, 0px);
  transform: translate3d(100%, 0px, 0px);
  -webkit-animation: fpFadeIn 400ms ease, fpSlideLeftNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeIn 400ms ease, fpSlideLeftNew 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-current-month.slideRight {
  -webkit-transform: translate3d(100%, 0px, 0px);
  transform: translate3d(100%, 0px, 0px);
  -webkit-animation: fpFadeOut 400ms ease, fpSlideRight 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeOut 400ms ease, fpSlideRight 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-current-month.slideRightNew {
  -webkit-transform: translate3d(0, 0, 0px);
  transform: translate3d(0, 0, 0px);
  -webkit-animation: fpFadeIn 400ms ease, fpSlideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeIn 400ms ease, fpSlideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-current-month span.cur-month {
  font-family: inherit;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  margin-left: 0.5ch;
  padding: 0; }

.flatpickr-current-month span.cur-month:hover {
  background: rgba(0, 0, 0, 0.05); }

.flatpickr-current-month .numInputWrapper {
  width: 6ch;
  width: 7ch\0;
  display: inline-block; }

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: rgba(0, 0, 0, 0.9); }

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: rgba(0, 0, 0, 0.9); }

.flatpickr-current-month input.cur-year {
  background: transparent;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: inherit;
  cursor: default;
  padding: 0 0 0 0.5ch;
  margin: 0;
  display: inline-block;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  line-height: inherit;
  height: initial;
  border: 0;
  border-radius: 0;
  vertical-align: initial; }

.flatpickr-current-month input.cur-year:focus {
  outline: 0; }

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  font-size: 100%;
  color: rgba(0, 0, 0, 0.5);
  background: transparent;
  pointer-events: none; }

.flatpickr-weekdays {
  background: transparent;
  text-align: center;
  overflow: hidden;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px; }

span.flatpickr-weekday {
  cursor: default;
  font-size: 90%;
  background: transparent;
  color: rgba(0, 0, 0, 0.54);
  line-height: 1;
  margin: 0;
  text-align: center;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-weight: bolder; }

.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0 0; }

.flatpickr-days {
  position: relative;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%; }

.flatpickr-days:focus {
  outline: 0; }

.dayContainer {
  padding: 0;
  outline: 0;
  text-align: left;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  -webkit-transform: translate3d(0px, 0px, 0px);
  transform: translate3d(0px, 0px, 0px);
  opacity: 1; }

.flatpickr-calendar.animate .dayContainer.slideLeft {
  -webkit-animation: fpFadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-calendar.animate .dayContainer.slideLeft,
.flatpickr-calendar.animate .dayContainer.slideLeftNew {
  -webkit-transform: translate3d(-100%, 0px, 0px);
  transform: translate3d(-100%, 0px, 0px); }

.flatpickr-calendar.animate .dayContainer.slideLeftNew {
  -webkit-animation: fpFadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideLeft 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-calendar.animate .dayContainer.slideRight {
  -webkit-animation: fpFadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideRight 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeOut 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideRight 400ms cubic-bezier(0.23, 1, 0.32, 1);
  -webkit-transform: translate3d(100%, 0px, 0px);
  transform: translate3d(100%, 0px, 0px); }

.flatpickr-calendar.animate .dayContainer.slideRightNew {
  -webkit-animation: fpFadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1);
  animation: fpFadeIn 400ms cubic-bezier(0.23, 1, 0.32, 1), fpSlideRightNew 400ms cubic-bezier(0.23, 1, 0.32, 1); }

.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #393939;
  cursor: pointer;
  font-weight: 400;
  width: 14.2857143%;
  -webkit-flex-basis: 14.2857143%;
  -ms-flex-preferred-size: 14.2857143%;
  flex-basis: 14.2857143%;
  height: 39px;
  line-height: 39px;
  margin: 0;
  display: inline-block;
  position: relative;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center; }

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  cursor: pointer;
  outline: 0;
  background: #e6e6e6;
  border-color: #e6e6e6; }

.flatpickr-day.today {
  border-color: #959ea9; }

.flatpickr-day.today:hover,
.flatpickr-day.today:focus {
  border-color: #959ea9;
  background: #959ea9;
  color: #fff; }

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #569ff7;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  border-color: #569ff7; }

.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange,
.flatpickr-day.endRange.startRange {
  border-radius: 50px 0 0 50px; }

.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange,
.flatpickr-day.endRange.endRange {
  border-radius: 0 50px 50px 0; }

.flatpickr-day.selected.startRange + .endRange,
.flatpickr-day.startRange.startRange + .endRange,
.flatpickr-day.endRange.startRange + .endRange {
  -webkit-box-shadow: -10px 0 0 #569ff7;
  box-shadow: -10px 0 0 #569ff7; }

.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange,
.flatpickr-day.endRange.startRange.endRange {
  border-radius: 50px; }

.flatpickr-day.inRange {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
  box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6; }

.flatpickr-day.disabled,
.flatpickr-day.disabled:hover {
  pointer-events: none; }

.flatpickr-day.disabled,
.flatpickr-day.disabled:hover,
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.notAllowed.nextMonthDay {
  color: rgba(57, 57, 57, 0.3);
  background: transparent;
  border-color: transparent;
  cursor: default; }

.flatpickr-day.week.selected {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
  box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7; }

.rangeMode .flatpickr-day {
  margin-top: 1px; }

.flatpickr-weekwrapper {
  display: inline-block;
  float: left; }

.flatpickr-weekwrapper .flatpickr-weeks {
  padding: 0 12px;
  -webkit-box-shadow: 1px 0 0 #e6e6e6;
  box-shadow: 1px 0 0 #e6e6e6; }

.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  width: 100%;
  line-height: 28px; }

.flatpickr-weekwrapper span.flatpickr-day {
  display: block;
  width: 100%;
  max-width: none; }

.flatpickr-innerContainer {
  display: block;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden; }

.flatpickr-rContainer {
  display: inline-block;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

.flatpickr-time {
  text-align: center;
  outline: 0;
  display: block;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex; }

.flatpickr-time:after {
  content: "";
  display: table;
  clear: both; }

.flatpickr-time .numInputWrapper {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 40%;
  height: 40px;
  float: left; }

.flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: #393939; }

.flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: #393939; }

.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%; }

.flatpickr-time.time24hr .numInputWrapper {
  width: 49%; }

.flatpickr-time input {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  text-align: center;
  margin: 0;
  padding: 0;
  height: inherit;
  line-height: inherit;
  cursor: pointer;
  color: #393939;
  font-size: 14px;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

.flatpickr-time input.flatpickr-hour {
  font-weight: bold; }

.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
  font-weight: 400; }

.flatpickr-time input:focus {
  outline: 0;
  border: 0; }

.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  height: inherit;
  display: inline-block;
  float: left;
  line-height: inherit;
  color: #393939;
  font-weight: bold;
  width: 2%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center; }

.flatpickr-time .flatpickr-am-pm {
  outline: 0;
  width: 18%;
  cursor: pointer;
  text-align: center;
  font-weight: 400; }

.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time .flatpickr-am-pm:focus {
  background: #f0f0f0; }

.flatpickr-input[readonly] {
  cursor: pointer; }

@-webkit-keyframes fpFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes fpFadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0); }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@-webkit-keyframes fpSlideLeft {
  from {
    -webkit-transform: translate3d(0px, 0px, 0px);
    transform: translate3d(0px, 0px, 0px); }
  to {
    -webkit-transform: translate3d(-100%, 0px, 0px);
    transform: translate3d(-100%, 0px, 0px); } }
@keyframes fpSlideLeft {
  from {
    -webkit-transform: translate3d(0px, 0px, 0px);
    transform: translate3d(0px, 0px, 0px); }
  to {
    -webkit-transform: translate3d(-100%, 0px, 0px);
    transform: translate3d(-100%, 0px, 0px); } }
@-webkit-keyframes fpSlideLeftNew {
  from {
    -webkit-transform: translate3d(100%, 0px, 0px);
    transform: translate3d(100%, 0px, 0px); }
  to {
    -webkit-transform: translate3d(0px, 0px, 0px);
    transform: translate3d(0px, 0px, 0px); } }
@keyframes fpSlideLeftNew {
  from {
    -webkit-transform: translate3d(100%, 0px, 0px);
    transform: translate3d(100%, 0px, 0px); }
  to {
    -webkit-transform: translate3d(0px, 0px, 0px);
    transform: translate3d(0px, 0px, 0px); } }
@-webkit-keyframes fpSlideRight {
  from {
    -webkit-transform: translate3d(0, 0, 0px);
    transform: translate3d(0, 0, 0px); }
  to {
    -webkit-transform: translate3d(100%, 0px, 0px);
    transform: translate3d(100%, 0px, 0px); } }
@keyframes fpSlideRight {
  from {
    -webkit-transform: translate3d(0, 0, 0px);
    transform: translate3d(0, 0, 0px); }
  to {
    -webkit-transform: translate3d(100%, 0px, 0px);
    transform: translate3d(100%, 0px, 0px); } }
@-webkit-keyframes fpSlideRightNew {
  from {
    -webkit-transform: translate3d(-100%, 0, 0px);
    transform: translate3d(-100%, 0, 0px); }
  to {
    -webkit-transform: translate3d(0, 0, 0px);
    transform: translate3d(0, 0, 0px); } }
@keyframes fpSlideRightNew {
  from {
    -webkit-transform: translate3d(-100%, 0, 0px);
    transform: translate3d(-100%, 0, 0px); }
  to {
    -webkit-transform: translate3d(0, 0, 0px);
    transform: translate3d(0, 0, 0px); } }
@-webkit-keyframes fpFadeOut {
  from {
    opacity: 1; }
  to {
    opacity: 0; } }
@keyframes fpFadeOut {
  from {
    opacity: 1; }
  to {
    opacity: 0; } }
@-webkit-keyframes fpFadeIn {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }
@keyframes fpFadeIn {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }
.flatpickr-wrapper {
  display: block; }

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  content: none; }

.flatpickr-calendar {
  border-radius: 2px;
  border: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.075);
  width: auto;
  margin-top: -2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
  border-color: #f6f6f6;
  height: 60px; }

.flatpickr-month {
  background-color: #0087be;
  color: #FFF;
  height: 60px;
  border-radius: 0.25rem 0.25rem 0 0;
  margin-bottom: 10px; }

.flatpickr-current-month {
  top: 0;
  font-size: 0.875rem;
  font-weight: 500;
  height: 60px;
  padding: 0;
  line-height: 60px; }

.flatpickr-current-month input.cur-year, .flatpickr-current-month span.cur-month {
  font-weight: 500; }

.flatpickr-current-month .numInputWrapper:hover, .flatpickr-current-month span.cur-month:hover {
  background-color: transparent; }

.flatpickr-current-month .numInputWrapper span {
  border: 0;
  right: -5px;
  padding: 0; }

.flatpickr-current-month .numInputWrapper span:after {
  left: 3px; }

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: #FFF; }

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: #FFF; }

.flatpickr-next-month,
.flatpickr-prev-month {
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 50%;
  font-size: 1.2rem;
  top: 13px;
  padding: 0; }

.flatpickr-next-month:hover, .flatpickr-prev-month:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #FFF; }

.flatpickr-prev-month {
  margin-left: 12px; }

.flatpickr-next-month {
  margin-right: 12px; }

span.flatpickr-weekday {
  font-weight: 500;
  color: #333; }

.flatpickr-day {
  font-size: 0.875rem;
  border: 0; }

.flatpickr-day.selected, .flatpickr-day.selected:hover {
  background-color: #0087be !important; }

.flatpickr-day.today, .flatpickr-day.today:hover {
  background-color: #f6f6f6;
  color: #333; }

.flatpickr-day:hover {
  background-color: #f6f6f6; }

.flatpickr-time {
  max-height: 60px;
  height: 60px;
  line-height: 60px; }

.flatpickr-time .flatpickr-am-pm, .flatpickr-time .numInputWrapper {
  height: auto; }

.flatpickr-time .flatpickr-am-pm:hover, .flatpickr-time .numInputWrapper:hover {
  background-color: #f9f9f9; }

.footer {
  background: #111;
  color: rgba(255, 255, 255, 0.7); }

.footer-top {
  padding: 4rem 0; }

.footer-bottom {
  padding: 1.5rem 0;
  background: #131313; }

.footer-bottom .copyright {
  font-size: 0.8rem; }

.footer .heading {
  color: rgba(255, 255, 255, 0.7); }

.footer ul.footer-links {
  margin: 0;
  padding: 0;
  list-style: none; }

.footer .footer-links > li > a {
  display: inline-block;
  padding: 0.25rem 0;
  font-size: 0.8rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7); }

.footer .footer-links > li > a:hover {
  color: rgba(255, 255, 255, 0.5); }

.footer .footer-links > li > .footer-link-date {
  display: block;
  margin-bottom: 0.5rem; }

.footer p {
  font-size: 0.875rem; }

.footer .social-media > li > a > i {
  color: rgba(255, 255, 255, 0.7); }

.footer .social-media > li > a:hover > i {
  color: rgba(255, 255, 255, 0.5); }

.footer .footer-menu {
  padding: 0;
  margin: 0;
  list-style: none; }

.footer .footer-menu > li {
  display: inline-block;
  margin-right: 1rem; }

.footer .footer-menu > li > a {
  display: inline-block;
  padding: 0.25rem 0.4rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: uppercase; }

.footer .footer-menu > li:last-child > a {
  margin-right: 0; }

.footer .footer-menu > li > a:hover {
  color: rgba(255, 255, 255, 0.5); }

.footer .footer-menu > li.logo-delimiter {
  padding-left: 2rem;
  padding-right: 2rem; }

@media (max-width: 991px) {
  .footer .footer-menu > li.logo-delimiter {
    display: none; } }
.footer .copy-links {
  margin: 10px 0 0 0;
  padding: 0;
  list-style: none; }

.footer .copy-links li {
  display: inline-block;
  margin-right: 10px;
  font-size: 0.75rem;
  color: #818a91; }

.footer .copy-links li a {
  color: #818a91; }

.footer .copy-links li a:hover {
  text-decoration: underline; }

.footer-inverse {
  background: #eeeeee;
  color: rgba(0, 0, 0, 0.7); }

.footer-inverse .footer-bottom {
  background: #ececec; }

.footer-inverse .heading {
  color: rgba(0, 0, 0, 0.7); }

.footer-inverse .footer-links > li > a {
  color: rgba(0, 0, 0, 0.7); }

.footer-inverse .footer-links > li > a:hover {
  color: rgba(0, 0, 0, 0.5); }

.footer-inverse .social-media > li > a > i {
  color: rgba(0, 0, 0, 0.7); }

.footer-inverse .social-media > li > a:hover > i {
  color: rgba(0, 0, 0, 0.5); }

.footer-inverse .footer-menu > li > a {
  color: rgba(0, 0, 0, 0.7); }

.footer-inverse .footer-menu > li > a:hover {
  color: rgba(0, 0, 0, 0.5); }

.footer--style-1 {
  background: #0087be;
  color: #FFF; }

.footer--style-1 .footer-links > li > a,
.footer--style-1 .heading {
  color: #FFF; }

.footer--style-1 .social-media > li > a > i {
  color: #FFF; }

.footer--style-2,
.footer--style-2 .footer-top,
.footer--style-2 .footer-bottom {
  background: #efefef; }

label {
  font-weight: 400;
  font-size: 0.7rem;
  text-transform: none; }

textarea.no-resize {
  resize: none !important; }

.form-control,
.form-control > .btn {
  font-size: 0.875rem;
  font-weight: 400;
  color: #555;
  background-color: #FFF;
  border-width: 1px;
  border-color: #e6e6e6;
  border-radius: 3px;
  -webkit-box-shadow: none;
  box-shadow: none; }

.form-group textarea.form-control,
textarea.form-control {
  height: auto; }

.form-control-lg,
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  padding: .75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 3px; }

.input-group-btn:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.form-default .form-control {
  background: #FFF;
  border-color: #e6e6e6;
  color: #555; }

.form-default .form-control:focus {
  background: #fafafa;
  border-color: #0087be; }

.form-default .form-control::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-default .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(0, 0, 0, 0.4); }

.form-default .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(0, 0, 0, 0.4); }

.form-default .form-control:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.4); }

.form-base-1 .form-control {
  background: #177196;
  border-color: #a71414;
  color: #FFF; }

.form-base-1 .form-control:focus {
  background: #177196;
  border-color: #a71414;
  color: #FFF; }

.form-base-1 .form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-1 .form-control:-moz-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-1 .form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-1 .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-2 .form-control {
  background: #131619;
  border-color: #0f1113;
  color: #FFF; }

.form-base-2 .form-control:focus {
  background: #131619;
  border-color: #0f1113;
  color: #FFF; }

.form-base-2 .form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-2 .form-control:-moz-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-2 .form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-base-2 .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

.form-inverse .form-control {
  background: #21252a;
  border-color: #333a41;
  color: #818a91; }

.form-inverse .form-control:focus {
  background: #21252a;
  border-color: white; }

.form-inverse .form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.4); }

.form-inverse .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(255, 255, 255, 0.4); }

.form-inverse .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.4); }

.form-inverse .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.4); }

/* Bootstrap select: plugin (assets/bootstrap-select) */
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100% !important; }

.form-control.bootstrap-select {
  background: transparent; }

.bootstrap-select .dropdown-menu {
  padding-top: 10px;
  padding-bottom: 10px; }

.bootstrap-select .dropdown-menu.show {
  border-color: #fff;
  top: -4px;
  border-radius: 3px; }

.bootstrap-select .dropdown-menu .dropdown-item {
  outline: 0;
  padding: 0.5rem 1rem; }

.form-inverse .bootstrap-select .dropdown-menu .dropdown-item:hover,
.form-inverse .bootstrap-select .dropdown-menu .selected .dropdown-item {
  background: #0d0d0d; }

.bootstrap-select.form-control {
  padding: 0;
  font-size: 1.9rem; }

.bootstrap-select .btn {
  font-size: 0.875rem;
  font-weight: 400;
  font-family: "Roboto", sans-serif;
  text-transform: none;
  letter-spacing: 0;
  border-radius: 3px; }

.form-control > .btn {
  padding: 0.5rem 0.75rem; }

.form-control-lg > .btn {
  padding: 0.75rem 1.5rem; }

.input-group-lg > .input-group-btn > select.btn:not([size]):not([multiple]),
.input-group-lg > select.form-control:not([size]):not([multiple]),
.input-group-lg > select.input-group-addon:not([size]):not([multiple]),
select.form-control-lg:not([size]):not([multiple]) {
  height: 2.6875rem; }

.form-default .bootstrap-select .btn-default {
  background: #FFF;
  border-color: #e6e6e6;
  color: rgba(0, 0, 0, 0.4); }

.form-default .btn-default.active.focus,
.form-default .btn-default.active:focus,
.form-default .btn-default.active:hover,
.form-default .btn-default:active:focus,
.form-default .btn-default:active:hover,
.form-default .form-inverse .btn-default:active.focus,
.form-default .open > .dropdown-toggle.btn-default,
.form-default .open > .dropdown-toggle.btn-default.focus,
.form-default .open > .dropdown-toggle.btn-default:focus,
.form-default .open > .dropdown-toggle.btn-default:hover {
  background: #fafafa;
  color: #333;
  border-color: transparent; }

.form-base-1 .bootstrap-select .btn-default {
  background: #177196;
  border-color: #a71414;
  color: #FFF; }

.form-base-2 .bootstrap-select .btn-default {
  background: #131619;
  border-color: #0f1113;
  color: #FFF; }

.form-inverse .bootstrap-select .btn-default {
  background: #21252a;
  border-color: #333a41;
  color: #818a91; }

.form-inverse .btn-default.active.focus,
.form-inverse .btn-default.active:focus,
.form-inverse .btn-default.active:hover,
.form-inverse .btn-default:active:focus,
.form-inverse .btn-default:active:hover,
.form-inverse .form-inverse .btn-default:active.focus,
.form-inverse .open > .dropdown-toggle.btn-default,
.form-inverse .open > .dropdown-toggle.btn-default.focus,
.form-inverse .open > .dropdown-toggle.btn-default:focus,
.form-inverse .open > .dropdown-toggle.btn-default:hover {
  background: #21252a;
  border-color: inherit; }

.form-inverse .bootstrap-select .dropdown-menu {
  background: black;
  border-color: black; }

.form-material .bootstrap-select .btn-default,
.form-material .form-control {
  background: transparent;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  border-width: 2px;
  padding-left: 1px;
  padding-right: 1px; }

.form-default.form-material .form-control:focus {
  background: transparent;
  border-color: #e6e6e6; }

.form-inverse.form-material .form-control:focus {
  background: transparent;
  border-color: #191919; }

.form-material .bar {
  position: relative;
  display: block; }

.form-material .bar:after,
.form-material .bar:before {
  content: '';
  height: 2px;
  width: 0;
  bottom: 0;
  position: absolute;
  background: #0087be;
  transition: 0.2s ease all; }

.form-material .bar:before {
  left: 50%; }

.form-material .bar:after {
  right: 50%; }

.form-material .form-control:focus ~ .bar:after,
.form-material .form-control:focus ~ .bar:before {
  width: 50%; }

/* CUSTOM CHECKBOXES AND RADIOS */
.checkbox {
  outline: none; }

.checkbox-inline {
  display: inline-block;
  margin-right: 10px; }

.checkbox label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding-left: 0.5rem;
  margin-bottom: 0.8rem;
  font-size: 0.875rem;
  line-height: 18px; }

.checkbox label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 19px;
  height: 19px;
  left: 0;
  margin-left: -20px;
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  background-color: #FFF;
  -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  transition: border 0.15s ease-in-out, color 0.15s ease-in-out; }

.checkbox label::after {
  display: inline-block;
  position: absolute;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  margin-left: -20px;
  padding-left: 3px;
  padding-top: 0;
  font-size: 12px;
  color: transparent; }

.checkbox input[type="checkbox"],
.checkbox input[type="radio"] {
  opacity: 0;
  z-index: 1; }

.checkbox input[type="checkbox"]:checked + label::after,
.checkbox input[type="radio"]:checked + label::after {
  color: #0087be;
  font-family: "FontAwesome";
  content: "\f00c"; }

.checkbox input[type="checkbox"]:disabled + label,
.checkbox input[type="radio"]:disabled + label {
  opacity: 0.65; }

.checkbox input[type="checkbox"]:disabled + label::before,
.checkbox input[type="radio"]:disabled + label::before {
  background-color: #ddd;
  cursor: not-allowed; }

.checkbox.checkbox-circle label::before {
  border-radius: 50%; }

.checkbox.checkbox-inline {
  margin-top: 0; }

.checkbox-success input[type="checkbox"]:checked + label::before,
.checkbox-success input[type="radio"]:checked + label::before {
  background-color: #5cb85c;
  border-color: #5cb85c; }

.checkbox-success input[type="checkbox"]:checked + label::after,
.checkbox-success input[type="radio"]:checked + label::after {
  color: #fff; }

.has-success input[type="checkbox"] + label::before,
.has-success input[type="radio"] + label::before {
  border-color: #5cb85c; }

.checkbox-primary input[type="checkbox"]:checked + label::before,
.checkbox-primary input[type="radio"]:checked + label::before {
  background-color: #0087be;
  border-color: #0087be; }

.checkbox-primary input[type="checkbox"]:checked + label::after,
.checkbox-primary input[type="radio"]:checked + label::after {
  color: #FFF; }

.checkbox-warning input[type="checkbox"]:checked + label::before,
.checkbox-warning input[type="radio"]:checked + label::before {
  background-color: #ec971f;
  border-color: #ec971f; }

.checkbox-warning input[type="checkbox"]:checked + label::after,
.checkbox-warning input[type="radio"]:checked + label::after {
  color: #fff; }

.has-warning input[type="checkbox"] + label::before,
.has-warning input[type="radio"] + label::before {
  border-color: #ec971f; }

.checkbox-danger input[type="checkbox"]:checked + label::before,
.checkbox-danger input[type="radio"]:checked + label::before {
  background-color: #c9302c;
  border-color: #c9302c; }

.checkbox-danger input[type="checkbox"]:checked + label::after,
.checkbox-danger input[type="radio"]:checked + label::after {
  color: #fff; }

.has-error input[type="checkbox"] + label::before,
.has-error input[type="radio"] + label::before {
  border-color: #c9302c; }

/* Radio inputs */
.radio {
  outline: none; }

.radio-inline {
  display: inline-block;
  margin-right: 10px; }

.radio label {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.875rem;
  line-height: 18px;
  position: relative;
  padding-left: 8px; }

.radio label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 17px;
  height: 17px;
  left: 0;
  margin-left: -20px;
  border: 2px solid #e6e6e6;
  border-radius: 50%;
  background-color: #FFF;
  -webkit-transition: border 0.15s ease-in-out;
  -o-transition: border 0.15s ease-in-out;
  transition: border 0.15s ease-in-out; }

.radio label::after {
  display: inline-block;
  position: absolute;
  content: " ";
  width: 11px;
  height: 11px;
  left: 3px;
  top: 3px;
  margin-left: -20px;
  border-radius: 50%;
  background-color: #333;
  -webkit-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  transform: scale(0, 0);
  -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33); }

.radio input[type="radio"] {
  opacity: 0;
  z-index: 1; }

.radio input[type="radio"]:focus + label::before {
  outline: none; }

.radio input[type="radio"]:checked + label::after {
  -webkit-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  transform: scale(1, 1); }

.radio input[type="radio"]:disabled + label {
  opacity: 0.65; }

.radio input[type="radio"]:disabled + label::before {
  cursor: not-allowed; }

.radio.radio-inline {
  margin-top: 0; }

.radio-primary input[type="radio"] + label::after {
  background-color: #0087be; }

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #0087be; }

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #0087be; }

.radio-danger input[type="radio"] + label::after {
  background-color: #d9534f; }

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #d9534f; }

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #d9534f; }

.radio-info input[type="radio"] + label::after {
  background-color: #5bc0de; }

.radio-info input[type="radio"]:checked + label::before {
  border-color: #5bc0de; }

.radio-info input[type="radio"]:checked + label::after {
  background-color: #5bc0de; }

.radio-warning input[type="radio"] + label::after {
  background-color: #f0ad4e; }

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #f0ad4e; }

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #f0ad4e; }

.radio-success input[type="radio"] + label::after {
  background-color: #5cb85c; }

.radio-success input[type="radio"]:checked + label::before {
  border-color: #5cb85c; }

.radio-success input[type="radio"]:checked + label::after {
  background-color: #5cb85c; }

.product-size::after,
.product-size::before {
  content: '';
  display: table; }

.product-size::after {
  clear: both; }

.product-size input {
  left: -9999px;
  position: absolute; }

.product-size label {
  width: 36px;
  height: 36px;
  float: left;
  padding: 6px 0;
  margin-right: 4px;
  display: block;
  color: #818a91;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  background: transparent;
  text-transform: uppercase;
  border: 1px solid #e6e6e6; }

.product-size label {
  -ms-transition: color 0.3s;
  -moz-transition: color 0.3s;
  -webkit-transition: color 0.3s; }

.product-size label > img {
  max-width: 100%; }

.product-size label:hover {
  color: #0087be;
  cursor: pointer;
  border-color: #0087be; }

.product-size input:checked ~ label {
  color: #0087be;
  border-color: #0087be;
  font-weight: 600; }

.product-size--style-1 label {
  width: auto;
  padding-left: 10px;
  padding-right: 10px; }

/* CUSTOM PRODUCT COLOR INPUT */
.product-color::after,
.product-color::before {
  content: '';
  display: table; }

.product-color::after {
  clear: both; }

.product-color input {
  left: -9999px;
  position: absolute; }

.product-color label {
  width: 36px;
  height: 36px;
  float: left;
  padding: 5px;
  margin-right: 4px;
  display: block;
  font-size: 14px;
  text-align: center;
  opacity: 0.7;
  border: 1px solid transparent; }

.product-color label {
  -ms-transition: color 0.3s;
  -moz-transition: color 0.3s;
  -webkit-transition: color 0.3s; }

.product-color label:hover {
  cursor: pointer;
  opacity: 1; }

.product-color input:checked ~ label {
  border-color: #0087be;
  opacity: 1; }

.product-color-circle label {
  border-radius: 100%; }

.product-color-circle input:checked ~ label {
  height: 36px; }

.form-card--style-1 {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.05); }

.form-card--style-1 .form-header {
  padding: 1.2rem;
  background-color: #f1f1f1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.form-card--style-1 .form-header .heading {
  margin: 0;
  padding: 0;
  color: #111111; }

.form-card--style-1 .form-body {
  padding: 2rem; }

.form-card--style-1 .form-footer {
  padding: 1rem 2rem;
  background-color: #f1f1f1; }

.form-card--style-2 .form-header {
  position: relative;
  padding: 2rem 0;
  background-color: #0087be; }

.form-card--style-2 .form-header .form-header-title {
  margin: 0;
  padding: 0;
  color: #FFF; }

.form-card--style-2 .form-header:after {
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-width: 14px;
  top: 100%;
  left: 50%;
  border-top-color: #0087be;
  margin-left: -14px; }

.form-card--style-2 .form-header .form-header-icon i {
  font-size: 3rem;
  color: #FFF; }

.form-card--style-2 .form-body {
  padding: 2rem;
  background-color: #fff; }

.form-card--style-2 .form-footer {
  padding: 1rem 0;
  background-color: #fff; }

.form-user-footer-links a {
  font-size: 0.75rem;
  color: #818a91; }

.help-block.with-errors > ul > li {
  font-size: 0.875rem;
  color: #ff3b30; }

.has-error .form-control:focus {
  box-shadow: none;
  -webkit-box-shadow: none;
  border-color: #ff3b30; }

.has-success .form-control:focus {
  box-shadow: none;
  -webkit-box-shadow: none;
  border-color: #4cd964; }

.has-feedback .glyphicon {
  font-family: "Ionicons" !important;
  font-size: 12px; }

.has-feedback .glyphicon-remove::before {
  content: "\f129";
  color: #ff3b30; }

.has-feedback .glyphicon-ok::before {
  content: "\f121";
  color: #4cd964; }

.form-subscribe-lg .form-control {
  border: 0;
  background: transparent;
  padding: 0.5rem 1rem;
  font-size: 2.5rem;
  font-weight: 300;
  color: black;
  border-left: 5px solid rgba(0, 0, 0, 0.3);
  border-radius: 0; }

.form-subscribe-lg .form-control:focus {
  background: transparent; }

.form-subscribe-lg.form-inverse .form-control {
  border-left: 5px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9); }

.form-subscribe-lg.form-inverse .form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.9); }

.form-subscribe-lg.form-inverse .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(255, 255, 255, 0.9); }

.form-subscribe-lg.form-inverse .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.9); }

.form-subscribe-lg.form-inverse .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.9); }

.icon-block {
  position: relative; }

.row .feature > .icon-block {
  margin-top: 0 !important; }

.feature--boxed-border {
  padding: 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
  position: relative; }

.feature--boxed-border.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 4px;
  background: #0087be;
  left: 0; }

.feature--boxed-border p {
  margin-bottom: 0; }

a > .feature--boxed-border p {
  color: #55595c; }

a > .feature--boxed-border:hover {
  border-color: #0087be; }

.feature--text-only .heading + p {
  max-width: 22.28571429em; }

.feature .feature-inner-spacer {
  max-width: 80%; }

.feature--bg-1 {
  background: #fff; }

.feature--bg-2 {
  background: #1b1e23; }

.feature--bg-2 *:not(.btn):not(.alert):not(.form-control):not(.heading):not(a),
.feature-inverse *:not(.btn):not(.alert):not(.form-control):not(.heading):not(a) {
  color: rgba(255, 255, 255, 0.5); }

.feature--bg-2 .heading,
.feature--bg-2 .heading > a,
.feature-inverse .heading,
.feature-inverse .heading > a {
  color: #f3f3f3; }

.feature--bg-3 {
  background: #fafafa; }

.feature-body {
  padding: 1rem; }

.feature-content .heading,
.feature-content p {
  margin-bottom: 0; }

.feature-headline {
  position: relative; }

.feature-headline:before,
.feature-headline:after {
  content: '';
  display: table; }

.feature-headline:after {
  clear: both; }

.feature-headline:not(.no-linethrough):before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(0, 0, 0, 0.2); }

.feature-headline > .headline-title {
  padding-right: 1rem;
  position: relative; }

.feature-headline > .headline-title + .headline-label {
  float: right;
  padding-left: 1rem;
  position: relative; }

.feature--bg-1 .feature-headline > .headline-title,
.feature--bg-1 .feature-headline > .headline-label {
  background: #fff; }

.feature--bg-2 .feature-headline > .headline-title,
.feature--bg-2 .feature-headline > .headline-label {
  background: #1b1e23; }

.feature--bg-3 .feature-headline > .headline-title,
.feature--bg-3 .feature-headline > .headline-label {
  background: #fafafa; }

.block-icon.circle {
  border-radius: 100%; }

.block-icon.rounded {
  border-radius: 0.25rem; }

.block-bordered-grid {
  border-right: 1px solid #e0eded;
  border-bottom: 1px solid #e0eded;
  padding: 40px;
  border-radius: 0; }

.block-bordered-grid--style-2 {
  border-top-style: dashed;
  border-right-style: dashed; }

@media (max-width: 991px) {
  .block-bordered-grid--style-2 {
    margin-bottom: 0 !important; } }
@media (min-width: 768px) and (max-width: 991px) {
  .block-bordered-grid.col-sm-6:nth-child(2n+2) {
    border-right: 0; }

  .block-bordered-grid.col-sm-4:nth-child(3n+3) {
    border-right: 0; }

  .block-bordered-grid.col-sm-3:nth-child(4n+4) {
    border-right: 0; } }
@media (min-width: 992px) {
  .block-bordered-grid[class*="col-"]:last-child {
    border-right: 0; }

  .block-bordered-grid.col-md-6:nth-child(2n+2) {
    border-right: 0; }

  .block-bordered-grid.col-md-4:nth-child(3n+3) {
    border-right: 0; }

  .block-bordered-grid.col-md-3:nth-child(4n+4) {
    border-right: 0; } }
.icon-block--style-1 {
  position: relative; }

[class^="icon-block--style-1"] {
  position: relative; }

.icon-block--style-1-v1 .block-icon {
  width: 50px;
  height: 50px;
  position: absolute;
  left: 0;
  top: 0;
  line-height: 1; }

.icon-block--style-1-v1 .block-icon > .icon-number,
.icon-block--style-1-v1 .block-icon i {
  color: #0087be; }

.icon-block--style-1-v1 .block-icon i,
.icon-block--style-1-v1 .block-icon .icon-number {
  font-size: 50px; }

.icon-block--style-1-v1 .block-icon.c-white i {
  color: white; }

.icon-block--style-1-v1 .block-content {
  padding-left: 70px; }

.icon-block--style-1-v1 .block-icon-sm {
  width: 30px;
  height: 30px;
  line-height: 1.3; }

.icon-block--style-1-v1 .block-icon-sm i {
  font-size: 30px; }

.icon-block--style-1-v1 .block-icon-sm + .block-content {
  padding-left: 50px; }

.icon-block--style-1-v1 .block-icon-right {
  left: auto;
  right: 0; }

.icon-block--style-1-v1 .block-icon-right + .block-content {
  padding-right: 70px;
  padding-left: 0;
  text-align: right; }

.icon-block--style-1-v2 .heading {
  margin-bottom: 10px; }

.icon-block--style-1-v2 .block-icon,
.icon-block--style-1-v3 .block-icon {
  width: 50px;
  height: 50px;
  position: absolute;
  left: 0; }

.icon-block--style-1-v2 .block-icon i,
.icon-block--style-1-v3 .block-icon i {
  font-size: 50px;
  line-height: 1; }

.icon-block--style-1-v2 .block-icon i {
  color: #2b2b2c; }

.icon-block--style-1-v2 .block-icon .icon-number {
  font-size: 50px;
  line-height: 1;
  display: block;
  text-align: right; }

.icon-block--style-1-v2 .block-icon .icon-number {
  color: #2b2b2c; }

.icon-block--style-1-v2 .block-content {
  padding-left: 70px; }

.icon-block--style-1-v2 .block-icon-lg,
.icon-block--style-1-v3 .block-icon-lg {
  width: 70px;
  height: 70px; }

.icon-block--style-1-v2 .block-icon-lg i,
.icon-block--style-1-v3 .block-icon-lg i {
  font-size: 70px; }

.icon-block--style-1-v2 .block-icon-lg + .block-content {
  padding-left: 80px; }

.icon-block--style-1-v2 .block-icon-sm {
  width: 30px;
  height: 30px; }

.icon-block--style-1-v2 .block-icon-sm i {
  font-size: 30px; }

.icon-block--style-1-v2 .block-icon-sm + .block-content {
  padding-left: 50px; }

.icon-block--style-1-v2 .block-icon-right {
  left: auto;
  right: 0;
  text-align: left; }

.icon-block--style-1-v2 .block-icon-right.block-icon-lg + .block-content {
  padding-left: 0;
  padding-right: 80px; }

.icon-block--style-1-v2 .block-icon-right + .block-content {
  padding-left: 0;
  padding-right: 70px;
  text-align: right; }

.icon-block--style-1-v2 .block-icon-right.block-icon-sm + .block-content {
  padding-left: 0;
  padding-right: 50px; }

.icon-block--style-1-v3 .block-icon,
.icon-block--style-1-v4 .block-icon {
  width: 60px;
  height: 60px;
  text-align: center;
  border-radius: 100%;
  position: absolute;
  left: 0;
  top: 5px; }

.icon-block--style-1-v3 .block-icon {
  border: 1px solid #0087be; }

.icon-block--style-1-v4 .block-icon {
  border: 1px solid #2b2b2c; }

.icon-block--style-1-v3-dashed .block-icon,
.icon-block--style-1-v4-dashed .block-icon {
  border-style: dashed; }

.icon-block--style-1-v3 .block-icon i,
.icon-block--style-1-v4 .block-icon i {
  font-size: 28px;
  line-height: 60px;
  font-weight: normal; }

.icon-block--style-1-v3 .block-icon i {
  color: #0087be; }

.icon-block--style-1-v4 .block-icon i {
  color: #2b2b2c; }

.icon-block--style-1-v3 .block-content,
.icon-block--style-1-v4 .block-content {
  padding-left: 85px; }

.icon-block--style-1.v3 .block-icon i {
  font-size: 15px;
  color: #0087be; }

.icon-block--style-1.v4 .block-icon i {
  font-size: 15px;
  color: #2b2b2c; }

.icon-block--style-1.v3 .block-content,
.icon-block--style-1.v4 .block-content {
  padding-left: 30px; }

.icon-block--style-1-v3.block-icon-right .block-icon,
.icon-block--style-1-v4.block-icon-right .block-icon {
  left: auto;
  right: 0; }

.icon-block--style-1-v3.block-icon-right .block-content,
.icon-block--style-1-v4.block-icon-right .block-content {
  padding-left: 0;
  padding-right: 85px;
  text-align: right; }

.icon-block--style-1-v5 {
  position: relative; }

.icon-block--style-1-v5 .block-icon {
  display: block;
  margin-bottom: 1rem; }

.icon-block--style-1-v5 .block-icon > i {
  font-size: 40px; }

.icon-block--style-1-v5 .block-icon-lg > i {
  font-size: 60px; }

.icon-block--style-1-v5 .block-icon {
  color: #0087be; }

.icon-block--style-1-v5 .block-content p {
  margin-top: 10px; }

.icon-block--style-1-v5.block-bordered-grid-animated {
  padding: 3rem 2rem; }

.icon-block--style-1-v5.block-bordered-grid-animated .block-inner {
  display: block;
  color: #818a91; }

.icon-block--style-1-v5.block-bordered-grid-animated .block-inner::after {
  content: "";
  display: block;
  position: absolute;
  width: 90%;
  height: 90%;
  left: 5%;
  top: 5%;
  background: transparent;
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;
  transform: scale(0.1); }

.icon-block--style-1-v5.block-bordered-grid-animated:hover .block-inner::after {
  border: 2px solid #0087be;
  transform: scale(1); }

.block.style-5 .block-icon.block-icon-lg i {
  font-size: 60px; }

.icon-block--style-1-v3.icon-block-inverse .block-icon {
  border-color: white; }

.icon-block--style-1-v1.icon-block-inverse .block-icon i,
.icon-block--style-1-v2.icon-block-inverse .block-icon i,
.icon-block--style-1-v3.icon-block-inverse .block-icon i,
.icon-block--style-1-v4.icon-block-inverse .block-icon i,
.icon-block--style-1-v5.icon-block-inverse .block-icon i {
  color: white; }

.icon-block--style-2-v1:hover .block-icon,
.icon-block--style-2-v2:hover .block-icon {
  transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out; }

.icon-block--style-2-v1 .heading,
.icon-block--style-2-v2 .heading {
  margin-bottom: 0.5rem !important; }

.icon-block--style-2-v1 .block-icon {
  margin: auto;
  width: 64px;
  height: 64px;
  line-height: 64px;
  text-align: center;
  font-size: 32px;
  margin-bottom: 2rem; }

.icon-block--style-2-v2 .block-icon {
  margin-bottom: 2rem; }

.icon-block--style-2-v2 .block-icon {
  margin: auto auto 2rem;
  width: 76px;
  height: 76px;
  line-height: 76px;
  text-align: center;
  font-size: 32px;
  border: 2px solid #818a91;
  color: #55595c; }

.icon-block--style-2-v2.active .block-icon,
.icon-block--style-2-v2:hover .block-icon {
  border-color: #0087be;
  background: #0087be;
  color: #FFF; }

.icon-block--style-3 {
  position: relative; }

.icon-block--style-3 > i {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  margin-right: 10px;
  display: inline-block;
  border-radius: 100%;
  font-size: 20px; }

.icon-block--style-3 > .icon-number {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  margin-right: 10px;
  display: inline-block;
  border-radius: 100%;
  font-size: 17px; }

.icon-block--style-3 .icon-label {
  display: block;
  position: absolute;
  width: 40px;
  left: 0;
  top: 45px;
  font-size: 13px;
  text-align: center; }

.icon-block--style-3 .icon-block-text {
  line-height: 40px;
  display: inline-block;
  font-size: 20px;
  font-family: "Lato", sans-serif; }

.icon-block--style-3 .icon-block-subtext {
  position: absolute;
  top: 34px;
  left: 54px; }

.icon-block--style-3-v1 > i {
  border: 1px solid #eceeef;
  background: transparent;
  color: #0087be; }

.icon-block--style-3-v2 > .icon-number,
.icon-block--style-3-v2 > i {
  position: absolute; }

.icon-block--style-3-v2 .icon-block-content {
  padding-left: 65px; }

.icon-block--style-4 {
  overflow: visible;
  position: relative;
  margin-top: 42px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-top: 0;
  border-radius: 0.25rem; }

.icon-block--style-4::before,
.icon-block--style-4::after {
  content: "";
  width: calc(50% - 52px);
  position: absolute;
  top: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05); }

.icon-block--style-4::before {
  left: 0;
  border-top-left-radius: 0.25rem; }

.icon-block--style-4::after {
  right: 0;
  border-top-right-radius: 0.25rem; }

.icon-block--style-4 .block-icon {
  width: 84px;
  height: 84px;
  text-align: center;
  line-height: 84px;
  position: absolute;
  top: -42px;
  left: 50%;
  margin-left: -42px;
  border-radius: 100%;
  background: #0087be;
  color: #FFF; }

.icon-block--style-4:hover .block-icon {
  -webkit-animation-name: rotate-icon;
  -webkit-animation-duration: 0.3s;
  -webkit-animation-iteration-count: 1;
  -webkit-animation-timing-function: ease-in-out; }

.icon-block--style-4 .block-icon::after {
  content: "";
  width: 84px;
  height: 84px;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -42px;
  border-radius: 100%;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.icon-block--style-4:hover .block-icon::after {
  width: 98px;
  height: 98px;
  top: -7px;
  left: 50%;
  margin-left: -49px;
  border: 3px solid #0087be; }

.icon-block--style-4 .block-icon i {
  font-size: 40px; }

.icon-block--style-4 .block-body {
  padding-top: 80px;
  padding-left: 2rem;
  padding-right: 2rem; }

@media (max-width: 991px) {
  .icon-block--style-4 {
    margin-top: 80px; } }
.icon-block--arrow {
  border: 0; }

.icon-block--arrow .block-icon {
  width: 120px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  text-align: center;
  border-radius: 0.25rem; }

.icon-block--arrow .block-icon i {
  font-size: 40px; }

.icon-block--arrow .block-icon:after {
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-top-color: #0087be;
  border-width: 12px;
  margin-left: -12px; }

.icon-block--arrow .block-body {
  margin-top: 1.7rem;
  padding: 0 2rem; }

@-webkit-keyframes rotate-icon {
  0% {
    -webkit-transform: scale(1) rotate(0deg); }
  10% {
    -webkit-transform: scale(1) rotate(0deg); }
  100% {
    -webkit-transform: scale(1) rotate(360deg); } }
.list-group {
  border-left: 0;
  border-right: 0; }

.list-group-item {
  padding: 1.5rem;
  border: 0;
  border-bottom: 1px solid rgba(243, 243, 243, 0.7);
  color: #818a91;
  font-size: 0.875rem;
  margin-bottom: 0; }

.list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-top: 1px solid rgba(243, 243, 243, 0.7); }

.list-group-item:last-child {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
  background-color: #eceeef;
  color: #2b2b2c;
  border-color: #eceeef; }

.list-group-item .label {
  margin-left: 1rem; }

ul.icons {
  margin: 0;
  padding: 0; }

ul.icons li {
  list-style: none;
  margin: 5px 0 0;
  vertical-align: top;
  font-size: 0.875rem; }

ul.icons li > a:not(.icon) {
  color: #818a91; }

ul.icons li > a:hover {
  color: #0087be; }

ul.icons li .icon {
  margin-right: 12px;
  position: relative; }

ul.inline-links {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-block; }

ul.inline-links > li {
  display: inline-block;
  padding: 0 .4em 0 .4em;
  position: relative;
  font-size: 0.75rem; }

ul.inline-links > li:first-child {
  padding-left: 0; }

ul.inline-links > li:last-child {
  padding-right: 0; }

ul.inline-links > li,
ul.inline-links > li > a {
  color: #818a91; }

ul.inline-links > li > a.active {
  color: #2b2b2c;
  font-weight: 500; }

ul.inline-links > li > a:hover {
  color: #0087be;
  text-decoration: none; }

ul.inline-links > li > a:hover > heading {
  color: #0087be; }

ul.inline-links > li > a > i,
ul.inline-links > li > i {
  margin-right: 4px; }

ul.inline-links.inline-links--style-2 > li,
ul.inline-links.inline-links--style-2 > li > a {
  font-size: 13px; }

ul.inline-links.inline-links--style-2 > li:not(:last-child) {
  padding-right: 12px; }

ul.inline-links.inline-links--style-2 > li:before {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  content: "\f111";
  font-family: 'FontAwesome';
  font-size: 4px;
  color: #0087be; }

ul.inline-links.inline-links--style-2 > li:last-child:before {
  content: ""; }

ul.social-icons {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%; }

ul.social-icons li {
  display: inline-block;
  font-size: 0.875rem; }

ul.social-icons li a {
  display: block;
  height: 32px;
  width: 32px;
  text-align: center;
  line-height: 32px; }

ul.social-icons li.text {
  height: 32px;
  padding-left: 10px;
  line-height: 32px; }

ul.social-icons li.facebook:hover {
  background: #3b5998;
  color: #fff; }

ul.social-icons li.facebook:hover > a {
  color: #fff; }

ul.social-icons li.twitter:hover {
  background: #1da1f2;
  color: #fff; }

ul.social-icons li.twitter:hover > a {
  color: #fff; }

ul.social-icons li.linkedin:hover {
  background: #0077b5;
  color: #fff; }

ul.social-icons li.linkedin:hover > a {
  color: #fff; }

.list-social-buttons {
  margin: 0;
  padding: 0;
  list-style: none;
  display: table;
  width: 100%; }

.list-social-buttons > li {
  border-right: 1px solid #e0eded;
  display: table-cell; }

.list-social-buttons > li > a {
  padding: 2rem;
  font-size: 20px;
  display: block;
  text-align: center;
  color: #2b2b2c; }

.list-social-buttons > li:last-child {
  border-right: 0; }

ul.categories {
  padding: 0;
  margin: 0;
  list-style: none; }

ul.categories > li {
  border-bottom: 1px solid #e0eded; }

ul.categories > li:last-child {
  border: 0; }

ul.categories > li > a {
  display: block;
  padding: 12px 0;
  color: #818a91;
  font-size: 0.875rem;
  font-family: "Lato", sans-serif; }

ul.categories > li > a:after,
ul.categories > li > a:before {
  content: "";
  display: table; }

ul.categories > li > a:after {
  clear: both; }

ul.categories > li:hover > a {
  color: #0087be;
  text-decoration: none; }

ul.categories--style-1 > li > a {
  padding-left: 20px;
  position: relative;
  display: flex;
  align-items: center; }

ul.categories--style-1 > li > a > .category-name {
  color: #2b2b2c;
  flex-grow: 1; }

ul.categories--style-1 > li > a:hover > .category-name {
  color: #0087be; }

ul.categories--style-1 > li > a > .category-count {
  color: #818a91;
  font-size: 0.75rem; }

ul.categories--style-1 > li > a:before {
  content: "\f125";
  font-family: "Ionicons";
  position: absolute;
  height: 16px;
  line-height: 16px;
  left: 0;
  top: 50%;
  margin-top: -8px;
  font-size: 10px;
  color: #818a91;
  -webkit-transition: all 0.1s linear;
  transition: all 0.1s linear; }

ul.categories--style-2 > li {
  border: 0; }

ul.categories--style-2 > li > a {
  padding: 5px 0; }

.list-wrapper {
  background: #0087be;
  padding: 1.5rem;
  border-radius: 0.25rem; }

.list-wrapper > .heading {
  color: #FFF;
  margin-bottom: 1rem; }

.list-wrapper ul.categories--style-1 > li {
  border-color: rgba(255, 255, 255, 0.3);
  color: #FFF; }

.list-wrapper ul.categories--style-1 > li > a,
.list-wrapper ul.categories--style-1 > li > a > .category-count,
.list-wrapper ul.categories--style-1 > li > a > .category-name {
  color: #FFF; }

.list-wrapper ul.categories--style-1 > li > a:hover,
.list-wrapper ul.categories--style-1 > li > a:hover > .category-name {
  color: #FFF; }

.list-wrapper ul.categories--style-1 > li > a:before {
  color: #FFF; }

ul.categories--style-1 > li > a:hover:before {
  left: 5px; }

ul.meta-info-cells {
  margin: 0;
  padding: 0; }

ul.meta-info-cells-v2 li,
ul.meta-info-cells-v3 li {
  display: inline-block;
  float: left;
  padding: 0 10px;
  vertical-align: middle;
  margin-right: 1rem; }

ul.meta-info-cells-v2 li a,
ul.meta-info-cells-v3 li a {
  color: #818a91; }

ul.meta-info-cells-v2 li {
  padding: 0;
  color: #2b2b2c;
  font-size: 13px; }

ul.meta-info-cells-v2 li i {
  color: #2b2b2c;
  font-size: 13px;
  margin-right: 5px; }

ul.meta-info-cells-v3 li i {
  width: 31px;
  height: 31px;
  margin-right: 5px;
  text-align: center;
  line-height: 31px;
  background: transparent;
  border: 1px solid #e0eded;
  color: #2b2b2c;
  font-size: 13px;
  border-radius: 100%; }

ul.meta-info-cells-v3 li:hover i {
  background: #0087be;
  color: #FFF;
  border-color: #0087be; }

ul.meta-info-cells-v3 li:first-child {
  padding-left: 0; }

ul.meta-info-cells-v3 li:last-child {
  border: 0;
  padding-right: 0; }

ul.meta-info-cells-v3 li .btn-link {
  color: #0087be;
  font-weight: 600;
  font-size: 0.875rem; }

ul.meta-info-cells-v4 > li,
ul.meta-info-cells-v5 > li {
  display: inline-block;
  margin-right: 18px;
  font-size: 0.875rem;
  color: #818a91;
  font-weight: 500;
  font-family: "Lato", sans-serif; }

ul.meta-info-cells-v5 > li {
  margin: 0; }

ul.meta-info-cells-v5 > li > span {
  display: block;
  padding-right: 10px;
  margin-right: 7px;
  border-right: 1px solid #e0eded; }

ul.meta-info-cells-v5 > li:last-child > span {
  border: 0; }

ul.meta-info-cells-v4 > li > a > i,
ul.meta-info-cells-v5 > li > a > i {
  margin-right: 5px;
  color: #818a91; }

ul.meta-info-cells-v4 > li > a,
ul.meta-info-cells-v5 > li > a {
  color: #818a91; }

ul.meta-info-cells-v4 > li > a:hover,
ul.meta-info-cells-v4 > li > a:hover > i,
ul.meta-info-cells-v5 > li > a:hover,
ul.meta-info-cells-v5 > li > a:hover > i {
  color: #0087be; }

ul.meta-info-cells-v4 > li:first-child,
ul.meta-info-cells-v5 > li:first-child {
  padding-left: 0; }

ul.meta-info-cells-v4 > li:last-child,
ul.meta-info-cells-v5 > li:last-child {
  border: 0;
  padding-right: 0; }

ul.meta-info-cells-v4 li .btn-link {
  color: #0087be;
  font-weight: 600;
  font-size: 0.875rem; }

@media (max-width: 771px) {
  ul.meta-info-cells-v4 li,
  ul.meta-info-cells-v5 li {
    margin-bottom: 5px; } }
.sidebar .list-unstyled {
  font-size: 0.875rem; }

.sidebar .list-unstyled > li > a {
  color: #2b2b2c;
  padding: 0.2rem 0;
  display: block; }

.sidebar .list-unstyled > li:hover > a {
  color: #0087be; }

.vertical-info {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative; }

.vertical-info > li {
  padding: 0.25rem 0; }

.vertical-info > li > span {
  display: inline-block;
  font-size: 0.875rem; }

.vertical-info > li > .vi-title {
  font-weight: 600;
  margin-right: 0.5rem; }

.vertical-info > li > .vi-content {
  color: #818a91; }

ul.list-bullet {
  list-style: none;
  margin: 0;
  padding: 0;
  background: transparent; }

ul.list-bullet li {
  clear: left;
  padding: 10px 0;
  display: block;
  width: 100%; }

ul.list-bullet li > figure {
  margin: 0;
  padding: 0;
  border-radius: 100%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 1rem;
  color: #eceeef;
  font-weight: 600;
  display: inline-block;
  float: left; }

ul.list-bullet li img {
  width: 60px; }

ul.list-bullet li h3 {
  font-size: 16px;
  font-weight: 600;
  margin-left: 15px;
  display: inline-block; }

ul.list-bullet li p {
  margin: 0 0 0 50px;
  padding: 0; }

ul.list-bullet li span {
  margin-left: 6px; }

ul.list-bullet li a {
  font-weight: 500; }

ul.list-bullet li a:hover {
  text-decoration: none;
  color: #464646; }

ul.list-bullet li span {
  font-size: 12px; }

.list-recent-stories {
  background: #e6edf2; }

.list-recent .widget-title {
  padding: 26px 24px 28px; }

.list-recent {
  list-style: none;
  margin: 0;
  padding: 0; }

.list-recent li {
  padding: 10px 0;
  border-bottom: 1px solid rgba(243, 243, 243, 0.7); }

.list-recent li:first-child {
  padding-top: 0;
  border-top: 0; }

.list-recent.list-recent-boxed li {
  padding: 10px 15px; }

.list-recent li:last-child {
  border: 0; }

.list-recent-stories li:nth-child(odd) {
  background: #c6d3dd; }

.list-recent-comments li:nth-child(odd) {
  background: #eceeef; }

.list-recent .inline-links {
  display: block; }

.list-recent .post-thumb {
  float: left;
  width: 72px;
  height: 72px;
  margin-right: 16px; }

.list-recent-stories .post-thumb,
.list-recent-stories .post-thumb img {
  border-radius: 50%; }

.list-recent-comments li:nth-child(even) .post-thumb {
  float: right;
  margin-right: 0;
  margin-left: 16px; }

.list-recent .post-thumb img {
  display: block;
  width: 100%;
  height: 100% !important; }

.list-recent .post-author,
.list-recent .post-title {
  display: block;
  margin-bottom: 0; }

.list-recent .post-author {
  font-size: 12px;
  color: #0087be; }

.list-recent .post-author a,
.list-recent .post-title {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 19px;
  color: #2b2b2c; }

.list-recent .post-author a:hover {
  color: #0087be; }

.list-recent .post-title a {
  color: #2b2b2c; }

.list-recent .post-title a:hover {
  color: #0087be; }

.list-recent .post-title .label {
  margin-top: 5px; }

.list-recent .post-title > a + .inline-links,
.list-recent .post-title > a + .star-rating {
  margin-top: 5px; }

.list-recent .post-entry,
.list-recent .post-desc {
  display: block;
  font-size: 12px;
  color: #818a91;
  line-height: 17px; }

.list-recent .post-meta-bot {
  padding: 21px 0;
  text-align: center; }

@media (max-width: 479px), (min-width: 768px) and (max-width: 991px) {
  .list-recent li {
    padding: 8px; }

  .list-recent .post-thumb {
    margin-right: 5px;
    margin-top: 3px; }

  .list-recent-comments li:nth-child(odd) .post-thumb {
    margin-left: 5px; } }
ul.list-border--dotted > li {
  border-bottom-style: dotted; }

.list-icon {
  display: inline-block;
  margin-right: 10px; }

.list-icon--style-1 {
  width: 36px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border: 2px solid #e0eded;
  border-radius: 100%;
  color: #818a91; }

.list-icon--style-1 i {
  color: #0087be; }

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out; }

.mask-base-1--style-1 {
  background: rgba(222, 27, 27, 0.9); }

.mask-base-1--style-2 {
  background: rgba(222, 27, 27, 0.5); }

.mask-base-1--style-3 {
  background: rgba(222, 27, 27, 0.7); }

.mask-base-2--style-1 {
  background: rgba(41, 47, 54, 0.9); }

.mask-base-2--style-2 {
  background: rgba(41, 47, 54, 0.5); }

.mask-base-2--style-3 {
  background: rgba(41, 47, 54, 0.7); }

.mask-gradient-1--style-1 {
  opacity: 0.9; }

.mask-gradient-1--style-2 {
  opacity: 0.5; }

.mask-light--style-1 {
  background: rgba(255, 255, 255, 0.9); }

.mask-light--style-2 {
  background: rgba(255, 255, 255, 0.4); }

.mask-light ~ .mask-container .heading {
  color: #333 !important; }

.mask-light ~ .mask-container p {
  color: #555 !important; }

.mask-dark--style-1 {
  background: rgba(0, 0, 0, 0.9); }

.mask-dark--style-2 {
  background: rgba(0, 0, 0, 0.4); }

.mask-dark ~ .mask-container .heading {
  color: #FFF; }

.mask-dark ~ .mask-container p {
  color: #d3d3d3; }

.mask-pattern-1--style-1 {
  background: url("@{cssRelativePath-1}/images/patterns/cream-pixels.png");
  opacity: 0.5; }

.modal-header {
  padding: 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.modal-header > .heading {
  margin-bottom: 0; }

.modal-body {
  position: relative;
  padding: 2rem; }

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.19); }

.modal-backdrop.in {
  opacity: 0.3; }

.modal[data-modal-color],
.modal[data-modal-color] .modal-footer .btn-link,
.modal[data-modal-color] .modal-title {
  color: #fff; }

.modal[data-modal-color=base-1] .modal-content {
  background-color: #0087be; }

.modal[data-modal-color=base-2] .modal-content {
  background-color: #292f36; }

.modal[data-modal-color=base-3] .modal-content {
  background-color: #818a91; }

.modal[data-modal-color=base-4] .modal-content {
  background-color: #2B2B2B; }

.modal[data-modal-color=base-5] .modal-content {
  background-color: #FFF; }

.modal[data-modal-color=dark] .modal-content {
  background-color: #2b2b2c; }

/* NAV PILLS */
.nav-pills {
  border: 1px solid transparent;
  border-radius: 0.25rem; }

.nav-pills .nav-link {
  border-radius: 0;
  border-right: 1px solid #ddd;
  background-color: #eee; }

.nav-pills .nav-item:first-child .nav-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem; }

.nav-pills .nav-item:last-child .nav-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.nav-pills .nav-link:focus,
.nav-pills .nav-link:hover {
  background: #0087be;
  color: #2b2b2c; }

.nav-pills .nav-item + .nav-item {
  margin-left: 2px; }

.nav-pills .nav-link.active,
.nav-pills .nav-link.active:focus,
.nav-pills .nav-link.active:hover {
  color: #2b2b2c;
  background: #0087be; }

.nav-pills--style-2 {
  margin-bottom: 15px;
  border: 1px solid transparent;
  border-radius: 0.25rem; }

.nav-pills--style-2 .nav-link {
  padding: 26px;
  border-radius: 0;
  border-right: 0;
  background-color: #eee;
  text-transform: uppercase; }

.nav-pills--style-2 .nav-item:first-child .nav-link {
  border-top-left-radius: 0.25rem; }

.nav-pills--style-2 .nav-item:last-child .nav-link {
  border-top-right-radius: 0.25rem;
  border-right: 0; }

.nav-pills--style-2 .nav-link:focus,
.nav-pills--style-2 .nav-link:hover {
  background: #f5f5f5; }

.nav-pills--style-2 .nav-item + .nav-item {
  margin-left: 2px; }

.nav-pills--style-2 .nav-link.active,
.nav-pills--style-2 .nav-link.active:focus,
.nav-pills--style-2 .nav-link.active:hover {
  background: transparent;
  color: inherit; }

.nav-pills--style-3 {
  border: 0; }

.nav-pills--style-3 .nav-link:after,
.nav-pills--style-3 .nav-link:before {
  display: table;
  content: ""; }

.nav-pills--style-3 .nav-link:after {
  clear: both; }

.nav-pills--style-3 .nav-link {
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-right: 20px;
  background: #fff;
  color: rgba(0, 0, 0, 0.5);
  text-align: left;
  transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out;
  border-radius: 0.25rem; }

.nav-pills--style-3 .nav-item:last-child .nav-link {
  margin: 0; }

.nav-pills--style-3 .nav-link:focus,
.nav-pills--style-3 .nav-link:hover {
  background: #0087be;
  color: #FFF; }

.nav-pills--style-3 .nav-link.active,
.nav-pills--style-3 .nav-link.active:focus,
.nav-pills--style-3 .nav-link.active:hover {
  background: #fff; }

.nav-pills--style-3 .nav-pill-text {
  display: table-cell;
  padding: 1rem;
  vertical-align: middle; }

.nav-pills--style-3 .nav-pill-text p {
  margin: 0; }

.nav-pills--style-3 .nav-pill-text .heading {
  display: block;
  margin: 0;
  padding: 0; }

.nav-pills--style-3 .nav-pill-text .nav-pill-subtitle {
  display: block;
  margin-top: 5px;
  color: #818a91;
  font-size: 0.875rem; }

.nav-pills--style-3 .nav-pill-text .nav-pill-aux-text {
  display: block;
  margin-top: 20px;
  color: #818a91;
  font-size: 0.875rem; }

.nav-pills--style-3 .nav-pill-text .nav-pill-aux-text strong {
  color: #2b2b2c; }

.nav-pills--style-3 .nav-pill-icon {
  width: 60px;
  display: table-cell;
  vertical-align: middle; }

.nav-pills--style-3 .nav-pill-icon i {
  font-size: 50px;
  color: #2b2b2c; }

.nav-pills--style-3 .nav-link:focus .nav-pill-icon i,
.nav-pills--style-3 .nav-link:hover .nav-pill-icon i {
  color: #FFF; }

.nav-pills--style-3 .nav-link.active .nav-pill-icon i {
  color: #2b2b2c; }

.nav-pills--style-3 .nav-link:hover * {
  color: #FFF; }

@media (max-width: 767px) {
  .nav-pills--style-3 .nav-link {
    margin-right: 0; } }
.nav-pills--style-3.nav-pills-inverse .nav-link {
  border: 1px solid rgba(0, 0, 0, 0.15);
  background: #1b1e23;
  color: #818a91; }

.nav-pills--style-3.nav-pills-inverse .nav-link:focus,
.nav-pills--style-3.nav-pills-inverse .nav-link:hover {
  background: #0087be;
  color: #FFF; }

.nav-pills--style-3.nav-pills-inverse .nav-link.active,
.nav-pills--style-3.nav-pills-inverse .nav-link.active:focus,
.nav-pills--style-3.nav-pills-inverse .nav-link.active:hover {
  background: #0087be;
  color: #FFF; }

.pagination-wrapper {
  margin: 1rem 0;
  font-size: 0; }

.pagination {
  margin: 0;
  border-radius: 0; }

.pagination .page-link,
.pagination .page-item > span {
  padding: 8px 14px;
  margin: 0 3px;
  font-family: "Lato", sans-serif;
  font-size: 0.7rem;
  color: #818a91;
  background-color: transparent;
  border: 1px solid #eceeef;
  border-radius: 0.1rem;
  text-align: center !important; }

.pagination .page-item:first-child .page-link,
.pagination .page-item:first-child > span {
  margin-left: 0;
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem; }

.pagination .page-item:last-child .page-link,
.pagination .page-item:last-child > span {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem; }

.pagination .page-item .page-link:focus,
.pagination .page-item .page-link:hover,
.pagination .page-item > span:focus,
.pagination .page-item > span:hover {
  color: #2b2b2c;
  background-color: #eceeef;
  border-color: #eceeef; }

.pagination > .active .page-link,
.pagination > .active .page-link:focus,
.pagination > .active .page-link:hover,
.pagination > .active > span,
.pagination > .active > span:focus,
.pagination > .active > span:hover {
  color: #FFF;
  background-color: #0087be;
  border-color: #0087be; }

.pagination > .disabled .page-link,
.pagination > .disabled .page-link:focus,
.pagination > .disabled .page-link:hover,
.pagination > .disabled > span,
.pagination > .disabled > span:focus,
.pagination > .disabled > span:hover {
  color: #818a91;
  background-color: #eceeef;
  border-color: #eceeef; }

.pagination-lg .page-item .page-link,
.pagination-lg .page-item > span {
  padding: 12px 18px;
  margin: 0 3px;
  font-family: "Lato", sans-serif;
  font-size: 0.7rem;
  color: #818a91;
  background-color: transparent;
  border: 1px solid #eceeef; }

.pagination--style-2 .page-item .page-link,
.pagination--style-2 .page-item > span {
  background: #fff;
  color: #818a91; }

.pagination-circle .page-item .page-link,
.pagination-circle .page-item > span {
  border-radius: 50% !important;
  margin: 0 5px;
  display: block;
  width: 36px;
  height: 36px;
  padding: 5px;
  line-height: 2.2; }

.pagination-square .page-item .page-link,
.pagination-square .page-item > span {
  border-radius: 0 !important;
  margin: 0 5px;
  display: block;
  width: 36px;
  height: 36px;
  padding: 5px;
  line-height: 2.2; }

.pager {
  padding-left: 0;
  margin: 1.5rem 0;
  list-style: none;
  text-align: center; }

.pager .page-item {
  display: inline; }

.pager .page-item .page-link,
.pager .page-item > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: transparent;
  border: 1px solid #e0eded;
  border-radius: 15px; }

.pager .page-item .page-link:focus,
.pager .page-item .page-link:hover {
  text-decoration: none;
  background-color: #0087be; }

.pager .next .page-link,
.pager .next > span {
  float: right; }

.pager .previous .page-link,
.pager .previous > span {
  float: left; }

.pager .disabled .page-link,
.pager .disabled .page-link:focus,
.pager .disabled .page-link:hover,
.pager .disabled > span {
  color: #777;
  background-color: #fff;
  cursor: not-allowed; }

.pager .page-item .page-link,
.pager .page-item > span {
  background-color: transparent;
  border: 1px solid #eceeef;
  border-radius: 0.2rem;
  font-size: 0.875rem;
  color: #2b2b2c; }

.pager .page-item .page-link:focus,
.pager .page-item .page-link:hover {
  background-color: #eceeef;
  color: #2b2b2c; }

.pager--style-1 .page-item .page-link,
.pager--style-1 .page-item > span {
  background-color: transparent;
  border: 0;
  padding: 0;
  color: #2b2b2c;
  opacity: 0.6; }

.pager--style-1 .page-item:hover .page-link,
.pager--style-1 .page-item:hover > span {
  background-color: transparent;
  opacity: 1; }

.pager--style-1 .page-item.pager-count span {
  background: transparent;
  border: 0;
  padding: 0;
  font-style: italic; }

.pricing-plans:after,
.pricing-plans:before,
.pricing-table:after,
.pricing-table:before {
  display: table;
  content: " "; }

.pricing-plans:before,
.pricing-table:before {
  clear: both; }

.pricing-plans .plan-title {
  text-align: center;
  margin: 0;
  padding: 15px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #2b2b2c; }

.pricing-plans .price-tag {
  margin: 0;
  height: 90px;
  line-height: 90px;
  font-size: 50px;
  font-weight: 700;
  text-align: center; }

.pricing-plans .price-tag span {
  font-size: 28px;
  font-weight: 600; }

.pricing-plans .price-tag span.price-type {
  font-size: 20px;
  font-weight: 500; }

.pricing-plans ul {
  margin: 0;
  padding: 0 15px;
  list-style: none; }

.pricing-plans ul li {
  padding: 10px 0;
  border-bottom: 1px solid #e0eded;
  font-size: 0.875rem; }

.pricing-plans ul li i {
  margin-right: 8px; }

.pricing-plans .plan-info {
  margin: 0;
  padding: 15px;
  text-align: center; }

.pricing-plans .plan-select {
  padding: 15px;
  border-top: 1px solid #e0eded; }

.pricing-plans--style-1 .block-pricing {
  background: #fff; }

.pricing-plans--style-1 .block-pricing:hover {
  background: #0087be;
  color: #FFF; }

.pricing-plans--style-1 .block-pricing:hover * {
  color: #FFF; }

.pricing-plans--style-1 .block-pricing:hover .btn {
  color: #FFF !important;
  border-color: #FFF !important; }

.pricing-plans--style-1 .plan-title {
  text-align: center;
  margin: 0;
  padding-top: 36px;
  font-size: 16px;
  font-weight: 500;
  background: transparent;
  color: #2b2b2c;
  text-transform: normal; }

.pricing-plans--style-1 .price-tag {
  margin: 0;
  padding: 1rem;
  font-size: 4rem;
  font-weight: 400;
  height: auto;
  line-height: 1;
  text-align: center;
  color: #0087be; }

.pricing-plans--style-1 .price-tag span {
  font-size: 28px;
  font-weight: 600; }

.pricing-plans--style-1 .price-tag span.price-type {
  font-size: 20px;
  font-weight: 500; }

.pricing-plans--style-1 .price-tag-subtitle {
  font-size: 16px;
  font-weight: 300;
  text-transform: capitalize;
  color: #818a91;
  display: block;
  text-align: center; }

.pricing-plans--style-1 ul {
  margin-top: 40px;
  margin-bottom: 20px; }

.pricing-plans--style-1 ul li {
  padding: 10px 0;
  border-bottom: 0;
  text-align: center;
  color: #818a91; }

.pricing-plans--style-1 ul li.active {
  color: #2b2b2c; }

.pricing-plans--style-2 .plan-title {
  text-align: center;
  margin: 0;
  padding: 36px 0;
  font-weight: 500;
  color: #2b2b2c;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  text-transform: normal; }

.pricing-plans--style-2 .block-pricing.active .plan-title {
  background: #0087be;
  color: #FFF;
  border-bottom: 1px solid #0087be; }

.pricing-plans--style-2 .block-pricing.active--style-2 .plan-title {
  background: #2B2B2B;
  color: #FFF;
  border-bottom: 1px solid #2B2B2B; }

.pricing-plans--style-2 .price-tag {
  margin: 0.5rem 0 0 0;
  padding: 1rem 0 0;
  font-size: 3.75rem;
  font-weight: 500;
  height: auto;
  line-height: 1;
  text-align: center;
  color: #2b2b2c; }

.pricing-plans--style-2 .price-tag span {
  font-size: 28px;
  font-weight: 600; }

.pricing-plans--style-2 .price-tag span.price-type {
  font-size: 20px;
  font-weight: 500; }

.pricing-plans--style-2 .price-tag-subtitle {
  margin-top: 0.8rem;
  font-size: 1rem;
  font-weight: 300;
  text-transform: capitalize;
  color: #818a91;
  display: block;
  text-align: center; }

.pricing-plans--style-2 ul {
  margin-top: 1.5rem; }

.pricing-plans--style-2 ul li {
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
  color: #818a91;
  font-size: 0.875rem; }

.pricing-plans--style-2 ul li:last-child {
  border: 0; }

.pricing-plans--style-2 ul li.active {
  color: #2b2b2c;
  font-weight: 500; }

.pricing-plans--style-3 .block-pricing {
  border: 1px solid rgba(0, 0, 0, 0.05); }

.pricing-plans--style-3 .row-no-padding .block-pricing {
  border-radius: 0; }

.pricing-plans--style-3 .row-no-padding [class^="col-"]:not(:last-child) .block-pricing {
  border-right: 0; }

.pricing-plans--style-3 .block-pricing .plan-title-wrapper {
  padding: 1rem;
  text-align: center;
  background: #f7f7f9;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.pricing-plans--style-3 .block-pricing .plan-title-wrapper.active {
  background: #0087be;
  color: #FFF; }

.pricing-plans--style-3 .block-pricing .plan-title-wrapper.active * {
  color: #FFF !important; }

.pricing-plans--style-3 .block-pricing .plan-title-wrapper .price-tag {
  font-size: 4rem;
  font-weight: 400;
  color: #2b2b2c; }

.pricing-plans--style-1 .block-pricing .price-tag sup,
.pricing-plans--style-2 .block-pricing .price-tag sup,
.pricing-plans--style-3 .block-pricing .price-tag sup {
  font-size: 1.5rem;
  top: -1em; }

.pricing-plans--style-3 .block-pricing .plan-title-wrapper .price-tag-subtitle {
  display: inline-block;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  font-weight: 300;
  text-transform: capitalize; }

.pricing-plans--style-3 ul {
  padding: 0; }

.pricing-plans--style-3 ul > li {
  padding: 15px 0; }

.pricing-plans .plan-select {
  padding: 15px;
  background: #f1f1f1;
  border-top: 1px solid #e0eded; }

.pricing-plans .block {
  margin-top: 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: #fff;
  overflow: hidden; }

@media (max-width: 767px) {
  .pricing-plans {
    margin-bottom: 30px; }

  .pricing-plans--style-3 [class^="col-"]:not(:last-child) .block-pricing {
    border: 1px solid rgba(0, 0, 0, 0.05); } }
@media (min-width: 992px) {
  .pricing-plans .block.popular {
    margin-top: -1rem; } }
.promos {
  margin: 0 auto; }

.promo {
  margin: 15px 10px 25px;
  padding: 5px 0 30px;
  font-weight: 400;
  line-height: 1.625;
  text-align: center; }

.promos.bg-base-1,
.promos.bg-base-2,
.promos.bg-base-3,
.promos.bg-base-4,
.promos.bg-base-5 {
  background: transparent; }

.promos.bg-base-1 .promo {
  background: #0087be; }

.promos.bg-base-2 .promo {
  background: #292f36; }

.promos.bg-base-3 .promo {
  background: #818a91; }

.promos.bg-base-4 .promo {
  background: #2B2B2B; }

.promos.bg-base-5 .promo {
  background: #FFF; }

.promo h4 {
  margin: 15px 0 0;
  font-size: 150%;
  font-weight: normal; }

.promos.bg-base-1 .promo h4 {
  color: #FFF; }

.promos.bg-base-2 .promo h4 {
  color: #FFF; }

.promos.bg-base-3 .promo h4 {
  color: rgba(255, 255, 255, 0.9); }

.promos.bg-base-4 .promo h4 {
  color: #FFF; }

.promos.bg-base-5 .promo h4 {
  color: #0A0814; }

.promo li {
  padding: 5px 0;
  font-size: 0.875rem; }

.promos.bg-base-1 .brief {
  color: #FFF; }

.promos.bg-base-2 .brief {
  color: #FFF; }

.promos.bg-base-3 .brief {
  color: rgba(255, 255, 255, 0.9); }

.promos.bg-base-4 .brief {
  color: #FFF; }

.promos.bg-base-5 .brief {
  color: #0A0814; }

.promo .price {
  margin: 10px 0;
  padding: 5px 0;
  font-size: 250%; }

.promo .features {
  margin: 0;
  padding: 0;
  list-style-type: none; }

.promo .buy {
  margin: 15px 0 0; }

.promos.bg-base-1 .promo .features {
  color: #FFF; }

.promos.bg-base-2 .promo .features {
  color: #FFF; }

.promos.bg-base-3 .promo .features {
  color: rgba(255, 255, 255, 0.9); }

.promos.bg-base-4 .promo .features {
  color: #FFF; }

.promos.bg-base-5 .promo .features {
  color: #0A0814; }

.promos.bg-base-1 .promo .price {
  background: #177196;
  color: #FFF; }

.promos.bg-base-2 .promo .price {
  background: #131619;
  color: #FFF; }

.promos.bg-base-3 .promo .price {
  background: #687077;
  color: rgba(255, 255, 255, 0.9); }

.promos.bg-base-4 .promo .price {
  background: #121212;
  color: #FFF; }

.promos.bg-base-5 .promo .price {
  background: #e6e6e6;
  color: #0A0814; }

@media (min-width: 768px) {
  .promo {
    display: inline;
    float: left;
    width: 33.333%;
    margin: 15px 0 0;
    transition: transform 0.25s ease-out; }

  .promo.first {
    border-right: none; }

  .promo.second {
    float: right;
    border-left: none; }

  .promo.first:hover,
  .promo.second:hover {
    transform: translateY(-25px); }

  .scale {
    transform: scale(1.2); }

  .promos.bg-base-1 .scale {
    box-shadow: 0 0 4px 1px #177196; }

  .promos.bg-base-2 .scale {
    box-shadow: 0 0 4px 1px #131619; }

  .promos.bg-base-3 .scale {
    box-shadow: 0 0 4px 1px #687077; }

  .promos.bg-base-4 .scale {
    box-shadow: 0 0 4px 1px #121212; }

  .promos.bg-base-5 .scale {
    box-shadow: 0 0 4px 1px #e6e6e6; } }
@media (max-width: 767px) {
  .promo {
    margin-bottom: 25px; }

  .promo:last-child {
    margin-bottom: 0; } }
.progress-wrapper {
  position: relative;
  padding-top: 1.5rem; }

.progress {
  height: 1rem;
  margin-bottom: 1rem;
  overflow: hidden;
  border-radius: 0.2rem;
  background-color: #f1f1f1;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); }

.progress-inverse {
  background-color: rgba(0, 0, 0, 0.3); }

.progress.progress-lg {
  height: 1.5rem; }

.progress.progress-md {
  height: 1rem; }

.progress.progress-sm {
  height: 0.625rem; }

.progress.progress-xs {
  height: 0.25rem; }

.progress .sr-only {
  width: auto;
  height: 20px;
  margin: 0 0 0 30px;
  left: 0;
  clip: auto;
  line-height: 20px;
  font-size: 13px; }

.progress.progress-lg .sr-only {
  height: 1.5rem;
  line-height: 1.5rem; }

.progress.progress-sm .sr-only {
  height: 0.625rem;
  line-height: 0.625rem; }

.progress.progress-xs .sr-only {
  height: 0.25rem;
  line-height: 0.25rem; }

.progress-heading {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 2px;
  padding: 0; }

.progress-bar {
  background-color: #0087be;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  height: auto; }

.progress-text {
  margin-bottom: 0; }

.progress-text .mark {
  float: left;
  width: 50%;
  background: transparent; }

.progress-text .mark.right {
  text-align: right; }

.progress-tooltip {
  display: inline-block;
  background: #2b2b2c;
  color: #eceeef;
  padding: 4px 6px;
  line-height: 1;
  font-size: 0.7rem;
  position: relative;
  bottom: 8px;
  border-radius: 3px;
  margin-left: -15px; }

.progress-tooltip:after {
  top: 100%;
  left: 10px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(51, 51, 51, 0);
  border-top-color: #2b2b2c;
  border-width: 5px; }

.progress-label {
  display: inline-block;
  color: #818a91;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 0.75rem; }

.progress-percentage {
  display: inline-block;
  color: #818a91;
  font-size: 0.875rem;
  position: absolute;
  right: 0;
  top: 0; }

.star-rating {
  display: inline-block; }

.star-rating i {
  display: inline-block;
  color: #ffcc00; }

.rating {
  display: inline-block; }

.rating span.star {
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  float: left;
  padding: 0 1px;
  cursor: pointer; }

.rating span.star:before {
  content: "\f005";
  display: block;
  font-size: 14px;
  color: #818a91;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.rating-lg span.star:before {
  font-size: 18px; }

.rating-xl span.star:before {
  font-size: 24px; }

.rating span.voted:before {
  color: #ffcc00; }

.rating:hover span.star:before {
  color: #818a91; }

.rating:hover span.star.over:before {
  color: #ffcc00; }

.rating > .rating-count {
  font-size: 0.75rem;
  color: #818a91;
  position: relative;
  top: -3px;
  margin-left: 1rem; }

section.ss-slice {
  position: relative;
  padding-top: 4rem;
  padding-bottom: 4rem; }

section.ss-slice::after {
  position: absolute;
  content: '';
  pointer-events: none; }

.ss-style-triangles::after {
  left: 50%;
  width: 100px;
  height: 100px;
  -webkit-transform: translateX(-50%) rotate(45deg);
  transform: translateX(-50%) rotate(45deg); }

.ss-style-triangles::after {
  bottom: -50px;
  z-index: 10;
  background: inherit; }

.ss-slice.ss-style-doublediagonal {
  z-index: 1;
  padding-bottom: 10em; }

.ss-style-doublediagonal::after,
.ss-style-doublediagonal::before {
  top: 0;
  left: -25%;
  z-index: -1;
  width: 150%;
  height: 75%;
  background: inherit;
  -webkit-transform: rotate(-4deg);
  transform: rotate(-4deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0; }

.ss-style-halfcircle::after,
.ss-style-halfcircle::before {
  left: 50%;
  z-index: 10;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: inherit;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%); }

.ss-style-halfcircle::before {
  top: -50px; }

.ss-style-halfcircle::after {
  bottom: -50px; }

.ss-style-multitriangles::after,
.ss-style-multitriangles::before {
  left: 50%;
  width: 50px;
  height: 50px;
  -webkit-transform: translateX(-50%) rotate(45deg);
  transform: translateX(-50%) rotate(45deg); }

.ss-style-multitriangles::before {
  top: -25px;
  background: inherit;
  box-shadow: -50px 50px 0 #3498db, 50px -50px 0 #3498db; }

.ss-style-multitriangles::after {
  bottom: -25px;
  z-index: 10;
  background: inherit;
  box-shadow: -50px 50px 0 #3498db, 50px -50px 0 #3498db; }

.ss-style-roundedsplit {
  padding-top: 7em;
  border-radius: 0 0 80px 80px; }

.ss-style-roundedsplit::before {
  left: 0;
  z-index: 10;
  width: 50%;
  background: inherit; }

.ss-style-roundedsplit::before {
  border-radius: 0 80px 0 0; }

.ss-style-invertedrounded {
  padding: 13em 10% 10em;
  border-radius: 0 0 0 90px; }

.ss-style-invertedrounded::after,
.ss-style-invertedrounded::before {
  left: 0;
  z-index: -1;
  height: 90px;
  background: #177196; }

.ss-style-invertedrounded::before {
  top: 100%;
  width: 100%;
  border-radius: 0 90px 0 0; }

.ss-style-invertedrounded::after {
  bottom: 0;
  z-index: -1;
  width: 50%; }

.ss-style-zigzag::after,
.ss-style-zigzag::before {
  right: 0;
  left: 0;
  z-index: 10;
  display: block;
  height: 90px;
  background-size: 50px 100%; }

.ss-style-zigzag::after {
  top: 100%;
  background-position: 50%; }

.bg-base-1.ss-style-zigzag::after {
  background-image: -webkit-gradient(linear, 0 0, 300% 100%, color-stop(0.25, #0087be), color-stop(0.25, #0087be));
  background-image: linear-gradient(135deg, #0087be 25%, transparent 25%), linear-gradient(225deg, #0087be 25%, transparent 25%); }

.sct-color-1.ss-style-zigzag::after {
  background-image: -webkit-gradient(linear, 0 0, 300% 100%, color-stop(0.25, #FFF), color-stop(0.25, #FFF));
  background-image: linear-gradient(135deg, #FFF 25%, transparent 25%), linear-gradient(225deg, #FFF 25%, transparent 25%); }

.sct-color-2.ss-style-zigzag::after {
  background-image: -webkit-gradient(linear, 0 0, 300% 100%, color-stop(0.25, #fcfcfc), color-stop(0.25, #fcfcfc));
  background-image: linear-gradient(135deg, #fcfcfc 25%, transparent 25%), linear-gradient(225deg, #fcfcfc 25%, transparent 25%); }

.sct-color-3.ss-style-zigzag::after {
  background-image: -webkit-gradient(linear, 0 0, 300% 100%, color-stop(0.25, #282d33), color-stop(0.25, #282d33));
  background-image: linear-gradient(135deg, #282d33 25%, transparent 25%), linear-gradient(225deg, #282d33 25%, transparent 25%); }

.sct-color-4.ss-style-zigzag::after {
  background-image: -webkit-gradient(linear, 0 0, 300% 100%, color-stop(0.25, #121416), color-stop(0.25, #121416));
  background-image: linear-gradient(135deg, #121416 25%, transparent 25%), linear-gradient(225deg, #121416 25%, transparent 25%); }

.section-title {
  margin-bottom: 1.5rem;
  position: relative; }

.section-title:after,
.section-title:before {
  content: "";
  display: table; }

.section-title:before {
  content: " ";
  display: table; }

.section-title.text-center,
.section-title.text-sm-center,
.section-title.text-md-center,
.section-title.text-lg-center,
.section-title.text-xl-center {
  margin-bottom: 3rem; }

.section-title .section-title-inner {
  margin: 0 0 5px 0;
  font-family: "Lato", sans-serif;
  font-size: 1.5rem;
  font-weight: 500;
  text-transform: capitalize;
  position: relative;
  z-index: 2;
  color: #2b2b2c; }

.section-title-inverse .section-title-inner,
.section-title-inverse .heading {
  color: white; }

.section-title .section-title-delimiter {
  display: block;
  position: relative;
  margin-bottom: 20px; }

.section-title .section-title-delimiter::before {
  background: #2b2b2c; }

.section-title-inverse .section-title-delimiter::before {
  background: white; }

.section-title--style-1 .section-title-delimiter::before {
  display: block;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 23px;
  height: 2px; }

.section-title--style-1.text-center .section-title-delimiter::before,
.section-title--style-1.text-sm-center .section-title-delimiter::before,
.section-title--style-1.text-md-center .section-title-delimiter::before,
.section-title--style-1.text-lg-center .section-title-delimiter::before,
.section-title--style-1.text-xl-center .section-title-delimiter::before {
  margin-bottom: -1rem;
  left: 50%;
  margin-left: -17.25px;
  width: 34.5px; }

@media (min-width: 768px) {
  .section-title--style-1.text-sm-left .section-title-delimiter::before {
    left: 0;
    margin-left: 0;
    width: 23px; } }
@media (min-width: 992px) {
  .section-title--style-1.text-md-left .section-title-delimiter::before {
    left: 0;
    margin-left: 0;
    width: 23px; } }
@media (min-width: 1201px) {
  .section-title--style-1.text-lg-left .section-title-delimiter::before {
    left: 0;
    margin-left: 0;
    width: 23px; } }
.section-title--style-1 .text-muted {
  display: block;
  margin: 0.5rem 0;
  text-transform: none; }

.section-title--style-1 .shadow-subtitle {
  position: absolute;
  top: -40px;
  left: -20px;
  color: rgba(0, 0, 0, 0.1);
  font-size: 50px;
  font-weight: 700;
  font-family: "Lato", sans-serif;
  z-index: 1; }

.section-title--style-1 .underlay-subtitle {
  color: #0087be;
  font-size: 5rem;
  font-weight: 700;
  font-family: "Lato", sans-serif;
  opacity: 0.5;
  line-height: 1; }

.section-title--style-1 .underlay-subtitle--top + .section-title-inner {
  margin-top: -36px; }

.section-title--style-1 .underlay-subtitle--bottom {
  top: 10%; }

.section-title--style-2 {
  position: relative; }

.section-title--style-2.text-center .section-title-inner {
  position: relative;
  cursor: default; }

.section-title--style-2.text-center .section-title-inner:before {
  content: "";
  display: block;
  width: 60px;
  height: 70px;
  position: relative;
  top: 22px;
  background: transparent;
  border: 3px solid;
  margin: auto;
  z-index: 3;
  border-color: #2b2b2c; }

.section-title--style-2.section-title-inverse .section-title-inner:before {
  border-color: #eceeef; }

.section-title--style-2.text-center .section-title-inner > span {
  display: inline-block;
  position: relative;
  top: -32px;
  height: 36px;
  line-height: 36px;
  z-index: 4;
  background: #FFF; }

.section-title--style-2.text-center .section-title-inner.heading-1 > span {
  top: -38px; }

.section-title--style-2.text-center .section-title-inner.heading-3 > span {
  top: -31px; }

.section-title--style-2.text-center .section-title-inner.heading-4 > span {
  top: -31px; }

.sct-color-2 .section-title--style-2.text-center .section-title-inner > span {
  background: #fcfcfc; }

.sct-color-3 .section-title--style-2.text-center .section-title-inner > span {
  background: #282d33; }

.bg-base-1 .section-title--style-2.text-center .section-title-inner > span {
  background: #0087be; }

.section-title--style-2.section-title-inverse .section-title-inner > span {
  background: #2b2b2c; }

.section-title--style-2 .section-title-inner .heading {
  margin: 0; }

.social-media {
  margin: 0;
  padding: 0;
  position: relative;
  list-style: none; }

.social-media > li {
  display: inline-block;
  font-size: 1rem; }

.social-media > li > a {
  display: block; }

[class*="social-media--style-1"] > li > a {
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  margin-right: 0.3rem;
  background: transparent; }

[class*="social-media--style-1"] > li > a:hover {
  background: #0087be;
  color: #FFF; }

.social-media-circle > li > a {
  border-radius: 100%; }

.social-media-round > li > a {
  border-radius: 0.25rem; }

.social-media.social-media--style-1-v1 > li > a {
  background: #0087be;
  color: #FFF; }

.social-media.social-media--style-1-v1 > li > a:hover {
  background: #177196; }

.social-media.social-media--style-1-v2 > li > a {
  background: #ddd;
  color: #333; }

.social-media.social-media--style-1-v2 > li > a:hover {
  background: #0087be;
  color: #FFF; }

.social-media.social-media--style-1-v3 > li > a {
  background: #2b2b2c;
  color: #eceeef; }

.social-media.social-media--style-1-v3 > li > a:hover {
  background: #0087be;
  color: #FFF; }

.social-media.social-media--style-1-v4 > li > a {
  font-size: 1rem;
  width: auto;
  height: auto;
  padding-left: 0;
  padding-right: 0;
  margin-right: 1rem;
  background: transparent;
  color: rgba(0, 0, 0, 0.7); }

.social-media.social-media--style-1-v4 > li > a:hover {
  background: transparent;
  color: rgba(0, 0, 0, 0.9); }

.social-media.social-media--style-1-v4 > li:last-child > a {
  margin-right: 0; }

.social-media.social-media--style-1-v5 > li > a {
  font-size: 1rem;
  width: auto;
  height: auto;
  padding-left: 0;
  padding-right: 0;
  margin-right: 1rem;
  background: transparent;
  color: rgba(255, 255, 255, 0.7); }

.social-media.social-media--style-1-v5 > li > a:hover {
  background: transparent;
  color: white; }

.social-media.social-media--style-1-v5 > li:last-child > a {
  margin-right: 0; }

.social-media.social-media--style-1-v5 > li > span {
  color: rgba(255, 255, 255, 0.5); }

.social-media-brand-color > li > a.facebook,
.social-media-brand-color--hover > li > a.facebook:hover {
  background: #3b5998;
  color: #fff; }

.social-media-brand-color > li > a.twitter,
.social-media-brand-color--hover > li > a.twitter:hover {
  background: #1da1f2;
  color: #fff; }

.social-media-brand-color > li > a.instagram,
.social-media-brand-color--hover > li > a.instagram:hover {
  background: #e1306c;
  color: #fff; }

.social-media-brand-color > li > a.linkedin,
.social-media-brand-color--hover > li > a.linkedin:hover {
  background: #0077b5;
  color: #fff; }

.social-media-brand-color > li > a.pinterest,
.social-media-brand-color--hover > li > a.pinterest:hover {
  background: #bd081c;
  color: #fff; }

.social-media-brand-color > li > a.dribbble,
.social-media-brand-color--hover > li > a.dribbble:hover {
  background: #444444;
  color: #fff; }

.social-media-brand-color > li > a.googleplus,
.social-media-brand-color--hover > li > a.googleplus:hover {
  background: #dd4b39;
  color: #fff; }

.social-media-brand-color > li > a.skype,
.social-media-brand-color--hover > li > a.skype:hover {
  background: #00aff0;
  color: #fff; }

.social-media-brand-color:not(.social-media-brand-color--hover) > li > a:hover {
  background: #0087be;
  color: #FFF; }

.table td,
.table th {
  font-size: 0.875rem; }

.table-bordered td,
.table-bordered th {
  border: 1px solid rgba(0, 0, 0, 0.05); }

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #f2f2f2; }

.table tbody td {
  vertical-align: middle; }

.table tbody + tbody {
  border-top: 2px solid #f2f2f2; }

.table .table {
  background-color: #f3f3f3; }

.table-sm td,
.table-sm th {
  padding: 0.75rem 1rem; }

.table-bordered,
.table-bordered td,
.table-bordered th {
  border: 1px solid #f2f2f2; }

.table-bordered thead td,
.table-bordered thead th {
  border-bottom-width: 2px; }

.table-inverse.table-bordered,
.table-responsive.table-bordered {
  border: 0; }

.table-active,
.table-active > td,
.table-active > th,
.table-hover tbody tr:hover,
.table-striped tbody tr:nth-of-type(odd) {
  background-color: #f2f2f2; }

.table-hover .table-active:hover,
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: #e5e5e5; }

.table-success,
.table-success > td,
.table-success > th {
  background-color: #57d59f; }

.table-hover .table-success:hover,
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #43d093; }

.table-info,
.table-info > td,
.table-info > th {
  background-color: #2ebcfc; }

.table-hover .table-info:hover,
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #14b4fc; }

.table-warning,
.table-warning > td,
.table-warning > th {
  background-color: #ffc721; }

.table-hover .table-warning:hover,
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #ffc107; }

.table-danger,
.table-danger > td,
.table-danger > th {
  background-color: #ff6f6c; }

.table-hover .table-danger:hover,
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #ff5652; }

.thead-inverse th {
  color: #f3f3f3;
  background-color: #404c54; }

.thead-default th {
  color: #464a4c;
  background-color: #fbfbfb; }

.table-inverse {
  color: #f3f3f3;
  background-color: #404c54; }

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar; }

.table thead th {
  border-bottom-width: 1px; }

.table tr[class*=table-] td,
.table tr[class*=table-] th,
.table tr[class*=table-] + tr td,
.table tr[class*=table-] + tr th {
  border: 0; }

.table:not(.table-bordered) > tbody:first-child td,
.table:not(.table-bordered) > tbody:first-child th,
.table:not(.table-bordered) > thead:first-child td,
.table:not(.table-bordered) > thead:first-child th {
  border-top: 0; }

.table-inverse td,
.table-inverse th,
.table-inverse thead th {
  border-color: #505b63; }

.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
  border-top: 1px solid rgba(243, 243, 243, 0.7);
  font-size: 0.875rem; }

.table-no-border > tbody > tr > td,
.table-no-border > tbody > tr > th,
.table-no-border > tfoot > tr > td,
.table-no-border > tfoot > tr > th,
.table-no-border > thead > tr > td,
.table-no-border > thead > tr > th {
  border-top: 0; }

.table-profile > tbody > tr > td:not(.td-actions) {
  font-size: 0.875rem; }

.table-profile > tbody > tr > td.td-label {
  font-size: 0.75rem;
  font-family: "Lato", sans-serif;
  font-weight: 500;
  color: #818a91;
  text-transform: uppercase;
  white-space: nowrap; }

.table-profile > tbody > tr > td.td-label > span {
  display: block;
  padding-right: 1rem; }

.table-profile > tbody > tr > td.td-actions > .btn {
  opacity: 0; }

.table-profile > tbody > tr:hover > td.td-actions > .btn {
  opacity: 1; }

.table-cart > thead > tr > th {
  padding: 12px 0;
  border: 0;
  font-weight: 400; }

.table-cart > tbody > tr > td,
.table-cart > tbody > tr > th,
.table-cart > tfoot > tr > td,
.table-cart > tfoot > tr > th,
.table-cart > thead > tr > td,
.table-cart > thead > tr > th {
  border-color: rgba(243, 243, 243, 0.7);
  vertical-align: middle; }

.table-cart > tbody > tr > td {
  padding: 1rem 0 !important; }

.table-cart .cart-item-img {
  width: 120px; }

.table-cart .cart-item-img img {
  width: 100%; }

.table-cart .cart-item-content {
  vertical-align: middle;
  width: 70%; }

.table-cart .cart-item-content .cart-item-title {
  font-weight: 500;
  font-size: 1rem;
  color: #2b2b2c; }

.table-cart .cart-item-content .cart-item-title:hover {
  color: #0087be; }

.table-cart .cart-item-content .label-quantity {
  color: #818a91;
  font-weight: 400;
  font-size: 0.875rem; }

.table-cart .cart-item-content .label-value {
  font-weight: 600; }

.table-cart .cart-item-unit-price {
  vertical-align: middle;
  font-size: 18px; }

.table-cart .cart-item-price {
  width: 20%;
  vertical-align: middle;
  text-align: right; }

.table-cart .cart-item-price > .price {
  font-weight: 600;
  font-size: 1.25rem;
  display: block;
  color: #2b2b2c; }

.table-cart .cart-item-price > .price.discount {
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: line-through;
  color: #ff3b30;
  margin-top: 8px; }

.table-cart .cart-item-price > .price.savings {
  font-weight: 400;
  font-size: 0.75rem; }

.table-cart .cart-item-count {
  vertical-align: middle; }

.table-cart .label-subtotal {
  padding-top: 15px;
  text-align: center;
  font-size: 14px;
  text-transform: uppercase; }

.table-cart .table-cart-footer {
  padding: 15px;
  border-top: 1px solid rgba(243, 243, 243, 0.7); }

.table-cart .cart-items {
  display: block;
  padding: 15px;
  font-size: 14px;
  font-weight: 500;
  background: #eee;
  color: #ccc;
  border-bottom: 1px solid #ccc; }

.dropdown-cart-wrapper {
  min-width: 260px; }

.dropdown-cart {
  min-width: 400px;
  padding: 0 1rem; }

.dropdown-cart .table {
  margin: 0; }

.dropdown-cart-header {
  padding: 1rem;
  border-bottom: 1px solid #f9f9f9; }

.dropdown-cart-header .heading {
  margin: 0; }

.dropdown-cart-header .dropdown-cart-header-count {
  color: #818a91;
  margin-left: 5px; }

.dropdown-cart .table-cart > tbody > tr:first-child > td {
  border-top: 0; }

.dropdown-cart .cart-item-img {
  width: 60px; }

.dropdown-cart .cart-item-content .cart-item-title {
  color: #666;
  font-size: 0.875rem; }

.dropdown-cart .cart-item-content .label-quantity {
  color: #818a91; }

.dropdown-cart .cart-item-content .label-value {
  font-weight: 600; }

.dropdown-cart .cart-item-price > span {
  font-weight: 600; }

.dropdown-cart-footer {
  padding: 1rem;
  border-top: 1px solid #f9f9f9; }

.dropdown-cart-footer-subtotal {
  padding: 1rem;
  border-top: 1px solid #f9f9f9; }

.navbar-dropdown--inverse .dropdown-cart .table > tbody > tr > td,
.navbar-dropdown--inverse .dropdown-cart .table > tbody > tr > th,
.navbar-dropdown--inverse .dropdown-cart .table > tfoot > tr > td,
.navbar-dropdown--inverse .dropdown-cart .table > tfoot > tr > th,
.navbar-dropdown--inverse .dropdown-cart .table > thead > tr > td,
.navbar-dropdown--inverse .dropdown-cart .table > thead > tr > th {
  border-color: rgba(30, 30, 30, 0.7); }

.navbar-dropdown--inverse .dropdown-cart-header {
  border-bottom: 1px solid rgba(30, 30, 30, 0.7); }

.navbar-dropdown--inverse .dropdown-cart-header .heading,
.navbar-dropdown--inverse .dropdown-cart-header .dropdown-cart-header-count {
  color: #eceeef; }

.navbar-dropdown--inverse .dropdown-cart .cart-item-content .cart-item-title {
  color: rgba(255, 255, 255, 0.5); }

.navbar-dropdown--inverse .dropdown-cart .cart-item-content .label-quantity {
  color: #818a91; }

.navbar-dropdown--inverse .dropdown-cart-footer {
  border-top: 1px solid rgba(30, 30, 30, 0.7); }

.navbar-dropdown--inverse .dropdown-cart-footer-subtotal {
  border-top: 1px solid rgba(30, 30, 30, 0.7); }

.table-inner-well {
  padding: 15px; }

.table-inner-well > thead > tr > th {
  padding: 12px 0;
  border: 0;
  font-weight: 600; }

.table-inner-well > tbody > tr > td {
  padding: 1rem 0;
  border-bottom: 1px solid rgba(243, 243, 243, 0.7);
  vertical-align: middle; }

.table-inner-well > tbody > tr:last-child > td {
  border: 0; }

.table-inner-well > tbody > tr.tr-sm > td {
  padding: 0.625rem 0; }

.table-inner-well .label-count {
  color: #818a91;
  font-weight: 600; }

.tab-body {
  padding: 15px; }

.tabbable-panel {
  border: 1px solid rgba(0, 0, 0, 0.125);
  padding: 10px; }

.tabs.tabs--centered > .nav {
  text-align: center; }

.tabs.tabs--centered > .nav > li {
  float: none;
  display: inline-block; }

.nav-tab-image-wrapper:hover {
  cursor: pointer; }

.nav-tab-image-wrapper .nav-tab-image {
  display: block;
  width: 96px;
  height: 96px;
  margin: auto;
  border: 2px solid #e0eded; }

.nav-tab-image-wrapper:hover .nav-tab-image {
  border-color: #0087be; }

.nav-tab-image-wrapper .nav-tab-image img {
  width: 100%; }

.nav-tab-image-wrapper .nav-tab-image-title {
  padding-top: 10px;
  padding-bottom: 10px;
  display: block;
  font-weight: 600;
  color: #111111;
  font-size: 1rem;
  font-family: "Lato", sans-serif; }

.nav-tab-image-wrapper:hover .nav-tab-image-title {
  color: #0087be; }

@media (max-width: 991px) {
  .tabs .nav-pills.nav-pills--rounded > li {
    display: block;
    margin-bottom: 5px; } }
@media (min-width: 992px) {
  .tabs .nav-pills.nav-pills--rounded {
    padding: 0 50px; }

  .tabs .nav-pills.nav-pills--rounded > li > a {
    min-width: 200px;
    background: transparent;
    border: 0;
    padding: 0;
    position: relative; }

  .tabs .nav-pills.nav-pills--rounded > li:last-child > a {
    min-width: auto; }

  .tabs .nav-pills.nav-pills--rounded > li > a:before {
    content: "";
    position: relative;
    display: block;
    top: 40px;
    height: 1px;
    margin-left: 105px;
    margin-right: 0;
    background: #f6f6f6;
    z-index: 0; }

  .tabs .nav-pills.nav-pills--rounded > li:last-child > a:before {
    background: transparent; }

  .tabs .nav-pills.nav-pills--rounded > li > a > .nav-pill {
    position: relative;
    display: block;
    width: 100px;
    height: 100px; }

  .tabs .nav-pills.nav-pills--rounded > li > a > .nav-pill > .nav-pill-circle {
    position: relative;
    display: block;
    width: 80px;
    height: 80px;
    line-height: 80px;
    margin: auto;
    text-align: center;
    border-radius: 100%;
    background: #eee;
    color: #2b2b2c;
    font-size: 16px;
    font-weight: 600;
    font-family: "Lato", sans-serif;
    z-index: 10; }

  .tabs .nav-pills.nav-pills--rounded > li > a > .nav-pill > .nav-pill-circle > i {
    display: block;
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 40px; }

  .tabs .nav-pills.nav-pills--rounded .active .nav-pill .nav-pill-circle {
    background: #0087be;
    color: #FFF; }

  .tabs .nav-pills.nav-pills--rounded > li > a > .nav-pill > .nav-pill-text {
    display: block;
    margin: 10px 0 20px 0;
    color: #818a91;
    font-size: 13px; } }
.tabs--style-1 .tab-content {
  background-color: #FFF;
  border: 1px solid rgba(0, 0, 0, 0.125); }

.tabs--style-1 .tab-pane {
  border: 0; }

.tabs--style-1 .nav-tabs {
  border: 0; }

.tabs--style-1 .nav-tabs > li {
  margin-right: 5px; }

.tabs--style-1 .nav-tabs > li > a {
  border-radius: 0;
  margin: 0;
  text-align: center;
  background-color: #FFF;
  border-left: 0;
  -webkit-transition: all 100ms linear;
  transition: all 100ms linear;
  padding: 18px 32px;
  display: table;
  width: 100%;
  border: 1px solid #f1f1f1;
  color: #000;
  font-size: 13px;
  font-family: "Lato", sans-serif;
  position: relative; }

.tabs--style-1 .nav-tabs > li > a:hover {
  background: #0087be;
  color: #FFF; }

.tabs--style-1 .nav-tabs > li.active > a {
  border: 1px solid #0087be;
  background: #0087be; }

.tabs--style-1 .nav-tabs > li.active:first-child > a {
  border-left: 1px solid #0087be; }

.tabs--style-1 .nav-tabs > li.active > a {
  color: #0087be; }

.tabs--style-1 .nav-tabs > li.active > a,
.tabs--style-1 .nav-tabs > li.active > a:focus,
.tabs--style-1 .nav-tabs > li.active > a:hover {
  background: #0087be;
  color: #FFF; }

.tabs--style-1 .tab-content {
  margin-top: 5px;
  border: 1px solid #f1f1f1; }

.tabs--style-2 .nav-tabs {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 0 20px;
  margin: 0; }

.tabs--style-2 .nav-tabs .nav-item {
  margin-right: 2rem; }

.tabs--style-2 .nav-tabs .nav-item:last-child {
  margin-right: 0; }

.tabs--style-2 .nav-tabs .nav-link {
  border: 0;
  border-bottom: 1px solid transparent;
  margin-right: 0;
  color: #2b2b2c;
  padding: 1rem 0;
  font-size: 0.875rem;
  font-family: "Lato", sans-serif; }

.tabs--style-2 > .nav-tabs > li > a > i {
  color: #a6a6a6; }

.tabs--style-2 .nav-tabs .nav-item.show .nav-link,
.tabs--style-2 .nav-tabs .nav-link.active,
.tabs--style-2 .nav-tabs .nav-link:hover {
  border-bottom: 1px solid #0087be;
  background: transparent !important;
  color: #0087be; }

.tabs--style-2 > .nav-tabs > li.show > a > i,
.tabs--style-2 > .nav-tabs > li:hover > a > i {
  color: #a6a6a6; }

.tabs--style-2 > .nav-tabs > li.open .dropdown-menu,
.tabs--style-2 > .nav-tabs > li:hover .dropdown-menu {
  margin-top: 0; }

.tabs--style-2 > .nav-tabs > li.active > a > i {
  color: #0087be; }

.tabs--style-2 > .tab-content {
  margin-top: 0;
  border: 0;
  border-top: 0;
  padding: 15px 0; }

.tabs--style-2.tabs--centered > .tab-content {
  border-top: 0;
  padding-top: 20px; }

.timeline-group:after {
  content: "";
  display: table;
  clear: both; }

.timeline-item {
  position: relative; }

.timeline-item:before {
  content: "";
  position: absolute;
  width: 3px;
  background: #e0eded;
  top: 0;
  left: 17px;
  bottom: 0; }

.timeline-item:last-child:before {
  background: transparent; }

.timeline-item.first:before {
  top: 30px; }

.timeline-item .timeline-info {
  min-height: 100%;
  padding: 0 20px 30px 40px; }

.timeline-item.first .timeline-info {
  padding-top: 20px; }

@media screen and (min-width: 760px) {
  .timeline-item .timeline-info {
    float: left;
    width: 100%; } }
.timeline-item .timeline-info .date {
  position: relative;
  font-size: 0.875rem;
  color: #818a91;
  margin-bottom: 10px; }

.timeline-item .timeline-info .date:before {
  content: "";
  position: absolute;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 2px solid #e0eded;
  background: #fff;
  left: -28px;
  top: 0; }

.timeline-item .timeline-info .timeline-title {
  margin: 0;
  padding: 0;
  font-size: 1rem;
  color: #2b2b2c;
  font-weight: 500; }

.timeline-item .timeline-info .timeline-subtitle {
  margin: 0;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 300;
  color: #818a91; }

.timeline-item .timeline-info .description {
  color: #818a91; }

.timeline-item .timeline-info .meta {
  color: black;
  margin-top: 10px; }

.timeline-item .timeline-info .meta p {
  margin: 0; }

.content .timeline-item .portfolio-image {
  padding: 20px;
  background: white;
  border-left: 1px solid #dedede;
  text-align: center; }

@media screen and (min-width: 760px) {
  .content .timeline-item .portfolio-image {
    float: left;
    width: 70%; } }
.content .timeline-item .portfolio-image img {
  width: 100%;
  max-width: 610px;
  height: auto; }

.content .timeline-item .portfolio-image div.loading img {
  width: auto;
  height: auto; }

.content div.loading {
  color: #c4c4c4;
  position: absolute;
  width: 100px;
  bottom: 15px;
  left: 50%;
  margin-left: -50px; }

.content div.loading img {
  vertical-align: middle; }

.content div.loading img.rotate {
  -webkit-animation-name: rotate;
  -ms-animation-name: rotate;
  animation-name: rotate;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-iteration-count: infinite;
  -ms-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -ms-animation-timing-function: linear;
  animation-timing-function: linear; }

.well {
  border-radius: 0.25rem;
  background: #eceeef;
  border-color: #818a91;
  -webkit-box-shadow: none;
  box-shadow: none; }

.widget {
  margin-bottom: 2rem; }

.view {
  width: 100%;
  overflow: hidden;
  position: relative;
  text-align: center;
  cursor: default; }

.view--rounded {
  border-radius: 0.25rem; }

.view--circle {
  border-radius: 100%; }

.view--circle img {
  border-radius: 100% !important; }

.view .content,
.view .mask {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  left: 0; }

.view .mask {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0; }

.view:hover .mask {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1; }

.view img {
  max-width: 100%;
  display: block;
  position: relative; }

.view h2 {
  text-align: center;
  position: relative;
  padding: 10px;
  background: rgba(222, 27, 27, 0.8);
  color: #FFF;
  margin: 20px 0 0;
  font-size: 1.125rem;
  font-weight: 500; }

.view p {
  font-size: 0.875rem;
  position: relative;
  color: #fff;
  padding: 10px 20px 20px;
  text-align: center; }

.view a.info {
  display: inline-block;
  text-decoration: none;
  padding: 7px 14px;
  background: #000;
  color: #fff;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 1px #000;
  -moz-box-shadow: 0 0 1px #000;
  box-shadow: 0 0 1px #000; }

.view a.info:hover {
  -webkit-box-shadow: 0 0 5px #000;
  -moz-box-shadow: 0 0 5px #000;
  box-shadow: 0 0 5px #000; }

.view .view-buttons {
  position: absolute;
  width: 100%;
  height: 100%;
  display: table; }

.view .view-buttons-inner {
  display: table-cell;
  vertical-align: middle;
  padding: 1rem; }

.view .view-buttons .icon {
  font-size: 32px; }

/* EFFECT 1 */
.view-first img {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.view-first .mask {
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out; }

.view-first h2 {
  -webkit-transform: translateY(-100px);
  -moz-transform: translateY(-100px);
  -o-transform: translateY(-100px);
  -ms-transform: translateY(-100px);
  transform: translateY(-100px);
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.view-first p {
  -webkit-transform: translateY(100px);
  -moz-transform: translateY(100px);
  -o-transform: translateY(100px);
  -ms-transform: translateY(100px);
  transform: translateY(100px);
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.view-first:hover img {
  -webkit-transform: scale(1.1, 1.1);
  -moz-transform: scale(1.1, 1.1);
  -o-transform: scale(1.1, 1.1);
  -ms-transform: scale(1.1, 1.1);
  transform: scale(1.1, 1.1); }

.view-first a.info {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.view-first:hover a.info,
.view-first:hover h2,
.view-first:hover p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px); }

.view-first:hover p {
  -webkit-transition-delay: 0.1s;
  -moz-transition-delay: 0.1s;
  -o-transition-delay: 0.1s;
  -ms-transition-delay: 0.1s;
  transition-delay: 0.1s; }

.view-first:hover a.info {
  -webkit-transition-delay: 0.2s;
  -moz-transition-delay: 0.2s;
  -o-transition-delay: 0.2s;
  -ms-transition-delay: 0.2s;
  transition-delay: 0.2s; }

/* EFFECT 2 */
.view-second img {
  -webkit-transform: scaleY(1);
  -moz-transform: scaleY(1);
  -o-transform: scaleY(1);
  -ms-transform: scaleY(1);
  transform: scaleY(1);
  -webkit-transition: all 0.7s ease-in-out;
  -moz-transition: all 0.7s ease-in-out;
  -o-transition: all 0.7s ease-in-out;
  -ms-transition: all 0.7s ease-in-out;
  transition: all 0.7s ease-in-out; }

.view-second .mask {
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  transition: all 0.5s linear;
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0; }

.view-second h2 {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  transition: all 0.5s linear;
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0; }

.view-second p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  transition: all 0.5s linear; }

.view-second a.info {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  transition: all 0.5s linear; }

.view-second:hover img {
  -webkit-transform: scale(1.5);
  -moz-transform: scale(1.5);
  -o-transform: scale(1.5);
  -ms-transform: scale(1.5);
  transform: scale(1.5); }

.view-second:hover a.info,
.view-second:hover h2,
.view-second:hover p {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1; }

/* EFFECT 3 */
.view-third img {
  -webkit-transition: all 0.2s ease-in;
  -moz-transition: all 0.2s ease-in;
  -o-transition: all 0.2s ease-in;
  -ms-transition: all 0.2s ease-in;
  transition: all 0.2s ease-in; }

.view-third .mask {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transform: translate(460px, -100px) rotate(180deg);
  -moz-transform: translate(460px, -100px) rotate(180deg);
  -o-transform: translate(460px, -100px) rotate(180deg);
  -ms-transform: translate(460px, -100px) rotate(180deg);
  transform: translate(460px, -100px) rotate(180deg);
  -webkit-transition: all 0.2s 0.4s ease-in-out;
  -moz-transition: all 0.2s 0.4s ease-in-out;
  -o-transition: all 0.2s 0.4s ease-in-out;
  -ms-transition: all 0.2s 0.4s ease-in-out;
  transition: all 0.2s 0.4s ease-in-out; }

.view-third h2 {
  -webkit-transform: translateY(-100px);
  -moz-transform: translateY(-100px);
  -o-transform: translateY(-100px);
  -ms-transform: translateY(-100px);
  transform: translateY(-100px);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.view-third p {
  -webkit-transform: translateX(300px) rotate(90deg);
  -moz-transform: translateX(300px) rotate(90deg);
  -o-transform: translateX(300px) rotate(90deg);
  -ms-transform: translateX(300px) rotate(90deg);
  transform: translateX(300px) rotate(90deg);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.view-third a.info {
  -webkit-transform: translateY(-200px);
  -moz-transform: translateY(-200px);
  -o-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out; }

.view-third:hover .mask {
  -webkit-transition-delay: 0s;
  -moz-transition-delay: 0s;
  -o-transition-delay: 0s;
  -ms-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-transform: translate(0px, 0px);
  -moz-transform: translate(0px, 0px);
  -o-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  transform: translate(0px, 0px); }

.view-third:hover h2 {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.5s;
  -moz-transition-delay: 0.5s;
  -o-transition-delay: 0.5s;
  -ms-transition-delay: 0.5s;
  transition-delay: 0.5s; }

.view-third:hover p {
  -webkit-transform: translateX(0px) rotate(0deg);
  -moz-transform: translateX(0px) rotate(0deg);
  -o-transform: translateX(0px) rotate(0deg);
  -ms-transform: translateX(0px) rotate(0deg);
  transform: translateX(0px) rotate(0deg);
  -webkit-transition-delay: 0.4s;
  -moz-transition-delay: 0.4s;
  -o-transition-delay: 0.4s;
  -ms-transition-delay: 0.4s;
  transition-delay: 0.4s; }

.view-third:hover a.info {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.3s;
  -moz-transition-delay: 0.3s;
  -o-transition-delay: 0.3s;
  -ms-transition-delay: 0.3s;
  transition-delay: 0.3s; }

/* EFFECT 4 */
.view-fourth img {
  -webkit-transition: all 0.4s ease-in-out 0.2s;
  -moz-transition: all 0.4s ease-in-out 0.2s;
  -o-transition: all 0.4s ease-in-out 0.2s;
  -ms-transition: all 0.4s ease-in-out 0.2s;
  transition: all 0.4s ease-in-out 0.2s;
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1; }

.view-fourth .mask {
  background-color: black;
  -webkit-transform: scale(0) rotate(-180deg);
  -moz-transform: scale(0) rotate(-180deg);
  -o-transform: scale(0) rotate(-180deg);
  -ms-transform: scale(0) rotate(-180deg);
  transform: scale(0) rotate(-180deg);
  -webkit-transition: all 0.4s ease-in;
  -moz-transition: all 0.4s ease-in;
  -o-transition: all 0.4s ease-in;
  -ms-transition: all 0.4s ease-in;
  transition: all 0.4s ease-in;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0; }

.view-fourth h2 {
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out; }

.view-fourth p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out; }

.view-fourth a.info {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out; }

.view-fourth:hover .mask {
  -webkit-transform: scale(1) rotate(0deg);
  -moz-transform: scale(1) rotate(0deg);
  -o-transform: scale(1) rotate(0deg);
  -ms-transform: scale(1) rotate(0deg);
  transform: scale(1) rotate(0deg);
  -webkit-transition-delay: 0.2s;
  -moz-transition-delay: 0.2s;
  -o-transition-delay: 0.2s;
  -ms-transition-delay: 0.2s;
  transition-delay: 0.2s; }

.view-fourth:hover img {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition-delay: 0s;
  -moz-transition-delay: 0s;
  -o-transition-delay: 0s;
  -ms-transition-delay: 0s;
  transition-delay: 0s; }

.view-fourth:hover a.info,
.view-fourth:hover h2,
.view-fourth:hover p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1;
  -webkit-transition-delay: 0.5s;
  -moz-transition-delay: 0.5s;
  -o-transition-delay: 0.5s;
  -ms-transition-delay: 0.5s;
  transition-delay: 0.5s; }

/* EFFECT 5 */
.view-fifth img {
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }

.view-fifth .mask {
  background-color: black;
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; }

.view-fifth h2 {
  background: #0087be; }

.view-fifth p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.view-fifth:hover .mask {
  -webkit-transform: translateX(0px);
  -moz-transform: translateX(0px);
  -o-transform: translateX(0px);
  -ms-transform: translateX(0px);
  transform: translateX(0px); }

.view-fifth:hover p {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1; }

/* EFFECT 6 */
.view-sixth .mask {
  top: -200px;
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.3s ease-out 0.5s;
  -moz-transition: all 0.3s ease-out 0.5s;
  -o-transition: all 0.3s ease-out 0.5s;
  -ms-transition: all 0.3s ease-out 0.5s;
  transition: all 0.3s ease-out 0.5s; }

.view-sixth h2 {
  -webkit-transform: translateY(-200px);
  -moz-transform: translateY(-200px);
  -o-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all 0.2s ease-in-out 0.1s;
  -moz-transition: all 0.2s ease-in-out 0.1s;
  -o-transition: all 0.2s ease-in-out 0.1s;
  -ms-transition: all 0.2s ease-in-out 0.1s;
  transition: all 0.2s ease-in-out 0.1s; }

.view-sixth p {
  -webkit-transform: translateY(-200px);
  -moz-transform: translateY(-200px);
  -o-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all 0.2s ease-in-out 0.2s;
  -moz-transition: all 0.2s ease-in-out 0.2s;
  -o-transition: all 0.2s ease-in-out 0.2s;
  -ms-transition: all 0.2s ease-in-out 0.2s;
  transition: all 0.2s ease-in-out 0.2s; }

.view-sixth a.info {
  -webkit-transform: translateY(-200px);
  -moz-transform: translateY(-200px);
  -o-transform: translateY(-200px);
  -ms-transform: translateY(-200px);
  transform: translateY(-200px);
  -webkit-transition: all 0.2s ease-in-out 0.3s;
  -moz-transition: all 0.2s ease-in-out 0.3s;
  -o-transition: all 0.2s ease-in-out 0.3s;
  -ms-transition: all 0.2s ease-in-out 0.3s;
  transition: all 0.2s ease-in-out 0.3s; }

.view-sixth:hover .mask {
  -ms-filter: "progid: DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  opacity: 1;
  top: 0;
  -webkit-transition-delay: 0s;
  -moz-transition-delay: 0s;
  -o-transition-delay: 0s;
  -ms-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-animation: bounceY 0.9s linear;
  -moz-animation: bounceY 0.9s linear;
  -ms-animation: bounceY 0.9s linear;
  animation: bounceY 0.9s linear; }

.view-sixth:hover h2 {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.4s;
  -moz-transition-delay: 0.4s;
  -o-transition-delay: 0.4s;
  -ms-transition-delay: 0.4s;
  transition-delay: 0.4s; }

.view-sixth:hover p {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0.2s;
  -moz-transition-delay: 0.2s;
  -o-transition-delay: 0.2s;
  -ms-transition-delay: 0.2s;
  transition-delay: 0.2s; }

.view-sixth:hover a.info {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -o-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition-delay: 0s;
  -moz-transition-delay: 0s;
  -o-transition-delay: 0s;
  -ms-transition-delay: 0s;
  transition-delay: 0s; }

@keyframes bounceY {
  0% {
    transform: translateY(-205px); }
  40% {
    transform: translateY(-100px); }
  65% {
    transform: translateY(-52px); }
  82% {
    transform: translateY(-25px); }
  92% {
    transform: translateY(-12px); }
  100%,
    55%,
    75%,
    87%,
    97% {
    transform: translateY(0px); } }
@-moz-keyframes bounceY {
  0% {
    -moz-transform: translateY(-205px); }
  40% {
    -moz-transform: translateY(-100px); }
  65% {
    -moz-transform: translateY(-52px); }
  82% {
    -moz-transform: translateY(-25px); }
  92% {
    -moz-transform: translateY(-12px); }
  100%,
    55%,
    75%,
    87%,
    97% {
    -moz-transform: translateY(0px); } }
@-webkit-keyframes bounceY {
  0% {
    -webkit-transform: translateY(-205px); }
  40% {
    -webkit-transform: translateY(-100px); }
  65% {
    -webkit-transform: translateY(-52px); }
  82% {
    -webkit-transform: translateY(-25px); }
  92% {
    -webkit-transform: translateY(-12px); }
  100%,
    55%,
    75%,
    87%,
    97% {
    -webkit-transform: translateY(0px); } }
/* PROFILE PAGES */
.profile-picture-wrapper {
  position: relative; }

.profile-picture img {
  max-width: 100%; }

.profile-picture.profile-picture--style-1 {
  padding: 1rem 1rem 0; }

.profile-picture.profile-picture--style-1 img {
  border-radius: 0.25rem; }

.profile-picture .btn-aux {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 40px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top: 35px;
  right: 50px;
  background: rgba(0, 0, 0, 0.8);
  color: #ccc; }

.profile-picture:hover .btn-aux {
  display: block; }

.profile-picture.btn-aux:hover {
  background: #0087be;
  color: #FFF; }

.profile-picture.profile-picture--style-2 {
  margin: 0 auto;
  width: 180px;
  position: relative; }

.profile-picture.profile-picture--style-2 img {
  border-radius: 100%;
  margin-top: 30px;
  border: 10px solid rgba(255, 255, 255, 0.1); }

.profile-picture.profile-picture--style-2 .btn-aux {
  right: 10px;
  top: 10px; }

.profile-details {
  text-align: center;
  margin-top: 20px; }

.profile-details .profile-name {
  margin: 0;
  padding: 0; }

.profile-details .profile-occupation {
  margin: 12px 0 0;
  padding: 0; }

.profile-details .profile-location {
  margin: 2px 0 0;
  padding: 0; }

.profile-connect {
  text-align: center;
  width: 180px;
  margin: 2rem auto; }

.profile-stats {
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.profile-stats .stats-entry {
  padding: 20px 0;
  width: 50%;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  display: inline-block;
  float: left; }

.profile-stats .stats-entry:last-child {
  border-right: 0; }

.profile-stats .stats-entry .stats-count {
  display: block;
  font-weight: 500;
  font-size: 1.25rem; }

.profile-stats .stats-entry .stats-label {
  display: block;
  font-weight: 300;
  font-size: 0.75rem; }

.profile-useful-links:nth-child(n+2):not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); }

.profile-useful-links .useful-links {
  padding: 2rem; }

.profile-useful-links .useful-links a {
  display: block;
  margin-bottom: 10px;
  font-size: 0.875rem; }

.profile-useful-links .useful-links a:last-child {
  margin-bottom: 0; }

.profile-useful-links .useful-links a > i {
  margin-right: 6px; }

.short-info {
  margin-bottom: 1.5rem; }

.short-info:last-child {
  margin: 0; }

.short-info .short-info-label {
  padding: 0;
  margin: 0;
  display: block;
  font-size: 0.75rem;
  color: #818a91;
  font-weight: 400; }

.short-info .short-info-title {
  padding: 0;
  margin: 0;
  display: block;
  font-size: 1rem;
  color: #2b2b2c;
  font-weight: 500; }

.short-info .short-info-subtitle {
  padding: 0;
  margin: 0;
  display: block;
  font-size: 1rem;
  color: #818a91;
  font-weight: 300; }

.short-info i {
  font-size: 36px;
  display: inline-block; }

.short-info .short-info-img {
  width: 50px; }

.short-info .short-info-img img {
  max-width: 100%;
  border-radius: 100%; }

.short-info .short-info-offset {
  display: inline-block;
  padding-left: 16px;
  margin-bottom: 15px;
  position: relative; }

.short-info:last-child .short-info-offset {
  margin-bottom: 0; }

.short-info .short-info-offset .btn {
  position: relative;
  float: right; }

/* SHOP DEFAULT - General styling for shop sections */
.shop-default-wrapper #divProductList {
  transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out; }

.shop-default-wrapper .shop-grid-section-header {
  height: 62px;
  padding: 15px;
  border-bottom: 1px solid #e0eded; }

.shop-default-wrapper .shop-grid-section-header .shop-grid-section-title {
  margin: 0;
  padding: 0 1rem;
  font-size: 1rem;
  font-family: "Lato", sans-serif;
  font-weight: 600;
  color: #111111; }

.shop-default-wrapper .shop-grid-section-footer {
  padding: 15px; }

.shop-default-wrapper .btn-view-mode {
  font-size: 20px;
  line-height: 1.6;
  margin-right: 0;
  color: #eceeef; }

.shop-default-wrapper .btn-view-mode.active {
  color: #2b2b2c; }

.shop-default-wrapper .aux-text {
  display: inline-block;
  font-family: "Roboto", sans-serif;
  font-weight: 600;
  font-size: 0.875rem;
  color: #2b2b2c; }

.shop-default-wrapper .btn-reset-filters {
  font-weight: 400; }

.shop-default-wrapper .btn-reset-filters > i {
  margin-right: 5px;
  font-size: 13px; }

.shop-default-wrapper .product .price-wrapper .price {
  font-size: 32px;
  color: #2b2b2c; }

.shop-default-wrapper .product .price-wrapper .price sup {
  font-size: 16px;
  top: -1em;
  font-weight: 400; }

.shop-default-wrapper .product .price-wrapper .price .price-value {
  margin: 0 3px; }

.shop-default-wrapper .product .price-wrapper .price.discount {
  text-decoration: none;
  margin-right: 8px; }

.shop-default-wrapper .product .price-wrapper .price.discount .price-value {
  margin: 0 1px;
  font-weight: 400;
  font-size: 24px;
  text-decoration: line-through; }

.shop-default-wrapper .product .price-wrapper .price.discount sup {
  font-size: 14px; }

.shop-default-wrapper .product .price-wrapper .price.discount sup.price-value-dec {
  text-decoration: line-through; }

.shop-default-wrapper .product .product-colors {
  margin-top: 12px;
  text-align: center;
  min-height: 44px; }

.shop-default-wrapper .product .product-colors .product-colors-label {
  display: block;
  margin-bottom: 4px;
  text-transform: uppercase;
  color: #2b2b2c;
  font-size: 0.75rem;
  font-weight: 500; }

.shop-default-wrapper .product .product-colors .color-switch a {
  display: inline-block;
  float: none !important;
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-bottom: 0;
  border-radius: 0.25rem; }

.shop-default-wrapper .product .product-actions {
  opacity: 0;
  width: 42px;
  position: absolute;
  left: 15px;
  bottom: 20px; }

.shop-default-wrapper .product .product-actions.in {
  opacity: 1; }

.shop-default-wrapper .product .product-actions .btn-product-action {
  display: block;
  width: 100%;
  height: 38px;
  line-height: 38px;
  margin-bottom: 6px;
  border: 0;
  border-radius: 38px;
  background: #eceeef;
  color: #2b2b2c;
  font-size: 18px;
  outline: 0; }

.shop-default-wrapper .product .product-actions .btn-product-action:hover {
  background: #0087be;
  color: #FFF;
  border-color: #177196; }

.shop-default-wrapper .product .product-actions .btn-product-action:active {
  border-color: transparent; }

.shop-default-wrapper .product .product-actions.animated {
  -webkit-animation-duration: 0.5s;
  -moz-animation-duration: 0.5s;
  -ms-animation-duration: 0.5s;
  -o-animation-duration: 0.5s;
  animation-duration: 0.5s; }

.shop-default-wrapper .filter-sidebar {
  padding: 30px; }

.shop-default-wrapper .filter-sidebar .filter-checkbox .checkbox {
  margin-bottom: 15px; }

.shop-default-wrapper .filter-sidebar .filter-checkbox .checkbox label {
  padding-left: 10px; }

.shop-default-wrapper .product figure {
  border: 0; }

.shop-default-wrapper .product .switch-on-hover img {
  display: none;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out; }

.shop-default-wrapper .product .switch-on-hover img.img-primary {
  display: block;
  opacity: 1; }

.shop-default-wrapper .product:hover .switch-on-hover img {
  display: block;
  opacity: 1; }

.shop-default-wrapper .product:hover .switch-on-hover img.img-primary {
  display: none;
  opacity: 0; }

/* SHOP CARDS */
.shop-cards-wrapper .product {
  margin: 0;
  padding: 1.5rem;
  background: #fff; }

.shop-cards-wrapper .product.product--style-2 {
  position: relative; }

.shop-cards-wrapper .product.product--style-2 .product-image {
  display: inline-block;
  float: left;
  width: 40%; }

.shop-cards-wrapper .product.product--style-2 .product-content {
  display: inline-block;
  width: 60%; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-info {
  position: absolute;
  right: 20px;
  bottom: 12px; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-info .price-wrapper {
  display: block;
  margin-bottom: 10px; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-icon-btn {
  display: inline-block;
  margin-right: 10px;
  font-size: 16px;
  color: #2b2b2c;
  font-weight: 500; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-icon-btn.active {
  color: #0087be; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-icon-btn:last-child {
  margin-right: 0; }

.shop-cards-wrapper .product.product--style-2 .product-content .product-icon-btn:hover {
  color: #0087be; }

.shop-cards-wrapper .product .product-title,
.shop-cards-wrapper .product .product-title a {
  color: #2b2b2c; }

.shop-cards-wrapper .product .product-description {
  color: #818a91;
  font-size: 0.875rem;
  line-height: 1.6; }

.shop-cards-wrapper .product .product-long-description {
  max-width: 360px;
  margin: 0 auto; }

.shop-cards-wrapper.shop-tech-wrapper .product-buttons .btn-cart {
  font-size: 0.625rem !important;
  padding: 0.625rem 0.875rem !important; }

.shop-cards-wrapper.shop-tech-wrapper .product-buttons .btn-icon {
  text-align: center;
  font-size: 20px;
  padding: 2px;
  color: #55595c;
  border: 0; }

.shop-cards-wrapper.shop-tech-wrapper .product-buttons .btn-icon:hover,
.shop-cards-wrapper.shop-tech-wrapper .product-buttons .btn-icon.active {
  color: #0087be; }

.shop-cards-wrapper .product .price-wrapper .price-sm {
  font-size: 24px; }

.shop-cards-wrapper .product .price-wrapper .price-sm.discount .price-value {
  font-size: 1rem;
  font-weight: 600; }

.shop-grid-wrapper .product {
  margin: 0;
  padding: 1.5rem; }

/* SHOP MINIMALIST */
.shop-minimalist-wrapper .product {
  border: 0;
  background: #eee; }

.shop-minimalist-wrapper .product .product-image {
  padding: 30px; }

.shop-minimalist-wrapper .product .product-image .mask {
  border-radius: 0.25rem; }

.shop-minimalist-wrapper .product .product-image .product-title {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-transform: none;
  margin: 30px 0; }

.shop-minimalist-wrapper .product .product-image .product-price-wrapper {
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%; }

.shop-minimalist-wrapper .product .product-image .product-price {
  font-weight: 400;
  color: white; }

.shop-minimalist-wrapper .product .product-image .product-price .price-value {
  font-size: 24px; }

.shop-minimalist-wrapper .product .product-image .product-price.discount {
  padding: 7px 0 0;
  margin-right: 4px;
  font-size: 0.875rem;
  font-weight: 400;
  color: #ff3b30;
  text-decoration: line-through; }

/* PRODUCT RIBBONS */
.product-ribbon {
  position: absolute;
  top: 15px; }

.product-ribbon.product-ribbon-left {
  left: 15px; }

.product-ribbon.product-ribbon-right {
  right: 15px; }

.product-ribbon.no-space {
  top: 0; }

.product-ribbon.no-space.product-ribbon-left {
  left: 0; }

.product-ribbon.no-space.product-ribbon-right {
  right: 0; }

.product-ribbon.product-ribbon--style-1 {
  width: 48px;
  height: 48px;
  border-radius: 100%;
  text-align: center;
  line-height: 48px;
  font-size: 11px;
  z-index: 200; }

/* SHOP - SWIPER CONTAINER */
.product-swiper-container {
  position: relative;
  padding-top: 20px; }

.product-swiper-container .swiper-slide img {
  max-width: 100%; }

.product-swiper-container .swiper-pagination {
  top: 0;
  bottom: auto;
  text-align: left; }

/* PRODUCT DESCRIPTION */
.product-description-wrapper .product-title {
  margin: 0;
  padding: 0;
  font-size: 1.25rem;
  font-weight: 600;
  font-family: "Lato", sans-serif;
  color: #2b2b2c; }

.product-description-wrapper .product-category-label {
  margin: 0 0 20px;
  padding: 0;
  font-weight: 400;
  color: #818a91; }

.product-description-wrapper .product-short-text {
  margin: 16px 0; }

.product-description-wrapper .product-price {
  margin: 16px 0;
  font-size: 36px;
  font-weight: 400;
  color: #2b2b2c; }

.product-description-wrapper .product-price.discount {
  padding: 7px 0 0;
  margin-right: 4px;
  font-size: 0.875rem;
  font-weight: 400;
  color: #ff3b30;
  text-decoration: line-through; }

.product-description-wrapper .product-short-info {
  margin: 30px 0; }

.product-description-wrapper .product-short-info span {
  display: block;
  padding: 4px; }

.product-description-wrapper .product-short-info strong {
  font-weight: 500;
  color: #818a91;
  margin-right: 5px; }

.product-description-wrapper .product-quantity .btn-cart,
.product-description-wrapper .product-quantity .btn-wishlist {
  height: 60px;
  margin-left: 15px; }

/* PLUS - MINUS CONTROL */
.spinner {
  width: 100px; }

.spinner input {
  text-align: right;
  padding: 16px 20px;
  height: 61px;
  border: 0;
  text-align: center;
  background: #f3f3f3; }

.input-group-btn-vertical {
  position: relative;
  white-space: nowrap;
  width: 1%;
  vertical-align: middle;
  display: table-cell; }

.input-group-btn-vertical > .btn {
  outline: 0 !important;
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
  height: 31px;
  width: 30px;
  border: 2px solid #f3f3f3;
  text-align: center;
  margin-left: -2px;
  position: relative;
  border-radius: 0; }

.input-group-btn-vertical > .btn:active,
.input-group-btn-vertical > .btn:focus {
  outline: 0 !important; }

.input-group-btn-vertical > .btn:hover {
  background-color: #0087be;
  color: #FFF; }

.input-group-btn-vertical > .btn:first-child {
  border-top-right-radius: 2px; }

.input-group-btn-vertical > .btn:last-child {
  margin-top: -2px;
  border-bottom-right-radius: 2px; }

.input-group-btn-vertical i {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -4.5px;
  margin-left: -4.5px;
  font-size: 9px; }

.input-group-btn-vertical i.icon {
  font-size: 14px;
  margin-top: -9px;
  margin-left: -3px; }

.c-base-1 {
  color: #0087be !important; }

.c-base-2 {
  color: #292f36 !important; }

.c-base-3 {
  color: #818a91 !important; }

.c-base-4 {
  color: #2B2B2B !important; }

.c-base-5 {
  color: #FFF !important; }

.c-base-text-1 {
  color: #FFF; }

.c-base-text-2 {
  color: #FFF; }

.c-base-text-3 {
  color: rgba(255, 255, 255, 0.9); }

.c-base-text-4 {
  color: #FFF; }

.c-base-text-5 {
  color: #0A0814; }

.c-gray-dark {
  color: #2b2b2c !important; }

.c-gray {
  color: #55595c !important; }

.c-gray-light {
  color: #818a91 !important; }

.c-gray-lighter {
  color: #eceeef !important; }

.c-gray-lightest {
  color: #f7f7f9 !important; }

.c-black {
  color: #000000 !important; }

.c-white {
  color: white !important; }

.c-blue {
  color: #007aff !important; }

.c-teal-blue {
  color: #5ac8fa !important; }

.c-green {
  color: #4cd964 !important; }

.c-red {
  color: #ff3b30 !important; }

.c-pink {
  color: #ff2d55 !important; }

.c-orange {
  color: #ff9500 !important; }

.c-yellow {
  color: #ffcc00 !important; }

.c-purple {
  color: #5856d6 !important; }

.c-twitter {
  color: #1da1f2; }

.c-facebook {
  color: #3b5998; }

.c-instagram {
  color: #e1306c; }

.c-skype {
  color: #00aff0; }

.no-bg {
  background: transparent !important; }

.bg-base-1 {
  background-color: #0087be !important;
  color: #FFF !important; }

.bg-base-1 .sct-inner *:not(.btn):not(.alert):not(.form-control):not(code) {
  color: #FFF !important; }

.bg-base-2 {
  background-color: #292f36 !important;
  color: #FFF !important; }

.bg-base-2 .sct-inner *:not(.btn):not(.alert):not(.form-control):not(code) {
  color: #FFF !important; }

.bg-base-3 {
  background-color: #818a91;
  color: rgba(255, 255, 255, 0.9); }

.bg-base-3 .heading,
.bg-base-3 a:not(.btn) {
  color: rgba(255, 255, 255, 0.9) !important; }

.bg-base-4 {
  background-color: #2B2B2B;
  color: #FFF; }

.bg-base-4 .heading,
.bg-base-4 a:not(.btn) {
  color: #FFF !important; }

.bg-base-5 {
  background-color: #FFF;
  color: #0A0814; }

.bg-base-5 .heading,
.bg-base-5 a:not(.btn) {
  color: #0A0814 !important; }

.bg-white {
  background-color: white;
  color: #333; }

.bg-gray-dark {
  background-color: #2b2b2c;
  color: #eceeef; }

.bg-gray-dark *:not(.btn) {
  color: #eceeef !important; }

.bg-gray-light {
  background-color: #818a91;
  color: #2b2b2c; }

.bg-gray-light *:not(.btn) {
  color: #2b2b2c !important; }

.bg-gray-lighter {
  background-color: #eceeef;
  color: #2b2b2c; }

.bg-gray-lighter *:not(.btn) {
  color: #2b2b2c !important; }

.bg-black {
  background-color: #000000;
  color: #FFF; }

.block-rainbow.bg-black *:not(.btn),
.bg-black .sct-inner *:not(.btn):not(.alert):not(.form-control):not(code) {
  color: #FFF; }

.bg-space-gray {
  background-color: #282d33;
  color: #FFF; }

.bg-space-gray *:not(.btn) {
  color: #FFF !important; }

.bg-blue {
  background-color: #007aff !important;
  color: #FFF; }

.bg-blue *:not(.btn) {
  color: #FFF !important; }

.bg-teal-blue {
  background-color: #5ac8fa !important;
  color: #FFF; }

.bg-teal-blue *:not(.btn) {
  color: #FFF !important; }

.bg-green {
  background-color: #4cd964 !important;
  color: #FFF; }

.bg-green *:not(.btn) {
  color: #FFF !important; }

.bg-red {
  background-color: #ff3b30 !important;
  color: #FFF; }

.bg-red *:not(.btn) {
  color: #FFF !important; }

.bg-pink {
  background-color: #ff2d55 !important;
  color: #FFF; }

.bg-pink *:not(.btn) {
  color: #FFF !important; }

.bg-orange {
  background-color: #ff9500 !important;
  color: #FFF; }

.bg-orange *:not(.btn) {
  color: #FFF !important; }

.bg-yellow {
  background-color: #ffcc00 !important;
  color: #FFF; }

.bg-yellow *:not(.btn) {
  color: #FFF !important; }

.bg-purple {
  background-color: #5856d6 !important;
  color: #FFF; }

.bg-purple *:not(.btn) {
  color: #FFF !important; }

.sct-color-1 {
  background-color: #FFF; }

.sct-color-2 {
  background-color: #fcfcfc; }

.sct-color-3 {
  background-color: #282d33; }

.sct-color-3 .sct-inner *:not(.btn):not(.alert):not(.form-control):not(code) {
  color: #d7d2cc; }

.sct-color-4 {
  background-color: #121416; }

.sct-color-4 .sct-inner *:not(.btn):not(.alert):not(.form-control):not(code) {
  color: #edebe9; }

.bg-gradient-1, .mask-gradient-1--style-1, .mask-gradient-1--style-2 {
  background: linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  background: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -o-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -ms-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -moz-linear-gradient(left, #e9168c 2%, #f75254 99%);
  border-image: -webkit-linear-gradient(left, #e9168c 2%, #f75254 99%); }

.bg-gradient-blue-pink {
  background-color: #E55D87;
  background-color: -webkit-linear-gradient(to left, #E55D87, #5FC3E4);
  background-color: linear-gradient(to left, #E55D87, #5FC3E4); }

.bg-gradient-electric-red {
  background-color: #D31027;
  background-color: -webkit-linear-gradient(to left, #D31027, #EA384D);
  background-color: linear-gradient(to left, #D31027, #EA384D); }

[class^="space-"] {
  display: block; }

.space-xs-sm {
  margin-bottom: 1.5rem; }

.space-xs-md {
  margin-bottom: 2.5rem; }

.space-xs-lg {
  margin-bottom: 3.5rem; }

.space-xs-xl {
  margin-bottom: 4.5rem; }

@media (min-width: 768px) {
  .space-sm-sm {
    margin-bottom: 1.5rem; }

  .space-sm-md {
    margin-bottom: 2.5rem; }

  .space-sm-lg {
    margin-bottom: 3.5rem; }

  .space-sm-xl {
    margin-bottom: 4.5rem; } }
@media (min-width: 992px) {
  .space-md-sm {
    margin-bottom: 1.5rem; }

  .space-md-md {
    margin-bottom: 2.5rem; }

  .space-md-lg {
    margin-bottom: 3.5rem; }

  .space-md-xl {
    margin-bottom: 4.5rem; } }
@media (min-width: 1201px) {
  .space-lg-sm {
    margin-bottom: 1.5rem; }

  .space-lg-md {
    margin-bottom: 2.5rem; }

  .space-lg-lg {
    margin-bottom: 3.5rem; }

  .space-lg-xl {
    margin-bottom: 4.5rem; } }
@media (max-width: 767px) {
  .space-xs-only-1 {
    clear: both;
    margin-bottom: 1rem; }

  .space-xs-only-2 {
    clear: both;
    margin-bottom: 2rem; }

  .space-xs-only-3 {
    clear: both;
    margin-bottom: 3rem; } }
@media (min-width: 768px) and (max-width: 991px) {
  .space-sm-only-1 {
    clear: both;
    margin-bottom: 1rem; }

  .space-sm-only-2 {
    clear: both;
    margin-bottom: 2rem; }

  .space-sm-only-3 {
    clear: both;
    margin-bottom: 3rem; } }
@media (min-width: 992px) and (max-width: 1200px) {
  .space-md-only-1 {
    clear: both;
    margin-bottom: 1rem; }

  .space-md-only-2 {
    clear: both;
    margin-bottom: 2rem; }

  .space-md-only-3 {
    clear: both;
    margin-bottom: 3rem; } }
@media (min-width: 1201px) {
  .space-lg-only-1 {
    clear: both;
    margin-bottom: 1rem; }

  .space-lg-only-2 {
    clear: both;
    margin-bottom: 2rem; }

  .space-lg-only-3 {
    clear: both;
    margin-bottom: 3rem; } }
.no-margin {
  margin: 0 !important; }

.mt-0 {
  margin-top: 0 !important; }

@media (min-width: 992px) {
  .mt--1 {
    margin-top: -1rem !important; }

  .mt--2 {
    margin-top: -2rem !important; }

  .mt--3 {
    margin-top: -3rem !important; }

  .mr--1 {
    margin-right: -1rem !important; }

  .mr--2 {
    margin-right: -2rem !important; }

  .mr--3 {
    margin-right: -3rem !important; }

  .mb--1 {
    margin-bottom: -1rem !important; }

  .mb--2 {
    margin-bottom: -2rem !important; }

  .mb--3 {
    margin-bottom: -3rem !important; }

  .ml--1 {
    margin-left: -1rem !important; }

  .ml--2 {
    margin-left: -2rem !important; }

  .ml--3 {
    margin-left: -3rem !important; }

  .mt-150 {
    margin-top: 150px !important; }

  .mb-150 {
    margin-bottom: 150px !important; }

  .mt-300 {
    margin-top: 300px !important; }

  .mb-300 {
    margin-bottom: 300px !important; }

  .mt--150 {
    margin-top: -150px !important; }

  .mb--150 {
    margin-bottom: -150px !important; }

  .mt--300 {
    margin-top: -300px !important; }

  .mb--300 {
    margin-bottom: -300px !important; } }
.no-padding {
  padding: 0 !important; }

.pt-0 {
  padding-top: 0 !important; }

@media (min-width: 992px) {
  .pb-150 {
    padding-bottom: 150px; } }
.top-10vh {
  top: 10vh; }

.text-uppercase {
  text-transform: uppercase !important; }

.text-capitalize {
  text-transform: capitalize !important; }

.text-normal {
  text-transform: none !important; }

.text-line-through {
  text-decoration: line-through; }

.text-underline {
  text-decoration: underline; }

.font-blzee {
  font-family: "Blzee", sans-serif !important; }

.font-custom-1 {
  font-family: "Sue Ellen Francisco", sans-serif !important; }

.font-custom-2 {
  font-family: "Pacifico", sans-serif !important; }

.font-custom-3 {
  font-family: "YellowTail", sans-serif !important; }

.font-custom-4 {
  font-family: "Slabo 27px", sans-serif !important; }

.no-border {
  border: 0 !important; }

.b-xs-all {
  border: 1px solid #e0eded; }

.b-xs-top {
  border-top: 1px solid #e0eded; }

.b-xs-right {
  border-right: 1px solid #e0eded; }

.b-xs-bottom {
  border-bottom: 1px solid #e0eded; }

.b-xs-left {
  border-left: 1px solid #e0eded; }

@media (min-width: 768px) {
  .b-md-all {
    border: 1px solid #e0eded; }

  .b-md-top {
    border-top: 1px solid #e0eded; }

  .b-md-right {
    border-right: 1px solid #e0eded; }

  .b-md-bottom {
    border-bottom: 1px solid #e0eded; }

  .b-md-left {
    border-left: 1px solid #e0eded; } }
@media (min-width: 992px) {
  .b-lg-all {
    border: 1px solid #e0eded; }

  .b-lg-top {
    border-top: 1px solid #e0eded; }

  .b-lg-right {
    border-right: 1px solid #e0eded; }

  .b-lg-bottom {
    border-bottom: 1px solid #e0eded; }

  .b-lg-left {
    border-left: 1px solid #e0eded; } }
.no-radius {
  border-radius: 0 !important; }

.sct-shadow-top-1 {
  background-image: url("../images/shadows/shadow-1.png");
  background-repeat: no-repeat;
  background-position: top center; }

.sct-shadow-bottom-1 {
  background-image: url("../images/shadows/shadow-1.png");
  background-repeat: no-repeat;
  background-position: bottom center; }

.strong {
  font-weight: bold !important; }

.strong-100 {
  font-weight: 100 !important; }

.strong-200 {
  font-weight: 200 !important; }

.strong-300 {
  font-weight: 300 !important; }

.strong-400 {
  font-weight: 400 !important; }

.strong-500 {
  font-weight: 500 !important; }

.strong-600 {
  font-weight: 600 !important; }

.strong-700 {
  font-weight: 700 !important; }

/* LINE SPACING */
.ls-1 {
  letter-spacing: 1px !important; }

.ls-2 {
  letter-spacing: 2px !important; }

.ls-3 {
  letter-spacing: 3px !important; }

.ls-4 {
  letter-spacing: 4px !important; }

.ls-5 {
  letter-spacing: 5px !important; }

.line-height-1_6 {
  line-height: 1.6 !important; }

.line-height-1_8 {
  line-height: 1.8 !important; }

.text-italic {
  font-style: italic !important; }

.z-depth--removed {
  border-width: 1px !important;
  box-shadow: none !important; }

.z-depth-1:not(.btn),
.z-depth-1--hover:not(.btn):hover,
.z-depth-1-top:not(.btn),
.z-depth-1-top--hover:not(.btn):hover,
.z-depth-1-bottom:not(.btn),
.swiper-slide > .block--style-4-v1:not(.btn),
.z-depth-1-bottom--hover:not(.btn):hover,
.z-depth-2:not(.btn),
.z-depth-2--hover:not(.btn):hover,
.z-depth-2-top:not(.btn),
.z-depth-2-top--hover:not(.btn):hover,
.z-depth-2-bottom:not(.btn),
.z-depth-2-bottom--hover:not(.btn):hover,
.z-depth-3:not(.btn),
.z-depth-3--hover:not(.btn):hover,
.z-depth-3-top:not(.btn),
.z-depth-3-top--hover:not(.btn):hover,
.z-depth-3-bottom:not(.btn),
.z-depth-3-bottom--hover:not(.btn):hover,
.z-depth-4:not(.btn),
.z-depth-4--hover:not(.btn):hover,
.z-depth-4-top:not(.btn),
.z-depth-4-top--hover:not(.btn):hover,
.z-depth-4-bottom:not(.btn),
.z-depth-4-bottom--hover:not(.btn):hover,
.z-depth-5:not(.btn),
.z-depth-5--hover:not(.btn):hover,
.z-depth-5-top:not(.btn),
.z-depth-5-top--hover:not(.btn):hover,
.z-depth-5-bottom:not(.btn),
.z-depth-5-bottom--hover:not(.btn):hover {
  border-width: 0 !important; }

.z-depth-0,
.z-depth-0--hover:hover {
  box-shadow: none !important; }

.z-depth-1,
.z-depth-1--hover:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12); }

.z-depth-1-top,
.z-depth-1-top--hover:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12); }

.z-depth-1-bottom, .swiper-slide > .block--style-4-v1,
.z-depth-1-bottom--hover:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16); }

.z-depth-2,
.z-depth-2--hover:hover {
  box-shadow: 0 8px 17px rgba(0, 0, 0, 0.2), 0 6px 20px rgba(0, 0, 0, 0.19); }

.z-depth-2-top,
.z-depth-2-top--hover:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.19); }

.z-depth-2-bottom,
.z-depth-2-bottom--hover:hover {
  box-shadow: 0 8px 17px rgba(0, 0, 0, 0.2); }

.z-depth-3,
.z-depth-3--hover:hover {
  box-shadow: 0 12px 15px rgba(0, 0, 0, 0.24), 0 17px 50px rgba(0, 0, 0, 0.19); }

.z-depth-3-top,
.z-depth-3-top--hover:hover {
  box-shadow: 0 17px 50px rgba(0, 0, 0, 0.19); }

.z-depth-3-bottom,
.z-depth-3-bottom--hover:hover {
  box-shadow: 0 12px 15px rgba(0, 0, 0, 0.24); }

.z-depth-4,
.z-depth-4--hover:hover {
  box-shadow: 0 16px 28px rgba(0, 0, 0, 0.22), 0 25px 55px rgba(0, 0, 0, 0.21); }

.z-depth-4-top,
.z-depth-4-top--hover:hover {
  box-shadow: 0 25px 55px rgba(0, 0, 0, 0.21); }

.z-depth-4-bottom,
.z-depth-4-bottom--hover:hover {
  box-shadow: 0 16px 28px rgba(0, 0, 0, 0.22); }

.z-depth-5,
.z-depth-5--hover:hover {
  box-shadow: 0 27px 24px rgba(0, 0, 0, 0.2), 0 40px 77px rgba(0, 0, 0, 0.22); }

.z-depth-5-top,
.z-depth-5-top--hover:hover {
  box-shadow: 0 40px 77px rgba(0, 0, 0, 0.22); }

.z-depth-5-bottom,
.z-depth-5-bottom--hover:hover {
  box-shadow: 0 27px 24px rgba(0, 0, 0, 0.2); }

.rounded {
  border-radius: 0.25rem; }

.img-center, .gallery-container .gallery-top .swiper-slide img, .gallery-container .gallery-thumbs .swiper-slide img, .product-swiper-container .swiper-slide img {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.img-circle {
  border-radius: 50% !important; }

.relative {
  position: relative; }

.overflow--visible {
  overflow: visible !important; }

.overflow--hidden {
  overflow: hidden !important; }

body {
  background: #fff; }

.body-wrap {
  position: relative;
  z-index: 0;
  background: #FFF; }

.body-wrap.body-boxed {
  margin: 20px auto;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15); }

.body-wrap.body-boxed--no-margin {
  margin: 0 auto;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15); }

.body-wrap:after,
.body-wrap:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  height: 260px; }

.body-wrap:after {
  top: auto;
  bottom: 0; }

@media (min-width: 1200px) {
  .body-wrap.body-boxed {
    width: 1230px; } }
@media (min-width: 992px) and (max-width: 1199px) {
  .body-wrap.body-boxed {
    width: 1000px; } }
.body-bg-1 {
  background: url("../images/patterns/pattern-1.png");
  background-repeat: repeat; }

.body-bg-2 {
  background: url("../images/patterns/pattern-2.png");
  background-repeat: repeat; }

.body-bg-3 {
  background: url("../images/patterns/pattern-3.png");
  background-repeat: repeat; }

.same-height {
  min-height: 400px; }

.page-title {
  position: relative; }

.page-title .breadcrumb {
  display: inline-block;
  background: transparent;
  float: none;
  padding: 0;
  margin: 0; }

.page-title .breadcrumb li,
.page-title .breadcrumb li > a {
  font-weight: 400;
  font-size: 0.75rem;
  text-transform: uppercase; }

.page-title .breadcrumb--style-1 li,
.page-title .breadcrumb--style-1 li a,
.page-title .breadcrumb--style-1 li a:hover,
.page-title .breadcrumb--style-1 li.active a {
  color: #eceeef; }

.page-title .breadcrumb--style-2 li,
.page-title .breadcrumb--style-2 li a,
.page-title .breadcrumb--style-2 li a:hover,
.page-title .breadcrumb--style-2 li.active a {
  color: #2b2b2c; }

.page-title-wrapper {
  position: relative; }

.page-title-wrapper .page-title-scroll-down {
  position: absolute;
  bottom: -18px;
  left: 50%;
  margin-left: -18px;
  display: block;
  width: 36px;
  height: 36px;
  line-height: 28px;
  text-align: center;
  background-color: inherit;
  border-radius: 100%;
  color: #818a91;
  font-size: 12px;
  z-index: 100; }

.page-title.page-title--style-1 {
  padding: 1.5rem 0;
  background: #fcfcfc;
  color: #333;
  border-top: 1px solid #e0eded;
  border-bottom: 1px solid #e0eded; }

.page-title.page-title--style-1 h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 100%;
  letter-spacing: 0;
  color: #333; }

.page-title.page-title--style-1 .breadcrumb {
  margin-bottom: 5px; }

.page-title.page-title--style-2 {
  padding: 12rem 0;
  color: #333;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover; }

.page-title.page-title--style-2 h2 {
  display: block;
  padding: 10px 0;
  font-family: "Lato", sans-serif; }

.page-title.page-title--style-2 .breadcrumb {
  display: inline-block;
  float: none;
  padding: 4px 8px; }

.page-title.page-title--style-2 .breadcrumb li,
.page-title.page-title--style-2 .breadcrumb li > a {
  font-size: 0.75rem;
  font-weight: 500; }

.page-title.page-title--style-2 .nav {
  position: relative;
  top: 30px;
  margin-top: 0;
  left: 0; }

.page-title.page-title--style-3 {
  padding: 30px 0;
  text-align: center; }

.page-title.page-title--style-3 h2 {
  display: block;
  text-align: center;
  padding: 10px 0;
  font-size: 1.5rem;
  color: white; }

.page-title.page-title--style-3 .breadcrumb {
  float: none;
  padding: 10px 0; }

.page-title.page-title--style-4 {
  position: relative;
  padding: 4rem 0;
  background: transparent;
  color: #333;
  border-color: #e0eded;
  background-position: 50 0; }

.page-title.page-title--style-4 .breadcrumb {
  padding: 8px 0; }

.page-title.page-title--style-4 .heading-md {
  padding: 8px 0; }

@media only screen and (max-width: 767px) {
  .page-title {
    text-align: center; }

  .page-title h2 {
    padding: 18px 0 0; }

  .page-title .breadcrumb {
    float: none;
    padding: 18px 0; }

  .page-title.page-title--style-4 .sorting-options {
    text-align: center;
    margin-top: 20px; } }
@media (min-width: 992px) {
  .page-title.page-title--style-1 .row {
    display: -ms-flexbox;
    -ms-flex-pack: center;
    -ms-flex-align: center;
    display: -moz-box;
    -moz-box-pack: center;
    -moz-box-align: center;
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    display: box;
    box-pack: center;
    box-align: center; } }
.cta-wrapper p {
  margin: 0; }

.cta-wrapper--over {
  position: absolute;
  top: 0;
  left: 50%;
  right: auto;
  width: 910px;
  height: 140px;
  margin-top: -70px;
  margin-left: -455px;
  z-index: 1000; }

.has-cta-over {
  position: relative;
  margin-top: 100px; }

@media (max-width: 991px) {
  .cta-wrapper--over {
    position: static;
    width: 100%;
    height: auto;
    left: 0;
    margin: 0;
    border-radius: 0; }

  .has-cta-over {
    margin-top: 0; } }
.cta-wrapper > a {
  display: block;
  text-underline: none; }

.cta-wrapper > a > .cta-inner {
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.cta-wrapper > a:hover > .bg-base-1 {
  background: #177196 !important; }

.cta-wrapper > a:hover > .bg-base-2 {
  background: #c71818 !important; }

.has-bg-cover {
  position: relative; }

.bg-size-cover {
  background-size: cover;
  background-repeat: no-repeat; }

.bg-size-contain {
  background-size: cover;
  background-repeat: no-repeat; }

.cover-caption {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center; }

.cover-caption-inner {
  position: relative;
  z-index: 600;
  padding: 2rem; }

.parallax-section {
  position: relative;
  overflow-x: hidden;
  background-position: center top;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover; }

.parallax-section {
  padding-top: 2rem;
  padding-bottom: 2rem; }

.parallax-section-lg {
  padding-top: 4rem;
  padding-bottom: 4rem; }

.parallax-section-xl {
  padding-top: 6rem;
  padding-bottom: 6rem; }

.text-cover-wrapper {
  padding: 80px 0; }

.text-cover-title {
  margin: 0;
  padding: 0;
  font-size: 10rem;
  font-weight: 700;
  line-height: 1; }

.text-cover-subtitle {
  margin: 0;
  padding: 0;
  font-size: 3rem;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1; }

.text-cover-p {
  width: 480px;
  font-size: 1rem; }

@media (max-width: 991px) {
  .text-cover-title {
    font-size: 8rem; }

  .text-cover-subtitle {
    font-size: 2rem; }

  .text-cover-p {
    width: 100%; } }
.quote-info .quote-info-img {
  border-radius: 100%;
  display: inline-block;
  max-width: 100px;
  padding: 5px;
  border: 1px solid transparent;
  background: transparent; }

.quote-info .quote-info-img > img {
  max-width: 100%;
  border-radius: 100%; }

.quote-info-light .quote-info-img {
  border-color: #eceeef; }

.quote-info-dark .quote-info-img {
  border-color: #2b2b2c; }

.floating-quote-holder {
  height: 500px; }

.floating-quote-wrapper {
  width: 510px;
  height: 390px;
  position: absolute;
  padding: 4rem 5rem;
  z-index: 10;
  border-radius: 0.25rem; }

.floating-quote-wrapper-lg {
  width: 720px; }

.floating-quote-wrapper .quote-logo {
  max-width: 160px; }

.floating-quote-wrapper.top-left {
  left: 13%;
  top: -60px; }

.floating-quote-wrapper.top-right {
  right: 13%;
  top: -60px; }

.floating-quote-wrapper.bottom-right {
  right: 13%;
  bottom: -60px; }

.floating-quote-wrapper.bottom-center {
  left: 50%;
  transform: translateX(-50%);
  bottom: -60px; }

.floating-quote-wrapper.bottom-left {
  left: 13%;
  top: -60px; }

@media (max-width: 991px) {
  .floating-quote-wrapper {
    width: 90%;
    left: 5%;
    right: auto; } }
.client-logo {
  padding: 1rem; }

.client-logo img {
  max-width: 100%; }

.client-logo--style-1 {
  background: #FFF;
  border-radius: 0.25rem; }

.client-logo--style-2 {
  background: #818a91;
  border-radius: 0.25rem; }

.client-logo--style-3 {
  background: transparent; }

.client-logo--style-4 {
  background: transparent;
  border-radius: 0.25rem;
  border: 1px solid #e0eded; }

.client-logo--style-4:hover {
  border-color: #d0e4e4; }

@media (max-width: 991px) {
  .client-logo {
    margin-bottom: 20px; } }
.single-brand-logo {
  width: 20%;
  float: left;
  overflow: hidden;
  padding: 30px;
  border-bottom: 1px solid rgba(243, 243, 243, 0.7);
  border-right: 1px solid rgba(243, 243, 243, 0.7); }

.single-brand-logo:nth-child(5n) {
  border-right: 0 none; }

.single-brand-logo:nth-child(6n),
.single-brand-logo:nth-child(7n),
.single-brand-logo:nth-child(8n),
.single-brand-logo:nth-child(9n),
.single-brand-logo:nth-child(10n) {
  border-bottom: 0 none; }

.brand-logo-outer {
  width: 100%;
  height: 100px;
  display: table; }

.brand-logo-inner {
  display: table-cell;
  vertical-align: middle; }

.single-brand-logo img {
  max-width: 120px;
  position: relative;
  cursor: pointer; }

@media (max-width: 991px) {
  .single-brand-logo {
    width: 50%; } }
.floating-content {
  position: relative;
  z-index: 100;
  padding: 2rem; }

.floating-content-fixed {
  height: 300px; }

@media (max-width: 767px) {
  .floating-content {
    box-shadow: none !important; } }
@media (min-width: 992px) {
  .floating-content.float-top {
    top: -100px; }

  .floating-content.float-right {
    right: -100px; }

  .floating-content.float-bottom {
    bottom: -100px; }

  .floating-content.float-left {
    left: -100px; } }
.caption-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center; }

.caption-wrapper > .caption-aligner {
  width: 100%; }

.caption-wrapper > .caption-aligner > .caption {
  width: 70%;
  margin: auto; }

.caption-wrapper > .caption-aligner > .caption.caption--center {
  flex: 1;
  word-wrap: break-word; }

.caption-wrapper > .caption-aligner > .caption.caption--fluid {
  width: 100%; }

.caption-wrapper > .caption-aligner > .caption.caption--style-1 {
  background: #FFF;
  padding: 46px; }

.overlayed-form--style-1 {
  padding: 2rem; }

@media (min-width: 992px) {
  .overlayed-form--style-1 {
    position: absolute;
    top: 0;
    left: 120px;
    width: 380px;
    height: 100%;
    background: rgba(255, 255, 255, 0.9); }

  .overlayed-form--style-2 {
    position: relative; } }
@media (max-width: 991px) {
  .overlayed-form--style-2 {
    margin: 0 !important; } }
.search-page-wrapper {
  width: 760px;
  margin: 50px auto;
  position: relative;
  z-index: 10; }

.search-widget {
  position: relative; }

@media (min-width: 992px) {
  .search-widget .form-group {
    margin: 0; } }
.search-widget .form-control:hover {
  border-color: #e0eded; }

.search-widget .form-control:focus {
  border-color: #0087be; }

.search-widget button.btn-inner {
  position: absolute;
  top: 0;
  right: 20px;
  background: transparent;
  border: medium none;
  width: 43px;
  height: 52px;
  line-height: 35px;
  text-align: center; }

.search-widget--style-1 .form-control:not(.bootstrap-select),
.search-widget--style-1 select.form-control-lg:not([size]):not([multiple]) {
  border-radius: 0.25rem;
  height: 62px; }

.search-widget--style-1 .form-control > .dropdown-toggle {
  border-radius: 0.25rem; }

.search-widget--style-1 button.btn {
  height: 62px;
  line-height: 35px; }

.search-widget--style-2 .form-control {
  border-radius: 0.25rem;
  padding: 22px;
  background: #FFF;
  -webkit-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.2);
  -moz-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.2); }

.search-widget--style-2 button.btn-inner {
  width: 43px;
  height: 62px;
  line-height: 35px; }

.search-widget--style-2 button.btn-inner:hover {
  color: #0087be; }

.search-widget--style-3 .form-control {
  height: 54px;
  border: 0;
  border-radius: 30px;
  padding-left: 20px;
  padding-right: 20px;
  background: rgba(255, 255, 255, 0.8);
  color: rgba(0, 0, 0, 0.5);
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

.search-widget--style-3.form-base-1 .form-control {
  border: 0; }

.search-widget--style-3 .form-control:focus {
  background: white; }

.search-widget--style-3 button.btn-inner {
  width: 43px;
  height: 54px;
  line-height: 35px; }

.search-widget--style-4 {
  margin: 0;
  border-bottom: 1px solid #e0eded; }

.search-widget--style-4 form {
  position: relative; }

.search-widget--style-4 .form-control {
  height: 94px;
  border: 0;
  border-radius: 0;
  padding: 0 90px 0 0;
  font-size: 1.25rem; }

.search-widget--style-4 button.btn-inner {
  right: 0;
  height: 94px;
  line-height: 94px;
  color: #818a91; }

.search-widget--style-4 .bar:after,
.search-widget--style-4 .bar:before {
  height: 1px; }

.search-widget--style-4 .form-control::-webkit-input-placeholder {
  color: #818a91; }

.search-widget--style-4 .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #818a91; }

.search-widget--style-4 .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #818a91; }

.form-default .form-control:-ms-input-placeholder {
  color: #818a91; }

@media (max-width: 991px) {
  .search-page-wrapper {
    width: 100%;
    padding: 0 20px; } }
.card-advanced-search {
  background: white; }

.advanced-search-visible {
  position: relative; }

.advanced-search-hidden {
  display: none; }

.advanced-search-hidden.in {
  display: block; }

.btn-advanced-search-open {
  display: block;
  width: 48px;
  height: 40px;
  position: absolute;
  left: 50%;
  bottom: 0;
  margin-left: -24px;
  text-align: center;
  line-height: 40px;
  background: #f2f2f2;
  color: #818a91;
  border-radius: 0.25rem 0.25rem 0 0; }

.listing-wrapper--style-1 {
  padding: 2rem 0;
  border-top: 1px solid #e0eded; }

@media (max-width: 1200px) {
  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
    height: 100%;
    z-index: 500; } }
.sidebar-inverse p {
  color: rgba(255, 255, 255, 0.5); }

.sidebar-inverse .heading {
  color: white; }

.sidebar-inverse .heading-light {
  color: rgba(255, 255, 255, 0.8); }

.sidebar-inverse .stats-entry .stats-count {
  color: white; }

.sidebar-inverse .stats-entry .stats-label {
  color: rgba(255, 255, 255, 0.8); }

.sidebar-inverse .useful-links a {
  color: rgba(255, 255, 255, 0.8); }

.sidebar-inverse .useful-links a:hover {
  color: white; }

.sidebar--style-1 {
  background: #fafafa;
  border: 1px solid #e0eded;
  border-radius: 0.25rem;
  padding: 1.5rem; }

.sidebar--style-2 {
  background: #eceeef;
  border-radius: 0.25rem;
  padding: 2rem; }

.sidebar--style-3 {
  background: #fcfcfc;
  border: 1px solid #e0eded;
  border-radius: 0.25rem;
  padding: 2rem; }

.sidebar-object {
  margin-bottom: 3rem; }

.sidebar--style-1 .sidebar-object {
  margin-bottom: 2rem;
  padding-bottom: 2rem; }

.sidebar--style-1 .sidebar-object.has-bb {
  border-bottom: 1px solid #e1e1e1; }

.sidebar--style-1 .sidebar-object:last-child {
  border: 0;
  padding: 0;
  margin: 0; }

.sidebar-object .section-title {
  margin-bottom: 1rem; }

.sidebar-object-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #111111;
  margin-bottom: 1rem;
  margin-top: 0;
  padding: 0;
  font-family: "Lato", sans-serif; }

.sidebar .sidebar-search-widget {
  position: relative; }

.sidebar .sidebar-search-widget .form-control {
  padding-right: 46px; }

.sidebar .sidebar-search-widget button {
  position: absolute;
  top: 0;
  right: 0;
  background: transparent;
  border: medium none;
  width: 46px;
  height: 100%;
  line-height: 35px;
  text-align: center; }

.sidebar .sidebar-search-widget button:hover {
  color: #0087be; }

.tagcloud a {
  display: inline-block;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  padding: 4px 8px;
  margin: 0 4px 4px 0;
  background: #0087be;
  color: #FFF;
  border-radius: 0.2rem;
  -webkit-transition: background 0.2s linear;
  -moz-transition: background 0.2s linear;
  -ms-transition: background 0.2s linear;
  transition: background 0.2s linear; }

.tagcloud a:hover {
  background: #e84444; }

.tagcloud--style-1,
.tagcloud--style-2 {
  background: transparent; }

.tagcloud--style-1 a {
  background: #eceeef;
  color: #818a91;
  padding: 8px 10px; }

.tagcloud--style-1 a:hover {
  background: #0087be;
  color: #FFF; }

.tagcloud--style-2 a {
  float: none;
  display: inline-block;
  margin: 0 20px 20px 0;
  background: transparent;
  border: 1px solid #2b2b2c;
  color: #2b2b2c;
  padding: 8px 12px; }

.tagcloud--style-2 a:hover {
  background: #2b2b2c;
  color: #eceeef; }

.tagcloud--style-3 a {
  float: none;
  display: inline-block;
  margin: 0 20px 20px 0;
  background: transparent;
  border: 1px solid #eceeef;
  color: #eceeef;
  padding: 8px 12px; }

.tagcloud--style-3 a:hover {
  background: #eceeef;
  color: #2b2b2c; }

.photostream:after,
.photostream:before {
  content: "";
  display: table; }

.photostream:after {
  clear: both; }

.photostream .photo-wrapper > a {
  display: block;
  width: calc(100% * 0.25 - 3px + 1px);
  float: left;
  margin-right: 3px;
  margin-bottom: 3px; }

.photostream .photo-wrapper:nth-child(4n) > a {
  width: calc(100% * 0.25 - 3px);
  margin-right: 0; }

.photostream .photo-wrapper > a > img {
  width: 100%; }

.photostream .photo-wrapper img:hover {
  box-shadow: 0 0 0 3px #0087be; }

.contacts:not(.c-profile) {
  padding: 0 8px; }

.contacts > [class*="col-"] {
  padding: 0 10px; }

.contacts .contact-entry {
  border: 1px solid rgba(243, 243, 243, 0.7);
  border-radius: 2px;
  margin-bottom: 24px;
  background: #fff; }

.contacts .contact-entry .contact-entry-image {
  display: block; }

.contacts .contact-entry .contact-entry-image img {
  width: 100%;
  border-radius: 2px 2px 0 0; }

.contacts .contact-entry-image {
  margin: -1px -1px 0; }

.contacts .contact-info {
  text-align: center;
  margin-top: 15px;
  padding: 0 5px; }

.contacts .contact-info strong {
  color: #2b2b2c;
  font-family: "Lato", sans-serif;
  font-size: 0.875rem;
  font-weight: 500; }

.contacts .contact-info small {
  color: #818a91;
  font-size: 0.75rem;
  margin-top: 3px; }

.contacts .contact-info small,
.contacts .contact-info strong {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block; }

.contacts .contact-footer {
  border-top: 1px solid rgba(243, 243, 243, 0.7);
  margin-top: 18px; }

.contacts .contact-footer > button {
  padding: 4px 10px 3px;
  display: block;
  width: 100%;
  text-align: center;
  color: #818a91;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  background: transparent;
  border: 0;
  outline: 0; }

.contacts .contact-footer > button:hover {
  background: #0087be;
  color: #FFF; }

.contacts .contact-footer > button:active {
  background: #c71818;
  color: #FFF; }

.contacts .contact-footer > button > i {
  font-size: 0.75rem;
  vertical-align: middle;
  margin-top: -2px;
  margin-right: 3px; }

.listview {
  position: relative; }

.listview:not(.listview-lg):not(.listview-message) .listview-item {
  padding: 1rem 0; }

.listview.listview-lg .listview-item:hover {
  background-color: #FFFFDB; }

.listview .listview-item {
  position: relative;
  display: block;
  -webkit-transition: background-color;
  -o-transition: background-color;
  transition: background-color;
  -webkit-transition-duration: 300ms;
  transition-duration: 300ms; }

.listview .listview-item .listview-small {
  font-size: 12px;
  color: #A9A9A9;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%; }

.listview .listview-item .checkbox,
.listview .listview-item.media {
  margin: 0; }

.listview .listview-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block; }

.listview a.listview-item:hover {
  background: #ECF9FF; }

.listview [class*="listview-img"] {
  border-radius: 50%; }

.listview .listview-img {
  width: 48px;
  height: 48px; }

.listview .listview-img-sm {
  width: 35px;
  height: 35px; }

.listview.listview-bordered .listview-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0; }

.listview .listview-attrs {
  list-style: none;
  padding: 0;
  margin: 5px 0 0; }

.listview .listview-attrs > li {
  display: inline-block;
  padding: 2px 10px 3px;
  font-size: 12px;
  margin-top: 5px;
  margin-right: 2px; }

.listview .listview-attrs > li:not(.info):not(.primary):not(.warning):not(.danger) {
  border: 1px solid #e0eded;
  background: transparent;
  color: #818a91; }

.listview .listview-attrs > li > a {
  display: block; }

.listview:not(.listview-message) .listview-title {
  color: #2b2b2c; }

.feature-badge > .feature-badge-value {
  display: block;
  font-size: 3rem;
  font-weight: 600;
  color: #2b2b2c; }

.feature-badge > .feature-badge-value > .feature-badge-subtext {
  font-size: 1.5rem;
  font-weight: 400;
  color: #2b2b2c; }

.feature-badge > .feature-badge-text {
  display: block;
  font-size: 0.875rem; }

.map-canvas {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 0.25rem; }

.map-canvas.style-1 {
  border-radius: 0.25rem; }

.map-canvas .info-window-content {
  min-width: 250px; }

.map-canvas .info-window-content h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px; }

.map-canvas .info-window-content h3 {
  font-size: 14px;
  font-weight: 500; }

.map-canvas .info-window-content p {
  margin-top: 20px;
  text-align: center;
  font-size: 12px;
  color: #999;
  text-shadow: none; }

.map-canvas-square {
  height: 200px; }

@media only screen and (min-width: 768px) {
  .map-canvas {
    height: 300px; } }
@media only screen and (min-width: 1170px) {
  .map-canvas {
    height: 400px; } }
.map-container {
  position: relative; }

.map-container address {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  padding: 20px;
  background-color: fade(#0087be, 80%);
  color: #FFF; }

@media only screen and (min-width: 768px) {
  .map-container address {
    text-align: center; } }
#map-zoom-in,
#map-zoom-out {
  height: 32px;
  width: 32px;
  cursor: pointer;
  margin-left: 10px;
  background-color: rgba(222, 27, 27, 0.5);
  background-repeat: no-repeat;
  background-size: 32px 64px;
  background-image: url("../images/icons/google-maps/icon-controller.svg");
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear; }

#map-zoom-in:hover,
#map-zoom-out:hover {
  background-color: #0087be; }

@media only screen and (min-width: 768px) {
  #map-zoom-in,
  #map-zoom-out {
    margin-left: 30px; } }
#map-zoom-in {
  background-position: 50% 0;
  margin-top: 10px;
  margin-bottom: 1px; }

@media only screen and (min-width: 768px) {
  #map-zoom-in {
    margin-top: 30px; } }
#map-zoom-out {
  background-position: 50% -32px; }

pre {
  padding: 1rem;
  margin: 0 0 10px;
  font-size: 0.875rem;
  line-height: 1.42857143;
  color: #979da9;
  background-color: #282c34;
  border: 1px solid #282c34;
  border-radius: 0.25rem; }

.color-code-preview {
  border: 1px solid 0.25rem;
  padding: 6px; }

.color-code-preview .color-preview {
  height: 150px;
  position: relative; }

.color-code-preview .color-code-rgb {
  display: block;
  padding: 0 0 5px;
  text-align: center;
  font-weight: 500;
  font-size: 13px; }

.color-code-preview .color-code-hex {
  display: block;
  padding: 10px 0 5px;
  text-align: center;
  font-weight: 500;
  font-size: 13px;
  text-transform: uppercase; }

.color-code-preview .color-text {
  position: absolute;
  width: 100%;
  top: 50%;
  margin-top: -10px;
  display: block;
  text-align: center; }

.color-code-preview .color-class {
  position: absolute;
  top: 10px;
  left: 10px; }

.play-video {
  position: absolute;
  left: 50%;
  top: 50%;
  background: #fff;
  color: #333;
  margin-left: -35px;
  width: 70px;
  height: 70px;
  line-height: 70px;
  -webkit-transition: 0.25s;
  -o-transition: 0.25s;
  transition: 0.25s;
  margin-top: -35px;
  border-radius: 50%;
  cursor: pointer;
  border: 0;
  animation-delay: 0.2s;
  text-align: center; }

.play-video:hover {
  transform: scale(1.1); }

.play-video--style-1 {
  background: #0087be;
  color: #FFF; }

.play-video > i {
  font-size: 20px;
  margin-left: 5px; }

.play-video-sm {
  width: 50px;
  height: 50px;
  line-height: 50px; }

.play-video-sm > i {
  font-size: 14px; }

.play-video-inline {
  display: inline-block;
  position: relative;
  left: 0;
  top: 0;
  margin-left: 0; }

.play-video-inline + .play-video-text {
  display: inline-block;
  margin-left: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2b2b2c; }

.gallery-top img {
  max-width: 100%;
  border-radius: 0.25rem; }

.gallery-bottom {
  margin-top: 1rem; }

.gallery-thumb img {
  max-width: 100%; }

.testimonial .testimonial-image {
  border-radius: 100%;
  max-width: 100%; }

.testimonial .testimonial-content .testimonial-text {
  line-height: 1.5; }

.link-menu > a {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 300;
  cursor: pointer;
  padding: 0.2em 1.3em;
  margin-right: 0.625rem; }

.link-menu > a:last-child {
  margin-right: 0; }

.link-menu--style-1 > a {
  margin-left: 1rem;
  padding-left: 0;
  padding-right: 1rem;
  color: #818a91; }

.link-menu--style-1 > a:first-child {
  margin-left: 0; }

.link-menu--style-1 > a.active,
.link-menu--style-1 > a:hover {
  color: #0087be; }

.link-menu--style-1 > a:active {
  text-shadow: 0 1px 1px; }

.link-menu--style-1 > a:after {
  content: "-";
  position: relative;
  right: -1rem; }

.link-menu--style-1 > a:after:hover {
  color: #818a91; }

.link-menu--style-1 > a:last-child:after {
  content: ""; }

.link-menu--style-1--v1 > a:active {
  text-shadow: 0; }

.link-menu--style-2 {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 0.25rem; }

.link-menu--style-2.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding: 1rem; }

.link-menu--style-2 a {
  display: inline-block;
  color: rgba(255, 255, 255, 0.8) !important;
  padding: 0 2rem;
  cursor: pointer;
  border-right: 1px dashed #eceeef; }

.link-menu--style-2 a:focus,
.link-menu--style-2 a:hover {
  color: white !important; }

.link-menu--style-2 a.active {
  color: #0087be !important; }

.link-menu--style-2 i {
  font-size: 1rem;
  margin-right: 0.75rem; }

.link-menu--style-2 a:last-child {
  border-right: none; }

@media (max-width: 991px) {
  .link-menu--style-2 a {
    padding: 0 1rem; } }
.link-menu--style-3 > a {
  background: #eceeef;
  color: #818a91;
  border-radius: 20px;
  text-align: center;
  font-weight: 400;
  font-size: 0.8rem; }

.link-menu--style-3 > a.active,
.link-menu--style-3 > a:hover {
  background: #0087be;
  color: #FFF !important; }

.background-image-holder h1 {
  font-size: 3rem;
  font-weight: 500;
  line-height: 1.2; }

.background-image-holder .button-link-option {
  display: block;
  margin-top: 1rem;
  font-size: 0.75rem;
  font-weight: 400; }

.background-image-holder .button-link-option > a {
  text-decoration: underline; }

.background-image-holder .holder-item {
  height: 100%;
  padding: 2rem 0; }

.navbar-transparent + .background-image-holder .holder-item {
  padding-top: 4rem; }

@media (max-width: 991px) {
  .background-image-holder .holder-item {
    height: auto;
    padding: 4rem 0; } }
.background-image-holder .holder-item-light .heading {
  color: #FFF; }

.background-image-holder .holder-item-light p {
  color: #d3d3d3; }

.background-image-holder .holder-item-dark .heading {
  color: #333; }

.background-image-holder .holder-item-dark p {
  color: #555; }

.background-image-holder .container {
  position: relative;
  height: 100%; }

.background-image-holder .swiper-pagination-bullet {
  width: 12px;
  height: 12px; }

.background-image-holder .text-boxed {
  display: inline-block;
  padding: 2rem;
  border: 2px solid #FFF; }

@media (max-width: 991px) {
  .background-image-holder .lead {
    font-size: 2.25vw !important; }

  .background-image-holder .heading-1 {
    font-size: 3.5vw !important; }

  .background-image-holder .heading-xl {
    font-size: 4.75vw !important; }

  .background-image-holder .heading-xxl {
    font-size: 6vw !important; }

  .background-image-holder .heading-xl-x2 {
    font-size: 7.5vw !important; } }
.palette-colors {
  border-radius: 0.25rem;
  cursor: pointer;
  margin-top: 8px;
  overflow: hidden;
  position: relative;
  display: table;
  width: 100%; }

.palette-colors div {
  height: 22px;
  display: table-cell;
  box-shadow: inset rgba(255, 255, 255, 0.05) 0 1px, inset rgba(255, 255, 255, 0.05) 0 -1px; }

.palette-colors div:first-of-type {
  border-radius: 0.25rem 0 0 0.25rem;
  box-shadow: inset rgba(255, 255, 255, 0.05) 1px 1px, inset rgba(255, 255, 255, 0.05) 0 -1px; }

.palette-colors div:last-child {
  border-radius: 0 0.25rem 0.25rem 0;
  box-shadow: inset rgba(255, 255, 255, 0.05) -1px 1px, inset rgba(255, 255, 255, 0.05) 0 -1px; }

.spotlight-floating-img {
  position: relative; }

.spotlight-floating-img.align-left {
  left: -15px; }

.spotlight-floating-img.align-right {
  right: -15px; }

.spotlight-img > img,
.spotlight-floating-img > img {
  max-width: 100%; }

.icons-holder i {
  margin-right: 1.5rem;
  color: #818a91; }

.icons-holder .desktop {
  font-size: 46px; }

.icons-holder .tablet {
  font-size: 40px; }

.icons-holder .mobile {
  font-size: 34px; }

/*# sourceMappingURL=global-style.css.map */
