/* Swedish locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.sv = {};

flatpickr.l10ns.sv.firstDayOfWeek = 1;
flatpickr.l10ns.sv.weekAbbreviation = "v";

flatpickr.l10ns.sv.weekdays = {
	shorthand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
	longhand: ["S<PERSON>ndag", "Måndag", "Tisdag", "Onsdag", "Torsdag", "Fredag", "Lördag"]
};

flatpickr.l10ns.sv.months = {
	shorthand: ["Jan", "Feb", "Mar", "Apr", "Maj", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dec"],
	longhand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mars", "April", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "September", "<PERSON><PERSON><PERSON>", "November", "December"]
};

flatpickr.l10ns.sv.ordinal = function () {
	return ".";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;