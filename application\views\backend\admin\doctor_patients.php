<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div class="panel-title">
                    <h3><i class="fa fa-user-md"></i> <?php echo get_phrase('doctor'); ?> - <?php echo get_phrase('manage_patient'); ?></h3>
                    <p>Manage patients as a doctor would</p>
                </div>
            </div>
            <div class="panel-body">
                
                <button onclick="showAjaxModal('<?php echo site_url('modal/popup/add_patient');?>');" 
                    class="btn btn-primary pull-right">
                        <i class="fa fa-plus"></i>&nbsp;<?php echo get_phrase('add_patient'); ?>
                </button>
                <div style="clear:both;"></div>
                <br>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped datatable" id="table-2">
                        <thead>
                            <tr>
                                <th><?php echo get_phrase('image');?></th>
                                <th><?php echo get_phrase('patient_number');?></th>
                                <th><?php echo get_phrase('name');?></th>
                                <th><?php echo get_phrase('email');?></th>
                                <th><?php echo get_phrase('phone');?></th>
                                <th><?php echo get_phrase('sex');?></th>
                                <th><?php echo get_phrase('birth_date');?></th>
                                <th><?php echo get_phrase('age');?></th>
                                <th><?php echo get_phrase('blood_group');?></th>
                                <th><?php echo get_phrase('options');?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($patient_info) && is_array($patient_info)): ?>
                                <?php foreach ($patient_info as $row): ?>   
                                    <tr>
                                        <td><img src="<?php echo $this->crud_model->get_image_url('patient' , $row['patient_id']);?>" class="img-circle" width="40px" height="40px"></td>
                                        <td><strong><?php echo $row['code']?></strong></td>
                                        <td><?php echo $row['name']?></td>
                                        <td><?php echo $row['email']?></td>
                                        <td><?php echo $row['phone']?></td>
                                        <td><?php echo $row['sex']?></td>
                                        <td><?php 
                                            if (is_numeric($row['birth_date'])) {
                                                echo date('d/m/Y', $row['birth_date']); 
                                            } else {
                                                echo date('d/m/Y', strtotime($row['birth_date'])); 
                                            }
                                        ?></td>
                                        <td><?php echo $row['age']?></td>
                                        <td><?php echo $row['blood_group']?></td>
                                        <td>
                                            <a  onclick="showAjaxModal('<?php echo site_url('modal/popup/edit_patient/'.$row['patient_id']);?>');" 
                                                class="btn btn-info btn-sm">
                                                    <i class="fa fa-pencil"></i>&nbsp;
                                                    <?php echo get_phrase('edit');?>
                                            </a>
                                            <a onclick="confirm_modal('<?php echo site_url('role_operations/doctor_patients/delete/'.$row['patient_id']); ?>')"
                                                class="btn btn-danger btn-sm">
                                                    <i class="fa fa-trash-o"></i>&nbsp;
                                                    <?php echo get_phrase('delete');?>
                                            </a>
                                            <a href="<?php echo site_url('admin/view_patient_profile/'.$row['patient_id']); ?>"
                                                class="btn btn-success btn-sm">
                                                    <i class="fa fa-eye"></i>&nbsp;
                                                    <?php echo get_phrase('view_profile');?>
                                            </a>
                                            <a onclick="viewAsUser('patient', <?php echo $row['patient_id']; ?>, '<?php echo $row['name']; ?>')"
                                                class="btn btn-warning btn-sm" title="<?php echo get_phrase('view_as_user'); ?>">
                                                    <i class="fa fa-user"></i>&nbsp;
                                                    <?php echo get_phrase('view_as'); ?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center text-muted">No patient data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="row" style="margin-top: 20px;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            <strong>Doctor Operations:</strong> You are performing patient management operations as a doctor would. 
                            All actions are logged under your admin account for audit purposes.
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <a href="<?php echo site_url('role_operations'); ?>" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Back to Role Operations
                        </a>
                        <a href="<?php echo site_url('role_operations/doctor_appointments'); ?>" class="btn btn-primary">
                            <i class="fa fa-calendar"></i> Manage Appointments
                        </a>
                        <a href="<?php echo site_url('role_operations/doctor_prescriptions'); ?>" class="btn btn-primary">
                            <i class="fa fa-file-text"></i> Manage Prescriptions
                        </a>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

<script>
function viewAsUser(userType, userId, userName) {
    if (confirm('Are you sure you want to view the system as ' + userName + '?\n\nThis will switch you to their account view. You can return to admin using the "Return to Admin" button that will appear.')) {
        window.location.href = '<?php echo site_url('admin/view_as_user/switch'); ?>/' + userType + '/' + userId;
    }
}
</script>
