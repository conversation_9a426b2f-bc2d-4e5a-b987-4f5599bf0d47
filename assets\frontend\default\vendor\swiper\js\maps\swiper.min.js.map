{"version": 3, "sources": ["swiper.js"], "names": ["addLibraryPlugin", "lib", "fn", "swiper", "params", "firstInstance", "this", "each", "s", "Swiper", "$", "container", "round", "a", "Math", "floor", "autoplay", "autoplayDelay", "activeSlide", "slides", "eq", "activeIndex", "attr", "autoplayTimeoutId", "setTimeout", "loop", "fixLoop", "_slideNext", "emit", "isEnd", "autoplayStopOnLast", "stopAutoplay", "_slideTo", "findElementInEvent", "e", "selector", "el", "target", "is", "parents", "nodeType", "found", "index", "_el", "length", "initObserver", "options", "ObserverFunc", "window", "MutationObserver", "WebkitMutationObserver", "observer", "mutations", "for<PERSON>ach", "mutation", "onResize", "observe", "attributes", "childList", "characterData", "observers", "push", "handleKeyboard", "originalEvent", "kc", "keyCode", "charCode", "allowSwipeToNext", "isHorizontal", "allowSwipeToPrev", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "document", "activeElement", "nodeName", "toLowerCase", "inView", "slideClass", "slideActiveClass", "windowScroll", "left", "pageXOffset", "top", "pageYOffset", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "swiperOffset", "offset", "rtl", "scrollLeft", "swiperCoord", "width", "height", "i", "point", "preventDefault", "returnValue", "slideNext", "slidePrev", "isEventSupported", "eventName", "isSupported", "element", "createElement", "setAttribute", "implementation", "hasFeature", "handleMousewheel", "delta", "rtlFactor", "data", "normalizeWheel", "mousewheelForceToAxis", "abs", "pixelX", "pixelY", "mousewheelInvert", "freeMode", "position", "getWrapperTranslate", "mousewheelSensitivity", "wasBeginning", "isBeginning", "wasEnd", "minTranslate", "maxTranslate", "setWrapperTransition", "setWrapperTranslate", "updateProgress", "updateActiveIndex", "updateClasses", "freeModeSticky", "clearTimeout", "mousewheel", "timeout", "slideReset", "lazyLoading", "lazy", "load", "autoplayDisableOnInteraction", "Date", "getTime", "lastScrollTime", "animating", "mousewheelReleaseOnEdges", "event", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "setParallaxTransform", "progress", "p", "indexOf", "parseInt", "transform", "normalizeEventName", "toUpperCase", "substring", "defaults", "direction", "touchEventsTarget", "initialSlide", "speed", "iOSEdgeSwipeDetection", "iOSEdgeSwipeThreshold", "freeModeMomentum", "freeModeMomentumRatio", "freeModeMomentumBounce", "freeModeMomentumBounceRatio", "freeModeMomentumVelocityRatio", "freeModeMinimumVelocity", "autoHeight", "setWrapperSize", "virtualTranslate", "effect", "coverflow", "rotate", "stretch", "depth", "modifier", "slideShadows", "flip", "limitRotation", "cube", "shadow", "shadowOffset", "shadowScale", "fade", "crossFade", "parallax", "zoom", "zoomMax", "zoomMin", "zoomToggle", "scrollbar", "scrollbarHide", "scrollbarDraggable", "scrollbarSnapOnRelease", "keyboardControl", "mousewheelControl", "mousewheelEventsTarged", "<PERSON><PERSON><PERSON>", "hashnavWatchState", "history", "replaceState", "breakpoints", "undefined", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumn", "slidesPerColumnFill", "slidesPerGroup", "centeredSlides", "slidesOffsetBefore", "slidesOffsetAfter", "roundLengths", "touchRatio", "touchAngle", "simulate<PERSON>ouch", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "onlyEx<PERSON><PERSON>", "threshold", "touchMoveStopPropagation", "touchReleaseOnEdges", "uniqueNavElements", "pagination", "paginationElement", "paginationClickable", "paginationHide", "paginationBulletRender", "paginationProgressRender", "paginationFractionRender", "paginationCustomRender", "paginationType", "resistance", "resistanceRatio", "nextButton", "prevButton", "watchSlidesProgress", "watchSlidesVisibility", "grabCursor", "preventClicks", "preventClicksPropagation", "slideToClickedSlide", "lazyLoadingInPrevNext", "lazyLoadingInPrevNextAmount", "lazyLoadingOnTransitionStart", "preloadImages", "updateOnImagesReady", "loopAdditionalSlides", "loopedSlides", "control", "controlInverse", "controlBy", "normalizeSlideIndex", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "passiveListeners", "containerModifierClass", "slideDuplicateActiveClass", "slideVisibleClass", "slideDuplicateClass", "slideNextClass", "slideDuplicateNextClass", "slidePrevClass", "slideDuplicatePrevClass", "wrapperClass", "bulletClass", "bulletActiveClass", "buttonDisabledClass", "paginationCurrentClass", "paginationTotalClass", "paginationHiddenClass", "paginationProgressbarClass", "paginationClickableClass", "paginationModifierClass", "lazyLoadingClass", "lazyStatusLoadingClass", "lazyStatusLoadedClass", "lazyPreloaderClass", "notificationClass", "preloaderClass", "zoomContainerClass", "observeParents", "a11y", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "runCallbacksOnInit", "initialVirtualTranslate", "originalParams", "param", "Dom7", "j<PERSON><PERSON><PERSON>", "deepParam", "def", "deepDef", "classNames", "Zepto", "currentBreakpoint", "getActiveBreakpoint", "breakpoint", "points", "hasOwnProperty", "sort", "b", "setBreakpoint", "breakPointsParams", "needsReLoop", "destroyLoop", "reLoop", "swipers", "support", "flexbox", "transforms3d", "touch", "wrapper", "children", "paginationContainer", "find", "addClass", "dir", "css", "wrongRTL", "device", "android", "join", "translate", "velocity", "lockSwipeToNext", "unsetGrabCursor", "lockSwipeToPrev", "lockSwipes", "unlockSwipeToNext", "setGrabCursor", "unlockSwipeToPrev", "unlockSwipes", "moving", "style", "cursor", "imagesToLoad", "imagesLoaded", "loadImage", "imgElement", "src", "srcset", "sizes", "checkForComplete", "callback", "onReady", "image", "complete", "Image", "onload", "onerror", "_onReady", "update", "currentSrc", "getAttribute", "autoplaying", "autoplayPaused", "startAutoplay", "internal", "pauseAutoplay", "transitionEnd", "snapGrid", "updateAutoHeight", "activeSlides", "newHeight", "ceil", "offsetHeight", "updateContainerSize", "clientWidth", "clientHeight", "size", "updateSlidesSize", "slidesGrid", "slidesSizesGrid", "slidePosition", "prevSlideSize", "parseFloat", "replace", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesNumberEvenToRows", "max", "slideSize", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "outerWidth", "outerHeight", "swiperSlideSize", "newSlidesGrid", "updateSlidesOffset", "swiperSlideOffset", "offsetLeft", "offsetTop", "currentSlidesPerView", "j", "spv", "breakLoop", "updateSlidesProgress", "offsetCenter", "removeClass", "slideProgress", "slideBefore", "slideAfter", "isVisible", "translatesDiff", "newActiveIndex", "snapIndex", "previousIndex", "updateRealIndex", "realIndex", "hasClass", "nextSlide", "next", "prevSlide", "prev", "current", "total", "bullets", "text", "scale", "scaleX", "scaleY", "transition", "html", "disable", "enable", "updatePagination", "paginationHTML", "numberOfBullets", "initPagination", "updateTranslate", "forceSetTranslate", "newTranslate", "min", "set", "translated", "controller", "spline", "slideTo", "forceUpdatePagination", "slideChangedBySlideTo", "touchEventsDesktop", "start", "move", "end", "navigator", "pointer<PERSON><PERSON>bled", "msPointer<PERSON><PERSON><PERSON>", "touchEvents", "initEvents", "detach", "actionDom", "action", "moveCapture", "nested", "browser", "ie", "onTouchStart", "onTouchMove", "onTouchEnd", "passiveListener", "passive", "capture", "ios", "onClickNext", "onEnterKey", "onClickPrev", "onClickIndex", "attachEvents", "detachEvents", "allowClick", "stopPropagation", "stopImmediatePropagation", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToIndex", "isTouched", "isMoved", "allowTouchCallbacks", "touchStartTime", "isScrolling", "currentTranslate", "startTranslate", "allowThresholdMove", "clickTimeout", "allowMomentumBounce", "formElements", "lastClickTime", "now", "velocities", "touches", "startX", "startY", "currentX", "currentY", "diff", "isTouchEvent", "startMoving", "type", "which", "targetTouches", "pageX", "pageY", "swipeDirection", "blur", "preventedByNestedSwiper", "atan2", "PI", "ieTouch", "trigger", "disableParentSwiper", "pow", "time", "touchEndTime", "timeDiff", "toggleClass", "currentPos", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "onTransitionStart", "onTransitionEnd", "stopIndex", "groupSize", "ratio", "slideIndex", "runCallbacks", "lteIE9", "setHistory", "setHash", "clientLeft", "_slidePrev", "disableTouchControl", "enableTouchControl", "duration", "byController", "effects", "setTransition", "x", "y", "z", "setTranslate", "getTranslate", "matrix", "curTransform", "curStyle", "transformMatrix", "getComputedStyle", "WebKitCSSMatrix", "webkitTransform", "split", "map", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "m41", "m42", "initObservers", "containerParents", "disconnectObservers", "disconnect", "createLoop", "remove", "prependSlides", "appendSlides", "append", "cloneNode", "prepend", "removeAttr", "updatePosition", "oldIndex", "newIndex", "appendSlide", "prependSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "tx", "ty", "slideOpacity", "opacity", "eventTriggered", "triggerEvents", "rotateY", "rotateX", "zIndex", "shadowBefore", "shadowAfter", "cubeShadow", "wrapperRotate", "slideAngle", "tz", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowAngle", "multiplier", "sin", "cos", "scale1", "scale2", "zFactor", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "center", "slideOffset", "offsetMultiplier", "translateZ", "translateY", "translateX", "slideTransform", "ws", "<PERSON><PERSON><PERSON><PERSON>", "initialImageLoaded", "loadImageInSlide", "loadInDuplicate", "img", "add", "_img", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "amount", "maxIndex", "minIndex", "setDragPosition", "sb", "pointerPosition", "clientX", "clientY", "track", "dragSize", "positionMin", "moveDivider", "positionMax", "dragStart", "dragTimeout", "drag", "dragMove", "dragEnd", "draggableEvents", "enableDraggable", "on", "disableDraggable", "off", "trackSize", "offsetWidth", "divider", "display", "newPos", "newSize", "LinearSpline", "lastIndex", "i1", "i3", "interpolate", "x2", "binarySearch", "guess", "array", "val", "getInterpolateFunction", "c", "setControlledTranslate", "controlledTranslate", "controlled", "isArray", "setControlledTransition", "onHashCange", "newHash", "location", "hash", "activeSlideHash", "initialized", "init", "slideHash", "destroy", "pushState", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "key", "value", "scrollToSlide", "addEventListener", "setHistoryPopState", "pathArray", "pathname", "slice", "slugify", "includes", "slideHistory", "disableKeyboardControl", "enableKeyboardControl", "userAgent", "disableMousewheelControl", "enableMousewheelControl", "parallaxDuration", "currentScale", "isScaling", "gesture", "slideWidth", "slideHeight", "imageWrap", "minX", "minY", "maxX", "maxY", "touchesStart", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "getDistanceBetweenTouches", "x1", "y1", "y2", "sqrt", "onGestureStart", "gestures", "scaleStart", "parent", "onGestureChange", "scaleMove", "onGestureEnd", "changedTouches", "os", "scaledWidth", "scaledHeight", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "toggleZoom", "touchX", "touchY", "offsetX", "offsetY", "diffX", "diffY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "_plugins", "plugin", "plugins", "callPlugins", "arguments", "emitterEventListeners", "handler", "splice", "once", "_handler", "makeFocusable", "$el", "addRole", "role", "addLabel", "label", "notify", "click", "liveRegion", "message", "notification", "bullet", "hashnavReplaceState", "cleanupStyles", "deleteInstance", "removeEventListener", "prototype", "ua", "test", "arr", "Object", "apply", "msMaxTouchPoints", "maxTouchPoints", "div", "innerHTML", "getElementsByTagName", "match", "ipad", "ipod", "iphone", "Modernizr", "DocumentTouch", "csstransforms3d", "styles", "supportsPassive", "opts", "defineProperty", "get", "_this", "context", "els", "tempParent", "trim", "toCreate", "childNodes", "querySelectorAll", "getElementById", "className", "classes", "classList", "contains", "toggle", "attrs", "attrName", "removeAttribute", "dom7ElementDataStorage", "dataKey", "elStyle", "webkitTransitionDuration", "MsTransitionDuration", "msTransitionDuration", "MozTransitionDuration", "OTransitionDuration", "transitionDuration", "targetSelector", "listener", "handleLiveEvent", "call", "k", "events", "dom7LiveListeners", "liveListener", "proxy", "dom", "eventData", "evt", "CustomEvent", "bubbles", "cancelable", "createEvent", "initEvent", "dispatchEvent", "fireCallBack", "<PERSON><PERSON><PERSON><PERSON>", "box", "getBoundingClientRect", "body", "clientTop", "scrollTop", "props", "prop", "textContent", "compareWith", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "child", "previousSibling", "returnIndex", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "insertBefore", "before", "parentNode", "insertAfter", "after", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "previousElementSibling", "prevAll", "prevEls", "unique", "foundElements", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "swiperDomPlugins", "domLib", "module", "exports", "define", "amd"], "mappings": ";;;;;;;;;;;;;;CAcA,WACI,YAqnKA,SAASA,GAAiBC,GACtBA,EAAIC,GAAGC,OAAS,SAAUC,GACtB,GAAIC,EAKJ,OAJAJ,GAAIK,MAAMC,KAAK,WACX,GAAIC,GAAI,GAAIC,GAAOH,KAAMF,EACpBC,KAAeA,EAAgBG,KAEjCH,GA3nKf,GAAIK,GAIAD,EAAS,SAAUE,EAAWP,GAqgB9B,QAASQ,GAAMC,GACX,MAAOC,MAAKC,MAAMF,GAuEtB,QAASG,KACL,GAAIC,GAAgBT,EAAEJ,OAAOY,SACzBE,EAAcV,EAAEW,OAAOC,GAAGZ,EAAEa,YAC5BH,GAAYI,KAAK,0BACjBL,EAAgBC,EAAYI,KAAK,yBAA2Bd,EAAEJ,OAAOY,UAEzER,EAAEe,kBAAoBC,WAAW,WACzBhB,EAAEJ,OAAOqB,MACTjB,EAAEkB,UACFlB,EAAEmB,aACFnB,EAAEoB,KAAK,aAAcpB,IAGhBA,EAAEqB,MAKEzB,EAAO0B,mBAKRtB,EAAEuB,gBAJFvB,EAAEwB,SAAS,GACXxB,EAAEoB,KAAK,aAAcpB,KANzBA,EAAEmB,aACFnB,EAAEoB,KAAK,aAAcpB,KAY9BS,GAqxBP,QAASgB,GAAmBC,EAAGC,GAC3B,GAAIC,GAAK1B,EAAEwB,EAAEG,OACb,KAAKD,EAAGE,GAAGH,GACP,GAAwB,gBAAbA,GACPC,EAAKA,EAAGG,QAAQJ,OAEf,IAAIA,EAASK,SAAU,CACxB,GAAIC,EAIJ,OAHAL,GAAGG,UAAUhC,KAAK,SAAUmC,EAAOC,GAC3BA,IAAQR,IAAUM,EAAQN,KAE7BM,EACON,EADA,OAIpB,GAAkB,IAAdC,EAAGQ,OAGP,MAAOR,GAAG,GAm2Bd,QAASS,GAAaR,EAAQS,GAC1BA,EAAUA,KAEV,IAAIC,GAAeC,OAAOC,kBAAoBD,OAAOE,uBACjDC,EAAW,GAAIJ,GAAa,SAAUK,GACtCA,EAAUC,QAAQ,SAAUC,GACxB9C,EAAE+C,UAAS,GACX/C,EAAEoB,KAAK,mBAAoBpB,EAAG8C,MAItCH,GAASK,QAAQnB,GACboB,WAA0C,mBAAvBX,GAAQW,YAAoCX,EAAQW,WACvEC,UAAwC,mBAAtBZ,GAAQY,WAAmCZ,EAAQY,UACrEC,cAAgD,mBAA1Bb,GAAQa,eAAuCb,EAAQa,gBAGjFnD,EAAEoD,UAAUC,KAAKV,GAo+BrB,QAASW,GAAe5B,GAChBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIC,GAAK9B,EAAE+B,SAAW/B,EAAEgC,QAExB,KAAK1D,EAAEJ,OAAO+D,mBAAqB3D,EAAE4D,gBAAyB,KAAPJ,IAAcxD,EAAE4D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,KAAKxD,EAAEJ,OAAOiE,mBAAqB7D,EAAE4D,gBAAyB,KAAPJ,IAAcxD,EAAE4D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,MAAI9B,EAAEoC,UAAYpC,EAAEqC,QAAUrC,EAAEsC,SAAWtC,EAAEuC,SAGzCC,SAASC,eAAiBD,SAASC,cAAcC,WAA+D,UAAlDF,SAASC,cAAcC,SAASC,eAA+E,aAAlDH,SAASC,cAAcC,SAASC,gBAA/J,CAGA,GAAW,KAAPb,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,EAAW,CAClD,GAAIc,IAAS,CAEb,IAAItE,EAAEG,UAAU4B,QAAQ,IAAM/B,EAAEJ,OAAO2E,YAAYnC,OAAS,GAAqE,IAAhEpC,EAAEG,UAAU4B,QAAQ,IAAM/B,EAAEJ,OAAO4E,kBAAkBpC,OAClH,MAEJ,IAAIqC,IACAC,KAAMlC,OAAOmC,YACbC,IAAKpC,OAAOqC,aAEZC,EAActC,OAAOuC,WACrBC,EAAexC,OAAOyC,YACtBC,EAAelF,EAAEG,UAAUgF,QAC3BnF,GAAEoF,MAAKF,EAAaR,KAAOQ,EAAaR,KAAO1E,EAAEG,UAAU,GAAGkF,WAOlE,KAAK,GANDC,KACCJ,EAAaR,KAAMQ,EAAaN,MAChCM,EAAaR,KAAO1E,EAAEuF,MAAOL,EAAaN,MAC1CM,EAAaR,KAAMQ,EAAaN,IAAM5E,EAAEwF,SACxCN,EAAaR,KAAO1E,EAAEuF,MAAOL,EAAaN,IAAM5E,EAAEwF,SAE9CC,EAAI,EAAGA,EAAIH,EAAYlD,OAAQqD,IAAK,CACzC,GAAIC,GAAQJ,EAAYG,EAEpBC,GAAM,IAAMjB,EAAaC,MAAQgB,EAAM,IAAMjB,EAAaC,KAAOI,GACjEY,EAAM,IAAMjB,EAAaG,KAAOc,EAAM,IAAMjB,EAAaG,IAAMI,IAE/DV,GAAS,GAIjB,IAAKA,EAAQ,OAEbtE,EAAE4D,gBACS,KAAPJ,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,IAEb,KAAPpC,IAAcxD,EAAEoF,KAAgB,KAAP5B,GAAaxD,EAAEoF,MAAMpF,EAAE6F,aACzC,KAAPrC,IAAcxD,EAAEoF,KAAgB,KAAP5B,GAAaxD,EAAEoF,MAAMpF,EAAE8F,cAG1C,KAAPtC,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GAEd,KAAPpC,GAAWxD,EAAE6F,YACN,KAAPrC,GAAWxD,EAAE8F,cAgCzB,QAASC,KACL,GAAIC,GAAY,UACZC,EAAcD,IAAa9B,SAE/B,KAAK+B,EAAa,CACd,GAAIC,GAAUhC,SAASiC,cAAc,MACrCD,GAAQE,aAAaJ,EAAW,WAChCC,EAA4C,kBAAvBC,GAAQF,GAajC,OAVKC,GACD/B,SAASmC,gBACTnC,SAASmC,eAAeC,YAGxBpC,SAASmC,eAAeC,WAAW,GAAI,OAAQ,IAE/CL,EAAc/B,SAASmC,eAAeC,WAAW,eAAgB,QAG9DL,EAGX,QAASM,GAAiB7E,GAClBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIiD,GAAQ,EACRC,EAAYzG,EAAEoF,KAAM,EAAK,EAEzBsB,EAAOC,EAAgBjF,EAE3B,IAAI1B,EAAEJ,OAAOgH,sBACT,GAAI5G,EAAE4D,eAAgB,CAClB,KAAItD,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SACrC,MAD8CP,GAAQE,EAAKI,OAASL,MAGxE,CACD,KAAInG,KAAKuG,IAAIH,EAAKK,QAAUzG,KAAKuG,IAAIH,EAAKI,SACrC,MAD8CN,GAAQE,EAAKK,WAKpEP,GAAQlG,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SAAYL,EAAKI,OAASL,GAAcC,EAAKK,MAG/F,IAAc,IAAVP,EAAJ,CAIA,GAFIxG,EAAEJ,OAAOoH,mBAAkBR,GAASA,GAEnCxG,EAAEJ,OAAOqH,SAoBT,CAED,GAAIC,GAAWlH,EAAEmH,sBAAwBX,EAAQxG,EAAEJ,OAAOwH,sBACtDC,EAAerH,EAAEsH,YACjBC,EAASvH,EAAEqB,KAgCf,IA9BI6F,GAAYlH,EAAEwH,iBAAgBN,EAAWlH,EAAEwH,gBAC3CN,GAAYlH,EAAEyH,iBAAgBP,EAAWlH,EAAEyH,gBAE/CzH,EAAE0H,qBAAqB,GACvB1H,EAAE2H,oBAAoBT,GACtBlH,EAAE4H,iBACF5H,EAAE6H,sBAEGR,GAAgBrH,EAAEsH,cAAgBC,GAAUvH,EAAEqB,QAC/CrB,EAAE8H,gBAGF9H,EAAEJ,OAAOmI,gBACTC,aAAahI,EAAEiI,WAAWC,SAC1BlI,EAAEiI,WAAWC,QAAUlH,WAAW,WAC9BhB,EAAEmI,cACH,MAGCnI,EAAEJ,OAAOwI,aAAepI,EAAEqI,MAC1BrI,EAAEqI,KAAKC,OAIftI,EAAEoB,KAAK,WAAYpB,EAAG0B,GAGlB1B,EAAEJ,OAAOY,UAAYR,EAAEJ,OAAO2I,8BAA8BvI,EAAEuB,eAGjD,IAAb2F,GAAkBA,IAAalH,EAAEyH,eAAgB,WAxDjC,CACpB,IAAI,GAAKjF,QAAOgG,MAAQC,UAAYzI,EAAEiI,WAAWS,eAAiB,GAC9D,GAAIlC,EAAQ,EACR,GAAMxG,EAAEqB,QAASrB,EAAEJ,OAAOqB,MAAUjB,EAAE2I,WAIjC,GAAI3I,EAAEJ,OAAOgJ,yBAA0B,OAAO,MAH/C5I,GAAE6F,YACF7F,EAAEoB,KAAK,WAAYpB,EAAG0B,OAK1B,IAAM1B,EAAEsH,cAAetH,EAAEJ,OAAOqB,MAAUjB,EAAE2I,WAIvC,GAAI3I,EAAEJ,OAAOgJ,yBAA0B,OAAO,MAH/C5I,GAAE8F,YACF9F,EAAEoB,KAAK,WAAYpB,EAAG0B,EAKlC1B,GAAEiI,WAAWS,gBAAiB,GAAKlG,QAAOgG,MAAQC,UA4CtD,MAFI/G,GAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GACd,GA0HX,QAASe,GAA2BkC,GAEhC,GAAIC,GAAa,GACbC,EAAc,GACdC,EAAc,IAEdC,EAAK,EAAGC,EAAK,EACbC,EAAK,EAAGC,EAAK,CAkDjB,OA/CI,UAAYP,KACZK,EAAKL,EAAMQ,QAEX,cAAgBR,KAChBK,GAAML,EAAMS,WAAa,KAEzB,eAAiBT,KACjBK,GAAML,EAAMU,YAAc,KAE1B,eAAiBV,KACjBI,GAAMJ,EAAMW,YAAc,KAI1B,QAAUX,IAASA,EAAMY,OAASZ,EAAMa,kBACxCT,EAAKC,EACLA,EAAK,GAGTC,EAAKF,EAAKH,EACVM,EAAKF,EAAKJ,EAEN,UAAYD,KACZO,EAAKP,EAAMc,QAEX,UAAYd,KACZM,EAAKN,EAAMe,SAGVT,GAAMC,IAAOP,EAAMgB,YACI,IAApBhB,EAAMgB,WACNV,GAAMJ,EACNK,GAAML,IAENI,GAAMH,EACNI,GAAMJ,IAKVG,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,GAErBC,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,IAIrBU,MAAOb,EACPc,MAAOb,EACPpC,OAAQqC,EACRpC,OAAQqC,GAOhB,QAASY,GAAqBpI,EAAIqI,GAC9BrI,EAAK1B,EAAE0B,EACP,IAAIsI,GAAGf,EAAIC,EACP3C,EAAYzG,EAAEoF,KAAM,EAAK,CAE7B8E,GAAItI,EAAGd,KAAK,yBAA2B,IACvCqI,EAAKvH,EAAGd,KAAK,0BACbsI,EAAKxH,EAAGd,KAAK,0BACTqI,GAAMC,GACND,EAAKA,GAAM,IACXC,EAAKA,GAAM,KAGPpJ,EAAE4D,gBACFuF,EAAKe,EACLd,EAAK,MAGLA,EAAKc,EACLf,EAAK,KAKTA,EADA,EAAKgB,QAAQ,MAAQ,EAChBC,SAASjB,EAAI,IAAMc,EAAWxD,EAAY,IAG1C0C,EAAKc,EAAWxD,EAAY,KAGjC2C,EADA,EAAKe,QAAQ,MAAQ,EAChBC,SAAShB,EAAI,IAAMa,EAAW,IAG9Bb,EAAKa,EAAW,KAGzBrI,EAAGyI,UAAU,eAAiBlB,EAAK,KAAOC,EAAK,SA2ZnD,QAASkB,GAAoBtE,GASzB,MARgC,KAA5BA,EAAUmE,QAAQ,QAEdnE,EADAA,EAAU,KAAOA,EAAU,GAAGuE,cAClB,KAAOvE,EAAU,GAAGuE,cAAgBvE,EAAUwE,UAAU,GAGxD,KAAOxE,GAGpBA,EAvjIX,KAAMlG,eAAgBG,IAAS,MAAO,IAAIA,GAAOE,EAAWP,EAE5D,IAAI6K,IACAC,UAAW,aACXC,kBAAmB,YACnBC,aAAc,EACdC,MAAO,IAEPrK,UAAU,EACV+H,8BAA8B,EAC9BjH,oBAAoB,EAEpBwJ,uBAAuB,EACvBC,sBAAuB,GAEvB9D,UAAU,EACV+D,kBAAkB,EAClBC,sBAAuB,EACvBC,wBAAwB,EACxBC,4BAA6B,EAC7BC,8BAA+B,EAC/BrD,gBAAgB,EAChBsD,wBAAyB,IAEzBC,YAAY,EAEZC,gBAAgB,EAEhBC,kBAAkB,EAElBC,OAAQ,QACRC,WACIC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPC,SAAU,EACVC,cAAe,GAEnBC,MACID,cAAe,EACfE,eAAe,GAEnBC,MACIH,cAAc,EACdI,QAAQ,EACRC,aAAc,GACdC,YAAa,KAEjBC,MACIC,WAAW,GAGfC,UAAU,EAEVC,MAAM,EACNC,QAAS,EACTC,QAAS,EACTC,YAAY,EAEZC,UAAW,KACXC,eAAe,EACfC,oBAAoB,EACpBC,wBAAwB,EAExBC,iBAAiB,EACjBC,mBAAmB,EACnBtE,0BAA0B,EAC1B5B,kBAAkB,EAClBJ,uBAAuB,EACvBQ,sBAAuB,EACvB+F,uBAAwB,YAExBC,SAAS,EACTC,mBAAmB,EAEnBC,SAAS,EAETC,cAAc,EAEdC,YAAaC,OAEbC,aAAc,EACdC,cAAe,EACfC,gBAAiB,EACjBC,oBAAqB,SACrBC,eAAgB,EAChBC,gBAAgB,EAChBC,mBAAoB,EACpBC,kBAAmB,EAEnBC,cAAc,EAEdC,WAAY,EACZC,WAAY,GACZC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,gBAAiB,GACjBC,aAAc,IACdC,cAAc,EACdC,cAAc,EACdC,UAAW,EACXC,0BAA0B,EAC1BC,qBAAqB,EAErBC,mBAAmB,EAEnBC,WAAY,KACZC,kBAAmB,OACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAwB,KACxBC,yBAA0B,KAC1BC,yBAA0B,KAC1BC,uBAAwB,KACxBC,eAAgB,UAEhBC,YAAY,EACZC,gBAAiB,IAEjBC,WAAY,KACZC,WAAY,KAEZC,qBAAqB,EACrBC,uBAAuB,EAEvBC,YAAY,EAEZC,eAAe,EACfC,0BAA0B,EAC1BC,qBAAqB,EAErB9H,aAAa,EACb+H,uBAAuB,EACvBC,4BAA6B,EAC7BC,8BAA8B,EAE9BC,eAAe,EACfC,qBAAqB,EAErBtP,MAAM,EACNuP,qBAAsB,EACtBC,aAAc,KAEdC,QAASjD,OACTkD,gBAAgB,EAChBC,UAAW,QACXC,qBAAqB,EAErBhN,kBAAkB,EAClBF,kBAAkB,EAClBmN,aAAc,KACdC,WAAW,EACXC,eAAgB,oBAEhBC,kBAAkB,EAElBC,uBAAwB,oBACxB3M,WAAY,eACZC,iBAAkB,sBAClB2M,0BAA2B,gCAC3BC,kBAAmB,uBACnBC,oBAAqB,yBACrBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,aAAc,iBACdC,YAAa,2BACbC,kBAAmB,kCACnBC,oBAAqB,yBACrBC,uBAAwB,4BACxBC,qBAAsB,0BACtBC,sBAAuB,2BACvBC,2BAA4B,gCAC5BC,yBAA0B,8BAC1BC,wBAAyB,qBACzBC,iBAAkB,cAClBC,uBAAwB,sBACxBC,sBAAuB,qBACvBC,mBAAoB,wBACpBC,kBAAmB,sBACnBC,eAAgB,YAChBC,mBAAoB,wBAGpB/P,UAAU,EACVgQ,gBAAgB,EAEhBC,MAAM,EACNC,iBAAkB,iBAClBC,iBAAkB,aAClBC,kBAAmB,0BACnBC,iBAAkB,yBAClBC,wBAAyB,wBAEzBC,oBAAoB,GA8BpBC,EAA0BvT,GAAUA,EAAO4L,gBAE/C5L,GAASA,KACT,IAAIwT,KACJ,KAAK,GAAIC,KAASzT,GACd,GAA6B,gBAAlBA,GAAOyT,IAAyC,OAAlBzT,EAAOyT,KAAqBzT,EAAOyT,GAAOrR,UAAYpC,EAAOyT,KAAW7Q,QAAU5C,EAAOyT,KAAWnP,UAA6B,mBAAToP,IAAwB1T,EAAOyT,YAAkBC,IAA4B,mBAAXC,SAA0B3T,EAAOyT,YAAkBE,SAOlRH,EAAeC,GAASzT,EAAOyT,OAP6P,CAC5RD,EAAeC,KACf,KAAK,GAAIG,KAAa5T,GAAOyT,GACzBD,EAAeC,GAAOG,GAAa5T,EAAOyT,GAAOG,GAO7D,IAAK,GAAIC,KAAOhJ,GACZ,GAA2B,mBAAhB7K,GAAO6T,GACd7T,EAAO6T,GAAOhJ,EAASgJ,OAEtB,IAA2B,gBAAhB7T,GAAO6T,GACnB,IAAK,GAAIC,KAAWjJ,GAASgJ,GACW,mBAAzB7T,GAAO6T,GAAKC,KACnB9T,EAAO6T,GAAKC,GAAWjJ,EAASgJ,GAAKC,GAOrD,IAAI1T,GAAIF,IAcR,IAXAE,EAAEJ,OAASA,EACXI,EAAEoT,eAAiBA,EAGnBpT,EAAE2T,cAIe,mBAANzT,IAAqC,mBAAToT,KACnCpT,EAAIoT,IAES,mBAANpT,KAEHA,EADgB,mBAAToT,GACH9Q,OAAO8Q,MAAQ9Q,OAAOoR,OAASpR,OAAO+Q,OAGtCD,MAKZtT,EAAEE,EAAIA,EAKNF,EAAE6T,kBAAoBpG,OACtBzN,EAAE8T,oBAAsB,WAEpB,IAAK9T,EAAEJ,OAAO4N,YAAa,OAAO,CAClC,IACiB9H,GADbqO,GAAa,EACbC,IACJ,KAAMtO,IAAS1F,GAAEJ,OAAO4N,YAChBxN,EAAEJ,OAAO4N,YAAYyG,eAAevO,IACpCsO,EAAO3Q,KAAKqC,EAGpBsO,GAAOE,KAAK,SAAU7T,EAAG8T,GACrB,MAAO/J,UAAS/J,EAAG,IAAM+J,SAAS+J,EAAG,KAEzC,KAAK,GAAI1O,GAAI,EAAGA,EAAIuO,EAAO5R,OAAQqD,IAC/BC,EAAQsO,EAAOvO,GACXC,GAASlD,OAAOuC,aAAegP,IAC/BA,EAAarO,EAGrB,OAAOqO,IAAc,OAEzB/T,EAAEoU,cAAgB,WAEd,GAAIL,GAAa/T,EAAE8T,qBACnB,IAAIC,GAAc/T,EAAE6T,oBAAsBE,EAAY,CAClD,GAAIM,GAAoBN,IAAc/T,GAAEJ,OAAO4N,YAAcxN,EAAEJ,OAAO4N,YAAYuG,GAAc/T,EAAEoT,eAC9FkB,EAActU,EAAEJ,OAAOqB,MAASoT,EAAkB1G,gBAAkB3N,EAAEJ,OAAO+N,aACjF,KAAM,GAAI0F,KAASgB,GACfrU,EAAEJ,OAAOyT,GAASgB,EAAkBhB,EAExCrT,GAAE6T,kBAAoBE,EACnBO,GAAetU,EAAEuU,aAChBvU,EAAEwU,QAAO,KAKjBxU,EAAEJ,OAAO4N,aACTxN,EAAEoU,gBAMNpU,EAAEG,UAAYD,EAAEC,GACW,IAAvBH,EAAEG,UAAUiC,QAAhB,CACA,GAAIpC,EAAEG,UAAUiC,OAAS,EAAG,CACxB,GAAIqS,KAKJ,OAJAzU,GAAEG,UAAUJ,KAAK,WAEb0U,EAAQpR,KAAK,GAAIpD,GAAOH,KAAMF,MAE3B6U,EAIXzU,EAAEG,UAAU,GAAGR,OAASK,EACxBA,EAAEG,UAAUuG,KAAK,SAAU1G,GAE3BA,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyBlR,EAAEJ,OAAO8K,WAEzD1K,EAAEJ,OAAOqH,UACTjH,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,aAEnDlR,EAAE0U,QAAQC,UACX3U,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,cACpDlR,EAAEJ,OAAOgO,gBAAkB,GAE3B5N,EAAEJ,OAAO0L,YACTtL,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,eAGpDlR,EAAEJ,OAAO4M,UAAYxM,EAAEJ,OAAOkQ,yBAC9B9P,EAAEJ,OAAOiQ,qBAAsB,GAG/B7P,EAAEJ,OAAOkP,sBACT9O,EAAEJ,OAAO8P,gBAAkB,IAG1B,OAAQ,YAAa,QAAQvF,QAAQnK,EAAEJ,OAAO6L,SAAW,IACtDzL,EAAE0U,QAAQE,cACV5U,EAAEJ,OAAOiQ,qBAAsB,EAC/B7P,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,OAGpDlR,EAAEJ,OAAO6L,OAAS,SAGF,UAApBzL,EAAEJ,OAAO6L,QACTzL,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyBlR,EAAEJ,OAAO6L,QAEzC,SAApBzL,EAAEJ,OAAO6L,SACTzL,EAAEJ,OAAO8P,gBAAkB,EAC3B1P,EAAEJ,OAAO+N,cAAgB,EACzB3N,EAAEJ,OAAOgO,gBAAkB,EAC3B5N,EAAEJ,OAAOkO,eAAiB,EAC1B9N,EAAEJ,OAAOmO,gBAAiB,EAC1B/N,EAAEJ,OAAO8N,aAAe,EACxB1N,EAAEJ,OAAO4L,kBAAmB,EAC5BxL,EAAEJ,OAAO2L,gBAAiB,GAEN,SAApBvL,EAAEJ,OAAO6L,QAAyC,SAApBzL,EAAEJ,OAAO6L,SACvCzL,EAAEJ,OAAO+N,cAAgB,EACzB3N,EAAEJ,OAAOgO,gBAAkB,EAC3B5N,EAAEJ,OAAOkO,eAAiB,EAC1B9N,EAAEJ,OAAOiQ,qBAAsB,EAC/B7P,EAAEJ,OAAO8N,aAAe,EACxB1N,EAAEJ,OAAO2L,gBAAiB,EACa,mBAA5B4H,KACPnT,EAAEJ,OAAO4L,kBAAmB,IAKhCxL,EAAEJ,OAAOmQ,YAAc/P,EAAE0U,QAAQG,QACjC7U,EAAEJ,OAAOmQ,YAAa,GAI1B/P,EAAE8U,QAAU9U,EAAEG,UAAU4U,SAAS,IAAM/U,EAAEJ,OAAO8R,cAG5C1R,EAAEJ,OAAOoP,aACThP,EAAEgV,oBAAsB9U,EAAEF,EAAEJ,OAAOoP,YAC/BhP,EAAEJ,OAAOmP,mBAAoD,gBAAxB/O,GAAEJ,OAAOoP,YAA2BhP,EAAEgV,oBAAoB5S,OAAS,GAAsD,IAAjDpC,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOoP,YAAY5M,SACnJpC,EAAEgV,oBAAsBhV,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOoP,aAGtB,YAA5BhP,EAAEJ,OAAO4P,gBAAgCxP,EAAEJ,OAAOsP,oBAClDlP,EAAEgV,oBAAoBE,SAASlV,EAAEJ,OAAOuS,wBAA0B,aAGlEnS,EAAEJ,OAAOsP,qBAAsB,EAEnClP,EAAEgV,oBAAoBE,SAASlV,EAAEJ,OAAOuS,wBAA0BnS,EAAEJ,OAAO4P,kBAG3ExP,EAAEJ,OAAO+P,YAAc3P,EAAEJ,OAAOgQ,cAC5B5P,EAAEJ,OAAO+P,aACT3P,EAAE2P,WAAazP,EAAEF,EAAEJ,OAAO+P,YACtB3P,EAAEJ,OAAOmP,mBAAoD,gBAAxB/O,GAAEJ,OAAO+P,YAA2B3P,EAAE2P,WAAWvN,OAAS,GAAsD,IAAjDpC,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAO+P,YAAYvN,SAC1IpC,EAAE2P,WAAa3P,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAO+P,cAG7C3P,EAAEJ,OAAOgQ,aACT5P,EAAE4P,WAAa1P,EAAEF,EAAEJ,OAAOgQ,YACtB5P,EAAEJ,OAAOmP,mBAAoD,gBAAxB/O,GAAEJ,OAAOgQ,YAA2B5P,EAAE4P,WAAWxN,OAAS,GAAsD,IAAjDpC,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOgQ,YAAYxN,SAC1IpC,EAAE4P,WAAa5P,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOgQ,eAMrD5P,EAAE4D,aAAe,WACb,MAA8B,eAAvB5D,EAAEJ,OAAO8K,WAKpB1K,EAAEoF,IAAMpF,EAAE4D,iBAAwD,QAArC5D,EAAEG,UAAU,GAAGgV,IAAI9Q,eAA4D,QAAjCrE,EAAEG,UAAUiV,IAAI,cACvFpV,EAAEoF,KACFpF,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,OAIpDlR,EAAEoF,MACFpF,EAAEqV,SAAwC,gBAA7BrV,EAAE8U,QAAQM,IAAI,YAI3BpV,EAAEJ,OAAOgO,gBAAkB,GAC3B5N,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,YAIpDlR,EAAEsV,OAAOC,SACTvV,EAAE2T,WAAWtQ,KAAKrD,EAAEJ,OAAOsR,uBAAyB,WAIxDlR,EAAEG,UAAU+U,SAASlV,EAAE2T,WAAW6B,KAAK,MAGvCxV,EAAEyV,UAAY,EAGdzV,EAAEiK,SAAW,EAGbjK,EAAE0V,SAAW,EAKb1V,EAAE2V,gBAAkB,WAChB3V,EAAEJ,OAAO+D,kBAAmB,EACxB3D,EAAEJ,OAAOiE,oBAAqB,GAAS7D,EAAEJ,OAAOmQ,YAChD/P,EAAE4V,mBAGV5V,EAAE6V,gBAAkB,WAChB7V,EAAEJ,OAAOiE,kBAAmB,EACxB7D,EAAEJ,OAAO+D,oBAAqB,GAAS3D,EAAEJ,OAAOmQ,YAChD/P,EAAE4V,mBAGV5V,EAAE8V,WAAa,WACX9V,EAAEJ,OAAO+D,iBAAmB3D,EAAEJ,OAAOiE,kBAAmB,EACpD7D,EAAEJ,OAAOmQ,YAAY/P,EAAE4V,mBAE/B5V,EAAE+V,kBAAoB,WAClB/V,EAAEJ,OAAO+D,kBAAmB,EACxB3D,EAAEJ,OAAOiE,oBAAqB,GAAQ7D,EAAEJ,OAAOmQ,YAC/C/P,EAAEgW,iBAGVhW,EAAEiW,kBAAoB,WAClBjW,EAAEJ,OAAOiE,kBAAmB,EACxB7D,EAAEJ,OAAO+D,oBAAqB,GAAQ3D,EAAEJ,OAAOmQ,YAC/C/P,EAAEgW,iBAGVhW,EAAEkW,aAAe,WACblW,EAAEJ,OAAO+D,iBAAmB3D,EAAEJ,OAAOiE,kBAAmB,EACpD7D,EAAEJ,OAAOmQ,YAAY/P,EAAEgW,iBAY/BhW,EAAEgW,cAAgB,SAASG,GACvBnW,EAAEG,UAAU,GAAGiW,MAAMC,OAAS,OAC9BrW,EAAEG,UAAU,GAAGiW,MAAMC,OAASF,EAAS,mBAAqB,eAC5DnW,EAAEG,UAAU,GAAGiW,MAAMC,OAASF,EAAS,eAAiB,YACxDnW,EAAEG,UAAU,GAAGiW,MAAMC,OAASF,EAAS,WAAY,QAEvDnW,EAAE4V,gBAAkB,WAChB5V,EAAEG,UAAU,GAAGiW,MAAMC,OAAS,IAE9BrW,EAAEJ,OAAOmQ,YACT/P,EAAEgW,gBAKNhW,EAAEsW,gBACFtW,EAAEuW,aAAe,EAEjBvW,EAAEwW,UAAY,SAAUC,EAAYC,EAAKC,EAAQC,EAAOC,EAAkBC,GAEtE,QAASC,KACDD,GAAUA,IAFlB,GAAIE,EAICP,GAAWQ,UAAaJ,EAmBzBE,IAlBIL,GACAM,EAAQ,GAAIxU,QAAO0U,MACnBF,EAAMG,OAASJ,EACfC,EAAMI,QAAUL,EACZH,IACAI,EAAMJ,MAAQA,GAEdD,IACAK,EAAML,OAASA,GAEfD,IACAM,EAAMN,IAAMA,IAGhBK,KAOZ/W,EAAEsQ,cAAgB,WAEd,QAAS+G,KACY,mBAANrX,IAA2B,OAANA,GAAeA,IACxByN,SAAnBzN,EAAEuW,cAA4BvW,EAAEuW,eAChCvW,EAAEuW,eAAiBvW,EAAEsW,aAAalU,SAC9BpC,EAAEJ,OAAO2Q,qBAAqBvQ,EAAEsX,SACpCtX,EAAEoB,KAAK,gBAAiBpB,KANhCA,EAAEsW,aAAetW,EAAEG,UAAU8U,KAAK,MASlC,KAAK,GAAIxP,GAAI,EAAGA,EAAIzF,EAAEsW,aAAalU,OAAQqD,IACvCzF,EAAEwW,UAAUxW,EAAEsW,aAAa7Q,GAAKzF,EAAEsW,aAAa7Q,GAAG8R,YAAcvX,EAAEsW,aAAa7Q,GAAG+R,aAAa,OAAUxX,EAAEsW,aAAa7Q,GAAGkR,QAAU3W,EAAEsW,aAAa7Q,GAAG+R,aAAa,UAAYxX,EAAEsW,aAAa7Q,GAAGmR,OAAS5W,EAAEsW,aAAa7Q,GAAG+R,aAAa,UAAU,EAAMH,IAOlQrX,EAAEe,kBAAoB0M,OACtBzN,EAAEyX,aAAc,EAChBzX,EAAE0X,gBAAiB,EA8BnB1X,EAAE2X,cAAgB,WACd,MAAmC,mBAAxB3X,GAAEe,sBACRf,EAAEJ,OAAOY,YACVR,EAAEyX,cACNzX,EAAEyX,aAAc,EAChBzX,EAAEoB,KAAK,kBAAmBpB,OAC1BQ,SAEJR,EAAEuB,aAAe,SAAUqW,GAClB5X,EAAEe,oBACHf,EAAEe,mBAAmBiH,aAAahI,EAAEe,mBACxCf,EAAEyX,aAAc,EAChBzX,EAAEe,kBAAoB0M,OACtBzN,EAAEoB,KAAK,iBAAkBpB,KAE7BA,EAAE6X,cAAgB,SAAUhN,GACpB7K,EAAE0X,iBACF1X,EAAEe,mBAAmBiH,aAAahI,EAAEe,mBACxCf,EAAE0X,gBAAiB,EACL,IAAV7M,GACA7K,EAAE0X,gBAAiB,EACnBlX,KAGAR,EAAE8U,QAAQgD,cAAc,WACf9X,IACLA,EAAE0X,gBAAiB,EACd1X,EAAEyX,YAIHjX,IAHAR,EAAEuB,oBAWlBvB,EAAEwH,aAAe,WACb,OAASxH,EAAE+X,SAAS,IAExB/X,EAAEyH,aAAe,WACb,OAASzH,EAAE+X,SAAS/X,EAAE+X,SAAS3V,OAAS,IAK5CpC,EAAEgY,iBAAmB,WACjB,GAEIvS,GAFAwS,KACAC,EAAY,CAIhB,IAA8B,SAA3BlY,EAAEJ,OAAO+N,eAA4B3N,EAAEJ,OAAO+N,cAAgB,EAC7D,IAAKlI,EAAI,EAAGA,EAAInF,KAAK6X,KAAKnY,EAAEJ,OAAO+N,eAAgBlI,IAAK,CACpD,GAAIvD,GAAQlC,EAAEa,YAAc4E,CAC5B,IAAGvD,EAAQlC,EAAEW,OAAOyB,OAAQ,KAC5B6V,GAAa5U,KAAKrD,EAAEW,OAAOC,GAAGsB,GAAO,QAGzC+V,GAAa5U,KAAKrD,EAAEW,OAAOC,GAAGZ,EAAEa,aAAa,GAIjD,KAAK4E,EAAI,EAAGA,EAAIwS,EAAa7V,OAAQqD,IACjC,GAA+B,mBAApBwS,GAAaxS,GAAoB,CACxC,GAAID,GAASyS,EAAaxS,GAAG2S,YAC7BF,GAAY1S,EAAS0S,EAAY1S,EAAS0S,EAK9CA,GAAWlY,EAAE8U,QAAQM,IAAI,SAAU8C,EAAY,OAEvDlY,EAAEqY,oBAAsB,WACpB,GAAI9S,GAAOC,CAEPD,GAD0B,mBAAnBvF,GAAEJ,OAAO2F,MACRvF,EAAEJ,OAAO2F,MAGTvF,EAAEG,UAAU,GAAGmY,YAGvB9S,EAD2B,mBAApBxF,GAAEJ,OAAO4F,OACPxF,EAAEJ,OAAO4F,OAGTxF,EAAEG,UAAU,GAAGoY,aAEd,IAAVhT,GAAevF,EAAE4D,gBAA6B,IAAX4B,IAAiBxF,EAAE4D,iBAK1D2B,EAAQA,EAAQ6E,SAASpK,EAAEG,UAAUiV,IAAI,gBAAiB,IAAMhL,SAASpK,EAAEG,UAAUiV,IAAI,iBAAkB,IAC3G5P,EAASA,EAAS4E,SAASpK,EAAEG,UAAUiV,IAAI,eAAgB,IAAMhL,SAASpK,EAAEG,UAAUiV,IAAI,kBAAmB,IAG7GpV,EAAEuF,MAAQA,EACVvF,EAAEwF,OAASA,EACXxF,EAAEwY,KAAOxY,EAAE4D,eAAiB5D,EAAEuF,MAAQvF,EAAEwF,SAG5CxF,EAAEyY,iBAAmB,WACjBzY,EAAEW,OAASX,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,YAC7CvE,EAAE+X,YACF/X,EAAE0Y,cACF1Y,EAAE2Y,kBAEF,IAEIlT,GAFAiI,EAAe1N,EAAEJ,OAAO8N,aACxBkL,GAAiB5Y,EAAEJ,OAAOoO,mBAE1B6K,EAAgB,EAChB3W,EAAQ,CACZ,IAAsB,mBAAXlC,GAAEwY,KAAb,CAC4B,gBAAjB9K,IAA6BA,EAAavD,QAAQ,MAAQ,IACjEuD,EAAeoL,WAAWpL,EAAaqL,QAAQ,IAAK,KAAO,IAAM/Y,EAAEwY,MAGvExY,EAAEgZ,aAAetL,EAEb1N,EAAEoF,IAAKpF,EAAEW,OAAOyU,KAAK6D,WAAY,GAAIC,UAAW,KAC/ClZ,EAAEW,OAAOyU,KAAK+D,YAAa,GAAIC,aAAc,IAElD,IAAIC,EACArZ,GAAEJ,OAAOgO,gBAAkB,IAEvByL,EADA/Y,KAAKC,MAAMP,EAAEW,OAAOyB,OAASpC,EAAEJ,OAAOgO,mBAAqB5N,EAAEW,OAAOyB,OAASpC,EAAEJ,OAAOgO,gBAC7D5N,EAAEW,OAAOyB,OAGT9B,KAAK6X,KAAKnY,EAAEW,OAAOyB,OAASpC,EAAEJ,OAAOgO,iBAAmB5N,EAAEJ,OAAOgO,gBAE/D,SAA3B5N,EAAEJ,OAAO+N,eAA6D,QAAjC3N,EAAEJ,OAAOiO,sBAC9CwL,EAAyB/Y,KAAKgZ,IAAID,EAAwBrZ,EAAEJ,OAAO+N,cAAgB3N,EAAEJ,OAAOgO,kBAKpG,IAAI2L,GACA3L,EAAkB5N,EAAEJ,OAAOgO,gBAC3B4L,EAAeH,EAAyBzL,EACxC6L,EAAiBD,GAAgBxZ,EAAEJ,OAAOgO,gBAAkB4L,EAAexZ,EAAEW,OAAOyB,OACxF,KAAKqD,EAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAAK,CAClC8T,EAAY,CACZ,IAAIG,GAAQ1Z,EAAEW,OAAOC,GAAG6E,EACxB,IAAIzF,EAAEJ,OAAOgO,gBAAkB,EAAG,CAE9B,GAAI+L,GACAC,EAAQC,CACyB,YAAjC7Z,EAAEJ,OAAOiO,qBACT+L,EAAStZ,KAAKC,MAAMkF,EAAImI,GACxBiM,EAAMpU,EAAImU,EAAShM,GACfgM,EAASH,GAAmBG,IAAWH,GAAkBI,IAAQjM,EAAgB,MAC3EiM,GAAOjM,IACTiM,EAAM,EACND,KAGRD,EAAqBC,EAASC,EAAMR,EAAyBzL,EAC7D8L,EACKtE,KACG0E,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAASP,MAIjBE,EAAMvZ,KAAKC,MAAMkF,EAAI+T,GACrBI,EAASnU,EAAIoU,EAAML,GAEvBE,EACKtE,IACG,WAAapV,EAAE4D,eAAiB,MAAQ,QAC/B,IAARiW,GAAa7Z,EAAEJ,OAAO8N,cAAkB1N,EAAEJ,OAAO8N,aAAe,MAEpE5M,KAAK,qBAAsB8Y,GAC3B9Y,KAAK,kBAAmB+Y,GAGJ,SAAzBH,EAAMtE,IAAI,aACiB,SAA3BpV,EAAEJ,OAAO+N,eACT4L,EAAYvZ,EAAE4D,eAAiB8V,EAAMS,YAAW,GAAQT,EAAMU,aAAY,GACtEpa,EAAEJ,OAAOsO,eAAcqL,EAAYnZ,EAAMmZ,MAG7CA,GAAavZ,EAAEwY,MAAQxY,EAAEJ,OAAO+N,cAAgB,GAAKD,GAAgB1N,EAAEJ,OAAO+N,cAC1E3N,EAAEJ,OAAOsO,eAAcqL,EAAYnZ,EAAMmZ,IAEzCvZ,EAAE4D,eACF5D,EAAEW,OAAO8E,GAAG2Q,MAAM7Q,MAAQgU,EAAY,KAGtCvZ,EAAEW,OAAO8E,GAAG2Q,MAAM5Q,OAAS+T,EAAY,MAG/CvZ,EAAEW,OAAO8E,GAAG4U,gBAAkBd,EAC9BvZ,EAAE2Y,gBAAgBtV,KAAKkW,GAGnBvZ,EAAEJ,OAAOmO,gBACT6K,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAInL,EAC1D,IAANjI,IAASmT,EAAgBA,EAAgB5Y,EAAEwY,KAAO,EAAI9K,GACtDpN,KAAKuG,IAAI+R,GAAiB,OAAUA,EAAgB,GACpD,EAAU5Y,EAAEJ,OAAOkO,iBAAmB,GAAG9N,EAAE+X,SAAS1U,KAAKuV,GAC7D5Y,EAAE0Y,WAAWrV,KAAKuV,KAGd,EAAU5Y,EAAEJ,OAAOkO,iBAAmB,GAAG9N,EAAE+X,SAAS1U,KAAKuV,GAC7D5Y,EAAE0Y,WAAWrV,KAAKuV,GAClBA,EAAgBA,EAAgBW,EAAY7L,GAGhD1N,EAAEgZ,aAAeO,EAAY7L,EAE7BmL,EAAgBU,EAEhBrX,KAEJlC,EAAEgZ,YAAc1Y,KAAKgZ,IAAItZ,EAAEgZ,YAAahZ,EAAEwY,MAAQxY,EAAEJ,OAAOqO,iBAC3D,IAAIqM,EAWJ,IARIta,EAAEoF,KAAOpF,EAAEqV,WAAiC,UAApBrV,EAAEJ,OAAO6L,QAA0C,cAApBzL,EAAEJ,OAAO6L,SAChEzL,EAAE8U,QAAQM,KAAK7P,MAAOvF,EAAEgZ,YAAchZ,EAAEJ,OAAO8N,aAAe,OAE7D1N,EAAE0U,QAAQC,UAAW3U,EAAEJ,OAAO2L,iBAC3BvL,EAAE4D,eAAgB5D,EAAE8U,QAAQM,KAAK7P,MAAOvF,EAAEgZ,YAAchZ,EAAEJ,OAAO8N,aAAe,OAC/E1N,EAAE8U,QAAQM,KAAK5P,OAAQxF,EAAEgZ,YAAchZ,EAAEJ,OAAO8N,aAAe,QAGpE1N,EAAEJ,OAAOgO,gBAAkB,IAC3B5N,EAAEgZ,aAAeO,EAAYvZ,EAAEJ,OAAO8N,cAAgB2L,EACtDrZ,EAAEgZ,YAAc1Y,KAAK6X,KAAKnY,EAAEgZ,YAAchZ,EAAEJ,OAAOgO,iBAAmB5N,EAAEJ,OAAO8N,aAC3E1N,EAAE4D,eAAgB5D,EAAE8U,QAAQM,KAAK7P,MAAOvF,EAAEgZ,YAAchZ,EAAEJ,OAAO8N,aAAe,OAC/E1N,EAAE8U,QAAQM,KAAK5P,OAAQxF,EAAEgZ,YAAchZ,EAAEJ,OAAO8N,aAAe,OAChE1N,EAAEJ,OAAOmO,gBAAgB,CAEzB,IADAuM,KACK7U,EAAI,EAAGA,EAAIzF,EAAE+X,SAAS3V,OAAQqD,IAC3BzF,EAAE+X,SAAStS,GAAKzF,EAAEgZ,YAAchZ,EAAE+X,SAAS,IAAIuC,EAAcjX,KAAKrD,EAAE+X,SAAStS,GAErFzF,GAAE+X,SAAWuC,EAKrB,IAAKta,EAAEJ,OAAOmO,eAAgB,CAE1B,IADAuM,KACK7U,EAAI,EAAGA,EAAIzF,EAAE+X,SAAS3V,OAAQqD,IAC3BzF,EAAE+X,SAAStS,IAAMzF,EAAEgZ,YAAchZ,EAAEwY,MACnC8B,EAAcjX,KAAKrD,EAAE+X,SAAStS,GAGtCzF,GAAE+X,SAAWuC,EACTha,KAAKC,MAAMP,EAAEgZ,YAAchZ,EAAEwY,MAAQlY,KAAKC,MAAMP,EAAE+X,SAAS/X,EAAE+X,SAAS3V,OAAS,IAAM,GACrFpC,EAAE+X,SAAS1U,KAAKrD,EAAEgZ,YAAchZ,EAAEwY,MAGhB,IAAtBxY,EAAE+X,SAAS3V,SAAcpC,EAAE+X,UAAY,IAEb,IAA1B/X,EAAEJ,OAAO8N,eACL1N,EAAE4D,eACE5D,EAAEoF,IAAKpF,EAAEW,OAAOyU,KAAK6D,WAAYvL,EAAe,OAC/C1N,EAAEW,OAAOyU,KAAK+D,YAAazL,EAAe,OAE9C1N,EAAEW,OAAOyU,KAAKgE,aAAc1L,EAAe,QAEhD1N,EAAEJ,OAAOiQ,qBACT7P,EAAEua,uBAGVva,EAAEua,mBAAqB,WACnB,IAAK,GAAI9U,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IACjCzF,EAAEW,OAAO8E,GAAG+U,kBAAoBxa,EAAE4D,eAAiB5D,EAAEW,OAAO8E,GAAGgV,WAAaza,EAAEW,OAAO8E,GAAGiV,WAOhG1a,EAAE2a,qBAAuB,WACrB,GAAalV,GAAGmV,EAAZC,EAAM,CACV,IAAI7a,EAAEJ,OAAOmO,eAAgB,CACzB,GACI+M,GADAtC,EAAOxY,EAAEW,OAAOX,EAAEa,aAAawZ,eAEnC,KAAK5U,EAAIzF,EAAEa,YAAc,EAAG4E,EAAIzF,EAAEW,OAAOyB,OAAQqD,IACzCzF,EAAEW,OAAO8E,KAAOqV,IAChBtC,GAAQxY,EAAEW,OAAO8E,GAAG4U,gBACpBQ,IACIrC,EAAOxY,EAAEwY,OAAMsC,GAAY,GAGvC,KAAKF,EAAI5a,EAAEa,YAAc,EAAG+Z,GAAK,EAAGA,IAC5B5a,EAAEW,OAAOia,KAAOE,IAChBtC,GAAQxY,EAAEW,OAAOia,GAAGP,gBACpBQ,IACIrC,EAAOxY,EAAEwY,OAAMsC,GAAY,QAKvC,KAAKrV,EAAIzF,EAAEa,YAAc,EAAG4E,EAAIzF,EAAEW,OAAOyB,OAAQqD,IACzCzF,EAAE0Y,WAAWjT,GAAKzF,EAAE0Y,WAAW1Y,EAAEa,aAAeb,EAAEwY,MAClDqC,GAIZ,OAAOA,IAKX7a,EAAE+a,qBAAuB,SAAUtF,GAI/B,GAHyB,mBAAdA,KACPA,EAAYzV,EAAEyV,WAAa,GAEP,IAApBzV,EAAEW,OAAOyB,OAAb,CAC6C,mBAAlCpC,GAAEW,OAAO,GAAG6Z,mBAAmCxa,EAAEua,oBAE5D,IAAIS,IAAgBvF,CAChBzV,GAAEoF,MAAK4V,EAAevF,GAG1BzV,EAAEW,OAAOsa,YAAYjb,EAAEJ,OAAOwR,kBAC9B,KAAK,GAAI3L,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAAK,CACtC,GAAIiU,GAAQ1Z,EAAEW,OAAO8E,GACjByV,GAAiBF,GAAgBhb,EAAEJ,OAAOmO,eAAiB/N,EAAEwH,eAAiB,GAAKkS,EAAMc,oBAAsBd,EAAMW,gBAAkBra,EAAEJ,OAAO8N,aACpJ,IAAI1N,EAAEJ,OAAOkQ,sBAAuB,CAChC,GAAIqL,KAAgBH,EAAetB,EAAMc,mBACrCY,EAAaD,EAAcnb,EAAE2Y,gBAAgBlT,GAC7C4V,EACCF,GAAe,GAAKA,EAAcnb,EAAEwY,MACpC4C,EAAa,GAAKA,GAAcpb,EAAEwY,MAClC2C,GAAe,GAAKC,GAAcpb,EAAEwY,IACrC6C,IACArb,EAAEW,OAAOC,GAAG6E,GAAGyP,SAASlV,EAAEJ,OAAOwR,mBAGzCsI,EAAMzP,SAAWjK,EAAEoF,KAAO8V,EAAgBA,KAGlDlb,EAAE4H,eAAiB,SAAU6N,GACA,mBAAdA,KACPA,EAAYzV,EAAEyV,WAAa,EAE/B,IAAI6F,GAAiBtb,EAAEyH,eAAiBzH,EAAEwH,eACtCH,EAAerH,EAAEsH,YACjBC,EAASvH,EAAEqB,KACQ,KAAnBia,GACAtb,EAAEiK,SAAW,EACbjK,EAAEsH,YAActH,EAAEqB,OAAQ,IAG1BrB,EAAEiK,UAAYwL,EAAYzV,EAAEwH,gBAAkB,EAC9CxH,EAAEsH,YAActH,EAAEiK,UAAY,EAC9BjK,EAAEqB,MAAQrB,EAAEiK,UAAY,GAExBjK,EAAEsH,cAAgBD,GAAcrH,EAAEoB,KAAK,mBAAoBpB,GAC3DA,EAAEqB,QAAUkG,GAAQvH,EAAEoB,KAAK,aAAcpB,GAEzCA,EAAEJ,OAAOiQ,qBAAqB7P,EAAE+a,qBAAqBtF,GACzDzV,EAAEoB,KAAK,aAAcpB,EAAGA,EAAEiK,WAE9BjK,EAAE6H,kBAAoB,WAClB,GACI0T,GAAgB9V,EAAG+V,EADnB/F,EAAYzV,EAAEoF,IAAMpF,EAAEyV,WAAazV,EAAEyV,SAEzC,KAAKhQ,EAAI,EAAGA,EAAIzF,EAAE0Y,WAAWtW,OAAQqD,IACE,mBAAxBzF,GAAE0Y,WAAWjT,EAAI,GACpBgQ,GAAazV,EAAE0Y,WAAWjT,IAAMgQ,EAAYzV,EAAE0Y,WAAWjT,EAAI,IAAMzF,EAAE0Y,WAAWjT,EAAI,GAAKzF,EAAE0Y,WAAWjT,IAAM,EAC5G8V,EAAiB9V,EAEZgQ,GAAazV,EAAE0Y,WAAWjT,IAAMgQ,EAAYzV,EAAE0Y,WAAWjT,EAAI,KAClE8V,EAAiB9V,EAAI,GAIrBgQ,GAAazV,EAAE0Y,WAAWjT,KAC1B8V,EAAiB9V,EAK1BzF,GAAEJ,OAAOiR,sBACJ0K,EAAiB,GAA+B,mBAAnBA,MAAgCA,EAAiB,GAOtFC,EAAYlb,KAAKC,MAAMgb,EAAiBvb,EAAEJ,OAAOkO,gBAC7C0N,GAAaxb,EAAE+X,SAAS3V,SAAQoZ,EAAYxb,EAAE+X,SAAS3V,OAAS,GAEhEmZ,IAAmBvb,EAAEa,cAGzBb,EAAEwb,UAAYA,EACdxb,EAAEyb,cAAgBzb,EAAEa,YACpBb,EAAEa,YAAc0a,EAChBvb,EAAE8H,gBACF9H,EAAE0b,oBAEN1b,EAAE0b,gBAAkB,WAChB1b,EAAE2b,UAAYvR,SAASpK,EAAEW,OAAOC,GAAGZ,EAAEa,aAAaC,KAAK,4BAA8Bd,EAAEa,YAAa,KAMxGb,EAAE8H,cAAgB,WACd9H,EAAEW,OAAOsa,YAAYjb,EAAEJ,OAAO4E,iBAAmB,IAAMxE,EAAEJ,OAAO0R,eAAiB,IAAMtR,EAAEJ,OAAO4R,eAAiB,IAAMxR,EAAEJ,OAAOuR,0BAA4B,IAAMnR,EAAEJ,OAAO2R,wBAA0B,IAAMvR,EAAEJ,OAAO6R,wBACpN,IAAI/Q,GAAcV,EAAEW,OAAOC,GAAGZ,EAAEa,YAEhCH,GAAYwU,SAASlV,EAAEJ,OAAO4E,kBAC1B5E,EAAOqB,OAEHP,EAAYkb,SAAS5b,EAAEJ,OAAOyR,qBAC9BrR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,SAAWvE,EAAEJ,OAAOyR,oBAAsB,8BAAgCrR,EAAE2b,UAAY,MAAMzG,SAASlV,EAAEJ,OAAOuR,2BAG/JnR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,IAAMvE,EAAEJ,OAAOyR,oBAAsB,6BAA+BrR,EAAE2b,UAAY,MAAMzG,SAASlV,EAAEJ,OAAOuR,2BAIjK,IAAI0K,GAAYnb,EAAYob,KAAK,IAAM9b,EAAEJ,OAAO2E,YAAY2Q,SAASlV,EAAEJ,OAAO0R,eAC1EtR,GAAEJ,OAAOqB,MAA6B,IAArB4a,EAAUzZ,SAC3ByZ,EAAY7b,EAAEW,OAAOC,GAAG,GACxBib,EAAU3G,SAASlV,EAAEJ,OAAO0R,gBAGhC,IAAIyK,GAAYrb,EAAYsb,KAAK,IAAMhc,EAAEJ,OAAO2E,YAAY2Q,SAASlV,EAAEJ,OAAO4R,eAsB9E,IArBIxR,EAAEJ,OAAOqB,MAA6B,IAArB8a,EAAU3Z,SAC3B2Z,EAAY/b,EAAEW,OAAOC,IAAG,GACxBmb,EAAU7G,SAASlV,EAAEJ,OAAO4R,iBAE5B5R,EAAOqB,OAEH4a,EAAUD,SAAS5b,EAAEJ,OAAOyR,qBAC5BrR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,SAAWvE,EAAEJ,OAAOyR,oBAAsB,8BAAgCwK,EAAU/a,KAAK,2BAA6B,MAAMoU,SAASlV,EAAEJ,OAAO2R,yBAG7LvR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,IAAMvE,EAAEJ,OAAOyR,oBAAsB,6BAA+BwK,EAAU/a,KAAK,2BAA6B,MAAMoU,SAASlV,EAAEJ,OAAO2R,yBAEvLwK,EAAUH,SAAS5b,EAAEJ,OAAOyR,qBAC5BrR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,SAAWvE,EAAEJ,OAAOyR,oBAAsB,8BAAgC0K,EAAUjb,KAAK,2BAA6B,MAAMoU,SAASlV,EAAEJ,OAAO6R,yBAG7LzR,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,IAAMvE,EAAEJ,OAAOyR,oBAAsB,6BAA+B0K,EAAUjb,KAAK,2BAA6B,MAAMoU,SAASlV,EAAEJ,OAAO6R,0BAK3LzR,EAAEgV,qBAAuBhV,EAAEgV,oBAAoB5S,OAAS,EAAG,CAE3D,GAAI6Z,GACAC,EAAQlc,EAAEJ,OAAOqB,KAAOX,KAAK6X,MAAMnY,EAAEW,OAAOyB,OAA0B,EAAjBpC,EAAEyQ,cAAoBzQ,EAAEJ,OAAOkO,gBAAkB9N,EAAE+X,SAAS3V,MAiCrH,IAhCIpC,EAAEJ,OAAOqB,MACTgb,EAAU3b,KAAK6X,MAAMnY,EAAEa,YAAcb,EAAEyQ,cAAczQ,EAAEJ,OAAOkO,gBAC1DmO,EAAUjc,EAAEW,OAAOyB,OAAS,EAAqB,EAAjBpC,EAAEyQ,eAClCwL,GAAqBjc,EAAEW,OAAOyB,OAA0B,EAAjBpC,EAAEyQ,cAEzCwL,EAAUC,EAAQ,IAAGD,GAAoBC,GACzCD,EAAU,GAAiC,YAA5Bjc,EAAEJ,OAAO4P,iBAA8ByM,EAAUC,EAAQD,IAIxEA,EADuB,mBAAhBjc,GAAEwb,UACCxb,EAAEwb,UAGFxb,EAAEa,aAAe,EAIH,YAA5Bb,EAAEJ,OAAO4P,gBAAgCxP,EAAEmc,SAAWnc,EAAEmc,QAAQ/Z,OAAS,IACzEpC,EAAEmc,QAAQlB,YAAYjb,EAAEJ,OAAOgS,mBAC3B5R,EAAEgV,oBAAoB5S,OAAS,EAC/BpC,EAAEmc,QAAQpc,KAAK,WACPG,EAAEJ,MAAMoC,UAAY+Z,GAAS/b,EAAEJ,MAAMoV,SAASlV,EAAEJ,OAAOgS,qBAI/D5R,EAAEmc,QAAQvb,GAAGqb,GAAS/G,SAASlV,EAAEJ,OAAOgS,oBAGhB,aAA5B5R,EAAEJ,OAAO4P,iBACTxP,EAAEgV,oBAAoBC,KAAK,IAAMjV,EAAEJ,OAAOkS,wBAAwBsK,KAAKH,EAAU,GACjFjc,EAAEgV,oBAAoBC,KAAK,IAAMjV,EAAEJ,OAAOmS,sBAAsBqK,KAAKF,IAEzC,aAA5Blc,EAAEJ,OAAO4P,eAA+B,CACxC,GAAI6M,IAASJ,EAAU,GAAKC,EACxBI,EAASD,EACTE,EAAS,CACRvc,GAAE4D,iBACH2Y,EAASF,EACTC,EAAS,GAEbtc,EAAEgV,oBAAoBC,KAAK,IAAMjV,EAAEJ,OAAOqS,4BAA4B5H,UAAU,6BAA+BiS,EAAS,YAAcC,EAAS,KAAKC,WAAWxc,EAAEJ,OAAOiL,OAE5I,WAA5B7K,EAAEJ,OAAO4P,gBAA+BxP,EAAEJ,OAAO2P,yBACjDvP,EAAEgV,oBAAoByH,KAAKzc,EAAEJ,OAAO2P,uBAAuBvP,EAAGic,EAAU,EAAGC,IAC3Elc,EAAEoB,KAAK,uBAAwBpB,EAAGA,EAAEgV,oBAAoB,KAK3DhV,EAAEJ,OAAOqB,OACNjB,EAAEJ,OAAOgQ,YAAc5P,EAAE4P,YAAc5P,EAAE4P,WAAWxN,OAAS,IACzDpC,EAAEsH,aACFtH,EAAE4P,WAAWsF,SAASlV,EAAEJ,OAAOiS,qBAC3B7R,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAK8J,QAAQ1c,EAAE4P,cAG9C5P,EAAE4P,WAAWqL,YAAYjb,EAAEJ,OAAOiS,qBAC9B7R,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAK+J,OAAO3c,EAAE4P,cAGjD5P,EAAEJ,OAAO+P,YAAc3P,EAAE2P,YAAc3P,EAAE2P,WAAWvN,OAAS,IACzDpC,EAAEqB,OACFrB,EAAE2P,WAAWuF,SAASlV,EAAEJ,OAAOiS,qBAC3B7R,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAK8J,QAAQ1c,EAAE2P,cAG9C3P,EAAE2P,WAAWsL,YAAYjb,EAAEJ,OAAOiS,qBAC9B7R,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAK+J,OAAO3c,EAAE2P,gBAS7D3P,EAAE4c,iBAAmB,WACjB,GAAK5c,EAAEJ,OAAOoP,YACVhP,EAAEgV,qBAAuBhV,EAAEgV,oBAAoB5S,OAAS,EAAG,CAC3D,GAAIya,GAAiB,EACrB,IAAgC,YAA5B7c,EAAEJ,OAAO4P,eAA8B,CAEvC,IAAK,GADDsN,GAAkB9c,EAAEJ,OAAOqB,KAAOX,KAAK6X,MAAMnY,EAAEW,OAAOyB,OAA0B,EAAjBpC,EAAEyQ,cAAoBzQ,EAAEJ,OAAOkO,gBAAkB9N,EAAE+X,SAAS3V,OACtHqD,EAAI,EAAGA,EAAIqX,EAAiBrX,IAE7BoX,GADA7c,EAAEJ,OAAOwP,uBACSpP,EAAEJ,OAAOwP,uBAAuBpP,EAAGyF,EAAGzF,EAAEJ,OAAO+R,aAG/C,IAAM3R,EAAEJ,OAAOqP,kBAAkB,WAAajP,EAAEJ,OAAO+R,YAAc,OAAS3R,EAAEJ,OAAOqP,kBAAoB,GAGrIjP,GAAEgV,oBAAoByH,KAAKI,GAC3B7c,EAAEmc,QAAUnc,EAAEgV,oBAAoBC,KAAK,IAAMjV,EAAEJ,OAAO+R,aAClD3R,EAAEJ,OAAOsP,qBAAuBlP,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MACnD5S,EAAE4S,KAAKmK,iBAGiB,aAA5B/c,EAAEJ,OAAO4P,iBAELqN,EADA7c,EAAEJ,OAAO0P,yBACQtP,EAAEJ,OAAO0P,yBAAyBtP,EAAGA,EAAEJ,OAAOkS,uBAAwB9R,EAAEJ,OAAOmS,sBAI5F,gBAAkB/R,EAAEJ,OAAOkS,uBAAyB,4BAElC9R,EAAEJ,OAAOmS,qBAAqB,YAExD/R,EAAEgV,oBAAoByH,KAAKI,IAEC,aAA5B7c,EAAEJ,OAAO4P,iBAELqN,EADA7c,EAAEJ,OAAOyP,yBACQrP,EAAEJ,OAAOyP,yBAAyBrP,EAAGA,EAAEJ,OAAOqS,4BAG9C,gBAAkBjS,EAAEJ,OAAOqS,2BAA6B,YAE7EjS,EAAEgV,oBAAoByH,KAAKI,IAEC,WAA5B7c,EAAEJ,OAAO4P,gBACTxP,EAAEoB,KAAK,uBAAwBpB,EAAGA,EAAEgV,oBAAoB,MAOpEhV,EAAEsX,OAAS,SAAU0F,GAUjB,QAASC,KACWjd,EAAEoF,KAAOpF,EAAEyV,UAAYzV,EAAEyV,SACzCyH,GAAe5c,KAAK6c,IAAI7c,KAAKgZ,IAAItZ,EAAEyV,UAAWzV,EAAEyH,gBAAiBzH,EAAEwH,gBACnExH,EAAE2H,oBAAoBuV,GACtBld,EAAE6H,oBACF7H,EAAE8H,gBAdN,GAAK9H,EAgBL,GAfAA,EAAEqY,sBACFrY,EAAEyY,mBACFzY,EAAE4H,iBACF5H,EAAE4c,mBACF5c,EAAE8H,gBACE9H,EAAEJ,OAAOiN,WAAa7M,EAAE6M,WACxB7M,EAAE6M,UAAUuQ,MASZJ,EAAiB,CACjB,GAAIK,GAAYH,CACZld,GAAEsd,YAActd,EAAEsd,WAAWC,SAC7Bvd,EAAEsd,WAAWC,OAAS9P,QAEtBzN,EAAEJ,OAAOqH,UACTgW,IACIjd,EAAEJ,OAAO0L,YACTtL,EAAEgY,qBAKFqF,GAD4B,SAA3Brd,EAAEJ,OAAO+N,eAA4B3N,EAAEJ,OAAO+N,cAAgB,IAAM3N,EAAEqB,QAAUrB,EAAEJ,OAAOmO,eAC7E/N,EAAEwd,QAAQxd,EAAEW,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzCpC,EAAEwd,QAAQxd,EAAEa,YAAa,GAAG,GAAO,GAE/Cwc,GACDJ,SAIHjd,GAAEJ,OAAO0L,YACdtL,EAAEgY,oBAOVhY,EAAE+C,SAAW,SAAU0a,GAEfzd,EAAEJ,OAAO4N,aACTxN,EAAEoU,eAIN,IAAIvQ,GAAmB7D,EAAEJ,OAAOiE,iBAC5BF,EAAmB3D,EAAEJ,OAAO+D,gBAChC3D,GAAEJ,OAAOiE,iBAAmB7D,EAAEJ,OAAO+D,kBAAmB,EAExD3D,EAAEqY,sBACFrY,EAAEyY,oBAC6B,SAA3BzY,EAAEJ,OAAO+N,eAA4B3N,EAAEJ,OAAOqH,UAAYwW,IAAuBzd,EAAE4c,mBACnF5c,EAAEJ,OAAOiN,WAAa7M,EAAE6M,WACxB7M,EAAE6M,UAAUuQ,MAEZpd,EAAEsd,YAActd,EAAEsd,WAAWC,SAC7Bvd,EAAEsd,WAAWC,OAAS9P,OAE1B,IAAIiQ,IAAwB,CAC5B,IAAI1d,EAAEJ,OAAOqH,SAAU,CACnB,GAAIiW,GAAe5c,KAAK6c,IAAI7c,KAAKgZ,IAAItZ,EAAEyV,UAAWzV,EAAEyH,gBAAiBzH,EAAEwH,eACvExH,GAAE2H,oBAAoBuV,GACtBld,EAAE6H,oBACF7H,EAAE8H,gBAEE9H,EAAEJ,OAAO0L,YACTtL,EAAEgY,uBAINhY,GAAE8H,gBAEE4V,GAD4B,SAA3B1d,EAAEJ,OAAO+N,eAA4B3N,EAAEJ,OAAO+N,cAAgB,IAAM3N,EAAEqB,QAAUrB,EAAEJ,OAAOmO,eAClE/N,EAAEwd,QAAQxd,EAAEW,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzCpC,EAAEwd,QAAQxd,EAAEa,YAAa,GAAG,GAAO,EAG/Db,GAAEJ,OAAOwI,cAAgBsV,GAAyB1d,EAAEqI,MACpDrI,EAAEqI,KAAKC,OAGXtI,EAAEJ,OAAOiE,iBAAmBA,EAC5B7D,EAAEJ,OAAO+D,iBAAmBA,GAQhC3D,EAAE2d,oBAAsBC,MAAO,YAAaC,KAAM,YAAaC,IAAK,WAChEtb,OAAOub,UAAUC,eAAgBhe,EAAE2d,oBAAsBC,MAAO,cAAeC,KAAM,cAAeC,IAAK,aACpGtb,OAAOub,UAAUE,mBAAkBje,EAAE2d,oBAAsBC,MAAO,gBAAiBC,KAAM,gBAAiBC,IAAK,gBACxH9d,EAAEke,aACEN,MAAQ5d,EAAE0U,QAAQG,QAAU7U,EAAEJ,OAAOyO,cAAiB,aAAerO,EAAE2d,mBAAmBC,MAC1FC,KAAO7d,EAAE0U,QAAQG,QAAU7U,EAAEJ,OAAOyO,cAAgB,YAAcrO,EAAE2d,mBAAmBE,KACvFC,IAAM9d,EAAE0U,QAAQG,QAAU7U,EAAEJ,OAAOyO,cAAgB,WAAarO,EAAE2d,mBAAmBG,MAKrFtb,OAAOub,UAAUC,gBAAkBxb,OAAOub,UAAUE,oBACpB,cAA/Bje,EAAEJ,OAAO+K,kBAAoC3K,EAAEG,UAAYH,EAAE8U,SAASI,SAAS,cAAgBlV,EAAEJ,OAAO8K,WAI7G1K,EAAEme,WAAa,SAAUC,GACrB,GAAIC,GAAYD,EAAS,MAAQ,KAC7BE,EAASF,EAAS,sBAAwB,mBAC1CzT,EAAmD,cAA/B3K,EAAEJ,OAAO+K,kBAAoC3K,EAAEG,UAAU,GAAKH,EAAE8U,QAAQ,GAC5FjT,EAAS7B,EAAE0U,QAAQG,MAAQlK,EAAoBzG,SAE/Cqa,IAAcve,EAAEJ,OAAO4e,MAG3B,IAAIxe,EAAEye,QAAQC,GACV/T,EAAkB2T,GAAQte,EAAEke,YAAYN,MAAO5d,EAAE2e,cAAc,GAC/D9c,EAAOyc,GAAQte,EAAEke,YAAYL,KAAM7d,EAAE4e,YAAaL,GAClD1c,EAAOyc,GAAQte,EAAEke,YAAYJ,IAAK9d,EAAE6e,YAAY,OAE/C,CACD,GAAI7e,EAAE0U,QAAQG,MAAO,CACjB,GAAIiK,KAA0C,eAAxB9e,EAAEke,YAAYN,QAA0B5d,EAAE0U,QAAQoK,kBAAmB9e,EAAEJ,OAAOqR,oBAAoB8N,SAAS,EAAMC,SAAS,EAChJrU,GAAkB2T,GAAQte,EAAEke,YAAYN,MAAO5d,EAAE2e,aAAcG,GAC/DnU,EAAkB2T,GAAQte,EAAEke,YAAYL,KAAM7d,EAAE4e,YAAaL,GAC7D5T,EAAkB2T,GAAQte,EAAEke,YAAYJ,IAAK9d,EAAE6e,WAAYC,IAE1Dlf,EAAOyO,gBAAkBrO,EAAEsV,OAAO2J,MAAQjf,EAAEsV,OAAOC,SAAa3V,EAAOyO,gBAAkBrO,EAAE0U,QAAQG,OAAS7U,EAAEsV,OAAO2J,OACtHtU,EAAkB2T,GAAQ,YAAate,EAAE2e,cAAc,GACvDza,SAASoa,GAAQ,YAAate,EAAE4e,YAAaL,GAC7Cra,SAASoa,GAAQ,UAAWte,EAAE6e,YAAY,IAGlDrc,OAAO8b,GAAQ,SAAUte,EAAE+C,UAGvB/C,EAAEJ,OAAO+P,YAAc3P,EAAE2P,YAAc3P,EAAE2P,WAAWvN,OAAS,IAC7DpC,EAAE2P,WAAW0O,GAAW,QAASre,EAAEkf,aAC/Blf,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE2P,WAAW0O,GAAW,UAAWre,EAAE4S,KAAKuM,aAEvEnf,EAAEJ,OAAOgQ,YAAc5P,EAAE4P,YAAc5P,EAAE4P,WAAWxN,OAAS,IAC7DpC,EAAE4P,WAAWyO,GAAW,QAASre,EAAEof,aAC/Bpf,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4P,WAAWyO,GAAW,UAAWre,EAAE4S,KAAKuM,aAEvEnf,EAAEJ,OAAOoP,YAAchP,EAAEJ,OAAOsP,sBAChClP,EAAEgV,oBAAoBqJ,GAAW,QAAS,IAAMre,EAAEJ,OAAO+R,YAAa3R,EAAEqf,cACpErf,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAEgV,oBAAoBqJ,GAAW,UAAW,IAAMre,EAAEJ,OAAO+R,YAAa3R,EAAE4S,KAAKuM,cAI5Gnf,EAAEJ,OAAOoQ,eAAiBhQ,EAAEJ,OAAOqQ,2BAA0BtF,EAAkB2T,GAAQ,QAASte,EAAEgQ,eAAe;EAEzHhQ,EAAEsf,aAAe,WACbtf,EAAEme,cAENne,EAAEuf,aAAe,WACbvf,EAAEme,YAAW,IAOjBne,EAAEwf,YAAa,EACfxf,EAAEgQ,cAAgB,SAAUtO,GACnB1B,EAAEwf,aACCxf,EAAEJ,OAAOoQ,eAAetO,EAAEiE,iBAC1B3F,EAAEJ,OAAOqQ,0BAA4BjQ,EAAE2I,YACvCjH,EAAE+d,kBACF/d,EAAEge,8BAKd1f,EAAEkf,YAAc,SAAUxd,GACtBA,EAAEiE,iBACE3F,EAAEqB,QAAUrB,EAAEJ,OAAOqB,MACzBjB,EAAE6F,aAEN7F,EAAEof,YAAc,SAAU1d,GACtBA,EAAEiE,iBACE3F,EAAEsH,cAAgBtH,EAAEJ,OAAOqB,MAC/BjB,EAAE8F,aAEN9F,EAAEqf,aAAe,SAAU3d,GACvBA,EAAEiE,gBACF,IAAIzD,GAAQhC,EAAEJ,MAAMoC,QAAUlC,EAAEJ,OAAOkO,cACnC9N,GAAEJ,OAAOqB,OAAMiB,GAAgBlC,EAAEyQ,cACrCzQ,EAAEwd,QAAQtb,IA0BdlC,EAAE2f,mBAAqB,SAAUje,GAC7B,GAAIgY,GAAQjY,EAAmBC,EAAG,IAAM1B,EAAEJ,OAAO2E,YAC7Cqb,GAAa,CACjB,IAAIlG,EACA,IAAK,GAAIjU,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAC7BzF,EAAEW,OAAO8E,KAAOiU,IAAOkG,GAAa,EAIhD,KAAIlG,IAASkG,EAOT,MAFA5f,GAAE6f,aAAepS,YACjBzN,EAAE8f,aAAerS,OAGrB,IARIzN,EAAE6f,aAAenG,EACjB1Z,EAAE8f,aAAe5f,EAAEwZ,GAAOxX,QAO1BlC,EAAEJ,OAAOsQ,qBAA0CzC,SAAnBzN,EAAE8f,cAA8B9f,EAAE8f,eAAiB9f,EAAEa,YAAa,CAClG,GACI8a,GADAoE,EAAe/f,EAAE8f,aAGjBnS,EAA2C,SAA3B3N,EAAEJ,OAAO+N,cAA2B3N,EAAE2a,uBAAyB3a,EAAEJ,OAAO+N,aAC5F,IAAI3N,EAAEJ,OAAOqB,KAAM,CACf,GAAIjB,EAAE2I,UAAW,MACjBgT,GAAYvR,SAASlK,EAAEF,EAAE6f,cAAc/e,KAAK,2BAA4B,IACpEd,EAAEJ,OAAOmO,eACJgS,EAAe/f,EAAEyQ,aAAe9C,EAAc,GAAOoS,EAAe/f,EAAEW,OAAOyB,OAASpC,EAAEyQ,aAAe9C,EAAc,GACtH3N,EAAEkB,UACF6e,EAAe/f,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,6BAA+BoX,EAAY,WAAa3b,EAAEJ,OAAOyR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPhB,EAAEwd,QAAQuC,IACX,IAGH/f,EAAEwd,QAAQuC,GAIVA,EAAe/f,EAAEW,OAAOyB,OAASuL,GACjC3N,EAAEkB,UACF6e,EAAe/f,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,6BAA+BoX,EAAY,WAAa3b,EAAEJ,OAAOyR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPhB,EAAEwd,QAAQuC,IACX,IAGH/f,EAAEwd,QAAQuC,OAKlB/f,GAAEwd,QAAQuC,IAKtB,IAAIC,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAI4BC,EAG5BC,EALAC,EAAe,yCAEfC,EAAgBnY,KAAKoY,MAErBC,IAIJ7gB,GAAE2I,WAAY,EAGd3I,EAAE8gB,SACEC,OAAQ,EACRC,OAAQ,EACRC,SAAU,EACVC,SAAU,EACVC,KAAM,EAIV,IAAIC,GAAcC,CAClBrhB,GAAE2e,aAAe,SAAUjd,GAGvB,GAFIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eAC3B6d,EAA0B,eAAX1f,EAAE4f,KACZF,KAAgB,SAAW1f,KAAiB,IAAZA,EAAE6f,MAAvC,CACA,GAAIvhB,EAAEJ,OAAOmR,WAAatP,EAAmBC,EAAG,IAAM1B,EAAEJ,OAAOoR,gBAE3D,YADAhR,EAAEwf,YAAa,EAGnB,KAAIxf,EAAEJ,OAAOkR,cACJrP,EAAmBC,EAAG1B,EAAEJ,OAAOkR,cADxC,CAIA,GAAIiQ,GAAS/gB,EAAE8gB,QAAQG,SAAsB,eAAXvf,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MACrFT,EAAShhB,EAAE8gB,QAAQI,SAAsB,eAAXxf,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,KAGzF,MAAG1hB,EAAEsV,OAAO2J,KAAOjf,EAAEJ,OAAOkL,uBAAyBiW,GAAU/gB,EAAEJ,OAAOmL,uBAAxE,CAgBA,GAZAiV,GAAY,EACZC,GAAU,EACVC,GAAsB,EACtBE,EAAc3S,OACd4T,EAAc5T,OACdzN,EAAE8gB,QAAQC,OAASA,EACnB/gB,EAAE8gB,QAAQE,OAASA,EACnBb,EAAiB3X,KAAKoY,MACtB5gB,EAAEwf,YAAa,EACfxf,EAAEqY,sBACFrY,EAAE2hB,eAAiBlU,OACfzN,EAAEJ,OAAOgP,UAAY,IAAG2R,GAAqB,GAClC,eAAX7e,EAAE4f,KAAuB,CACzB,GAAI3b,IAAiB,CACjBzF,GAAEwB,EAAEG,QAAQC,GAAG4e,KAAe/a,GAAiB,GAC/CzB,SAASC,eAAiBjE,EAAEgE,SAASC,eAAerC,GAAG4e,IACvDxc,SAASC,cAAcyd,OAEvBjc,GACAjE,EAAEiE,iBAGV3F,EAAEoB,KAAK,eAAgBpB,EAAG0B,OAG9B1B,EAAE4e,YAAc,SAAUld,GAEtB,GADIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,gBACvB6d,GAA2B,cAAX1f,EAAE4f,KAAtB,CACA,GAAI5f,EAAEmgB,wBAGF,MAFA7hB,GAAE8gB,QAAQC,OAAoB,cAAXrf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,WACzEzhB,EAAE8gB,QAAQE,OAAoB,cAAXtf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,MAG7E,IAAI1hB,EAAEJ,OAAO+O,aAQT,MANA3O,GAAEwf,YAAa,OACXQ,IACAhgB,EAAE8gB,QAAQC,OAAS/gB,EAAE8gB,QAAQG,SAAsB,cAAXvf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC9FzhB,EAAE8gB,QAAQE,OAAShhB,EAAE8gB,QAAQI,SAAsB,cAAXxf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,MAC9FvB,EAAiB3X,KAAKoY,OAI9B,IAAIQ,GAAgBphB,EAAEJ,OAAOkP,sBAAwB9O,EAAEJ,OAAOqB,KAC1D,GAAKjB,EAAE4D,gBAUH,GACK5D,EAAE8gB,QAAQG,SAAWjhB,EAAE8gB,QAAQC,QAAU/gB,EAAEyV,WAAazV,EAAEyH,gBAC1DzH,EAAE8gB,QAAQG,SAAWjhB,EAAE8gB,QAAQC,QAAU/gB,EAAEyV,WAAazV,EAAEwH,eAE3D,WAZJ,IACKxH,EAAE8gB,QAAQI,SAAWlhB,EAAE8gB,QAAQE,QAAUhhB,EAAEyV,WAAazV,EAAEyH,gBAC1DzH,EAAE8gB,QAAQI,SAAWlhB,EAAE8gB,QAAQE,QAAUhhB,EAAEyV,WAAazV,EAAEwH,eAE3D,MAYZ,IAAI4Z,GAAgBld,SAASC,eACrBzC,EAAEG,SAAWqC,SAASC,eAAiBjE,EAAEwB,EAAEG,QAAQC,GAAG4e,GAGtD,MAFAT,IAAU,OACVjgB,EAAEwf,YAAa,EAOvB,IAHIU,GACAlgB,EAAEoB,KAAK,cAAepB,EAAG0B,KAEzBA,EAAE8f,eAAiB9f,EAAE8f,cAAcpf,OAAS,GAAhD,CAKA,GAHApC,EAAE8gB,QAAQG,SAAsB,cAAXvf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC3EzhB,EAAE8gB,QAAQI,SAAsB,cAAXxf,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,MAEhD,mBAAhBtB,GAA6B,CACpC,GAAIhS,EACApO,GAAE4D,gBAAkB5D,EAAE8gB,QAAQI,WAAalhB,EAAE8gB,QAAQE,SAAWhhB,EAAE4D,gBAAkB5D,EAAE8gB,QAAQG,WAAajhB,EAAE8gB,QAAQC,OACrHX,GAAc,GAGdhS,EAA4H,IAA/G9N,KAAKwhB,MAAMxhB,KAAKuG,IAAI7G,EAAE8gB,QAAQI,SAAWlhB,EAAE8gB,QAAQE,QAAS1gB,KAAKuG,IAAI7G,EAAE8gB,QAAQG,SAAWjhB,EAAE8gB,QAAQC,SAAiBzgB,KAAKyhB,GACvI3B,EAAcpgB,EAAE4D,eAAiBwK,EAAapO,EAAEJ,OAAOwO,WAAc,GAAKA,EAAapO,EAAEJ,OAAOwO,YAWxG,GARIgS,GACApgB,EAAEoB,KAAK,sBAAuBpB,EAAG0B,GAEV,mBAAhB2f,IAA+BrhB,EAAEye,QAAQuD,UAC5ChiB,EAAE8gB,QAAQG,WAAajhB,EAAE8gB,QAAQC,QAAU/gB,EAAE8gB,QAAQI,WAAalhB,EAAE8gB,QAAQE,SAC5EK,GAAc,IAGjBrB,EAAL,CACA,GAAII,EAEA,YADAJ,GAAY,EAGhB,IAAKqB,IAAerhB,EAAEye,QAAQuD,QAA9B,CAGAhiB,EAAEwf,YAAa,EACfxf,EAAEoB,KAAK,eAAgBpB,EAAG0B,GAC1BA,EAAEiE,iBACE3F,EAAEJ,OAAOiP,2BAA6B7O,EAAEJ,OAAO4e,QAC/C9c,EAAE+d,kBAGDQ,IACGrgB,EAAOqB,MACPjB,EAAEkB,UAENof,EAAiBtgB,EAAEmH,sBACnBnH,EAAE0H,qBAAqB,GACnB1H,EAAE2I,WACF3I,EAAE8U,QAAQmN,QAAQ,oFAElBjiB,EAAEJ,OAAOY,UAAYR,EAAEyX,cACnBzX,EAAEJ,OAAO2I,6BACTvI,EAAEuB,eAGFvB,EAAE6X,iBAGV4I,GAAsB,GAElBzgB,EAAEJ,OAAOmQ,YAAe/P,EAAEJ,OAAO+D,oBAAqB,GAAQ3D,EAAEJ,OAAOiE,oBAAqB,GAC5F7D,EAAEgW,eAAc,IAGxBiK,GAAU,CAEV,IAAIkB,GAAOnhB,EAAE8gB,QAAQK,KAAOnhB,EAAE4D,eAAiB5D,EAAE8gB,QAAQG,SAAWjhB,EAAE8gB,QAAQC,OAAS/gB,EAAE8gB,QAAQI,SAAWlhB,EAAE8gB,QAAQE,MAEtHG,IAAcnhB,EAAEJ,OAAOuO,WACnBnO,EAAEoF,MAAK+b,GAAQA,GAEnBnhB,EAAE2hB,eAAiBR,EAAO,EAAI,OAAS,OACvCd,EAAmBc,EAAOb,CAE1B,IAAI4B,IAAsB,CAwB1B,IAvBKf,EAAO,GAAKd,EAAmBrgB,EAAEwH,gBAClC0a,GAAsB,EAClBliB,EAAEJ,OAAO6P,aAAY4Q,EAAmBrgB,EAAEwH,eAAiB,EAAIlH,KAAK6hB,KAAKniB,EAAEwH,eAAiB8Y,EAAiBa,EAAMnhB,EAAEJ,OAAO8P,mBAE3HyR,EAAO,GAAKd,EAAmBrgB,EAAEyH,iBACtCya,GAAsB,EAClBliB,EAAEJ,OAAO6P,aAAY4Q,EAAmBrgB,EAAEyH,eAAiB,EAAInH,KAAK6hB,IAAIniB,EAAEyH,eAAiB6Y,EAAiBa,EAAMnhB,EAAEJ,OAAO8P,mBAG/HwS,IACAxgB,EAAEmgB,yBAA0B,IAI3B7hB,EAAEJ,OAAO+D,kBAAyC,SAArB3D,EAAE2hB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,IAElBtgB,EAAEJ,OAAOiE,kBAAyC,SAArB7D,EAAE2hB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,GAKnBtgB,EAAEJ,OAAOgP,UAAY,EAAG,CACxB,KAAItO,KAAKuG,IAAIsa,GAAQnhB,EAAEJ,OAAOgP,WAAa2R,GAYvC,YADAF,EAAmBC,EAVnB,KAAKC,EAMD,MALAA,IAAqB,EACrBvgB,EAAE8gB,QAAQC,OAAS/gB,EAAE8gB,QAAQG,SAC7BjhB,EAAE8gB,QAAQE,OAAShhB,EAAE8gB,QAAQI,SAC7Bb,EAAmBC,OACnBtgB,EAAE8gB,QAAQK,KAAOnhB,EAAE4D,eAAiB5D,EAAE8gB,QAAQG,SAAWjhB,EAAE8gB,QAAQC,OAAS/gB,EAAE8gB,QAAQI,SAAWlhB,EAAE8gB,QAAQE,QAUlHhhB,EAAEJ,OAAO8O,gBAGV1O,EAAEJ,OAAOqH,UAAYjH,EAAEJ,OAAOiQ,sBAC9B7P,EAAE6H,oBAEF7H,EAAEJ,OAAOqH,WAEiB,IAAtB4Z,EAAWze,QACXye,EAAWxd,MACP6D,SAAUlH,EAAE8gB,QAAQ9gB,EAAE4D,eAAiB,SAAW,UAClDwe,KAAMjC,IAGdU,EAAWxd,MACP6D,SAAUlH,EAAE8gB,QAAQ9gB,EAAE4D,eAAiB,WAAa,YACpDwe,MAAM,GAAK5f,QAAOgG,MAAQC,aAIlCzI,EAAE4H,eAAeyY,GAEjBrgB,EAAE2H,oBAAoB0Y,SAE1BrgB,EAAE6e,WAAa,SAAUnd,GAMrB,GALIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eACvB2c,GACAlgB,EAAEoB,KAAK,aAAcpB,EAAG0B,GAE5Bwe,GAAsB,EACjBF,EAAL,CAEIhgB,EAAEJ,OAAOmQ,YAAckQ,GAAWD,IAAehgB,EAAEJ,OAAO+D,oBAAqB,GAAQ3D,EAAEJ,OAAOiE,oBAAqB,IACrH7D,EAAEgW,eAAc,EAIpB,IAAIqM,GAAe7Z,KAAKoY,MACpB0B,EAAWD,EAAelC,CA4B9B,IAzBIngB,EAAEwf,aACFxf,EAAE2f,mBAAmBje,GACrB1B,EAAEoB,KAAK,QAASpB,EAAG0B,GACf4gB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcxY,aAAawY,GAC/BA,EAAexf,WAAW,WACjBhB,IACDA,EAAEJ,OAAOuP,gBAAkBnP,EAAEgV,oBAAoB5S,OAAS,IAAMlC,EAAEwB,EAAEG,QAAQ+Z,SAAS5b,EAAEJ,OAAO+R,cAC9F3R,EAAEgV,oBAAoBuN,YAAYviB,EAAEJ,OAAOoS,uBAE/ChS,EAAEoB,KAAK,UAAWpB,EAAG0B,KACtB,MAGH4gB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcxY,aAAawY,GAC/BxgB,EAAEoB,KAAK,cAAepB,EAAG0B,KAIjCif,EAAgBnY,KAAKoY,MACrB5f,WAAW,WACHhB,IAAGA,EAAEwf,YAAa,IACvB,IAEEQ,IAAcC,IAAYjgB,EAAE2hB,gBAAqC,IAAnB3hB,EAAE8gB,QAAQK,MAAcd,IAAqBC,EAE5F,YADAN,EAAYC,GAAU,EAG1BD,GAAYC,GAAU,CAEtB,IAAIuC,EAOJ,IALIA,EADAxiB,EAAEJ,OAAO8O,aACI1O,EAAEoF,IAAMpF,EAAEyV,WAAazV,EAAEyV,WAGxB4K,EAEdrgB,EAAEJ,OAAOqH,SAAU,CACnB,GAAIub,GAAcxiB,EAAEwH,eAEhB,WADAxH,GAAEwd,QAAQxd,EAAEa,YAGX,IAAI2hB,GAAcxiB,EAAEyH,eAOrB,YANIzH,EAAEW,OAAOyB,OAASpC,EAAE+X,SAAS3V,OAC7BpC,EAAEwd,QAAQxd,EAAE+X,SAAS3V,OAAS,GAG9BpC,EAAEwd,QAAQxd,EAAEW,OAAOyB,OAAS,GAKpC,IAAIpC,EAAEJ,OAAOoL,iBAAkB,CAC3B,GAAI6V,EAAWze,OAAS,EAAG,CACvB,GAAIqgB,GAAgB5B,EAAW6B,MAAOC,EAAgB9B,EAAW6B,MAE7DE,EAAWH,EAAcvb,SAAWyb,EAAczb,SAClDkb,EAAOK,EAAcL,KAAOO,EAAcP,IAC9CpiB,GAAE0V,SAAWkN,EAAWR,EACxBpiB,EAAE0V,SAAW1V,EAAE0V,SAAW,EACtBpV,KAAKuG,IAAI7G,EAAE0V,UAAY1V,EAAEJ,OAAOyL,0BAChCrL,EAAE0V,SAAW,IAIb0M,EAAO,MAAQ,GAAI5f,QAAOgG,MAAOC,UAAYga,EAAcL,KAAQ,OACnEpiB,EAAE0V,SAAW,OAGjB1V,GAAE0V,SAAW,CAEjB1V,GAAE0V,SAAW1V,EAAE0V,SAAW1V,EAAEJ,OAAOwL,8BAEnCyV,EAAWze,OAAS,CACpB,IAAIygB,GAAmB,IAAO7iB,EAAEJ,OAAOqL,sBACnC6X,EAAmB9iB,EAAE0V,SAAWmN,EAEhCE,EAAc/iB,EAAEyV,UAAYqN,CAC5B9iB,GAAEoF,MAAK2d,GAAgBA,EAC3B,IACIC,GADAC,GAAW,EAEXC,EAAsC,GAAvB5iB,KAAKuG,IAAI7G,EAAE0V,UAAiB1V,EAAEJ,OAAOuL,2BACxD,IAAI4X,EAAc/iB,EAAEyH,eACZzH,EAAEJ,OAAOsL,wBACL6X,EAAc/iB,EAAEyH,gBAAkByb,IAClCH,EAAc/iB,EAAEyH,eAAiByb,GAErCF,EAAsBhjB,EAAEyH,eACxBwb,GAAW,EACXxC,GAAsB,GAGtBsC,EAAc/iB,EAAEyH,mBAGnB,IAAIsb,EAAc/iB,EAAEwH,eACjBxH,EAAEJ,OAAOsL,wBACL6X,EAAc/iB,EAAEwH,eAAiB0b,IACjCH,EAAc/iB,EAAEwH,eAAiB0b,GAErCF,EAAsBhjB,EAAEwH,eACxByb,GAAW,EACXxC,GAAsB,GAGtBsC,EAAc/iB,EAAEwH,mBAGnB,IAAIxH,EAAEJ,OAAOmI,eAAgB,CAC9B,GACI8T,GADAjB,EAAI,CAER,KAAKA,EAAI,EAAGA,EAAI5a,EAAE+X,SAAS3V,OAAQwY,GAAK,EACpC,GAAI5a,EAAE+X,SAAS6C,IAAMmI,EAAa,CAC9BlH,EAAYjB,CACZ,OAKJmI,EADAziB,KAAKuG,IAAI7G,EAAE+X,SAAS8D,GAAakH,GAAeziB,KAAKuG,IAAI7G,EAAE+X,SAAS8D,EAAY,GAAKkH,IAAqC,SAArB/iB,EAAE2hB,eACzF3hB,EAAE+X,SAAS8D,GAEX7b,EAAE+X,SAAS8D,EAAY,GAEpC7b,EAAEoF,MAAK2d,GAAgBA,GAGhC,GAAmB,IAAf/iB,EAAE0V,SAEEmN,EADA7iB,EAAEoF,IACiB9E,KAAKuG,MAAMkc,EAAc/iB,EAAEyV,WAAazV,EAAE0V,UAG1CpV,KAAKuG,KAAKkc,EAAc/iB,EAAEyV,WAAazV,EAAE0V,cAG/D,IAAI1V,EAAEJ,OAAOmI,eAEd,WADA/H,GAAEmI,YAIFnI,GAAEJ,OAAOsL,wBAA0B+X,GACnCjjB,EAAE4H,eAAeob,GACjBhjB,EAAE0H,qBAAqBmb,GACvB7iB,EAAE2H,oBAAoBob,GACtB/iB,EAAEmjB,oBACFnjB,EAAE2I,WAAY,EACd3I,EAAE8U,QAAQgD,cAAc,WACf9X,GAAMygB,IACXzgB,EAAEoB,KAAK,mBAAoBpB,GAE3BA,EAAE0H,qBAAqB1H,EAAEJ,OAAOiL,OAChC7K,EAAE2H,oBAAoBqb,GACtBhjB,EAAE8U,QAAQgD,cAAc,WACf9X,GACLA,EAAEojB,wBAGHpjB,EAAE0V,UACT1V,EAAE4H,eAAemb,GACjB/iB,EAAE0H,qBAAqBmb,GACvB7iB,EAAE2H,oBAAoBob,GACtB/iB,EAAEmjB,oBACGnjB,EAAE2I,YACH3I,EAAE2I,WAAY,EACd3I,EAAE8U,QAAQgD,cAAc,WACf9X,GACLA,EAAEojB,sBAKVpjB,EAAE4H,eAAemb,GAGrB/iB,EAAE6H,oBAMN,cAJK7H,EAAEJ,OAAOoL,kBAAoBsX,GAAYtiB,EAAEJ,OAAO6O,gBACnDzO,EAAE4H,iBACF5H,EAAE6H,sBAMV,GAAIpC,GAAG4d,EAAY,EAAGC,EAAYtjB,EAAE2Y,gBAAgB,EACpD,KAAKlT,EAAI,EAAGA,EAAIzF,EAAE0Y,WAAWtW,OAAQqD,GAAKzF,EAAEJ,OAAOkO,eACU,mBAA9C9N,GAAE0Y,WAAWjT,EAAIzF,EAAEJ,OAAOkO,gBAC7B0U,GAAcxiB,EAAE0Y,WAAWjT,IAAM+c,EAAaxiB,EAAE0Y,WAAWjT,EAAIzF,EAAEJ,OAAOkO,kBACxEuV,EAAY5d,EACZ6d,EAAYtjB,EAAE0Y,WAAWjT,EAAIzF,EAAEJ,OAAOkO,gBAAkB9N,EAAE0Y,WAAWjT,IAIrE+c,GAAcxiB,EAAE0Y,WAAWjT,KAC3B4d,EAAY5d,EACZ6d,EAAYtjB,EAAE0Y,WAAW1Y,EAAE0Y,WAAWtW,OAAS,GAAKpC,EAAE0Y,WAAW1Y,EAAE0Y,WAAWtW,OAAS,GAMnG,IAAImhB,IAASf,EAAaxiB,EAAE0Y,WAAW2K,IAAcC,CAErD,IAAIhB,EAAWtiB,EAAEJ,OAAO6O,aAAc,CAElC,IAAKzO,EAAEJ,OAAO2O,WAEV,WADAvO,GAAEwd,QAAQxd,EAAEa,YAGS,UAArBb,EAAE2hB,iBACE4B,GAASvjB,EAAEJ,OAAO4O,gBAAiBxO,EAAEwd,QAAQ6F,EAAYrjB,EAAEJ,OAAOkO,gBACjE9N,EAAEwd,QAAQ6F,IAGM,SAArBrjB,EAAE2hB,iBACE4B,EAAS,EAAIvjB,EAAEJ,OAAO4O,gBAAkBxO,EAAEwd,QAAQ6F,EAAYrjB,EAAEJ,OAAOkO,gBACtE9N,EAAEwd,QAAQ6F,QAGlB,CAED,IAAKrjB,EAAEJ,OAAO0O,YAEV,WADAtO,GAAEwd,QAAQxd,EAAEa,YAGS,UAArBb,EAAE2hB,gBACF3hB,EAAEwd,QAAQ6F,EAAYrjB,EAAEJ,OAAOkO,gBAGV,SAArB9N,EAAE2hB,gBACF3hB,EAAEwd,QAAQ6F,MAOtBrjB,EAAEwB,SAAW,SAAUgiB,EAAY3Y,GAC/B,MAAO7K,GAAEwd,QAAQgG,EAAY3Y,GAAO,GAAM,IAE9C7K,EAAEwd,QAAU,SAAUgG,EAAY3Y,EAAO4Y,EAAc7L,GACvB,mBAAjB6L,KAA8BA,GAAe,GAC9B,mBAAfD,KAA4BA,EAAa,GAChDA,EAAa,IAAGA,EAAa,GACjCxjB,EAAEwb,UAAYlb,KAAKC,MAAMijB,EAAaxjB,EAAEJ,OAAOkO,gBAC3C9N,EAAEwb,WAAaxb,EAAE+X,SAAS3V,SAAQpC,EAAEwb,UAAYxb,EAAE+X,SAAS3V,OAAS,EAExE,IAAIqT,IAAczV,EAAE+X,SAAS/X,EAAEwb,UAc/B,IAZIxb,EAAEJ,OAAOY,UAAYR,EAAEyX,cACnBG,IAAa5X,EAAEJ,OAAO2I,6BACtBvI,EAAE6X,cAAchN,GAGhB7K,EAAEuB,gBAIVvB,EAAE4H,eAAe6N,GAGdzV,EAAEJ,OAAOiR,oBACR,IAAK,GAAIpL,GAAI,EAAGA,EAAIzF,EAAE0Y,WAAWtW,OAAQqD,KAC/BnF,KAAKC,MAAkB,IAAZkV,IAAoBnV,KAAKC,MAAwB,IAAlBP,EAAE0Y,WAAWjT,MACzD+d,EAAa/d,EAMzB,UAAKzF,EAAEJ,OAAO+D,kBAAoB8R,EAAYzV,EAAEyV,WAAaA,EAAYzV,EAAEwH,qBAGtExH,EAAEJ,OAAOiE,kBAAoB4R,EAAYzV,EAAEyV,WAAaA,EAAYzV,EAAEyH,iBAClEzH,EAAEa,aAAe,KAAO2iB,KAIZ,mBAAV3Y,KAAuBA,EAAQ7K,EAAEJ,OAAOiL,OACnD7K,EAAEyb,cAAgBzb,EAAEa,aAAe,EACnCb,EAAEa,YAAc2iB,EAChBxjB,EAAE0b,kBACG1b,EAAEoF,MAAQqQ,IAAczV,EAAEyV,YAAgBzV,EAAEoF,KAAOqQ,IAAczV,EAAEyV,WAEhEzV,EAAEJ,OAAO0L,YACTtL,EAAEgY,mBAENhY,EAAE8H,gBACsB,UAApB9H,EAAEJ,OAAO6L,QACTzL,EAAE2H,oBAAoB8N,IAEnB,IAEXzV,EAAE8H,gBACF9H,EAAEmjB,kBAAkBM,GAEN,IAAV5Y,GAAe7K,EAAEye,QAAQiF,QACzB1jB,EAAE2H,oBAAoB8N,GACtBzV,EAAE0H,qBAAqB,GACvB1H,EAAEojB,gBAAgBK,KAGlBzjB,EAAE2H,oBAAoB8N,GACtBzV,EAAE0H,qBAAqBmD,GAClB7K,EAAE2I,YACH3I,EAAE2I,WAAY,EACd3I,EAAE8U,QAAQgD,cAAc,WACf9X,GACLA,EAAEojB,gBAAgBK,QAMvB,MAGXzjB,EAAEmjB,kBAAoB,SAAUM,GACA,mBAAjBA,KAA8BA,GAAe,GACpDzjB,EAAEJ,OAAO0L,YACTtL,EAAEgY,mBAEFhY,EAAEqI,MAAMrI,EAAEqI,KAAK8a,oBACfM,IACAzjB,EAAEoB,KAAK,oBAAqBpB,GACxBA,EAAEa,cAAgBb,EAAEyb,gBACpBzb,EAAEoB,KAAK,qBAAsBpB,GACzBA,EAAEa,YAAcb,EAAEyb,cAClBzb,EAAEoB,KAAK,mBAAoBpB,GAG3BA,EAAEoB,KAAK,mBAAoBpB,MAM3CA,EAAEojB,gBAAkB,SAAUK,GAC1BzjB,EAAE2I,WAAY,EACd3I,EAAE0H,qBAAqB,GACK,mBAAjB+b,KAA8BA,GAAe,GACpDzjB,EAAEqI,MAAMrI,EAAEqI,KAAK+a,kBACfK,IACAzjB,EAAEoB,KAAK,kBAAmBpB,GACtBA,EAAEa,cAAgBb,EAAEyb,gBACpBzb,EAAEoB,KAAK,mBAAoBpB,GACvBA,EAAEa,YAAcb,EAAEyb,cAClBzb,EAAEoB,KAAK,iBAAkBpB,GAGzBA,EAAEoB,KAAK,iBAAkBpB,KAIjCA,EAAEJ,OAAO0N,SAAWtN,EAAEsN,SACtBtN,EAAEsN,QAAQqW,WAAW3jB,EAAEJ,OAAO0N,QAAStN,EAAEa,aAEzCb,EAAEJ,OAAOwN,SAAWpN,EAAEoN,SACtBpN,EAAEoN,QAAQwW,WAIlB5jB,EAAE6F,UAAY,SAAU4d,EAAc5Y,EAAO+M,GACzC,GAAI5X,EAAEJ,OAAOqB,KAAM,CACf,GAAIjB,EAAE2I,UAAW,OAAO,CACxB3I,GAAEkB,SACelB,GAAEG,UAAU,GAAG0jB,UAChC,OAAO7jB,GAAEwd,QAAQxd,EAAEa,YAAcb,EAAEJ,OAAOkO,eAAgBjD,EAAO4Y,EAAc7L,GAE9E,MAAO5X,GAAEwd,QAAQxd,EAAEa,YAAcb,EAAEJ,OAAOkO,eAAgBjD,EAAO4Y,EAAc7L,IAExF5X,EAAEmB,WAAa,SAAU0J,GACrB,MAAO7K,GAAE6F,WAAU,EAAMgF,GAAO,IAEpC7K,EAAE8F,UAAY,SAAU2d,EAAc5Y,EAAO+M,GACzC,GAAI5X,EAAEJ,OAAOqB,KAAM,CACf,GAAIjB,EAAE2I,UAAW,OAAO,CACxB3I,GAAEkB,SACelB,GAAEG,UAAU,GAAG0jB,UAChC,OAAO7jB,GAAEwd,QAAQxd,EAAEa,YAAc,EAAGgK,EAAO4Y,EAAc7L,GAExD,MAAO5X,GAAEwd,QAAQxd,EAAEa,YAAc,EAAGgK,EAAO4Y,EAAc7L,IAElE5X,EAAE8jB,WAAa,SAAUjZ,GACrB,MAAO7K,GAAE8F,WAAU,EAAM+E,GAAO,IAEpC7K,EAAEmI,WAAa,SAAUsb,EAAc5Y,EAAO+M,GAC1C,MAAO5X,GAAEwd,QAAQxd,EAAEa,YAAagK,EAAO4Y,IAG3CzjB,EAAE+jB,oBAAsB,WAEpB,MADA/jB,GAAEJ,OAAO+O,cAAe,GACjB,GAEX3O,EAAEgkB,mBAAqB,WAEnB,MADAhkB,GAAEJ,OAAO+O,cAAe,GACjB,GAMX3O,EAAE0H,qBAAuB,SAAUuc,EAAUC,GACzClkB,EAAE8U,QAAQ0H,WAAWyH,GACG,UAApBjkB,EAAEJ,OAAO6L,QAAsBzL,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,SAClDzL,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,QAAQ2Y,cAAcH,GAEzCjkB,EAAEJ,OAAO4M,UAAYxM,EAAEwM,UACvBxM,EAAEwM,SAAS4X,cAAcH,GAEzBjkB,EAAEJ,OAAOiN,WAAa7M,EAAE6M,WACxB7M,EAAE6M,UAAUuX,cAAcH,GAE1BjkB,EAAEJ,OAAO8Q,SAAW1Q,EAAEsd,YACtBtd,EAAEsd,WAAW8G,cAAcH,EAAUC,GAEzClkB,EAAEoB,KAAK,kBAAmBpB,EAAGikB,IAEjCjkB,EAAE2H,oBAAsB,SAAU8N,EAAW5N,EAAmBqc,GAC5D,GAAIG,GAAI,EAAGC,EAAI,EAAGC,EAAI,CAClBvkB,GAAE4D,eACFygB,EAAIrkB,EAAEoF,KAAOqQ,EAAYA,EAGzB6O,EAAI7O,EAGJzV,EAAEJ,OAAOsO,eACTmW,EAAIjkB,EAAMikB,GACVC,EAAIlkB,EAAMkkB,IAGTtkB,EAAEJ,OAAO4L,mBACNxL,EAAE0U,QAAQE,aAAc5U,EAAE8U,QAAQzK,UAAU,eAAiBga,EAAI,OAASC,EAAI,OAASC,EAAI,OAC1FvkB,EAAE8U,QAAQzK,UAAU,aAAega,EAAI,OAASC,EAAI,QAG7DtkB,EAAEyV,UAAYzV,EAAE4D,eAAiBygB,EAAIC,CAGrC,IAAIra,GACAqR,EAAiBtb,EAAEyH,eAAiBzH,EAAEwH,cAEtCyC,GADmB,IAAnBqR,EACW,GAGC7F,EAAYzV,EAAEwH,gBAAkB,EAE5CyC,IAAajK,EAAEiK,UACfjK,EAAE4H,eAAe6N,GAGjB5N,GAAmB7H,EAAE6H,oBACD,UAApB7H,EAAEJ,OAAO6L,QAAsBzL,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,SAClDzL,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,QAAQ+Y,aAAaxkB,EAAEyV,WAE1CzV,EAAEJ,OAAO4M,UAAYxM,EAAEwM,UACvBxM,EAAEwM,SAASgY,aAAaxkB,EAAEyV,WAE1BzV,EAAEJ,OAAOiN,WAAa7M,EAAE6M,WACxB7M,EAAE6M,UAAU2X,aAAaxkB,EAAEyV,WAE3BzV,EAAEJ,OAAO8Q,SAAW1Q,EAAEsd,YACtBtd,EAAEsd,WAAWkH,aAAaxkB,EAAEyV,UAAWyO,GAE3ClkB,EAAEoB,KAAK,iBAAkBpB,EAAGA,EAAEyV,YAGlCzV,EAAEykB,aAAe,SAAU7iB,EAAI6H,GAC3B,GAAIib,GAAQC,EAAcC,EAAUC,CAOpC,OAJoB,mBAATpb,KACPA,EAAO,KAGPzJ,EAAEJ,OAAO4L,iBACFxL,EAAEoF,KAAOpF,EAAEyV,UAAYzV,EAAEyV,WAGpCmP,EAAWpiB,OAAOsiB,iBAAiBljB,EAAI,MACnCY,OAAOuiB,iBACPJ,EAAeC,EAASva,WAAaua,EAASI,gBAC1CL,EAAaM,MAAM,KAAK7iB,OAAS,IACjCuiB,EAAeA,EAAaM,MAAM,MAAMC,IAAI,SAAS7kB,GACjD,MAAOA,GAAE0Y,QAAQ,IAAI,OACtBvD,KAAK,OAIZqP,EAAkB,GAAIriB,QAAOuiB,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAG5EE,EAAkBD,EAASO,cAAgBP,EAASQ,YAAcR,EAASS,aAAeT,EAASU,aAAgBV,EAASva,WAAaua,EAASW,iBAAiB,aAAaxM,QAAQ,aAAc,sBACtM2L,EAASG,EAAgBW,WAAWP,MAAM,MAGjC,MAATxb,IAGIkb,EADAniB,OAAOuiB,gBACQF,EAAgBY,IAER,KAAlBf,EAAOtiB,OACG0W,WAAW4L,EAAO,KAGlB5L,WAAW4L,EAAO,KAE5B,MAATjb,IAGIkb,EADAniB,OAAOuiB,gBACQF,EAAgBa,IAER,KAAlBhB,EAAOtiB,OACG0W,WAAW4L,EAAO,KAGlB5L,WAAW4L,EAAO,KAErC1kB,EAAEoF,KAAOuf,IAAcA,GAAgBA,GACpCA,GAAgB,IAE3B3kB,EAAEmH,oBAAsB,SAAUsC,GAI9B,MAHoB,mBAATA,KACPA,EAAOzJ,EAAE4D,eAAiB,IAAM,KAE7B5D,EAAEykB,aAAazkB,EAAE8U,QAAQ,GAAIrL,IAMxCzJ,EAAEoD,aAoBFpD,EAAE2lB,cAAgB,WACd,GAAI3lB,EAAEJ,OAAO+S,eAET,IAAK,GADDiT,GAAmB5lB,EAAEG,UAAU4B,UAC1B0D,EAAI,EAAGA,EAAImgB,EAAiBxjB,OAAQqD,IACzCpD,EAAaujB,EAAiBngB,GAKtCpD,GAAarC,EAAEG,UAAU,IAAK+C,WAAW,IAGzCb,EAAarC,EAAE8U,QAAQ,IAAK7R,YAAY,KAE5CjD,EAAE6lB,oBAAsB,WACpB,IAAK,GAAIpgB,GAAI,EAAGA,EAAIzF,EAAEoD,UAAUhB,OAAQqD,IACpCzF,EAAEoD,UAAUqC,GAAGqgB,YAEnB9lB,GAAEoD,cAMNpD,EAAE+lB,WAAa,WAEX/lB,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,IAAMvE,EAAEJ,OAAOyR,qBAAqB2U,QAEnF,IAAIrlB,GAASX,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAEjB,UAA3BvE,EAAEJ,OAAO+N,eAA6B3N,EAAEJ,OAAO6Q,eAAczQ,EAAEJ,OAAO6Q,aAAe9P,EAAOyB,QAE/FpC,EAAEyQ,aAAerG,SAASpK,EAAEJ,OAAO6Q,cAAgBzQ,EAAEJ,OAAO+N,cAAe,IAC3E3N,EAAEyQ,aAAezQ,EAAEyQ,aAAezQ,EAAEJ,OAAO4Q,qBACvCxQ,EAAEyQ,aAAe9P,EAAOyB,SACxBpC,EAAEyQ,aAAe9P,EAAOyB,OAG5B,IAA2CqD,GAAvCwgB,KAAoBC,IAOxB,KANAvlB,EAAOZ,KAAK,SAAUmC,EAAON,GACzB,GAAI8X,GAAQxZ,EAAEJ,KACVoC,GAAQlC,EAAEyQ,cAAcyV,EAAa7iB,KAAKzB,GAC1CM,EAAQvB,EAAOyB,QAAUF,GAASvB,EAAOyB,OAASpC,EAAEyQ,cAAcwV,EAAc5iB,KAAKzB,GACzF8X,EAAM5Y,KAAK,0BAA2BoB,KAErCuD,EAAI,EAAGA,EAAIygB,EAAa9jB,OAAQqD,IACjCzF,EAAE8U,QAAQqR,OAAOjmB,EAAEgmB,EAAazgB,GAAG2gB,WAAU,IAAOlR,SAASlV,EAAEJ,OAAOyR,qBAE1E,KAAK5L,EAAIwgB,EAAc7jB,OAAS,EAAGqD,GAAK,EAAGA,IACvCzF,EAAE8U,QAAQuR,QAAQnmB,EAAE+lB,EAAcxgB,GAAG2gB,WAAU,IAAOlR,SAASlV,EAAEJ,OAAOyR,uBAGhFrR,EAAEuU,YAAc,WACZvU,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,IAAMvE,EAAEJ,OAAOyR,qBAAqB2U,SACnFhmB,EAAEW,OAAO2lB,WAAW,4BAExBtmB,EAAEwU,OAAS,SAAU+R,GACjB,GAAIC,GAAWxmB,EAAEa,YAAcb,EAAEyQ,YACjCzQ,GAAEuU,cACFvU,EAAE+lB,aACF/lB,EAAEyY,mBACE8N,GACAvmB,EAAEwd,QAAQgJ,EAAWxmB,EAAEyQ,aAAc,GAAG,IAIhDzQ,EAAEkB,QAAU,WACR,GAAIulB,EAEAzmB,GAAEa,YAAcb,EAAEyQ,cAClBgW,EAAWzmB,EAAEW,OAAOyB,OAA0B,EAAjBpC,EAAEyQ,aAAmBzQ,EAAEa,YACpD4lB,GAAsBzmB,EAAEyQ,aACxBzQ,EAAEwd,QAAQiJ,EAAU,GAAG,GAAO,KAGG,SAA3BzmB,EAAEJ,OAAO+N,eAA4B3N,EAAEa,aAAgC,EAAjBb,EAAEyQ,cAAsBzQ,EAAEa,YAAcb,EAAEW,OAAOyB,OAAkC,EAAzBpC,EAAEJ,OAAO+N,iBAC/H8Y,GAAYzmB,EAAEW,OAAOyB,OAASpC,EAAEa,YAAcb,EAAEyQ,aAChDgW,GAAsBzmB,EAAEyQ,aACxBzQ,EAAEwd,QAAQiJ,EAAU,GAAG,GAAO,KAMtCzmB,EAAE0mB,YAAc,SAAU/lB,GAItB,GAHIX,EAAEJ,OAAOqB,MACTjB,EAAEuU,cAEgB,gBAAX5T,IAAuBA,EAAOyB,OACrC,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIzF,EAAE8U,QAAQqR,OAAOxlB,EAAO8E,QAI3CzF,GAAE8U,QAAQqR,OAAOxlB,EAEjBX,GAAEJ,OAAOqB,MACTjB,EAAE+lB,aAEA/lB,EAAEJ,OAAO+C,UAAY3C,EAAE0U,QAAQ/R,UACjC3C,EAAEsX,QAAO,IAGjBtX,EAAE2mB,aAAe,SAAUhmB,GACnBX,EAAEJ,OAAOqB,MACTjB,EAAEuU,aAEN,IAAIgH,GAAiBvb,EAAEa,YAAc,CACrC,IAAsB,gBAAXF,IAAuBA,EAAOyB,OAAQ,CAC7C,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIzF,EAAE8U,QAAQuR,QAAQ1lB,EAAO8E,GAE5C8V,GAAiBvb,EAAEa,YAAcF,EAAOyB,WAGxCpC,GAAE8U,QAAQuR,QAAQ1lB,EAElBX,GAAEJ,OAAOqB,MACTjB,EAAE+lB,aAEA/lB,EAAEJ,OAAO+C,UAAY3C,EAAE0U,QAAQ/R,UACjC3C,EAAEsX,QAAO,GAEbtX,EAAEwd,QAAQjC,EAAgB,GAAG,IAEjCvb,EAAE4mB,YAAc,SAAUC,GAClB7mB,EAAEJ,OAAOqB,OACTjB,EAAEuU,cACFvU,EAAEW,OAASX,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,YAEjD,IACIuiB,GADAvL,EAAiBvb,EAAEa,WAEvB,IAA6B,gBAAlBgmB,IAA8BA,EAAczkB,OAAQ,CAC3D,IAAK,GAAIqD,GAAI,EAAGA,EAAIohB,EAAczkB,OAAQqD,IACtCqhB,EAAgBD,EAAcphB,GAC1BzF,EAAEW,OAAOmmB,IAAgB9mB,EAAEW,OAAOC,GAAGkmB,GAAed,SACpDc,EAAgBvL,GAAgBA,GAExCA,GAAiBjb,KAAKgZ,IAAIiC,EAAgB,OAG1CuL,GAAgBD,EACZ7mB,EAAEW,OAAOmmB,IAAgB9mB,EAAEW,OAAOC,GAAGkmB,GAAed,SACpDc,EAAgBvL,GAAgBA,IACpCA,EAAiBjb,KAAKgZ,IAAIiC,EAAgB,EAG1Cvb,GAAEJ,OAAOqB,MACTjB,EAAE+lB,aAGA/lB,EAAEJ,OAAO+C,UAAY3C,EAAE0U,QAAQ/R,UACjC3C,EAAEsX,QAAO,GAETtX,EAAEJ,OAAOqB,KACTjB,EAAEwd,QAAQjC,EAAiBvb,EAAEyQ,aAAc,GAAG,GAG9CzQ,EAAEwd,QAAQjC,EAAgB,GAAG,IAIrCvb,EAAE+mB,gBAAkB,WAEhB,IAAK,GADDF,MACKphB,EAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IACjCohB,EAAcxjB,KAAKoC,EAEvBzF,GAAE4mB,YAAYC,IAOlB7mB,EAAEmkB,SACE7X,MACIkY,aAAc,WACV,IAAK,GAAI/e,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAAK,CACtC,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpBN,EAASuU,EAAM,GAAGc,kBAClBwM,GAAM7hB,CACLnF,GAAEJ,OAAO4L,mBAAkBwb,GAAUhnB,EAAEyV,UAC5C,IAAIwR,GAAK,CACJjnB,GAAE4D,iBACHqjB,EAAKD,EACLA,EAAK,EAET,IAAIE,GAAelnB,EAAEJ,OAAO0M,KAAKC,UACzBjM,KAAKgZ,IAAI,EAAIhZ,KAAKuG,IAAI6S,EAAM,GAAGzP,UAAW,GAC1C,EAAI3J,KAAK6c,IAAI7c,KAAKgZ,IAAII,EAAM,GAAGzP,UAAU,GAAK,EACtDyP,GACKtE,KACG+R,QAASD,IAEZ7c,UAAU,eAAiB2c,EAAK,OAASC,EAAK,cAK3D7C,cAAe,SAAUH,GAErB,GADAjkB,EAAEW,OAAO6b,WAAWyH,GAChBjkB,EAAEJ,OAAO4L,kBAAiC,IAAbyY,EAAgB,CAC7C,GAAImD,IAAiB,CACrBpnB,GAAEW,OAAOmX,cAAc,WACnB,IAAIsP,GACCpnB,EAAL,CACAonB,GAAiB,EACjBpnB,EAAE2I,WAAY,CAEd,KAAK,GADD0e,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzF5hB,EAAI,EAAGA,EAAI4hB,EAAcjlB,OAAQqD,IACtCzF,EAAE8U,QAAQmN,QAAQoF,EAAc5hB,UAMpDuG,MACIwY,aAAc,WACV,IAAK,GAAI/e,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAAK,CACtC,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpBwE,EAAWyP,EAAM,GAAGzP,QACpBjK,GAAEJ,OAAOoM,KAAKC,gBACdhC,EAAW3J,KAAKgZ,IAAIhZ,KAAK6c,IAAIzD,EAAM,GAAGzP,SAAU,IAAI,GAExD,IAAI9E,GAASuU,EAAM,GAAGc,kBAClB7O,GAAS,IAAO1B,EAChBqd,EAAU3b,EACV4b,EAAU,EACVP,GAAM7hB,EACN8hB,EAAK,CAaT,IAZKjnB,EAAE4D,eAME5D,EAAEoF,MACPkiB,GAAWA,IANXL,EAAKD,EACLA,EAAK,EACLO,GAAWD,EACXA,EAAU,GAMd5N,EAAM,GAAGtD,MAAMoR,QAAUlnB,KAAKuG,IAAIvG,KAAKF,MAAM6J,IAAajK,EAAEW,OAAOyB,OAE/DpC,EAAEJ,OAAOoM,KAAKD,aAAc,CAE5B,GAAI0b,GAAeznB,EAAE4D,eAAiB8V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAc1nB,EAAE4D,eAAiB8V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAarlB,SACbqlB,EAAevnB,EAAE,oCAAsCF,EAAE4D,eAAiB,OAAS,OAAS,YAC5F8V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYtlB,SACZslB,EAAcxnB,EAAE,oCAAsCF,EAAE4D,eAAiB,QAAU,UAAY,YAC/F8V,EAAMyM,OAAOuB,IAEbD,EAAarlB,SAAQqlB,EAAa,GAAGrR,MAAM+Q,QAAU7mB,KAAKgZ,KAAKrP,EAAU,IACzEyd,EAAYtlB,SAAQslB,EAAY,GAAGtR,MAAM+Q,QAAU7mB,KAAKgZ,IAAIrP,EAAU,IAG9EyP,EACKrP,UAAU,eAAiB2c,EAAK,OAASC,EAAK,oBAAsBM,EAAU,gBAAkBD,EAAU,UAGvHlD,cAAe,SAAUH,GAErB,GADAjkB,EAAEW,OAAO6b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,GAC1JjkB,EAAEJ,OAAO4L,kBAAiC,IAAbyY,EAAgB,CAC7C,GAAImD,IAAiB,CACrBpnB,GAAEW,OAAOC,GAAGZ,EAAEa,aAAaiX,cAAc,WACrC,IAAIsP,GACCpnB,GACAE,EAAEJ,MAAM8b,SAAS5b,EAAEJ,OAAO4E,kBAA/B,CACA4iB,GAAiB,EACjBpnB,EAAE2I,WAAY,CAEd,KAAK,GADD0e,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzF5hB,EAAI,EAAGA,EAAI4hB,EAAcjlB,OAAQqD,IACtCzF,EAAE8U,QAAQmN,QAAQoF,EAAc5hB,UAMpDyG,MACIsY,aAAc,WACV,GAAuBmD,GAAnBC,EAAgB,CAChB5nB,GAAEJ,OAAOsM,KAAKC,SACVnM,EAAE4D,gBACF+jB,EAAa3nB,EAAE8U,QAAQG,KAAK,uBACF,IAAtB0S,EAAWvlB,SACXulB,EAAaznB,EAAE,0CACfF,EAAE8U,QAAQqR,OAAOwB,IAErBA,EAAWvS,KAAK5P,OAAQxF,EAAEuF,MAAQ,SAGlCoiB,EAAa3nB,EAAEG,UAAU8U,KAAK,uBACJ,IAAtB0S,EAAWvlB,SACXulB,EAAaznB,EAAE,0CACfF,EAAEG,UAAUgmB,OAAOwB,KAI/B,KAAK,GAAIliB,GAAI,EAAGA,EAAIzF,EAAEW,OAAOyB,OAAQqD,IAAK,CACtC,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpBoiB,EAAiB,GAAJpiB,EACbrF,EAAQE,KAAKC,MAAMsnB,EAAa,IAChC7nB,GAAEoF,MACFyiB,GAAcA,EACdznB,EAAQE,KAAKC,OAAOsnB,EAAa,KAErC,IAAI5d,GAAW3J,KAAKgZ,IAAIhZ,KAAK6c,IAAIzD,EAAM,GAAGzP,SAAU,IAAI,GACpD+c,EAAK,EAAGC,EAAK,EAAGa,EAAK,CACrBriB,GAAI,IAAM,GACVuhB,EAAe,GAAR5mB,EAAYJ,EAAEwY,KACrBsP,EAAK,IAECriB,EAAI,GAAK,IAAM,GACrBuhB,EAAK,EACLc,EAAe,GAAR1nB,EAAYJ,EAAEwY,OAEf/S,EAAI,GAAK,IAAM,GACrBuhB,EAAKhnB,EAAEwY,KAAe,EAARpY,EAAYJ,EAAEwY,KAC5BsP,EAAK9nB,EAAEwY,OAED/S,EAAI,GAAK,IAAM,IACrBuhB,GAAOhnB,EAAEwY,KACTsP,EAAK,EAAI9nB,EAAEwY,KAAgB,EAATxY,EAAEwY,KAAWpY,GAE/BJ,EAAEoF,MACF4hB,GAAMA,GAGLhnB,EAAE4D,iBACHqjB,EAAKD,EACLA,EAAK,EAGT,IAAI3c,GAAY,YAAcrK,EAAE4D,eAAiB,GAAKikB,GAAc,iBAAmB7nB,EAAE4D,eAAiBikB,EAAa,GAAK,oBAAsBb,EAAK,OAASC,EAAK,OAASa,EAAK,KAMnL,IALI7d,GAAY,GAAKA,GAAW,IAC5B2d,EAAoB,GAAJniB,EAAoB,GAAXwE,EACrBjK,EAAEoF,MAAKwiB,EAAqB,IAAJniB,EAAoB,GAAXwE,IAEzCyP,EAAMrP,UAAUA,GACZrK,EAAEJ,OAAOsM,KAAKH,aAAc,CAE5B,GAAI0b,GAAeznB,EAAE4D,eAAiB8V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAc1nB,EAAE4D,eAAiB8V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAarlB,SACbqlB,EAAevnB,EAAE,oCAAsCF,EAAE4D,eAAiB,OAAS,OAAS,YAC5F8V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYtlB,SACZslB,EAAcxnB,EAAE,oCAAsCF,EAAE4D,eAAiB,QAAU,UAAY,YAC/F8V,EAAMyM,OAAOuB,IAEbD,EAAarlB,SAAQqlB,EAAa,GAAGrR,MAAM+Q,QAAU7mB,KAAKgZ,KAAKrP,EAAU,IACzEyd,EAAYtlB,SAAQslB,EAAY,GAAGtR,MAAM+Q,QAAU7mB,KAAKgZ,IAAIrP,EAAU,KAUlF,GAPAjK,EAAE8U,QAAQM,KACN2S,2BAA4B,YAAe/nB,EAAEwY,KAAO,EAAK,KACzDwP,wBAAyB,YAAehoB,EAAEwY,KAAO,EAAK,KACtDyP,uBAAwB,YAAejoB,EAAEwY,KAAO,EAAK,KACrD0P,mBAAoB,YAAeloB,EAAEwY,KAAO,EAAK,OAGjDxY,EAAEJ,OAAOsM,KAAKC,OACd,GAAInM,EAAE4D,eACF+jB,EAAWtd,UAAU,qBAAuBrK,EAAEuF,MAAQ,EAAIvF,EAAEJ,OAAOsM,KAAKE,cAAgB,QAAWpM,EAAEuF,MAAQ,EAAK,0CAA6CvF,EAAEJ,OAAOsM,KAAgB,YAAI,SAE3L,CACD,GAAIic,GAAc7nB,KAAKuG,IAAI+gB,GAA4D,GAA3CtnB,KAAKC,MAAMD,KAAKuG,IAAI+gB,GAAiB,IAC7EQ,EAAa,KAAO9nB,KAAK+nB,IAAkB,EAAdF,EAAkB7nB,KAAKyhB,GAAK,KAAO,EAAIzhB,KAAKgoB,IAAkB,EAAdH,EAAkB7nB,KAAKyhB,GAAK,KAAO,GAChHwG,EAASvoB,EAAEJ,OAAOsM,KAAKG,YACvBmc,EAASxoB,EAAEJ,OAAOsM,KAAKG,YAAc+b,EACrCjjB,EAASnF,EAAEJ,OAAOsM,KAAKE,YAC3Bub,GAAWtd,UAAU,WAAake,EAAS,QAAUC,EAAS,uBAAyBxoB,EAAEwF,OAAS,EAAIL,GAAU,QAAWnF,EAAEwF,OAAS,EAAIgjB,EAAU,uBAG5J,GAAIC,GAAWzoB,EAAE0oB,UAAY1oB,EAAE2oB,aAAiB3oB,EAAEwY,KAAO,EAAK,CAC9DxY,GAAE8U,QAAQzK,UAAU,qBAAuBoe,EAAU,gBAAkBzoB,EAAE4D,eAAiB,EAAIgkB,GAAiB,iBAAmB5nB,EAAE4D,gBAAkBgkB,EAAgB,GAAK,SAE/KxD,cAAe,SAAUH,GACrBjkB,EAAEW,OAAO6b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,GAC1JjkB,EAAEJ,OAAOsM,KAAKC,SAAWnM,EAAE4D,gBAC3B5D,EAAEG,UAAU8U,KAAK,uBAAuBuH,WAAWyH,KAI/DvY,WACI8Y,aAAc,WAMV,IAAK,GALDna,GAAYrK,EAAEyV,UACdmT,EAAS5oB,EAAE4D,gBAAkByG,EAAYrK,EAAEuF,MAAQ,GAAK8E,EAAYrK,EAAEwF,OAAS,EAC/EmG,EAAS3L,EAAE4D,eAAiB5D,EAAEJ,OAAO8L,UAAUC,QAAS3L,EAAEJ,OAAO8L,UAAUC,OAC3E8J,EAAYzV,EAAEJ,OAAO8L,UAAUG,MAE1BpG,EAAI,EAAGrD,EAASpC,EAAEW,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpB8T,EAAYvZ,EAAE2Y,gBAAgBlT,GAC9BojB,EAAcnP,EAAM,GAAGc,kBACvBsO,GAAoBF,EAASC,EAActP,EAAY,GAAKA,EAAYvZ,EAAEJ,OAAO8L,UAAUI,SAE3Fwb,EAAUtnB,EAAE4D,eAAiB+H,EAASmd,EAAmB,EACzDvB,EAAUvnB,EAAE4D,eAAiB,EAAI+H,EAASmd,EAE1CC,GAActT,EAAYnV,KAAKuG,IAAIiiB,GAEnCE,EAAahpB,EAAE4D,eAAiB,EAAI5D,EAAEJ,OAAO8L,UAAUE,QAAU,EACjEqd,EAAajpB,EAAE4D,eAAiB5D,EAAEJ,OAAO8L,UAAUE,QAAU,EAAqB,CAGlFtL,MAAKuG,IAAIoiB,GAAc,OAAOA,EAAa,GAC3C3oB,KAAKuG,IAAImiB,GAAc,OAAOA,EAAa,GAC3C1oB,KAAKuG,IAAIkiB,GAAc,OAAOA,EAAa,GAC3CzoB,KAAKuG,IAAIygB,GAAW,OAAOA,EAAU,GACrChnB,KAAKuG,IAAI0gB,GAAW,OAAOA,EAAU,EAEzC,IAAI2B,GAAiB,eAAiBD,EAAa,MAAQD,EAAa,MAAQD,EAAa,gBAAkBxB,EAAU,gBAAkBD,EAAU,MAIrJ,IAFA5N,EAAMrP,UAAU6e,GAChBxP,EAAM,GAAGtD,MAAMoR,QAAUlnB,KAAKuG,IAAIvG,KAAKF,MAAM0oB,IAAqB,EAC9D9oB,EAAEJ,OAAO8L,UAAUK,aAAc,CAEjC,GAAI0b,GAAeznB,EAAE4D,eAAiB8V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFyS,EAAc1nB,EAAE4D,eAAiB8V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBwS,EAAarlB,SACbqlB,EAAevnB,EAAE,oCAAsCF,EAAE4D,eAAiB,OAAS,OAAS,YAC5F8V,EAAMyM,OAAOsB,IAEU,IAAvBC,EAAYtlB,SACZslB,EAAcxnB,EAAE,oCAAsCF,EAAE4D,eAAiB,QAAU,UAAY,YAC/F8V,EAAMyM,OAAOuB,IAEbD,EAAarlB,SAAQqlB,EAAa,GAAGrR,MAAM+Q,QAAU2B,EAAmB,EAAIA,EAAmB,GAC/FpB,EAAYtlB,SAAQslB,EAAY,GAAGtR,MAAM+Q,SAAY2B,EAAoB,GAAKA,EAAmB,IAK7G,GAAI9oB,EAAEye,QAAQC,GAAI,CACd,GAAIyK,GAAKnpB,EAAE8U,QAAQ,GAAGsB,KACtB+S,GAAGC,kBAAoBR,EAAS,WAGxCxE,cAAe,SAAUH,GACrBjkB,EAAEW,OAAO6b,WAAWyH,GAAUhP,KAAK,gHAAgHuH,WAAWyH,MAQ1KjkB,EAAEqI,MACEghB,oBAAoB,EACpBC,iBAAkB,SAAUpnB,EAAOqnB,GAC/B,GAAqB,mBAAVrnB,KACoB,mBAApBqnB,KAAiCA,GAAkB,GACtC,IAApBvpB,EAAEW,OAAOyB,QAAb,CAEA,GAAIsX,GAAQ1Z,EAAEW,OAAOC,GAAGsB,GACpBsnB,EAAM9P,EAAMzE,KAAK,IAAMjV,EAAEJ,OAAOwS,iBAAmB,SAAWpS,EAAEJ,OAAO0S,sBAAwB,UAAYtS,EAAEJ,OAAOyS,uBAAyB,MAC7IqH,EAAMkC,SAAS5b,EAAEJ,OAAOwS,mBAAsBsH,EAAMkC,SAAS5b,EAAEJ,OAAO0S,wBAA2BoH,EAAMkC,SAAS5b,EAAEJ,OAAOyS,0BACzHmX,EAAMA,EAAIC,IAAI/P,EAAM,KAEL,IAAf8P,EAAIpnB,QAERonB,EAAIzpB,KAAK,WACL,GAAI2pB,GAAOxpB,EAAEJ,KACb4pB,GAAKxU,SAASlV,EAAEJ,OAAOyS,uBACvB,IAAIsX,GAAaD,EAAK5oB,KAAK,mBACvB4V,EAAMgT,EAAK5oB,KAAK,YAChB6V,EAAS+S,EAAK5oB,KAAK,eACnB8V,EAAQ8S,EAAK5oB,KAAK,aACtBd,GAAEwW,UAAUkT,EAAK,GAAKhT,GAAOiT,EAAahT,EAAQC,GAAO,EAAO,WAuB5D,GAtBI+S,GACAD,EAAKtU,IAAI,mBAAoB,QAAUuU,EAAa,MACpDD,EAAKpD,WAAW,qBAGZ3P,IACA+S,EAAK5oB,KAAK,SAAU6V,GACpB+S,EAAKpD,WAAW,gBAEhB1P,IACA8S,EAAK5oB,KAAK,QAAS8V,GACnB8S,EAAKpD,WAAW,eAEhB5P,IACAgT,EAAK5oB,KAAK,MAAO4V,GACjBgT,EAAKpD,WAAW,cAKxBoD,EAAKxU,SAASlV,EAAEJ,OAAO0S,uBAAuB2I,YAAYjb,EAAEJ,OAAOyS,wBACnEqH,EAAMzE,KAAK,IAAMjV,EAAEJ,OAAO2S,mBAAqB,MAAQvS,EAAEJ,OAAO6S,gBAAgBuT,SAC5EhmB,EAAEJ,OAAOqB,MAAQsoB,EAAiB,CAClC,GAAIK,GAAqBlQ,EAAM5Y,KAAK,0BACpC,IAAI4Y,EAAMkC,SAAS5b,EAAEJ,OAAOyR,qBAAsB,CAC9C,GAAIwY,GAAgB7pB,EAAE8U,QAAQC,SAAS,6BAA+B6U,EAAqB,WAAa5pB,EAAEJ,OAAOyR,oBAAsB,IACvIrR,GAAEqI,KAAKihB,iBAAiBO,EAAc3nB,SAAS,OAE9C,CACD,GAAI4nB,GAAkB9pB,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAOyR,oBAAsB,6BAA+BuY,EAAqB,KAClI5pB,GAAEqI,KAAKihB,iBAAiBQ,EAAgB5nB,SAAS,IAGzDlC,EAAEoB,KAAK,mBAAoBpB,EAAG0Z,EAAM,GAAIgQ,EAAK,MAGjD1pB,EAAEoB,KAAK,kBAAmBpB,EAAG0Z,EAAM,GAAIgQ,EAAK,QAIpDphB,KAAM,WACF,GAAI7C,GACAkI,EAAgB3N,EAAEJ,OAAO+N,aAK7B,IAJsB,SAAlBA,IACAA,EAAgB,GAEf3N,EAAEqI,KAAKghB,qBAAoBrpB,EAAEqI,KAAKghB,oBAAqB,GACxDrpB,EAAEJ,OAAOkQ,sBACT9P,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAOwR,mBAAmBrR,KAAK,WACtDC,EAAEqI,KAAKihB,iBAAiBppB,EAAEJ,MAAMoC,eAIpC,IAAIyL,EAAgB,EAChB,IAAKlI,EAAIzF,EAAEa,YAAa4E,EAAIzF,EAAEa,YAAc8M,EAAgBlI,IACpDzF,EAAEW,OAAO8E,IAAIzF,EAAEqI,KAAKihB,iBAAiB7jB,OAI7CzF,GAAEqI,KAAKihB,iBAAiBtpB,EAAEa,YAGlC,IAAIb,EAAEJ,OAAOuQ,sBACT,GAAIxC,EAAgB,GAAM3N,EAAEJ,OAAOwQ,6BAA+BpQ,EAAEJ,OAAOwQ,4BAA8B,EAAI,CACzG,GAAI2Z,GAAS/pB,EAAEJ,OAAOwQ,4BAClByK,EAAMlN,EACNqc,EAAW1pB,KAAK6c,IAAInd,EAAEa,YAAcga,EAAMva,KAAKgZ,IAAIyQ,EAAQlP,GAAM7a,EAAEW,OAAOyB,QAC1E6nB,EAAW3pB,KAAKgZ,IAAItZ,EAAEa,YAAcP,KAAKgZ,IAAIuB,EAAKkP,GAAS,EAE/D,KAAKtkB,EAAIzF,EAAEa,YAAc8M,EAAelI,EAAIukB,EAAUvkB,IAC9CzF,EAAEW,OAAO8E,IAAIzF,EAAEqI,KAAKihB,iBAAiB7jB,EAG7C,KAAKA,EAAIwkB,EAAUxkB,EAAIzF,EAAEa,YAAc4E,IAC/BzF,EAAEW,OAAO8E,IAAIzF,EAAEqI,KAAKihB,iBAAiB7jB,OAG5C,CACD,GAAIoW,GAAY7b,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO0R,eAC9CuK,GAAUzZ,OAAS,GAAGpC,EAAEqI,KAAKihB,iBAAiBzN,EAAU3Z,QAE5D,IAAI6Z,GAAY/b,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO4R,eAC9CuK,GAAU3Z,OAAS,GAAGpC,EAAEqI,KAAKihB,iBAAiBvN,EAAU7Z,WAIxEihB,kBAAmB,WACXnjB,EAAEJ,OAAOwI,cACLpI,EAAEJ,OAAOyQ,+BAAkCrQ,EAAEJ,OAAOyQ,+BAAiCrQ,EAAEqI,KAAKghB,qBAC5FrpB,EAAEqI,KAAKC,QAInB8a,gBAAiB,WACTpjB,EAAEJ,OAAOwI,cAAgBpI,EAAEJ,OAAOyQ,8BAClCrQ,EAAEqI,KAAKC,SASnBtI,EAAE6M,WACEmT,WAAW,EACXkK,gBAAiB,SAAUxoB,GACvB,GAAIyoB,GAAKnqB,EAAE6M,UAGPud,EAAkBpqB,EAAE4D,eACP,eAAXlC,EAAE4f,MAAoC,cAAX5f,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,OAAS/f,EAAE2oB,QAClF,eAAX3oB,EAAE4f,MAAoC,cAAX5f,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,OAAShgB,EAAE4oB,QAC/FpjB,EAAW,EAAoBijB,EAAGI,MAAMplB,SAASnF,EAAE4D,eAAiB,OAAS,OAASumB,EAAGK,SAAW,EACpGC,GAAezqB,EAAEwH,eAAiB2iB,EAAGO,YACrCC,GAAe3qB,EAAEyH,eAAiB0iB,EAAGO,WACrCxjB,GAAWujB,EACXvjB,EAAWujB,EAENvjB,EAAWyjB,IAChBzjB,EAAWyjB,GAEfzjB,GAAYA,EAAWijB,EAAGO,YAC1B1qB,EAAE4H,eAAeV,GACjBlH,EAAE2H,oBAAoBT,GAAU,IAEpC0jB,UAAW,SAAUlpB,GACjB,GAAIyoB,GAAKnqB,EAAE6M,SACXsd,GAAGnK,WAAY,EACfte,EAAEiE,iBACFjE,EAAE+d,kBAEF0K,EAAGD,gBAAgBxoB,GACnBsG,aAAamiB,EAAGU,aAEhBV,EAAGI,MAAM/N,WAAW,GAChBxc,EAAEJ,OAAOkN,eACTqd,EAAGI,MAAMnV,IAAI,UAAW,GAE5BpV,EAAE8U,QAAQ0H,WAAW,KACrB2N,EAAGW,KAAKtO,WAAW,KACnBxc,EAAEoB,KAAK,uBAAwBpB,IAEnC+qB,SAAU,SAAUrpB,GAChB,GAAIyoB,GAAKnqB,EAAE6M,SACNsd,GAAGnK,YACJte,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,EACrBukB,EAAGD,gBAAgBxoB,GACnB1B,EAAE8U,QAAQ0H,WAAW,GACrB2N,EAAGI,MAAM/N,WAAW,GACpB2N,EAAGW,KAAKtO,WAAW,GACnBxc,EAAEoB,KAAK,sBAAuBpB,KAElCgrB,QAAS,SAAUtpB,GACf,GAAIyoB,GAAKnqB,EAAE6M,SACNsd,GAAGnK,YACRmK,EAAGnK,WAAY,EACXhgB,EAAEJ,OAAOkN,gBACT9E,aAAamiB,EAAGU,aAChBV,EAAGU,YAAc7pB,WAAW,WACxBmpB,EAAGI,MAAMnV,IAAI,UAAW,GACxB+U,EAAGI,MAAM/N,WAAW,MACrB,MAGPxc,EAAEoB,KAAK,qBAAsBpB,GACzBA,EAAEJ,OAAOoN,wBACThN,EAAEmI,eAGV8iB,gBAAiB,WACb,MAAKjrB,GAAEJ,OAAOyO,iBAAkB,GAAUrO,EAAE0U,QAAQG,MACxC7U,EAAEke,YADqDle,EAAE2d,sBAGzEuN,gBAAiB,WACb,GAAIf,GAAKnqB,EAAE6M,UACPhL,EAAS7B,EAAE0U,QAAQG,MAAQsV,EAAGI,MAAQrmB,QAC1ChE,GAAEiqB,EAAGI,OAAOY,GAAGhB,EAAGc,gBAAgBrN,MAAOuM,EAAGS,WAC5C1qB,EAAE2B,GAAQspB,GAAGhB,EAAGc,gBAAgBpN,KAAMsM,EAAGY,UACzC7qB,EAAE2B,GAAQspB,GAAGhB,EAAGc,gBAAgBnN,IAAKqM,EAAGa,UAE5CI,iBAAkB,WACd,GAAIjB,GAAKnqB,EAAE6M,UACPhL,EAAS7B,EAAE0U,QAAQG,MAAQsV,EAAGI,MAAQrmB,QAC1ChE,GAAEiqB,EAAGI,OAAOc,IAAIlB,EAAGc,gBAAgBrN,MAAOuM,EAAGS,WAC7C1qB,EAAE2B,GAAQwpB,IAAIlB,EAAGc,gBAAgBpN,KAAMsM,EAAGY,UAC1C7qB,EAAE2B,GAAQwpB,IAAIlB,EAAGc,gBAAgBnN,IAAKqM,EAAGa,UAE7C5N,IAAK,WACD,GAAKpd,EAAEJ,OAAOiN,UAAd,CACA,GAAIsd,GAAKnqB,EAAE6M,SACXsd,GAAGI,MAAQrqB,EAAEF,EAAEJ,OAAOiN,WAClB7M,EAAEJ,OAAOmP,mBAAmD,gBAAvB/O,GAAEJ,OAAOiN,WAA0Bsd,EAAGI,MAAMnoB,OAAS,GAAqD,IAAhDpC,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOiN,WAAWzK,SACpI+nB,EAAGI,MAAQvqB,EAAEG,UAAU8U,KAAKjV,EAAEJ,OAAOiN,YAEzCsd,EAAGW,KAAOX,EAAGI,MAAMtV,KAAK,0BACD,IAAnBkV,EAAGW,KAAK1oB,SACR+nB,EAAGW,KAAO5qB,EAAE,6CACZiqB,EAAGI,MAAMpE,OAAOgE,EAAGW,OAEvBX,EAAGW,KAAK,GAAG1U,MAAM7Q,MAAQ,GACzB4kB,EAAGW,KAAK,GAAG1U,MAAM5Q,OAAS,GAC1B2kB,EAAGmB,UAAYtrB,EAAE4D,eAAiBumB,EAAGI,MAAM,GAAGgB,YAAcpB,EAAGI,MAAM,GAAGnS,aAExE+R,EAAGqB,QAAUxrB,EAAEwY,KAAOxY,EAAEgZ,YACxBmR,EAAGO,YAAcP,EAAGqB,SAAWrB,EAAGmB,UAAYtrB,EAAEwY,MAChD2R,EAAGK,SAAWL,EAAGmB,UAAYnB,EAAGqB,QAE5BxrB,EAAE4D,eACFumB,EAAGW,KAAK,GAAG1U,MAAM7Q,MAAQ4kB,EAAGK,SAAW,KAGvCL,EAAGW,KAAK,GAAG1U,MAAM5Q,OAAS2kB,EAAGK,SAAW,KAGxCL,EAAGqB,SAAW,EACdrB,EAAGI,MAAM,GAAGnU,MAAMqV,QAAU,OAG5BtB,EAAGI,MAAM,GAAGnU,MAAMqV,QAAU,GAE5BzrB,EAAEJ,OAAOkN,gBACTqd,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,KAGpC3C,aAAc,WACV,GAAKxkB,EAAEJ,OAAOiN,UAAd,CACA,GAGI6e,GAFAvB,EAAKnqB,EAAE6M,UAIP8e,GAHY3rB,EAAEyV,WAAa,EAGjB0U,EAAGK,SACjBkB,IAAUvB,EAAGmB,UAAYnB,EAAGK,UAAYxqB,EAAEiK,SACtCjK,EAAEoF,KAAOpF,EAAE4D,gBACX8nB,GAAUA,EACNA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,IAEHA,EAASvB,EAAGK,SAAWL,EAAGmB,YAChCK,EAAUxB,EAAGmB,UAAYI,IAIzBA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,GAEJA,EAASvB,EAAGK,SAAWL,EAAGmB,YAC/BK,EAAUxB,EAAGmB,UAAYI,GAG7B1rB,EAAE4D,gBACE5D,EAAE0U,QAAQE,aACVuV,EAAGW,KAAKzgB,UAAU,eAAiB,EAAW,aAG9C8f,EAAGW,KAAKzgB,UAAU,cAAgB,EAAW,OAEjD8f,EAAGW,KAAK,GAAG1U,MAAM7Q,MAAQomB,EAAU,OAG/B3rB,EAAE0U,QAAQE,aACVuV,EAAGW,KAAKzgB,UAAU,oBAAsB,EAAW,UAGnD8f,EAAGW,KAAKzgB,UAAU,cAAgB,EAAW,OAEjD8f,EAAGW,KAAK,GAAG1U,MAAM5Q,OAASmmB,EAAU,MAEpC3rB,EAAEJ,OAAOkN,gBACT9E,aAAamiB,EAAGjiB,SAChBiiB,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,EAC5BgD,EAAGjiB,QAAUlH,WAAW,WACpBmpB,EAAGI,MAAM,GAAGnU,MAAM+Q,QAAU,EAC5BgD,EAAGI,MAAM/N,WAAW,MACrB,QAGX4H,cAAe,SAAUH,GAChBjkB,EAAEJ,OAAOiN,WACd7M,EAAE6M,UAAUie,KAAKtO,WAAWyH,KAOpCjkB,EAAEsd,YACEsO,aAAc,SAAUvH,EAAGC,GACvBxkB,KAAKukB,EAAIA,EACTvkB,KAAKwkB,EAAIA,EACTxkB,KAAK+rB,UAAYxH,EAAEjiB,OAAS,CAI5B,IAAI0pB,GAAIC,CACAjsB,MAAKukB,EAAEjiB,MAEftC,MAAKksB,YAAc,SAAUC,GACzB,MAAKA,IAGLF,EAAKG,EAAapsB,KAAKukB,EAAG4H,GAC1BH,EAAKC,EAAK,GAIDE,EAAKnsB,KAAKukB,EAAEyH,KAAQhsB,KAAKwkB,EAAEyH,GAAMjsB,KAAKwkB,EAAEwH,KAAShsB,KAAKukB,EAAE0H,GAAMjsB,KAAKukB,EAAEyH,IAAOhsB,KAAKwkB,EAAEwH,IAR5E,EAWpB,IAAII,GAAe,WACf,GAAIlC,GAAUC,EAAUkC,CACxB,OAAO,UAASC,EAAOC,GAGnB,IAFApC,GAAW,EACXD,EAAWoC,EAAMhqB,OACV4nB,EAAWC,EAAW,GACrBmC,EAAMD,EAAQnC,EAAWC,GAAY,IAAMoC,EAC3CpC,EAAWkC,EAEXnC,EAAWmC,CAEnB,OAAOnC,QAKnBsC,uBAAwB,SAASC,GACzBvsB,EAAEsd,WAAWC,SAAQvd,EAAEsd,WAAWC,OAASvd,EAAEJ,OAAOqB,KACpD,GAAIjB,GAAEsd,WAAWsO,aAAa5rB,EAAE0Y,WAAY6T,EAAE7T,YAC9C,GAAI1Y,GAAEsd,WAAWsO,aAAa5rB,EAAE+X,SAAUwU,EAAExU,YAEpDyM,aAAc,SAAU/O,EAAWyO,GAGhC,QAASsI,GAAuBD,GAK3B9W,EAAY8W,EAAEnnB,KAA8B,eAAvBmnB,EAAE3sB,OAAO8K,WAA8B1K,EAAEyV,UAAYzV,EAAEyV,UACjD,UAAvBzV,EAAEJ,OAAOgR,YACT5Q,EAAEsd,WAAWgP,uBAAuBC,GAGpCE,GAAuBzsB,EAAEsd,WAAWC,OAAOyO,aAAavW,IAGxDgX,GAA8C,cAAvBzsB,EAAEJ,OAAOgR,YAChCwX,GAAcmE,EAAE9kB,eAAiB8kB,EAAE/kB,iBAAmBxH,EAAEyH,eAAiBzH,EAAEwH,gBAC3EilB,GAAuBhX,EAAYzV,EAAEwH,gBAAkB4gB,EAAamE,EAAE/kB,gBAGtExH,EAAEJ,OAAO+Q,iBACT8b,EAAsBF,EAAE9kB,eAAiBglB,GAE7CF,EAAE3kB,eAAe6kB,GACjBF,EAAE5kB,oBAAoB8kB,GAAqB,EAAOzsB,GAClDusB,EAAE1kB,oBAzBP,GACIugB,GAAYqE,EADZC,EAAa1sB,EAAEJ,OAAO8Q,OA2B1B,IAAI1Q,EAAE2sB,QAAQD,GACV,IAAK,GAAIjnB,GAAI,EAAGA,EAAIinB,EAAWtqB,OAAQqD,IAC/BinB,EAAWjnB,KAAOye,GAAgBwI,EAAWjnB,YAAcxF,IAC3DusB,EAAuBE,EAAWjnB,QAIrCinB,aAAsBzsB,IAAUikB,IAAiBwI,GAEtDF,EAAuBE,IAG9BtI,cAAe,SAAUH,EAAUC,GAG/B,QAAS0I,GAAwBL;AAC7BA,EAAE7kB,qBAAqBuc,EAAUjkB,GAChB,IAAbikB,IACAsI,EAAEpJ,oBACFoJ,EAAEzX,QAAQgD,cAAc,WACf4U,IACDH,EAAE3sB,OAAOqB,MAA+B,UAAvBjB,EAAEJ,OAAOgR,WAC1B2b,EAAErrB,UAENqrB,EAAEnJ,sBAXd,GACI3d,GADAinB,EAAa1sB,EAAEJ,OAAO8Q,OAgB1B,IAAI1Q,EAAE2sB,QAAQD,GACV,IAAKjnB,EAAI,EAAGA,EAAIinB,EAAWtqB,OAAQqD,IAC3BinB,EAAWjnB,KAAOye,GAAgBwI,EAAWjnB,YAAcxF,IAC3D2sB,EAAwBF,EAAWjnB,QAItCinB,aAAsBzsB,IAAUikB,IAAiBwI,GACtDE,EAAwBF,KAQpC1sB,EAAEoN,SACEyf,YAAa,SAAUnrB,EAAGrB,GACtB,GAAIysB,GAAU5oB,SAAS6oB,SAASC,KAAKjU,QAAQ,IAAK,IAC9CkU,EAAkBjtB,EAAEW,OAAOC,GAAGZ,EAAEa,aAAaC,KAAK,YAClDgsB,KAAYG,GACZjtB,EAAEwd,QAAQxd,EAAE8U,QAAQC,SAAS,IAAM/U,EAAEJ,OAAO2E,WAAa,eAAiB,EAAY,MAAMrC,UAGpGod,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAC9Ble,GAAEsC,QAAQ8b,GAAQ,aAActe,EAAEoN,QAAQyf,cAE9CjJ,QAAS,WACL,GAAK5jB,EAAEoN,QAAQ8f,aAAgBltB,EAAEJ,OAAOwN,QACxC,GAAIpN,EAAEJ,OAAO2N,cAAgB/K,OAAO8K,SAAW9K,OAAO8K,QAAQC,aAC1D/K,OAAO8K,QAAQC,aAAa,KAAM,KAAO,IAAMvN,EAAEW,OAAOC,GAAGZ,EAAEa,aAAaC,KAAK,cAAgB,QAC5F,CACH,GAAI4Y,GAAQ1Z,EAAEW,OAAOC,GAAGZ,EAAEa,aACtBmsB,EAAOtT,EAAM5Y,KAAK,cAAgB4Y,EAAM5Y,KAAK,eACjDoD,UAAS6oB,SAASC,KAAOA,GAAQ,KAGzCG,KAAM,WACF,GAAKntB,EAAEJ,OAAOwN,UAAWpN,EAAEJ,OAAO0N,QAAlC,CACAtN,EAAEoN,QAAQ8f,aAAc,CACxB,IAAIF,GAAO9oB,SAAS6oB,SAASC,KAAKjU,QAAQ,IAAK,GAC/C,IAAIiU,EAEA,IAAK,GADDniB,GAAQ,EACHpF,EAAI,EAAGrD,EAASpC,EAAEW,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpB2nB,EAAY1T,EAAM5Y,KAAK,cAAgB4Y,EAAM5Y,KAAK,eACtD,IAAIssB,IAAcJ,IAAStT,EAAMkC,SAAS5b,EAAEJ,OAAOyR,qBAAsB,CACrE,GAAInP,GAAQwX,EAAMxX,OAClBlC,GAAEwd,QAAQtb,EAAO2I,EAAO7K,EAAEJ,OAAOsT,oBAAoB,IAI7DlT,EAAEJ,OAAOyN,mBAAmBrN,EAAEoN,QAAQkS,iBAE9C+N,QAAS,WACDrtB,EAAEJ,OAAOyN,mBAAmBrN,EAAEoN,QAAQkS,cAAa,KAO/Dtf,EAAEsN,SACE6f,KAAM,WACF,GAAKntB,EAAEJ,OAAO0N,QAAd,CACA,IAAK9K,OAAO8K,UAAY9K,OAAO8K,QAAQggB,UAGnC,MAFAttB,GAAEJ,OAAO0N,SAAU,OACnBtN,EAAEJ,OAAOwN,SAAU,EAGvBpN,GAAEsN,QAAQ4f,aAAc,EACxBptB,KAAKytB,MAAQztB,KAAK0tB,iBACb1tB,KAAKytB,MAAME,KAAQ3tB,KAAKytB,MAAMG,SACnC5tB,KAAK6tB,cAAc,EAAG7tB,KAAKytB,MAAMG,MAAO1tB,EAAEJ,OAAOsT,oBAC5ClT,EAAEJ,OAAO2N,cACV/K,OAAOorB,iBAAiB,WAAY9tB,KAAK+tB,uBAGjDA,mBAAoB,WAChB7tB,EAAEsN,QAAQigB,MAAQvtB,EAAEsN,QAAQkgB,gBAC5BxtB,EAAEsN,QAAQqgB,cAAc3tB,EAAEJ,OAAOiL,MAAO7K,EAAEsN,QAAQigB,MAAMG,OAAO,IAEnEF,cAAe,WACX,GAAIM,GAAYtrB,OAAOuqB,SAASgB,SAASC,MAAM,GAAG/I,MAAM,KACpD/I,EAAQ4R,EAAU1rB,OAClBqrB,EAAMK,EAAU5R,EAAQ,GACxBwR,EAAQI,EAAU5R,EAAQ,EAC9B,QAASuR,IAAKA,EAAKC,MAAOA,IAE9B/J,WAAY,SAAU8J,EAAKvrB,GACvB,GAAKlC,EAAEsN,QAAQ4f,aAAgBltB,EAAEJ,OAAO0N,QAAxC,CACA,GAAIoM,GAAQ1Z,EAAEW,OAAOC,GAAGsB,GACpBwrB,EAAQ5tB,KAAKmuB,QAAQvU,EAAM5Y,KAAK,gBAC/B0B,QAAOuqB,SAASgB,SAASG,SAAST,KACnCC,EAAQD,EAAM,IAAMC,GAEpB1tB,EAAEJ,OAAO2N,aACT/K,OAAO8K,QAAQC,aAAa,KAAM,KAAMmgB,GAExClrB,OAAO8K,QAAQggB,UAAU,KAAM,KAAMI,KAG7CO,QAAS,SAAS7R,GACd,MAAOA,GAAKoJ,WAAWnhB,cAClB0U,QAAQ,OAAQ,KAChBA,QAAQ,YAAa,IACrBA,QAAQ,SAAU,KAClBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAExB4U,cAAe,SAAS9iB,EAAO6iB,EAAOjK,GAClC,GAAIiK,EACA,IAAK,GAAIjoB,GAAI,EAAGrD,EAASpC,EAAEW,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIiU,GAAQ1Z,EAAEW,OAAOC,GAAG6E,GACpB0oB,EAAeruB,KAAKmuB,QAAQvU,EAAM5Y,KAAK,gBAC3C,IAAIqtB,IAAiBT,IAAUhU,EAAMkC,SAAS5b,EAAEJ,OAAOyR,qBAAsB,CACzE,GAAInP,GAAQwX,EAAMxX,OAClBlC,GAAEwd,QAAQtb,EAAO2I,EAAO4Y,QAIhCzjB,GAAEwd,QAAQ,EAAG3S,EAAO4Y,KAyEhCzjB,EAAEouB,uBAAyB,WACvBpuB,EAAEJ,OAAOqN,iBAAkB,EAC3B/M,EAAEgE,UAAUmnB,IAAI,UAAW/nB,IAE/BtD,EAAEquB,sBAAwB,WACtBruB,EAAEJ,OAAOqN,iBAAkB,EAC3B/M,EAAEgE,UAAUinB,GAAG,UAAW7nB,IAO9BtD,EAAEiI,YACEY,OAAO,EACPH,gBAAgB,GAAKlG,QAAOgG,MAAQC,WAEpCzI,EAAEJ,OAAOsN,oBAMTlN,EAAEiI,WAAWY,MAASkV,UAAUuQ,UAAUnkB,QAAQ,YAAa,EAC3D,iBACApE,IACI,QAAU,cAkHtB/F,EAAEuuB,yBAA2B,WACzB,IAAKvuB,EAAEiI,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS7B,EAAEG,SAKf,OAJwC,cAApCH,EAAEJ,OAAOuN,yBACTtL,EAAS3B,EAAEF,EAAEJ,OAAOuN,yBAExBtL,EAAOwpB,IAAIrrB,EAAEiI,WAAWY,MAAOtC,IACxB,GAGXvG,EAAEwuB,wBAA0B,WACxB,IAAKxuB,EAAEiI,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS7B,EAAEG,SAKf,OAJwC,cAApCH,EAAEJ,OAAOuN,yBACTtL,EAAS3B,EAAEF,EAAEJ,OAAOuN,yBAExBtL,EAAOspB,GAAGnrB,EAAEiI,WAAWY,MAAOtC,IACvB,GAiNXvG,EAAEwM,UACEgY,aAAc,WACVxkB,EAAEG,UAAU4U,SAAS,8EAA8EhV,KAAK,WACpGiK,EAAqBlK,KAAME,EAAEiK,YAGjCjK,EAAEW,OAAOZ,KAAK,WACV,GAAI2Z,GAAQxZ,EAAEJ,KACd4Z,GAAMzE,KAAK,8EAA8ElV,KAAK,WAC1F,GAAIkK,GAAW3J,KAAK6c,IAAI7c,KAAKgZ,IAAII,EAAM,GAAGzP,UAAU,GAAK,EACzDD,GAAqBlK,KAAMmK,QAIvCma,cAAe,SAAUH,GACG,mBAAbA,KAA0BA,EAAWjkB,EAAEJ,OAAOiL,OACzD7K,EAAEG,UAAU8U,KAAK,8EAA8ElV,KAAK,WAChG,GAAI6B,GAAK1B,EAAEJ,MACP2uB,EAAmBrkB,SAASxI,EAAGd,KAAK,iCAAkC,KAAOmjB,CAChE,KAAbA,IAAgBwK,EAAmB,GACvC7sB,EAAG4a,WAAWiS,OAS1BzuB,EAAEyM,MAEE4P,MAAO,EACPqS,aAAc,EACdC,WAAW,EACXC,SACIlV,MAAOjM,OACPohB,WAAYphB,OACZqhB,YAAarhB,OACbuJ,MAAOvJ,OACPshB,UAAWthB,OACXf,QAAS1M,EAAEJ,OAAO8M,SAEtBsK,OACIgJ,UAAWvS,OACXwS,QAASxS,OACTwT,SAAUxT,OACVyT,SAAUzT,OACVuhB,KAAMvhB,OACNwhB,KAAMxhB,OACNyhB,KAAMzhB,OACN0hB,KAAM1hB,OACNlI,MAAOkI,OACPjI,OAAQiI,OACRsT,OAAQtT,OACRuT,OAAQvT,OACR2hB,gBACAC,mBAEJ3Z,UACI2O,EAAG5W,OACH6W,EAAG7W,OACH6hB,cAAe7hB,OACf8hB,cAAe9hB,OACf+hB,SAAU/hB,QAGdgiB,0BAA2B,SAAU/tB,GACjC,GAAIA,EAAE8f,cAAcpf,OAAS,EAAG,MAAO,EACvC,IAAIstB,GAAKhuB,EAAE8f,cAAc,GAAGC,MACxBkO,EAAKjuB,EAAE8f,cAAc,GAAGE,MACxBuK,EAAKvqB,EAAE8f,cAAc,GAAGC,MACxBmO,EAAKluB,EAAE8f,cAAc,GAAGE,MACxBkB,EAAWtiB,KAAKuvB,KAAKvvB,KAAK6hB,IAAI8J,EAAKyD,EAAI,GAAKpvB,KAAK6hB,IAAIyN,EAAKD,EAAI,GAClE,OAAO/M,IAGXkN,eAAgB,SAAUpuB,GACtB,GAAI6iB,GAAIvkB,EAAEyM,IACV,KAAKzM,EAAE0U,QAAQqb,SAAU,CACrB,GAAe,eAAXruB,EAAE4f,MAAoC,eAAX5f,EAAE4f,MAAyB5f,EAAE8f,cAAcpf,OAAS,EAC/E,MAEJmiB,GAAEqK,QAAQoB,WAAazL,EAAEkL,0BAA0B/tB,GAEvD,MAAK6iB,GAAEqK,QAAQlV,OAAU6K,EAAEqK,QAAQlV,MAAMtX,SACrCmiB,EAAEqK,QAAQlV,MAAQxZ,EAAEJ,MACW,IAA3BykB,EAAEqK,QAAQlV,MAAMtX,SAAcmiB,EAAEqK,QAAQlV,MAAQ1Z,EAAEW,OAAOC,GAAGZ,EAAEa,cAClE0jB,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQlV,MAAMzE,KAAK,oBACvCsP,EAAEqK,QAAQG,UAAYxK,EAAEqK,QAAQ5X,MAAMiZ,OAAO,IAAMjwB,EAAEJ,OAAO8S,oBAC5D6R,EAAEqK,QAAQliB,QAAU6X,EAAEqK,QAAQG,UAAUjuB,KAAK,qBAAuBd,EAAEJ,OAAO8M,QAC1C,IAA/B6X,EAAEqK,QAAQG,UAAU3sB,SAK5BmiB,EAAEqK,QAAQ5X,MAAMwF,WAAW,QAC3B+H,EAAEoK,WAAY,SALNpK,EAAEqK,QAAQ5X,MAAQvJ,SAO9ByiB,gBAAiB,SAAUxuB,GACvB,GAAI6iB,GAAIvkB,EAAEyM,IACV,KAAKzM,EAAE0U,QAAQqb,SAAU,CACrB,GAAe,cAAXruB,EAAE4f,MAAmC,cAAX5f,EAAE4f,MAAwB5f,EAAE8f,cAAcpf,OAAS,EAC7E,MAEJmiB,GAAEqK,QAAQuB,UAAY5L,EAAEkL,0BAA0B/tB,GAEjD6iB,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,SACpCpC,EAAE0U,QAAQqb,SACVxL,EAAElI,MAAQ3a,EAAE2a,MAAQkI,EAAEmK,aAGtBnK,EAAElI,MAASkI,EAAEqK,QAAQuB,UAAY5L,EAAEqK,QAAQoB,WAAczL,EAAEmK,aAE3DnK,EAAElI,MAAQkI,EAAEqK,QAAQliB,UACpB6X,EAAElI,MAAQkI,EAAEqK,QAAQliB,QAAU,EAAIpM,KAAK6hB,IAAKoC,EAAElI,MAAQkI,EAAEqK,QAAQliB,QAAU,EAAI,KAE9E6X,EAAElI,MAAQrc,EAAEJ,OAAO+M,UACnB4X,EAAElI,MAASrc,EAAEJ,OAAO+M,QAAU,EAAIrM,KAAK6hB,IAAKniB,EAAEJ,OAAO+M,QAAU4X,EAAElI,MAAQ,EAAI,KAEjFkI,EAAEqK,QAAQ5X,MAAM3M,UAAU,4BAA8Bka,EAAElI,MAAQ,OAEtE+T,aAAc,SAAU1uB,GACpB,GAAI6iB,GAAIvkB,EAAEyM,MACLzM,EAAE0U,QAAQqb,WACI,aAAXruB,EAAE4f,MAAkC,aAAX5f,EAAE4f,MAAuB5f,EAAE2uB,eAAejuB,OAAS,IAI/EmiB,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,SACxCmiB,EAAElI,MAAQ/b,KAAKgZ,IAAIhZ,KAAK6c,IAAIoH,EAAElI,MAAOkI,EAAEqK,QAAQliB,SAAU1M,EAAEJ,OAAO+M,SAClE4X,EAAEqK,QAAQ5X,MAAMwF,WAAWxc,EAAEJ,OAAOiL,OAAOR,UAAU,4BAA8Bka,EAAElI,MAAQ,KAC7FkI,EAAEmK,aAAenK,EAAElI,MACnBkI,EAAEoK,WAAY,EACE,IAAZpK,EAAElI,QAAakI,EAAEqK,QAAQlV,MAAQjM,UAEzCkR,aAAc,SAAU3e,EAAG0B,GACvB,GAAI6iB,GAAIvkB,EAAEyM,IACL8X,GAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,SACpCmiB,EAAEvN,MAAMgJ,YACQ,YAAhBhgB,EAAEsV,OAAOgb,IAAkB5uB,EAAEiE,iBACjC4e,EAAEvN,MAAMgJ,WAAY,EACpBuE,EAAEvN,MAAMoY,aAAa/K,EAAe,eAAX3iB,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAChF8C,EAAEvN,MAAMoY,aAAa9K,EAAe,eAAX5iB,EAAE4f,KAAwB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,SAEpF9C,YAAa,SAAUld,GACnB,GAAI6iB,GAAIvkB,EAAEyM,IACV,IAAK8X,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,SACxCpC,EAAEwf,YAAa,EACV+E,EAAEvN,MAAMgJ,WAAcuE,EAAEqK,QAAQlV,OAArC,CAEK6K,EAAEvN,MAAMiJ,UACTsE,EAAEvN,MAAMzR,MAAQgf,EAAEqK,QAAQ5X,MAAM,GAAGuU,YACnChH,EAAEvN,MAAMxR,OAAS+e,EAAEqK,QAAQ5X,MAAM,GAAGoB,aACpCmM,EAAEvN,MAAM+J,OAAS/gB,EAAEykB,aAAaF,EAAEqK,QAAQG,UAAU,GAAI,MAAQ,EAChExK,EAAEvN,MAAMgK,OAAShhB,EAAEykB,aAAaF,EAAEqK,QAAQG,UAAU,GAAI,MAAQ,EAChExK,EAAEqK,QAAQC,WAAatK,EAAEqK,QAAQlV,MAAM,GAAG6R,YAC1ChH,EAAEqK,QAAQE,YAAcvK,EAAEqK,QAAQlV,MAAM,GAAGtB,aAC3CmM,EAAEqK,QAAQG,UAAUvS,WAAW,GAC3Bxc,EAAEoF,MAAKmf,EAAEvN,MAAM+J,QAAUwD,EAAEvN,MAAM+J,QACjC/gB,EAAEoF,MAAKmf,EAAEvN,MAAMgK,QAAUuD,EAAEvN,MAAMgK,QAGzC,IAAIuP,GAAchM,EAAEvN,MAAMzR,MAAQgf,EAAElI,MAChCmU,EAAejM,EAAEvN,MAAMxR,OAAS+e,EAAElI,KAEtC,MAAIkU,EAAchM,EAAEqK,QAAQC,YAAc2B,EAAejM,EAAEqK,QAAQE,aAAnE,CAUA,GARAvK,EAAEvN,MAAMgY,KAAO1uB,KAAK6c,IAAKoH,EAAEqK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEhM,EAAEvN,MAAMkY,MAAQ3K,EAAEvN,MAAMgY,KACxBzK,EAAEvN,MAAMiY,KAAO3uB,KAAK6c,IAAKoH,EAAEqK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxEjM,EAAEvN,MAAMmY,MAAQ5K,EAAEvN,MAAMiY,KAExB1K,EAAEvN,MAAMqY,eAAehL,EAAe,cAAX3iB,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MACjF8C,EAAEvN,MAAMqY,eAAe/K,EAAe,cAAX5iB,EAAE4f,KAAuB5f,EAAE8f,cAAc,GAAGE,MAAQhgB,EAAEggB,OAE5E6C,EAAEvN,MAAMiJ,UAAYsE,EAAEoK,UAAW,CAClC,GAAI3uB,EAAE4D,gBACDtD,KAAKC,MAAMgkB,EAAEvN,MAAMgY,QAAU1uB,KAAKC,MAAMgkB,EAAEvN,MAAM+J,SAAWwD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,GAC3G/jB,KAAKC,MAAMgkB,EAAEvN,MAAMkY,QAAU5uB,KAAKC,MAAMgkB,EAAEvN,MAAM+J,SAAWwD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,EAG5G,YADAE,EAAEvN,MAAMgJ,WAAY,EAGnB,KAAKhgB,EAAE4D,gBACPtD,KAAKC,MAAMgkB,EAAEvN,MAAMiY,QAAU3uB,KAAKC,MAAMgkB,EAAEvN,MAAMgK,SAAWuD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,GAC3GhkB,KAAKC,MAAMgkB,EAAEvN,MAAMmY,QAAU7uB,KAAKC,MAAMgkB,EAAEvN,MAAMgK,SAAWuD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,EAG5G,YADAC,EAAEvN,MAAMgJ,WAAY,GAI5Bte,EAAEiE,iBACFjE,EAAE+d,kBAEF8E,EAAEvN,MAAMiJ,SAAU,EAClBsE,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAEvN,MAAMoY,aAAa/K,EAAIE,EAAEvN,MAAM+J,OAC/EwD,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAEvN,MAAMoY,aAAa9K,EAAIC,EAAEvN,MAAMgK,OAE3EuD,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMgY,OAC3BzK,EAAEvN,MAAMiK,SAAYsD,EAAEvN,MAAMgY,KAAO,EAAI1uB,KAAK6hB,IAAKoC,EAAEvN,MAAMgY,KAAOzK,EAAEvN,MAAMiK,SAAW,EAAI,KAEvFsD,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,OAC3B3K,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,KAAO,EAAI5uB,KAAK6hB,IAAKoC,EAAEvN,MAAMiK,SAAWsD,EAAEvN,MAAMkY,KAAO,EAAI,KAGtF3K,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMiY,OAC3B1K,EAAEvN,MAAMkK,SAAYqD,EAAEvN,MAAMiY,KAAO,EAAI3uB,KAAK6hB,IAAKoC,EAAEvN,MAAMiY,KAAO1K,EAAEvN,MAAMkK,SAAW,EAAI,KAEvFqD,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,OAC3B5K,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,KAAO,EAAI7uB,KAAK6hB,IAAKoC,EAAEvN,MAAMkK,SAAWqD,EAAEvN,MAAMmY,KAAO,EAAI,KAIrF5K,EAAE7O,SAAS4Z,gBAAe/K,EAAE7O,SAAS4Z,cAAgB/K,EAAEvN,MAAMqY,eAAehL,GAC5EE,EAAE7O,SAAS6Z,gBAAehL,EAAE7O,SAAS6Z,cAAgBhL,EAAEvN,MAAMqY,eAAe/K,GAC5EC,EAAE7O,SAAS8Z,WAAUjL,EAAE7O,SAAS8Z,SAAWhnB,KAAKoY,OACrD2D,EAAE7O,SAAS2O,GAAKE,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAE7O,SAAS4Z,gBAAkB9mB,KAAKoY,MAAQ2D,EAAE7O,SAAS8Z,UAAY,EAC5GjL,EAAE7O,SAAS4O,GAAKC,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAE7O,SAAS6Z,gBAAkB/mB,KAAKoY,MAAQ2D,EAAE7O,SAAS8Z,UAAY,EACxGlvB,KAAKuG,IAAI0d,EAAEvN,MAAMqY,eAAehL,EAAIE,EAAE7O,SAAS4Z,eAAiB,IAAG/K,EAAE7O,SAAS2O,EAAI,GAClF/jB,KAAKuG,IAAI0d,EAAEvN,MAAMqY,eAAe/K,EAAIC,EAAE7O,SAAS6Z,eAAiB,IAAGhL,EAAE7O,SAAS4O,EAAI,GACtFC,EAAE7O,SAAS4Z,cAAgB/K,EAAEvN,MAAMqY,eAAehL,EAClDE,EAAE7O,SAAS6Z,cAAgBhL,EAAEvN,MAAMqY,eAAe/K,EAClDC,EAAE7O,SAAS8Z,SAAWhnB,KAAKoY,MAE3B2D,EAAEqK,QAAQG,UAAU1kB,UAAU,eAAiBka,EAAEvN,MAAMiK,SAAW,OAASsD,EAAEvN,MAAMkK,SAAW,YAElGrC,WAAY,SAAU7e,EAAG0B,GACrB,GAAI6iB,GAAIvkB,EAAEyM,IACV,IAAK8X,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,OAAxC,CACA,IAAKmiB,EAAEvN,MAAMgJ,YAAcuE,EAAEvN,MAAMiJ,QAG/B,MAFAsE,GAAEvN,MAAMgJ,WAAY,OACpBuE,EAAEvN,MAAMiJ,SAAU,EAGtBsE,GAAEvN,MAAMgJ,WAAY,EACpBuE,EAAEvN,MAAMiJ,SAAU,CAClB,IAAIwQ,GAAoB,IACpBC,EAAoB,IACpBC,EAAoBpM,EAAE7O,SAAS2O,EAAIoM,EACnCG,EAAerM,EAAEvN,MAAMiK,SAAW0P,EAClCE,EAAoBtM,EAAE7O,SAAS4O,EAAIoM,EACnCI,EAAevM,EAAEvN,MAAMkK,SAAW2P,CAGjB,KAAjBtM,EAAE7O,SAAS2O,IAASoM,EAAoBnwB,KAAKuG,KAAK+pB,EAAerM,EAAEvN,MAAMiK,UAAYsD,EAAE7O,SAAS2O,IAC/E,IAAjBE,EAAE7O,SAAS4O,IAASoM,EAAoBpwB,KAAKuG,KAAKiqB,EAAevM,EAAEvN,MAAMkK,UAAYqD,EAAE7O,SAAS4O,GACpG,IAAIzB,GAAmBviB,KAAKgZ,IAAImX,EAAmBC,EAEnDnM,GAAEvN,MAAMiK,SAAW2P,EACnBrM,EAAEvN,MAAMkK,SAAW4P,CAGnB,IAAIP,GAAchM,EAAEvN,MAAMzR,MAAQgf,EAAElI,MAChCmU,EAAejM,EAAEvN,MAAMxR,OAAS+e,EAAElI,KACtCkI,GAAEvN,MAAMgY,KAAO1uB,KAAK6c,IAAKoH,EAAEqK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEhM,EAAEvN,MAAMkY,MAAQ3K,EAAEvN,MAAMgY,KACxBzK,EAAEvN,MAAMiY,KAAO3uB,KAAK6c,IAAKoH,EAAEqK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxEjM,EAAEvN,MAAMmY,MAAQ5K,EAAEvN,MAAMiY,KACxB1K,EAAEvN,MAAMiK,SAAW3gB,KAAKgZ,IAAIhZ,KAAK6c,IAAIoH,EAAEvN,MAAMiK,SAAUsD,EAAEvN,MAAMkY,MAAO3K,EAAEvN,MAAMgY,MAC9EzK,EAAEvN,MAAMkK,SAAW5gB,KAAKgZ,IAAIhZ,KAAK6c,IAAIoH,EAAEvN,MAAMkK,SAAUqD,EAAEvN,MAAMmY,MAAO5K,EAAEvN,MAAMiY,MAE9E1K,EAAEqK,QAAQG,UAAUvS,WAAWqG,GAAkBxY,UAAU,eAAiBka,EAAEvN,MAAMiK,SAAW,OAASsD,EAAEvN,MAAMkK,SAAW,WAE/HkC,gBAAiB,SAAUpjB,GACvB,GAAIukB,GAAIvkB,EAAEyM,IACN8X,GAAEqK,QAAQlV,OAAS1Z,EAAEyb,gBAAkBzb,EAAEa,cACzC0jB,EAAEqK,QAAQ5X,MAAM3M,UAAU,+BAC1Bka,EAAEqK,QAAQG,UAAU1kB,UAAU,sBAC9Bka,EAAEqK,QAAQlV,MAAQ6K,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQG,UAAYthB,OAC1D8W,EAAElI,MAAQkI,EAAEmK,aAAe,IAInCqC,WAAY,SAAU/wB,EAAG0B,GACrB,GAAI6iB,GAAIvkB,EAAEyM,IAMV,IALK8X,EAAEqK,QAAQlV,QACX6K,EAAEqK,QAAQlV,MAAQ1Z,EAAE6f,aAAe3f,EAAEF,EAAE6f,cAAgB7f,EAAEW,OAAOC,GAAGZ,EAAEa,aACrE0jB,EAAEqK,QAAQ5X,MAAQuN,EAAEqK,QAAQlV,MAAMzE,KAAK,oBACvCsP,EAAEqK,QAAQG,UAAYxK,EAAEqK,QAAQ5X,MAAMiZ,OAAO,IAAMjwB,EAAEJ,OAAO8S,qBAE3D6R,EAAEqK,QAAQ5X,OAAoC,IAA3BuN,EAAEqK,QAAQ5X,MAAM5U,OAAxC,CAEA,GAAI4uB,GAAQC,EAAQC,EAASC,EAASC,EAAOC,EAAOpI,EAAYD,EAAYsI,EAAYC,EAAahB,EAAaC,EAAcgB,EAAeC,EAAeC,EAAeC,EAAe9C,EAAYC,CAElK,oBAA3BvK,GAAEvN,MAAMoY,aAAa/K,GAAqB3iB,GACjDsvB,EAAoB,aAAXtvB,EAAE4f,KAAsB5f,EAAE2uB,eAAe,GAAG5O,MAAQ/f,EAAE+f,MAC/DwP,EAAoB,aAAXvvB,EAAE4f,KAAsB5f,EAAE2uB,eAAe,GAAG3O,MAAQhgB,EAAEggB,QAG/DsP,EAASzM,EAAEvN,MAAMoY,aAAa/K,EAC9B4M,EAAS1M,EAAEvN,MAAMoY,aAAa9K,GAG9BC,EAAElI,OAAqB,IAAZkI,EAAElI,OAEbkI,EAAElI,MAAQkI,EAAEmK,aAAe,EAC3BnK,EAAEqK,QAAQG,UAAUvS,WAAW,KAAKnS,UAAU,sBAC9Cka,EAAEqK,QAAQ5X,MAAMwF,WAAW,KAAKnS,UAAU,+BAC1Cka,EAAEqK,QAAQlV,MAAQjM,SAIlB8W,EAAElI,MAAQkI,EAAEmK,aAAenK,EAAEqK,QAAQG,UAAUjuB,KAAK,qBAAuBd,EAAEJ,OAAO8M,QAChFhL,GACAmtB,EAAatK,EAAEqK,QAAQlV,MAAM,GAAG6R,YAChCuD,EAAcvK,EAAEqK,QAAQlV,MAAM,GAAGtB,aACjC8Y,EAAU3M,EAAEqK,QAAQlV,MAAMvU,SAAST,KACnCysB,EAAU5M,EAAEqK,QAAQlV,MAAMvU,SAASP,IACnCwsB,EAAQF,EAAUrC,EAAW,EAAImC,EACjCK,EAAQF,EAAUrC,EAAY,EAAImC,EAElCK,EAAa/M,EAAEqK,QAAQ5X,MAAM,GAAGuU,YAChCgG,EAAchN,EAAEqK,QAAQ5X,MAAM,GAAGoB,aACjCmY,EAAce,EAAa/M,EAAElI,MAC7BmU,EAAee,EAAchN,EAAElI,MAE/BmV,EAAgBlxB,KAAK6c,IAAK0R,EAAa,EAAI0B,EAAc,EAAI,GAC7DkB,EAAgBnxB,KAAK6c,IAAK2R,EAAc,EAAI0B,EAAe,EAAI,GAC/DkB,GAAiBF,EACjBG,GAAiBF,EAEjBxI,EAAamI,EAAQ7M,EAAElI,MACvB2M,EAAaqI,EAAQ9M,EAAElI,MAEnB4M,EAAauI,IACbvI,EAAcuI,GAEdvI,EAAayI,IACbzI,EAAayI,GAGb1I,EAAayI,IACbzI,EAAcyI,GAEdzI,EAAa2I,IACb3I,EAAa2I,KAIjB1I,EAAa,EACbD,EAAa,GAEjBzE,EAAEqK,QAAQG,UAAUvS,WAAW,KAAKnS,UAAU,eAAiB4e,EAAa,OAASD,EAAa,SAClGzE,EAAEqK,QAAQ5X,MAAMwF,WAAW,KAAKnS,UAAU,4BAA8Bka,EAAElI,MAAQ,QAI1FiD,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAE9B,IAAIpe,EAAEJ,OAAO6M,KAAM,CACf,GACIqS,IADS9e,EAAEW,SAC+B,eAAxBX,EAAEke,YAAYN,QAA0B5d,EAAE0U,QAAQoK,kBAAmB9e,EAAEJ,OAAOqR,oBAAoB8N,SAAS,EAAMC,SAAS,GAE5Ihf,GAAE0U,QAAQqb,UACV/vB,EAAEW,OAAO2d,GAAQ,eAAgBte,EAAEyM,KAAKqjB,eAAgBhR,GACxD9e,EAAEW,OAAO2d,GAAQ,gBAAiBte,EAAEyM,KAAKyjB,gBAAiBpR,GAC1D9e,EAAEW,OAAO2d,GAAQ,aAActe,EAAEyM,KAAK2jB,aAActR,IAEvB,eAAxB9e,EAAEke,YAAYN,QACnB5d,EAAEW,OAAO2d,GAAQte,EAAEke,YAAYN,MAAO5d,EAAEyM,KAAKqjB,eAAgBhR,GAC7D9e,EAAEW,OAAO2d,GAAQte,EAAEke,YAAYL,KAAM7d,EAAEyM,KAAKyjB,gBAAiBpR,GAC7D9e,EAAEW,OAAO2d,GAAQte,EAAEke,YAAYJ,IAAK9d,EAAEyM,KAAK2jB,aAActR,IAI7D9e,EAAEse,GAAQ,aAActe,EAAEyM,KAAKkS,cAC/B3e,EAAEW,OAAOZ,KAAK,SAAUmC,EAAOwX,GACvBxZ,EAAEwZ,GAAOzE,KAAK,IAAMjV,EAAEJ,OAAO8S,oBAAoBtQ,OAAS,GAC1DlC,EAAEwZ,GAAO4E,GAAQte,EAAEke,YAAYL,KAAM7d,EAAEyM,KAAKmS,eAGpD5e,EAAEse,GAAQ,WAAYte,EAAEyM,KAAKoS,YAG7B7e,EAAEse,GAAQ,gBAAiBte,EAAEyM,KAAK2W,iBAC9BpjB,EAAEJ,OAAOgN,YACT5M,EAAEmrB,GAAG,YAAanrB,EAAEyM,KAAKskB,cAIrC5D,KAAM,WACFntB,EAAEyM,KAAK6S,gBAEX+N,QAAS,WACLrtB,EAAEyM,KAAK6S,cAAa,KAO5Btf,EAAE4xB,WACF,KAAK,GAAIC,KAAU7xB,GAAE8xB,QAAS,CAC1B,GAAI5nB,GAAIlK,EAAE8xB,QAAQD,GAAQ7xB,EAAGA,EAAEJ,OAAOiyB,GAClC3nB,IAAGlK,EAAE4xB,SAASvuB,KAAK6G,GAkU3B,MA/TAlK,GAAE+xB,YAAc,SAAU/rB,GACtB,IAAK,GAAIP,GAAI,EAAGA,EAAIzF,EAAE4xB,SAASxvB,OAAQqD,IAC/BO,IAAahG,GAAE4xB,SAASnsB,IACxBzF,EAAE4xB,SAASnsB,GAAGO,GAAWgsB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAmBvGhyB,EAAEiyB,yBAGFjyB,EAAEoB,KAAO,SAAU4E,GAEXhG,EAAEJ,OAAOoG,IACThG,EAAEJ,OAAOoG,GAAWgsB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAE1F,IAAIvsB,EAEJ,IAAIzF,EAAEiyB,sBAAsBjsB,GACxB,IAAKP,EAAI,EAAGA,EAAIzF,EAAEiyB,sBAAsBjsB,GAAW5D,OAAQqD,IACvDzF,EAAEiyB,sBAAsBjsB,GAAWP,GAAGusB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAI5GhyB,GAAE+xB,aAAa/xB,EAAE+xB,YAAY/rB,EAAWgsB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAElHhyB,EAAEmrB,GAAK,SAAUnlB,EAAWksB,GAIxB,MAHAlsB,GAAYsE,EAAmBtE,GAC1BhG,EAAEiyB,sBAAsBjsB,KAAYhG,EAAEiyB,sBAAsBjsB,OACjEhG,EAAEiyB,sBAAsBjsB,GAAW3C,KAAK6uB,GACjClyB,GAEXA,EAAEqrB,IAAM,SAAUrlB,EAAWksB,GACzB,GAAIzsB,EAEJ,IADAO,EAAYsE,EAAmBtE,GACR,mBAAZksB,GAGP,MADAlyB,GAAEiyB,sBAAsBjsB,MACjBhG,CAEX,IAAKA,EAAEiyB,sBAAsBjsB,IAA4D,IAA9ChG,EAAEiyB,sBAAsBjsB,GAAW5D,OAA9E,CACA,IAAKqD,EAAI,EAAGA,EAAIzF,EAAEiyB,sBAAsBjsB,GAAW5D,OAAQqD,IACpDzF,EAAEiyB,sBAAsBjsB,GAAWP,KAAOysB,GAASlyB,EAAEiyB,sBAAsBjsB,GAAWmsB,OAAO1sB,EAAG,EAEvG,OAAOzF,KAEXA,EAAEoyB,KAAO,SAAUpsB,EAAWksB,GAC1BlsB,EAAYsE,EAAmBtE,EAC/B,IAAIqsB,GAAW,WACXH,EAAQF,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,IAC1EhyB,EAAEqrB,IAAIrlB,EAAWqsB,GAGrB,OADAryB,GAAEmrB,GAAGnlB,EAAWqsB,GACTryB,GAIXA,EAAE4S,MACE0f,cAAe,SAAUC,GAErB,MADAA,GAAIzxB,KAAK,WAAY,KACdyxB,GAEXC,QAAS,SAAUD,EAAKE,GAEpB,MADAF,GAAIzxB,KAAK,OAAQ2xB,GACVF,GAGXG,SAAU,SAAUH,EAAKI,GAErB,MADAJ,GAAIzxB,KAAK,aAAc6xB,GAChBJ,GAGX7V,QAAS,SAAU6V,GAEf,MADAA,GAAIzxB,KAAK,iBAAiB,GACnByxB,GAGX5V,OAAQ,SAAU4V,GAEd,MADAA,GAAIzxB,KAAK,iBAAiB,GACnByxB,GAGXpT,WAAY,SAAUtW,GACI,KAAlBA,EAAMpF,UACNvD,EAAE2I,EAAMhH,QAAQC,GAAG9B,EAAEJ,OAAO+P,aAC5B3P,EAAEkf,YAAYrW,GACV7I,EAAEqB,MACFrB,EAAE4S,KAAKggB,OAAO5yB,EAAEJ,OAAOoT,kBAGvBhT,EAAE4S,KAAKggB,OAAO5yB,EAAEJ,OAAOkT,mBAGtB5S,EAAE2I,EAAMhH,QAAQC,GAAG9B,EAAEJ,OAAOgQ,cACjC5P,EAAEof,YAAYvW,GACV7I,EAAEsH,YACFtH,EAAE4S,KAAKggB,OAAO5yB,EAAEJ,OAAOmT,mBAGvB/S,EAAE4S,KAAKggB,OAAO5yB,EAAEJ,OAAOiT,mBAG3B3S,EAAE2I,EAAMhH,QAAQC,GAAG,IAAM9B,EAAEJ,OAAO+R,cAClCzR,EAAE2I,EAAMhH,QAAQ,GAAGgxB,UAI3BC,WAAY5yB,EAAE,gBAAkBF,EAAEJ,OAAO4S,kBAAoB,sDAE7DogB,OAAQ,SAAUG,GACd,GAAIC,GAAehzB,EAAE4S,KAAKkgB,UACE,KAAxBE,EAAa5wB,SACjB4wB,EAAavW,KAAK,IAClBuW,EAAavW,KAAKsW,KAEtB5F,KAAM,WAEEntB,EAAEJ,OAAO+P,YAAc3P,EAAE2P,YAAc3P,EAAE2P,WAAWvN,OAAS,IAC7DpC,EAAE4S,KAAK0f,cAActyB,EAAE2P,YACvB3P,EAAE4S,KAAK4f,QAAQxyB,EAAE2P,WAAY,UAC7B3P,EAAE4S,KAAK8f,SAAS1yB,EAAE2P,WAAY3P,EAAEJ,OAAOkT,mBAEvC9S,EAAEJ,OAAOgQ,YAAc5P,EAAE4P,YAAc5P,EAAE4P,WAAWxN,OAAS,IAC7DpC,EAAE4S,KAAK0f,cAActyB,EAAE4P,YACvB5P,EAAE4S,KAAK4f,QAAQxyB,EAAE4P,WAAY,UAC7B5P,EAAE4S,KAAK8f,SAAS1yB,EAAE4P,WAAY5P,EAAEJ,OAAOiT,mBAG3C3S,EAAEF,EAAEG,WAAWgmB,OAAOnmB,EAAE4S,KAAKkgB,aAEjC/V,eAAgB,WACR/c,EAAEJ,OAAOoP,YAAchP,EAAEJ,OAAOsP,qBAAuBlP,EAAEmc,SAAWnc,EAAEmc,QAAQ/Z,QAC9EpC,EAAEmc,QAAQpc,KAAK,WACX,GAAIkzB,GAAS/yB,EAAEJ,KACfE,GAAE4S,KAAK0f,cAAcW,GACrBjzB,EAAE4S,KAAK4f,QAAQS,EAAQ,UACvBjzB,EAAE4S,KAAK8f,SAASO,EAAQjzB,EAAEJ,OAAOqT,wBAAwB8F,QAAQ,YAAaka,EAAO/wB,QAAU,OAI3GmrB,QAAS,WACDrtB,EAAE4S,KAAKkgB,YAAc9yB,EAAE4S,KAAKkgB,WAAW1wB,OAAS,GAAGpC,EAAE4S,KAAKkgB,WAAW9M,WAQjFhmB,EAAEmtB,KAAO,WACDntB,EAAEJ,OAAOqB,MAAMjB,EAAE+lB,aACrB/lB,EAAEqY,sBACFrY,EAAEyY,mBACFzY,EAAE4c,mBACE5c,EAAEJ,OAAOiN,WAAa7M,EAAE6M,YACxB7M,EAAE6M,UAAUuQ,MACRpd,EAAEJ,OAAOmN,oBACT/M,EAAE6M,UAAUqe,mBAGI,UAApBlrB,EAAEJ,OAAO6L,QAAsBzL,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,UAC7CzL,EAAEJ,OAAOqB,MAAMjB,EAAE4H,iBACtB5H,EAAEmkB,QAAQnkB,EAAEJ,OAAO6L,QAAQ+Y,gBAE3BxkB,EAAEJ,OAAOqB,KACTjB,EAAEwd,QAAQxd,EAAEJ,OAAOgL,aAAe5K,EAAEyQ,aAAc,EAAGzQ,EAAEJ,OAAOsT,qBAG9DlT,EAAEwd,QAAQxd,EAAEJ,OAAOgL,aAAc,EAAG5K,EAAEJ,OAAOsT,oBACf,IAA1BlT,EAAEJ,OAAOgL,eACL5K,EAAEwM,UAAYxM,EAAEJ,OAAO4M,UAAUxM,EAAEwM,SAASgY,eAC5CxkB,EAAEqI,MAAQrI,EAAEJ,OAAOwI,cACnBpI,EAAEqI,KAAKC,OACPtI,EAAEqI,KAAKghB,oBAAqB,KAIxCrpB,EAAEsf,eACEtf,EAAEJ,OAAO+C,UAAY3C,EAAE0U,QAAQ/R,UAC/B3C,EAAE2lB,gBAEF3lB,EAAEJ,OAAO0Q,gBAAkBtQ,EAAEJ,OAAOwI,aACpCpI,EAAEsQ,gBAEFtQ,EAAEJ,OAAO6M,MAAQzM,EAAEyM,MACnBzM,EAAEyM,KAAK0gB,OAEPntB,EAAEJ,OAAOY,UACTR,EAAE2X,gBAEF3X,EAAEJ,OAAOqN,iBACLjN,EAAEquB,uBAAuBruB,EAAEquB,wBAE/BruB,EAAEJ,OAAOsN,mBACLlN,EAAEwuB,yBAAyBxuB,EAAEwuB,0BAGjCxuB,EAAEJ,OAAOszB,sBACTlzB,EAAEJ,OAAO2N,aAAevN,EAAEJ,OAAOszB,qBAEjClzB,EAAEJ,OAAO0N,SACLtN,EAAEsN,SAAStN,EAAEsN,QAAQ6f,OAEzBntB,EAAEJ,OAAOwN,SACLpN,EAAEoN,SAASpN,EAAEoN,QAAQ+f,OAEzBntB,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAKua,OACpCntB,EAAEoB,KAAK,SAAUpB,IAIrBA,EAAEmzB,cAAgB,WAEdnzB,EAAEG,UAAU8a,YAAYjb,EAAE2T,WAAW6B,KAAK,MAAM8Q,WAAW,SAG3DtmB,EAAE8U,QAAQwR,WAAW,SAGjBtmB,EAAEW,QAAUX,EAAEW,OAAOyB,QACrBpC,EAAEW,OACGsa,aACCjb,EAAEJ,OAAOwR,kBACTpR,EAAEJ,OAAO4E,iBACTxE,EAAEJ,OAAO0R,eACTtR,EAAEJ,OAAO4R,gBACTgE,KAAK,MACN8Q,WAAW,SACXA,WAAW,sBACXA,WAAW,mBAIhBtmB,EAAEgV,qBAAuBhV,EAAEgV,oBAAoB5S,QAC/CpC,EAAEgV,oBAAoBiG,YAAYjb,EAAEJ,OAAOoS,uBAE3ChS,EAAEmc,SAAWnc,EAAEmc,QAAQ/Z,QACvBpC,EAAEmc,QAAQlB,YAAYjb,EAAEJ,OAAOgS,mBAI/B5R,EAAEJ,OAAOgQ,YAAY1P,EAAEF,EAAEJ,OAAOgQ,YAAYqL,YAAYjb,EAAEJ,OAAOiS,qBACjE7R,EAAEJ,OAAO+P,YAAYzP,EAAEF,EAAEJ,OAAO+P,YAAYsL,YAAYjb,EAAEJ,OAAOiS,qBAGjE7R,EAAEJ,OAAOiN,WAAa7M,EAAE6M,YACpB7M,EAAE6M,UAAU0d,OAASvqB,EAAE6M,UAAU0d,MAAMnoB,QAAQpC,EAAE6M,UAAU0d,MAAMjE,WAAW,SAC5EtmB,EAAE6M,UAAUie,MAAQ9qB,EAAE6M,UAAUie,KAAK1oB,QAAQpC,EAAE6M,UAAUie,KAAKxE,WAAW,WAKrFtmB,EAAEqtB,QAAU,SAAU+F,EAAgBD,GAElCnzB,EAAEuf,eAEFvf,EAAEuB,eAEEvB,EAAEJ,OAAOiN,WAAa7M,EAAE6M,WACpB7M,EAAEJ,OAAOmN,oBACT/M,EAAE6M,UAAUue,mBAIhBprB,EAAEJ,OAAOqB,MACTjB,EAAEuU,cAGF4e,GACAnzB,EAAEmzB,gBAGNnzB,EAAE6lB,sBAGE7lB,EAAEJ,OAAO6M,MAAQzM,EAAEyM,MACnBzM,EAAEyM,KAAK4gB,UAGPrtB,EAAEJ,OAAOqN,iBACLjN,EAAEouB,wBAAwBpuB,EAAEouB,yBAEhCpuB,EAAEJ,OAAOsN,mBACLlN,EAAEuuB,0BAA0BvuB,EAAEuuB,2BAGlCvuB,EAAEJ,OAAOgT,MAAQ5S,EAAE4S,MAAM5S,EAAE4S,KAAKya,UAEhCrtB,EAAEJ,OAAO0N,UAAYtN,EAAEJ,OAAO2N,cAC9B/K,OAAO6wB,oBAAoB,WAAYrzB,EAAEsN,QAAQugB,oBAEjD7tB,EAAEJ,OAAOwN,SAAWpN,EAAEoN,SACtBpN,EAAEoN,QAAQigB,UAGdrtB,EAAEoB,KAAK,aAEHgyB,KAAmB,IAAOpzB,EAAI,OAGtCA,EAAEmtB,OAKKntB,GAOXC,GAAOqzB,WACH5K,SAAU,WACN,GAAI6K,GAAK/wB,OAAOub,UAAUuQ,UAAUjqB,aACpC,OAAQkvB,GAAGppB,QAAQ,WAAa,GAAKopB,EAAGppB,QAAQ,UAAY,GAAKopB,EAAGppB,QAAQ,WAAa,KAE7Fwe,YAAa,+CAA+C6K,KAAKhxB,OAAOub,UAAUuQ,WAClF3B,QAAS,SAAU8G,GACf,MAAgD,mBAAzCC,OAAOJ,UAAU9N,SAASmO,MAAMF,IAK3ChV,SACIC,GAAIlc,OAAOub,UAAUC,gBAAkBxb,OAAOub,UAAUE,iBACxD+D,QAAUxf,OAAOub,UAAUE,kBAAoBzb,OAAOub,UAAU6V,iBAAmB,GAAOpxB,OAAOub,UAAUC,gBAAkBxb,OAAOub,UAAU8V,eAAiB,EAC/JnQ,OAAQ,WAEJ,GAAIoQ,GAAM5vB,SAASiC,cAAc,MAIjC,OAFA2tB,GAAIC,UAAY,wCAEgC,IAAzCD,EAAIE,qBAAqB,KAAK5xB,WAM7CkT,OAAQ,WACJ,GAAIie,GAAK/wB,OAAOub,UAAUuQ,UACtB/Y,EAAUge,EAAGU,MAAM,+BACnBC,EAAOX,EAAGU,MAAM,wBAChBE,EAAOZ,EAAGU,MAAM,2BAChBG,GAAUF,GAAQX,EAAGU,MAAM,6BAC/B,QACIhV,IAAKiV,GAAQE,GAAUD,EACvB5e,QAASA,MAMjBb,SACIG,MAASrS,OAAO6xB,WAAaA,UAAUxf,SAAU,GAAS,WACtD,SAAW,gBAAkBrS,SAAWA,OAAO8xB,eAAiBpwB,mBAAoBowB,mBAGxF1f,aAAgBpS,OAAO6xB,WAAaA,UAAUE,mBAAoB,GAAS,WACvE,GAAIT,GAAM5vB,SAASiC,cAAc,OAAOiQ,KACxC,OAAQ,qBAAuB0d,IAAO,kBAAoBA,IAAO,gBAAkBA,IAAO,iBAAmBA,IAAO,eAAiBA,MAGzInf,QAAS,WAGL,IAAK,GAFDmf,GAAM5vB,SAASiC,cAAc,OAAOiQ,MACpCoe,EAAS,yKAA2KvP,MAAM,KACrLxf,EAAI,EAAGA,EAAI+uB,EAAOpyB,OAAQqD,IAC/B,GAAI+uB,EAAO/uB,IAAMquB,GAAK,OAAO,KAIrCnxB,SAAU,WACN,MAAQ,oBAAsBH,SAAU,0BAA4BA,WAGxEsc,gBAAiB,WACb,GAAI2V,IAAkB,CACtB,KACI,GAAIC,GAAOhB,OAAOiB,kBAAmB,WACjCC,IAAK,WACDH,GAAkB,IAG1BjyB,QAAOorB,iBAAiB,sBAAuB,KAAM8G,GACvD,MAAOhzB,IACT,MAAO+yB,MAGX1E,SAAU,WACN,MAAO,kBAAoBvtB,YAMnCsvB,WAkqBJ,KAAK,GA3pBDxe,IAAO,WACP,GAAIA,GAAO,SAAUmgB,GACjB,GAAIoB,GAAQ/0B,KAAM2F,EAAI,CAEtB,KAAKA,EAAI,EAAGA,EAAIguB,EAAIrxB,OAAQqD,IACxBovB,EAAMpvB,GAAKguB,EAAIhuB,EAInB,OAFAovB,GAAMzyB,OAASqxB,EAAIrxB,OAEZtC,MAEPI,EAAI,SAAUyB,EAAUmzB,GACxB,GAAIrB,MAAUhuB,EAAI,CAClB,IAAI9D,IAAamzB,GACTnzB,YAAoB2R,GACpB,MAAO3R,EAGf,IAAIA,EAEA,GAAwB,gBAAbA,GAAuB,CAC9B,GAAIozB,GAAKC,EAAYvY,EAAO9a,EAASszB,MACrC,IAAIxY,EAAKtS,QAAQ,MAAQ,GAAKsS,EAAKtS,QAAQ,MAAQ,EAAG,CAClD,GAAI+qB,GAAW,KAQf,KAP4B,IAAxBzY,EAAKtS,QAAQ,SAAc+qB,EAAW,MACd,IAAxBzY,EAAKtS,QAAQ,SAAc+qB,EAAW,SACd,IAAxBzY,EAAKtS,QAAQ,QAAwC,IAAxBsS,EAAKtS,QAAQ,SAAc+qB,EAAW,MACxC,IAA3BzY,EAAKtS,QAAQ,YAAiB+qB,EAAW,SACb,IAA5BzY,EAAKtS,QAAQ,aAAkB+qB,EAAW,UAC9CF,EAAa9wB,SAASiC,cAAc+uB,GACpCF,EAAWjB,UAAYpyB,EAClB8D,EAAI,EAAGA,EAAIuvB,EAAWG,WAAW/yB,OAAQqD,IAC1CguB,EAAIpwB,KAAK2xB,EAAWG,WAAW1vB,QAYnC,KAFIsvB,EANCD,GAA2B,MAAhBnzB,EAAS,IAAeA,EAASsyB,MAAM,aAM5Ca,GAAW5wB,UAAUkxB,iBAAiBzzB,IAJtCuC,SAASmxB,eAAe1zB,EAASsjB,MAAM,KAAK,KAMlDxf,EAAI,EAAGA,EAAIsvB,EAAI3yB,OAAQqD,IACpBsvB,EAAItvB,IAAIguB,EAAIpwB,KAAK0xB,EAAItvB,QAKhC,IAAI9D,EAASK,UAAYL,IAAaa,QAAUb,IAAauC,SAC9DuvB,EAAIpwB,KAAK1B,OAGR,IAAIA,EAASS,OAAS,GAAKT,EAAS,GAAGK,SACxC,IAAKyD,EAAI,EAAGA,EAAI9D,EAASS,OAAQqD,IAC7BguB,EAAIpwB,KAAK1B,EAAS8D,GAI9B,OAAO,IAAI6N,GAAKmgB,GAulBpB,OArlBAngB,GAAKggB,WAEDpe,SAAU,SAAUogB,GAChB,GAAyB,mBAAdA,GACP,MAAOx1B,KAGX,KAAK,GADDy1B,GAAUD,EAAUrQ,MAAM,KACrBxf,EAAI,EAAGA,EAAI8vB,EAAQnzB,OAAQqD,IAChC,IAAK,GAAImV,GAAI,EAAGA,EAAI9a,KAAKsC,OAAQwY,IAC7B9a,KAAK8a,GAAG4a,UAAU/L,IAAI8L,EAAQ9vB,GAGtC,OAAO3F,OAEXmb,YAAa,SAAUqa,GAEnB,IAAK,GADDC,GAAUD,EAAUrQ,MAAM,KACrBxf,EAAI,EAAGA,EAAI8vB,EAAQnzB,OAAQqD,IAChC,IAAK,GAAImV,GAAI,EAAGA,EAAI9a,KAAKsC,OAAQwY,IAC7B9a,KAAK8a,GAAG4a,UAAUxP,OAAOuP,EAAQ9vB,GAGzC,OAAO3F,OAEX8b,SAAU,SAAU0Z,GAChB,QAAKx1B,KAAK,IACEA,KAAK,GAAG01B,UAAUC,SAASH,IAE3C/S,YAAa,SAAU+S,GAEnB,IAAK,GADDC,GAAUD,EAAUrQ,MAAM,KACrBxf,EAAI,EAAGA,EAAI8vB,EAAQnzB,OAAQqD,IAChC,IAAK,GAAImV,GAAI,EAAGA,EAAI9a,KAAKsC,OAAQwY,IAC7B9a,KAAK8a,GAAG4a,UAAUE,OAAOH,EAAQ9vB,GAGzC,OAAO3F,OAEXgB,KAAM,SAAU60B,EAAOjI,GACnB,GAAyB,IAArBsE,UAAU5vB,QAAiC,gBAAVuzB,GAEjC,MAAI71B,MAAK,GAAWA,KAAK,GAAG0X,aAAame,GACpC,MAIL,KAAK,GAAIlwB,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B,GAAyB,IAArBusB,UAAU5vB,OAEVtC,KAAK2F,GAAGW,aAAauvB,EAAOjI,OAI5B,KAAK,GAAIkI,KAAYD,GACjB71B,KAAK2F,GAAGmwB,GAAYD,EAAMC,GAC1B91B,KAAK2F,GAAGW,aAAawvB,EAAUD,EAAMC,GAIjD,OAAO91B,OAGfwmB,WAAY,SAAUxlB,GAClB,IAAK,GAAI2E,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B3F,KAAK2F,GAAGowB,gBAAgB/0B,EAE5B,OAAOhB,OAEX4G,KAAM,SAAU+mB,EAAKC,GACjB,GAAqB,mBAAVA,GAAX,CAYI,IAAK,GAAIjoB,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAI7D,GAAK9B,KAAK2F,EACT7D,GAAGk0B,yBAAwBl0B,EAAGk0B,2BACnCl0B,EAAGk0B,uBAAuBrI,GAAOC,EAErC,MAAO5tB,MAfP,GAAIA,KAAK,GAAI,CACT,GAAIi2B,GAAUj2B,KAAK,GAAG0X,aAAa,QAAUiW,EAC7C,OAAIsI,GAAgBA,EACXj2B,KAAK,GAAGg2B,wBAA2BrI,IAAO3tB,MAAK,GAAGg2B,uBAAgCh2B,KAAK,GAAGg2B,uBAAuBrI,GACrH,SAejBpjB,UAAY,SAAUA,GAClB,IAAK,GAAI5E,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAIuwB,GAAUl2B,KAAK2F,GAAG2Q,KACtB4f,GAAQhR,gBAAkBgR,EAAQ3Q,YAAc2Q,EAAQ1Q,YAAc0Q,EAAQ7Q,aAAe6Q,EAAQ5Q,WAAa4Q,EAAQ3rB,UAAYA,EAE1I,MAAOvK,OAEX0c,WAAY,SAAUyH,GACM,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAIxe,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAIuwB,GAAUl2B,KAAK2F,GAAG2Q,KACtB4f,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBrS,EAEhM,MAAOnkB,OAGXqrB,GAAI,SAAUnlB,EAAWuwB,EAAgBC,EAAUxX,GAC/C,QAASyX,GAAgB/0B,GACrB,GAAIG,GAASH,EAAEG,MACf,IAAI3B,EAAE2B,GAAQC,GAAGy0B,GAAiBC,EAASE,KAAK70B,EAAQH,OAGpD,KAAK,GADDK,GAAU7B,EAAE2B,GAAQE,UACf40B,EAAI,EAAGA,EAAI50B,EAAQK,OAAQu0B,IAC5Bz2B,EAAE6B,EAAQ40B,IAAI70B,GAAGy0B,IAAiBC,EAASE,KAAK30B,EAAQ40B,GAAIj1B,GAI5E,GACI+D,GAAGmV,EADHgc,EAAS5wB,EAAUif,MAAM,IAE7B,KAAKxf,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB,GAA8B,kBAAnB8wB,IAAiCA,KAAmB,EAM3D,IAJ8B,kBAAnBA,KACPC,EAAWxE,UAAU,GACrBhT,EAAUgT,UAAU,KAAM,GAEzBpX,EAAI,EAAGA,EAAIgc,EAAOx0B,OAAQwY,IAC3B9a,KAAK2F,GAAGmoB,iBAAiBgJ,EAAOhc,GAAI4b,EAAUxX,OAKlD,KAAKpE,EAAI,EAAGA,EAAIgc,EAAOx0B,OAAQwY,IACtB9a,KAAK2F,GAAGoxB,oBAAmB/2B,KAAK2F,GAAGoxB,sBACxC/2B,KAAK2F,GAAGoxB,kBAAkBxzB,MAAMmzB,SAAUA,EAAUM,aAAcL,IAClE32B,KAAK2F,GAAGmoB,iBAAiBgJ,EAAOhc,GAAI6b,EAAiBzX,EAKjE,OAAOlf,OAEXurB,IAAK,SAAUrlB,EAAWuwB,EAAgBC,EAAUxX,GAEhD,IAAK,GADD4X,GAAS5wB,EAAUif,MAAM,KACpBxf,EAAI,EAAGA,EAAImxB,EAAOx0B,OAAQqD,IAC/B,IAAK,GAAImV,GAAI,EAAGA,EAAI9a,KAAKsC,OAAQwY,IAC7B,GAA8B,kBAAnB2b,IAAiCA,KAAmB,EAE7B,kBAAnBA,KACPC,EAAWxE,UAAU,GACrBhT,EAAUgT,UAAU,KAAM,GAE9BlyB,KAAK8a,GAAGyY,oBAAoBuD,EAAOnxB,GAAI+wB,EAAUxX,OAIjD,IAAIlf,KAAK8a,GAAGic,kBACR,IAAK,GAAIF,GAAI,EAAGA,EAAI72B,KAAK8a,GAAGic,kBAAkBz0B,OAAQu0B,IAC9C72B,KAAK8a,GAAGic,kBAAkBF,GAAGH,WAAaA,GAC1C12B,KAAK8a,GAAGyY,oBAAoBuD,EAAOnxB,GAAI3F,KAAK8a,GAAGic,kBAAkBF,GAAGG,aAAc9X,EAO1G,OAAOlf,OAEXsyB,KAAM,SAAUpsB,EAAWuwB,EAAgBC,EAAUxX,GAOjD,QAAS+X,GAAMr1B,GACX80B,EAAS90B,GACTs1B,EAAI3L,IAAIrlB,EAAWuwB,EAAgBQ,EAAO/X,GAR9C,GAAIgY,GAAMl3B,IACoB,mBAAnBy2B,KACPA,GAAiB,EACjBC,EAAWxE,UAAU,GACrBhT,EAAUgT,UAAU,IAMxBgF,EAAI7L,GAAGnlB,EAAWuwB,EAAgBQ,EAAO/X,IAE7CiD,QAAS,SAAUjc,EAAWixB,GAC1B,IAAK,GAAIxxB,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAIyxB,EACJ,KACIA,EAAM,GAAI10B,QAAO20B,YAAYnxB,GAAYqD,OAAQ4tB,EAAWG,SAAS,EAAMC,YAAY,IAE3F,MAAO31B,GACHw1B,EAAMhzB,SAASozB,YAAY,SAC3BJ,EAAIK,UAAUvxB,GAAW,GAAM,GAC/BkxB,EAAI7tB,OAAS4tB,EAEjBn3B,KAAK2F,GAAG+xB,cAAcN,GAE1B,MAAOp3B,OAEXgY,cAAe,SAAUhB,GAGrB,QAAS2gB,GAAa/1B,GAElB,GAAIA,EAAEG,SAAW/B,KAEjB,IADAgX,EAAS4f,KAAK52B,KAAM4B,GACf+D,EAAI,EAAGA,EAAImxB,EAAOx0B,OAAQqD,IAC3BuxB,EAAI3L,IAAIuL,EAAOnxB,GAAIgyB,GAP3B,GACIhyB,GADAmxB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFI,EAAMl3B,IAShB,IAAIgX,EACA,IAAKrR,EAAI,EAAGA,EAAImxB,EAAOx0B,OAAQqD,IAC3BuxB,EAAI7L,GAAGyL,EAAOnxB,GAAIgyB,EAG1B,OAAO33B,OAGXyF,MAAO,WACH,MAAIzF,MAAK,KAAO0C,OACLA,OAAOuC,WAGVjF,KAAKsC,OAAS,EACP0W,WAAWhZ,KAAKsV,IAAI,UAGpB,MAInB+E,WAAY,SAAUud,GAClB,MAAI53B,MAAKsC,OAAS,EACVs1B,EACO53B,KAAK,GAAGyrB,YAAczS,WAAWhZ,KAAKsV,IAAI,iBAAmB0D,WAAWhZ,KAAKsV,IAAI,gBAEjFtV,KAAK,GAAGyrB,YAEX,MAEhB/lB,OAAQ,WACJ,MAAI1F,MAAK,KAAO0C,OACLA,OAAOyC,YAGVnF,KAAKsC,OAAS,EACP0W,WAAWhZ,KAAKsV,IAAI,WAGpB,MAInBgF,YAAa,SAAUsd,GACnB,MAAI53B,MAAKsC,OAAS,EACVs1B,EACO53B,KAAK,GAAGsY,aAAeU,WAAWhZ,KAAKsV,IAAI,eAAiB0D,WAAWhZ,KAAKsV,IAAI,kBAEhFtV,KAAK,GAAGsY,aAEX,MAEhBjT,OAAQ,WACJ,GAAIrF,KAAKsC,OAAS,EAAG,CACjB,GAAIR,GAAK9B,KAAK,GACV63B,EAAM/1B,EAAGg2B,wBACTC,EAAO3zB,SAAS2zB,KAChBC,EAAal2B,EAAGk2B,WAAcD,EAAKC,WAAc,EACjDjU,EAAajiB,EAAGiiB,YAAcgU,EAAKhU,YAAc,EACjDkU,EAAav1B,OAAOqC,aAAejD,EAAGm2B,UACtC1yB,EAAa7C,OAAOmC,aAAe/C,EAAGyD,UAC1C,QACIT,IAAK+yB,EAAI/yB,IAAOmzB,EAAaD,EAC7BpzB,KAAMizB,EAAIjzB,KAAOW,EAAawe,GAIlC,MAAO,OAGfzO,IAAK,SAAU4iB,EAAOtK,GAClB,GAAIjoB,EACJ,IAAyB,IAArBusB,UAAU5vB,OAAc,CACxB,GAAqB,gBAAV41B,GAGN,CACD,IAAKvyB,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB,IAAK,GAAIwyB,KAAQD,GACbl4B,KAAK2F,GAAG2Q,MAAM6hB,GAAQD,EAAMC,EAGpC,OAAOn4B,MARP,GAAIA,KAAK,GAAI,MAAO0C,QAAOsiB,iBAAiBhlB,KAAK,GAAI,MAAMylB,iBAAiByS,GAWpF,GAAyB,IAArBhG,UAAU5vB,QAAiC,gBAAV41B,GAAoB,CACrD,IAAKvyB,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB3F,KAAK2F,GAAG2Q,MAAM4hB,GAAStK,CAE3B,OAAO5tB,MAEX,MAAOA,OAIXC,KAAM,SAAU+W,GACZ,IAAK,GAAIrR,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7BqR,EAAS4f,KAAK52B,KAAK2F,GAAIA,EAAG3F,KAAK2F,GAEnC,OAAO3F,OAEX2c,KAAM,SAAUA,GACZ,GAAoB,mBAATA,GACP,MAAO3c,MAAK,GAAKA,KAAK,GAAGi0B,UAAYtmB,MAGrC,KAAK,GAAIhI,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B3F,KAAK2F,GAAGsuB,UAAYtX,CAExB,OAAO3c,OAGfsc,KAAM,SAAUA,GACZ,GAAoB,mBAATA,GACP,MAAItc,MAAK,GACEA,KAAK,GAAGo4B,YAAYjD,OAEnB,IAGZ,KAAK,GAAIxvB,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B3F,KAAK2F,GAAGyyB,YAAc9b,CAE1B,OAAOtc,OAGfgC,GAAI,SAAUH,GACV,IAAK7B,KAAK,GAAI,OAAO,CACrB,IAAIq4B,GAAa1yB,CACjB,IAAwB,gBAAb9D,GAAuB,CAC9B,GAAIC,GAAK9B,KAAK,EACd,IAAI8B,IAAOsC,SAAU,MAAOvC,KAAauC,QACzC,IAAItC,IAAOY,OAAQ,MAAOb,KAAaa,MAEvC,IAAIZ,EAAGw2B,QAAS,MAAOx2B,GAAGw2B,QAAQz2B,EAC7B,IAAIC,EAAGy2B,sBAAuB,MAAOz2B,GAAGy2B,sBAAsB12B,EAC9D,IAAIC,EAAG02B,mBAAoB,MAAO12B,GAAG02B,mBAAmB32B,EACxD,IAAIC,EAAG22B,kBAAmB,MAAO32B,GAAG22B,kBAAkB52B,EAGvD,KADAw2B,EAAcj4B,EAAEyB,GACX8D,EAAI,EAAGA,EAAI0yB,EAAY/1B,OAAQqD,IAChC,GAAI0yB,EAAY1yB,KAAO3F,KAAK,GAAI,OAAO,CAE3C,QAAO,EAGV,GAAI6B,IAAauC,SAAU,MAAOpE,MAAK,KAAOoE,QAC9C,IAAIvC,IAAaa,OAAQ,MAAO1C,MAAK,KAAO0C,MAE7C,IAAIb,EAASK,UAAYL,YAAoB2R,GAAM,CAE/C,IADA6kB,EAAcx2B,EAASK,UAAYL,GAAYA,EAC1C8D,EAAI,EAAGA,EAAI0yB,EAAY/1B,OAAQqD,IAChC,GAAI0yB,EAAY1yB,KAAO3F,KAAK,GAAI,OAAO,CAE3C,QAAO,EAEX,OAAO,GAIfoC,MAAO,WACH,GAAIpC,KAAK,GAAI,CAGT,IAFA,GAAI04B,GAAQ14B,KAAK,GACb2F,EAAI,EACmC,QAAnC+yB,EAAQA,EAAMC,kBACK,IAAnBD,EAAMx2B,UAAgByD,GAE9B,OAAOA,KAIf7E,GAAI,SAAUsB,GACV,GAAqB,mBAAVA,GAAuB,MAAOpC,KACzC,IACI44B,GADAt2B,EAAStC,KAAKsC,MAElB,OAAIF,GAAQE,EAAS,EACV,GAAIkR,OAEXpR,EAAQ,GACRw2B,EAAct2B,EAASF,EACK,GAAIoR,GAA5BolB,EAAc,MACI54B,KAAK44B,MAExB,GAAIplB,IAAMxT,KAAKoC,MAE1BikB,OAAQ,SAAUwS,GACd,GAAIlzB,GAAGmV,CACP,KAAKnV,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB,GAAwB,gBAAbkzB,GAAuB,CAC9B,GAAIC,GAAU10B,SAASiC,cAAc,MAErC,KADAyyB,EAAQ7E,UAAY4E,EACbC,EAAQC,YACX/4B,KAAK2F,GAAGqzB,YAAYF,EAAQC,gBAG/B,IAAIF,YAAoBrlB,GACzB,IAAKsH,EAAI,EAAGA,EAAI+d,EAASv2B,OAAQwY,IAC7B9a,KAAK2F,GAAGqzB,YAAYH,EAAS/d,QAIjC9a,MAAK2F,GAAGqzB,YAAYH,EAG5B,OAAO74B,OAEXumB,QAAS,SAAUsS,GACf,GAAIlzB,GAAGmV,CACP,KAAKnV,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB,GAAwB,gBAAbkzB,GAAuB,CAC9B,GAAIC,GAAU10B,SAASiC,cAAc,MAErC,KADAyyB,EAAQ7E,UAAY4E,EACf/d,EAAIge,EAAQzD,WAAW/yB,OAAS,EAAGwY,GAAK,EAAGA,IAC5C9a,KAAK2F,GAAGszB,aAAaH,EAAQzD,WAAWva,GAAI9a,KAAK2F,GAAG0vB,WAAW,QAIlE,IAAIwD,YAAoBrlB,GACzB,IAAKsH,EAAI,EAAGA,EAAI+d,EAASv2B,OAAQwY,IAC7B9a,KAAK2F,GAAGszB,aAAaJ,EAAS/d,GAAI9a,KAAK2F,GAAG0vB,WAAW,QAIzDr1B,MAAK2F,GAAGszB,aAAaJ,EAAU74B,KAAK2F,GAAG0vB,WAAW,GAG1D,OAAOr1B,OAEXi5B,aAAc,SAAUp3B,GAEpB,IAAK,GADDq3B,GAAS94B,EAAEyB,GACN8D,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B,GAAsB,IAAlBuzB,EAAO52B,OACP42B,EAAO,GAAGC,WAAWF,aAAaj5B,KAAK2F,GAAIuzB,EAAO,QAEjD,IAAIA,EAAO52B,OAAS,EACrB,IAAK,GAAIwY,GAAI,EAAGA,EAAIoe,EAAO52B,OAAQwY,IAC/Boe,EAAOpe,GAAGqe,WAAWF,aAAaj5B,KAAK2F,GAAG2gB,WAAU,GAAO4S,EAAOpe,KAKlFse,YAAa,SAAUv3B,GAEnB,IAAK,GADDw3B,GAAQj5B,EAAEyB,GACL8D,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAC7B,GAAqB,IAAjB0zB,EAAM/2B,OACN+2B,EAAM,GAAGF,WAAWF,aAAaj5B,KAAK2F,GAAI0zB,EAAM,GAAGC,iBAElD,IAAID,EAAM/2B,OAAS,EACpB,IAAK,GAAIwY,GAAI,EAAGA,EAAIue,EAAM/2B,OAAQwY,IAC9Bue,EAAMve,GAAGqe,WAAWF,aAAaj5B,KAAK2F,GAAG2gB,WAAU,GAAO+S,EAAMve,GAAGwe,cAKnFtd,KAAM,SAAUna,GACZ,MAE6F,IAAI2R,GAF7FxT,KAAKsC,OAAS,EACVT,EACI7B,KAAK,GAAGu5B,oBAAsBn5B,EAAEJ,KAAK,GAAGu5B,oBAAoBv3B,GAAGH,IAA4B7B,KAAK,GAAGu5B,uBAInGv5B,KAAK,GAAGu5B,oBAAqCv5B,KAAK,GAAGu5B,4BAMrEC,QAAS,SAAU33B,GACf,GAAI43B,MACA33B,EAAK9B,KAAK,EACd,KAAK8B,EAAI,MAAO,IAAI0R,MACpB,MAAO1R,EAAGy3B,oBAAoB,CAC1B,GAAIvd,GAAOla,EAAGy3B,kBACV13B,GACGzB,EAAE4b,GAAMha,GAAGH,IAAW43B,EAAQl2B,KAAKyY,GAErCyd,EAAQl2B,KAAKyY,GAClBla,EAAKka,EAET,MAAO,IAAIxI,GAAKimB,IAEpBvd,KAAM,SAAUra,GACZ,MAEqG,IAAI2R,GAFrGxT,KAAKsC,OAAS,EACVT,EACI7B,KAAK,GAAG05B,wBAA0Bt5B,EAAEJ,KAAK,GAAG05B,wBAAwB13B,GAAGH,IAA4B7B,KAAK,GAAG05B,2BAI3G15B,KAAK,GAAG05B,wBAAyC15B,KAAK,GAAG05B,gCAMzEC,QAAS,SAAU93B,GACf,GAAI+3B,MACA93B,EAAK9B,KAAK,EACd,KAAK8B,EAAI,MAAO,IAAI0R,MACpB,MAAO1R,EAAG43B,wBAAwB,CAC9B,GAAIxd,GAAOpa,EAAG43B,sBACV73B,GACGzB,EAAE8b,GAAMla,GAAGH,IAAW+3B,EAAQr2B,KAAK2Y,GAErC0d,EAAQr2B,KAAK2Y,GAClBpa,EAAKoa,EAET,MAAO,IAAI1I,GAAKomB,IAEpBzJ,OAAQ,SAAUtuB,GAEd,IAAK,GADDI,MACK0D,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB9D,EACIzB,EAAEJ,KAAK2F,GAAGwzB,YAAYn3B,GAAGH,IAAWI,EAAQsB,KAAKvD,KAAK2F,GAAGwzB,YAG7Dl3B,EAAQsB,KAAKvD,KAAK2F,GAAGwzB,WAG7B,OAAO/4B,GAAEA,EAAEy5B,OAAO53B,KAEtBA,QAAS,SAAUJ,GAEf,IAAK,GADDI,MACK0D,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAE7B,IADA,GAAIwqB,GAASnwB,KAAK2F,GAAGwzB,WACdhJ,GACCtuB,EACIzB,EAAE+vB,GAAQnuB,GAAGH,IAAWI,EAAQsB,KAAK4sB,GAGzCluB,EAAQsB,KAAK4sB,GAEjBA,EAASA,EAAOgJ,UAGxB,OAAO/4B,GAAEA,EAAEy5B,OAAO53B,KAEtBkT,KAAO,SAAUtT,GAEb,IAAK,GADDi4B,MACKn0B,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAE7B,IAAK,GADDxD,GAAQnC,KAAK2F,GAAG2vB,iBAAiBzzB,GAC5BiZ,EAAI,EAAGA,EAAI3Y,EAAMG,OAAQwY,IAC9Bgf,EAAcv2B,KAAKpB,EAAM2Y,GAGjC,OAAO,IAAItH,GAAKsmB,IAEpB7kB,SAAU,SAAUpT,GAEhB,IAAK,GADDoT,MACKtP,EAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAG7B,IAAK,GAFD0vB,GAAar1B,KAAK2F,GAAG0vB,WAEhBva,EAAI,EAAGA,EAAIua,EAAW/yB,OAAQwY,IAC9BjZ,EAI8B,IAA3BwzB,EAAWva,GAAG5Y,UAAkB9B,EAAEi1B,EAAWva,IAAI9Y,GAAGH,IAAWoT,EAAS1R,KAAK8xB,EAAWva,IAH7D,IAA3Bua,EAAWva,GAAG5Y,UAAgB+S,EAAS1R,KAAK8xB,EAAWva,GAOvE,OAAO,IAAItH,GAAKpT,EAAEy5B,OAAO5kB,KAE7BiR,OAAQ,WACJ,IAAK,GAAIvgB,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IACzB3F,KAAK2F,GAAGwzB,YAAYn5B,KAAK2F,GAAGwzB,WAAWY,YAAY/5B,KAAK2F,GAEhE,OAAO3F,OAEX2pB,IAAK,WACD,GACIhkB,GAAGmV,EADHoc,EAAMl3B,IAEV,KAAK2F,EAAI,EAAGA,EAAIusB,UAAU5vB,OAAQqD,IAAK,CACnC,GAAIq0B,GAAQ55B,EAAE8xB,UAAUvsB,GACxB,KAAKmV,EAAI,EAAGA,EAAIkf,EAAM13B,OAAQwY,IAC1Boc,EAAIA,EAAI50B,QAAU03B,EAAMlf,GACxBoc,EAAI50B,SAGZ,MAAO40B,KAGf92B,EAAER,GAAK4T,EAAKggB,UACZpzB,EAAEy5B,OAAS,SAAUlG,GAEjB,IAAK,GADDkG,MACKl0B,EAAI,EAAGA,EAAIguB,EAAIrxB,OAAQqD,IACxBk0B,EAAOxvB,QAAQspB,EAAIhuB,OAAQ,GAAIk0B,EAAOt2B,KAAKowB,EAAIhuB,GAEvD,OAAOk0B,IAGJz5B,MAOP65B,GAAoB,SAAU,QAAS,QAClCt0B,EAAI,EAAGA,EAAIs0B,EAAiB33B,OAAQqD,IACxCjD,OAAOu3B,EAAiBt0B,KAC3BjG,EAAiBgD,OAAOu3B,EAAiBt0B,IAI3C,IAAIu0B,EAEHA,GADmB,mBAAT1mB,GACD9Q,OAAO8Q,MAAQ9Q,OAAOoR,OAASpR,OAAO+Q,OAGtCD,EAiBN0mB,IACM,iBAAmBA,GAAOt6B,KAC5Bs6B,EAAOt6B,GAAGoY,cAAgB,SAAUhB,GAGhC,QAAS2gB,GAAa/1B,GAElB,GAAIA,EAAEG,SAAW/B,KAEjB,IADAgX,EAAS4f,KAAK52B,KAAM4B,GACf+D,EAAI,EAAGA,EAAImxB,EAAOx0B,OAAQqD,IAC3BuxB,EAAI3L,IAAIuL,EAAOnxB,GAAIgyB,GAP3B,GACIhyB,GADAmxB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFI,EAAMl3B,IAShB,IAAIgX,EACA,IAAKrR,EAAI,EAAGA,EAAImxB,EAAOx0B,OAAQqD,IAC3BuxB,EAAI7L,GAAGyL,EAAOnxB,GAAIgyB,EAG1B,OAAO33B,QAGT,aAAek6B,GAAOt6B,KACxBs6B,EAAOt6B,GAAG2K,UAAY,SAAUA,GAC5B,IAAK,GAAI5E,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAIuwB,GAAUl2B,KAAK2F,GAAG2Q,KACtB4f,GAAQhR,gBAAkBgR,EAAQ3Q,YAAc2Q,EAAQ1Q,YAAc0Q,EAAQ7Q,aAAe6Q,EAAQ5Q,WAAa4Q,EAAQ3rB,UAAYA,EAE1I,MAAOvK,QAGT,cAAgBk6B,GAAOt6B,KACzBs6B,EAAOt6B,GAAG8c,WAAa,SAAUyH,GACL,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAIxe,GAAI,EAAGA,EAAI3F,KAAKsC,OAAQqD,IAAK,CAClC,GAAIuwB,GAAUl2B,KAAK2F,GAAG2Q,KACtB4f,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBrS;CAEhM,MAAOnkB,QAGT,cAAgBk6B,GAAOt6B,KACzBs6B,EAAOt6B,GAAGya,WAAa,SAAUud,GAC7B,MAAI53B,MAAKsC,OAAS,EACVs1B,EACO53B,KAAK,GAAGyrB,YAAczS,WAAWhZ,KAAKsV,IAAI,iBAAmB0D,WAAWhZ,KAAKsV,IAAI,gBAEjFtV,KAAK,GAAGyrB,YAEX,QAKxB/oB,OAAOvC,OAASA,KAKG,mBAAb,QAENg6B,OAAOC,QAAU13B,OAAOvC,OAED,kBAAXk6B,SAAyBA,OAAOC,KAC5CD,UAAW,WACP,YACA,OAAO33B,QAAOvC", "file": "../swiper.min.js", "sourcesContent": ["/**\n * Swiper 3.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * \n * http://www.idangero.us/swiper/\n * \n * Copyright 2016, <PERSON>\n * The iDangero.us\n * http://www.idangero.us/\n * \n * Licensed under MIT\n * \n * Released on: December 13, 2016\n */\n(function () {\n    'use strict';\n    var $;\n    /*===========================\n    Swiper\n    ===========================*/\n    var Swiper = function (container, params) {\n        if (!(this instanceof Swiper)) return new Swiper(container, params);\n\n        var defaults = {\n            direction: 'horizontal',\n            touchEventsTarget: 'container',\n            initialSlide: 0,\n            speed: 300,\n            // autoplay\n            autoplay: false,\n            autoplayDisableOnInteraction: true,\n            autoplayStopOnLast: false,\n            // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n            iOSEdgeSwipeDetection: false,\n            iOSEdgeSwipeThreshold: 20,\n            // Free mode\n            freeMode: false,\n            freeModeMomentum: true,\n            freeModeMomentumRatio: 1,\n            freeModeMomentumBounce: true,\n            freeModeMomentumBounceRatio: 1,\n            freeModeMomentumVelocityRatio: 1,\n            freeModeSticky: false,\n            freeModeMinimumVelocity: 0.02,\n            // Autoheight\n            autoHeight: false,\n            // Set wrapper width\n            setWrapperSize: false,\n            // Virtual Translate\n            virtualTranslate: false,\n            // Effects\n            effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n            coverflow: {\n                rotate: 50,\n                stretch: 0,\n                depth: 100,\n                modifier: 1,\n                slideShadows : true\n            },\n            flip: {\n                slideShadows : true,\n                limitRotation: true\n            },\n            cube: {\n                slideShadows: true,\n                shadow: true,\n                shadowOffset: 20,\n                shadowScale: 0.94\n            },\n            fade: {\n                crossFade: false\n            },\n            // Parallax\n            parallax: false,\n            // Zoom\n            zoom: false,\n            zoomMax: 3,\n            zoomMin: 1,\n            zoomToggle: true,\n            // Scrollbar\n            scrollbar: null,\n            scrollbarHide: true,\n            scrollbarDraggable: false,\n            scrollbarSnapOnRelease: false,\n            // Keyboard Mousewheel\n            keyboardControl: false,\n            mousewheelControl: false,\n            mousewheelReleaseOnEdges: false,\n            mousewheelInvert: false,\n            mousewheelForceToAxis: false,\n            mousewheelSensitivity: 1,\n            mousewheelEventsTarged: 'container',\n            // Hash Navigation\n            hashnav: false,\n            hashnavWatchState: false,\n            // History\n            history: false,\n            // Commong Nav State\n            replaceState: false,\n            // Breakpoints\n            breakpoints: undefined,\n            // Slides grid\n            spaceBetween: 0,\n            slidesPerView: 1,\n            slidesPerColumn: 1,\n            slidesPerColumnFill: 'column',\n            slidesPerGroup: 1,\n            centeredSlides: false,\n            slidesOffsetBefore: 0, // in px\n            slidesOffsetAfter: 0, // in px\n            // Round length\n            roundLengths: false,\n            // Touches\n            touchRatio: 1,\n            touchAngle: 45,\n            simulateTouch: true,\n            shortSwipes: true,\n            longSwipes: true,\n            longSwipesRatio: 0.5,\n            longSwipesMs: 300,\n            followFinger: true,\n            onlyExternal: false,\n            threshold: 0,\n            touchMoveStopPropagation: true,\n            touchReleaseOnEdges: false,\n            // Unique Navigation Elements\n            uniqueNavElements: true,\n            // Pagination\n            pagination: null,\n            paginationElement: 'span',\n            paginationClickable: false,\n            paginationHide: false,\n            paginationBulletRender: null,\n            paginationProgressRender: null,\n            paginationFractionRender: null,\n            paginationCustomRender: null,\n            paginationType: 'bullets', // 'bullets' or 'progress' or 'fraction' or 'custom'\n            // Resistance\n            resistance: true,\n            resistanceRatio: 0.85,\n            // Next/prev buttons\n            nextButton: null,\n            prevButton: null,\n            // Progress\n            watchSlidesProgress: false,\n            watchSlidesVisibility: false,\n            // Cursor\n            grabCursor: false,\n            // Clicks\n            preventClicks: true,\n            preventClicksPropagation: true,\n            slideToClickedSlide: false,\n            // Lazy Loading\n            lazyLoading: false,\n            lazyLoadingInPrevNext: false,\n            lazyLoadingInPrevNextAmount: 1,\n            lazyLoadingOnTransitionStart: false,\n            // Images\n            preloadImages: true,\n            updateOnImagesReady: true,\n            // loop\n            loop: false,\n            loopAdditionalSlides: 0,\n            loopedSlides: null,\n            // Control\n            control: undefined,\n            controlInverse: false,\n            controlBy: 'slide', //or 'container'\n            normalizeSlideIndex: true,\n            // Swiping/no swiping\n            allowSwipeToPrev: true,\n            allowSwipeToNext: true,\n            swipeHandler: null, //'.swipe-handler',\n            noSwiping: true,\n            noSwipingClass: 'swiper-no-swiping',\n            // Passive Listeners\n            passiveListeners: true,\n            // NS\n            containerModifierClass: 'swiper-container-', // NEW\n            slideClass: 'swiper-slide',\n            slideActiveClass: 'swiper-slide-active',\n            slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n            slideVisibleClass: 'swiper-slide-visible',\n            slideDuplicateClass: 'swiper-slide-duplicate',\n            slideNextClass: 'swiper-slide-next',\n            slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n            slidePrevClass: 'swiper-slide-prev',\n            slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n            wrapperClass: 'swiper-wrapper',\n            bulletClass: 'swiper-pagination-bullet',\n            bulletActiveClass: 'swiper-pagination-bullet-active',\n            buttonDisabledClass: 'swiper-button-disabled',\n            paginationCurrentClass: 'swiper-pagination-current',\n            paginationTotalClass: 'swiper-pagination-total',\n            paginationHiddenClass: 'swiper-pagination-hidden',\n            paginationProgressbarClass: 'swiper-pagination-progressbar',\n            paginationClickableClass: 'swiper-pagination-clickable', // NEW\n            paginationModifierClass: 'swiper-pagination-', // NEW\n            lazyLoadingClass: 'swiper-lazy',\n            lazyStatusLoadingClass: 'swiper-lazy-loading',\n            lazyStatusLoadedClass: 'swiper-lazy-loaded',\n            lazyPreloaderClass: 'swiper-lazy-preloader',\n            notificationClass: 'swiper-notification',\n            preloaderClass: 'preloader',\n            zoomContainerClass: 'swiper-zoom-container',\n        \n            // Observer\n            observer: false,\n            observeParents: false,\n            // Accessibility\n            a11y: false,\n            prevSlideMessage: 'Previous slide',\n            nextSlideMessage: 'Next slide',\n            firstSlideMessage: 'This is the first slide',\n            lastSlideMessage: 'This is the last slide',\n            paginationBulletMessage: 'Go to slide {{index}}',\n            // Callbacks\n            runCallbacksOnInit: true\n            /*\n            Callbacks:\n            onInit: function (swiper)\n            onDestroy: function (swiper)\n            onClick: function (swiper, e)\n            onTap: function (swiper, e)\n            onDoubleTap: function (swiper, e)\n            onSliderMove: function (swiper, e)\n            onSlideChangeStart: function (swiper)\n            onSlideChangeEnd: function (swiper)\n            onTransitionStart: function (swiper)\n            onTransitionEnd: function (swiper)\n            onImagesReady: function (swiper)\n            onProgress: function (swiper, progress)\n            onTouchStart: function (swiper, e)\n            onTouchMove: function (swiper, e)\n            onTouchMoveOpposite: function (swiper, e)\n            onTouchEnd: function (swiper, e)\n            onReachBeginning: function (swiper)\n            onReachEnd: function (swiper)\n            onSetTransition: function (swiper, duration)\n            onSetTranslate: function (swiper, translate)\n            onAutoplayStart: function (swiper)\n            onAutoplayStop: function (swiper),\n            onLazyImageLoad: function (swiper, slide, image)\n            onLazyImageReady: function (swiper, slide, image)\n            */\n        \n        };\n        var initialVirtualTranslate = params && params.virtualTranslate;\n        \n        params = params || {};\n        var originalParams = {};\n        for (var param in params) {\n            if (typeof params[param] === 'object' && params[param] !== null && !(params[param].nodeType || params[param] === window || params[param] === document || (typeof Dom7 !== 'undefined' && params[param] instanceof Dom7) || (typeof jQuery !== 'undefined' && params[param] instanceof jQuery))) {\n                originalParams[param] = {};\n                for (var deepParam in params[param]) {\n                    originalParams[param][deepParam] = params[param][deepParam];\n                }\n            }\n            else {\n                originalParams[param] = params[param];\n            }\n        }\n        for (var def in defaults) {\n            if (typeof params[def] === 'undefined') {\n                params[def] = defaults[def];\n            }\n            else if (typeof params[def] === 'object') {\n                for (var deepDef in defaults[def]) {\n                    if (typeof params[def][deepDef] === 'undefined') {\n                        params[def][deepDef] = defaults[def][deepDef];\n                    }\n                }\n            }\n        }\n        \n        // Swiper\n        var s = this;\n        \n        // Params\n        s.params = params;\n        s.originalParams = originalParams;\n        \n        // Classname\n        s.classNames = [];\n        /*=========================\n          Dom Library and plugins\n          ===========================*/\n        if (typeof $ !== 'undefined' && typeof Dom7 !== 'undefined'){\n            $ = Dom7;\n        }\n        if (typeof $ === 'undefined') {\n            if (typeof Dom7 === 'undefined') {\n                $ = window.Dom7 || window.Zepto || window.jQuery;\n            }\n            else {\n                $ = Dom7;\n            }\n            if (!$) return;\n        }\n        // Export it to Swiper instance\n        s.$ = $;\n        \n        /*=========================\n          Breakpoints\n          ===========================*/\n        s.currentBreakpoint = undefined;\n        s.getActiveBreakpoint = function () {\n            //Get breakpoint for window width\n            if (!s.params.breakpoints) return false;\n            var breakpoint = false;\n            var points = [], point;\n            for ( point in s.params.breakpoints ) {\n                if (s.params.breakpoints.hasOwnProperty(point)) {\n                    points.push(point);\n                }\n            }\n            points.sort(function (a, b) {\n                return parseInt(a, 10) > parseInt(b, 10);\n            });\n            for (var i = 0; i < points.length; i++) {\n                point = points[i];\n                if (point >= window.innerWidth && !breakpoint) {\n                    breakpoint = point;\n                }\n            }\n            return breakpoint || 'max';\n        };\n        s.setBreakpoint = function () {\n            //Set breakpoint for window width and update parameters\n            var breakpoint = s.getActiveBreakpoint();\n            if (breakpoint && s.currentBreakpoint !== breakpoint) {\n                var breakPointsParams = breakpoint in s.params.breakpoints ? s.params.breakpoints[breakpoint] : s.originalParams;\n                var needsReLoop = s.params.loop && (breakPointsParams.slidesPerView !== s.params.slidesPerView);\n                for ( var param in breakPointsParams ) {\n                    s.params[param] = breakPointsParams[param];\n                }\n                s.currentBreakpoint = breakpoint;\n                if(needsReLoop && s.destroyLoop) {\n                    s.reLoop(true);\n                }\n            }\n        };\n        // Set breakpoint on load\n        if (s.params.breakpoints) {\n            s.setBreakpoint();\n        }\n        \n        /*=========================\n          Preparation - Define Container, Wrapper and Pagination\n          ===========================*/\n        s.container = $(container);\n        if (s.container.length === 0) return;\n        if (s.container.length > 1) {\n            var swipers = [];\n            s.container.each(function () {\n                var container = this;\n                swipers.push(new Swiper(this, params));\n            });\n            return swipers;\n        }\n        \n        // Save instance in container HTML Element and in data\n        s.container[0].swiper = s;\n        s.container.data('swiper', s);\n        \n        s.classNames.push(s.params.containerModifierClass + s.params.direction);\n        \n        if (s.params.freeMode) {\n            s.classNames.push(s.params.containerModifierClass + 'free-mode');\n        }\n        if (!s.support.flexbox) {\n            s.classNames.push(s.params.containerModifierClass + 'no-flexbox');\n            s.params.slidesPerColumn = 1;\n        }\n        if (s.params.autoHeight) {\n            s.classNames.push(s.params.containerModifierClass + 'autoheight');\n        }\n        // Enable slides progress when required\n        if (s.params.parallax || s.params.watchSlidesVisibility) {\n            s.params.watchSlidesProgress = true;\n        }\n        // Max resistance when touchReleaseOnEdges\n        if (s.params.touchReleaseOnEdges) {\n            s.params.resistanceRatio = 0;\n        }\n        // Coverflow / 3D\n        if (['cube', 'coverflow', 'flip'].indexOf(s.params.effect) >= 0) {\n            if (s.support.transforms3d) {\n                s.params.watchSlidesProgress = true;\n                s.classNames.push(s.params.containerModifierClass + '3d');\n            }\n            else {\n                s.params.effect = 'slide';\n            }\n        }\n        if (s.params.effect !== 'slide') {\n            s.classNames.push(s.params.containerModifierClass + s.params.effect);\n        }\n        if (s.params.effect === 'cube') {\n            s.params.resistanceRatio = 0;\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.centeredSlides = false;\n            s.params.spaceBetween = 0;\n            s.params.virtualTranslate = true;\n            s.params.setWrapperSize = false;\n        }\n        if (s.params.effect === 'fade' || s.params.effect === 'flip') {\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.watchSlidesProgress = true;\n            s.params.spaceBetween = 0;\n            s.params.setWrapperSize = false;\n            if (typeof initialVirtualTranslate === 'undefined') {\n                s.params.virtualTranslate = true;\n            }\n        }\n        \n        // Grab Cursor\n        if (s.params.grabCursor && s.support.touch) {\n            s.params.grabCursor = false;\n        }\n        \n        // Wrapper\n        s.wrapper = s.container.children('.' + s.params.wrapperClass);\n        \n        // Pagination\n        if (s.params.pagination) {\n            s.paginationContainer = $(s.params.pagination);\n            if (s.params.uniqueNavElements && typeof s.params.pagination === 'string' && s.paginationContainer.length > 1 && s.container.find(s.params.pagination).length === 1) {\n                s.paginationContainer = s.container.find(s.params.pagination);\n            }\n        \n            if (s.params.paginationType === 'bullets' && s.params.paginationClickable) {\n                s.paginationContainer.addClass(s.params.paginationModifierClass + 'clickable');\n            }\n            else {\n                s.params.paginationClickable = false;\n            }\n            s.paginationContainer.addClass(s.params.paginationModifierClass + s.params.paginationType);\n        }\n        // Next/Prev Buttons\n        if (s.params.nextButton || s.params.prevButton) {\n            if (s.params.nextButton) {\n                s.nextButton = $(s.params.nextButton);\n                if (s.params.uniqueNavElements && typeof s.params.nextButton === 'string' && s.nextButton.length > 1 && s.container.find(s.params.nextButton).length === 1) {\n                    s.nextButton = s.container.find(s.params.nextButton);\n                }\n            }\n            if (s.params.prevButton) {\n                s.prevButton = $(s.params.prevButton);\n                if (s.params.uniqueNavElements && typeof s.params.prevButton === 'string' && s.prevButton.length > 1 && s.container.find(s.params.prevButton).length === 1) {\n                    s.prevButton = s.container.find(s.params.prevButton);\n                }\n            }\n        }\n        \n        // Is Horizontal\n        s.isHorizontal = function () {\n            return s.params.direction === 'horizontal';\n        };\n        // s.isH = isH;\n        \n        // RTL\n        s.rtl = s.isHorizontal() && (s.container[0].dir.toLowerCase() === 'rtl' || s.container.css('direction') === 'rtl');\n        if (s.rtl) {\n            s.classNames.push(s.params.containerModifierClass + 'rtl');\n        }\n        \n        // Wrong RTL support\n        if (s.rtl) {\n            s.wrongRTL = s.wrapper.css('display') === '-webkit-box';\n        }\n        \n        // Columns\n        if (s.params.slidesPerColumn > 1) {\n            s.classNames.push(s.params.containerModifierClass + 'multirow');\n        }\n        \n        // Check for Android\n        if (s.device.android) {\n            s.classNames.push(s.params.containerModifierClass + 'android');\n        }\n        \n        // Add classes\n        s.container.addClass(s.classNames.join(' '));\n        \n        // Translate\n        s.translate = 0;\n        \n        // Progress\n        s.progress = 0;\n        \n        // Velocity\n        s.velocity = 0;\n        \n        /*=========================\n          Locks, unlocks\n          ===========================*/\n        s.lockSwipeToNext = function () {\n            s.params.allowSwipeToNext = false;\n            if (s.params.allowSwipeToPrev === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = false;\n            if (s.params.allowSwipeToNext === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = false;\n            if (s.params.grabCursor) s.unsetGrabCursor();\n        };\n        s.unlockSwipeToNext = function () {\n            s.params.allowSwipeToNext = true;\n            if (s.params.allowSwipeToPrev === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = true;\n            if (s.params.allowSwipeToNext === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = true;\n            if (s.params.grabCursor) s.setGrabCursor();\n        };\n        \n        /*=========================\n          Round helper\n          ===========================*/\n        function round(a) {\n            return Math.floor(a);\n        }\n        /*=========================\n          Set grab cursor\n          ===========================*/\n        s.setGrabCursor = function(moving) {\n            s.container[0].style.cursor = 'move';\n            s.container[0].style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n            s.container[0].style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n            s.container[0].style.cursor = moving ? 'grabbing': 'grab';\n        };\n        s.unsetGrabCursor = function () {\n            s.container[0].style.cursor = '';\n        };\n        if (s.params.grabCursor) {\n            s.setGrabCursor();\n        }\n        /*=========================\n          Update on Images Ready\n          ===========================*/\n        s.imagesToLoad = [];\n        s.imagesLoaded = 0;\n        \n        s.loadImage = function (imgElement, src, srcset, sizes, checkForComplete, callback) {\n            var image;\n            function onReady () {\n                if (callback) callback();\n            }\n            if (!imgElement.complete || !checkForComplete) {\n                if (src) {\n                    image = new window.Image();\n                    image.onload = onReady;\n                    image.onerror = onReady;\n                    if (sizes) {\n                        image.sizes = sizes;\n                    }\n                    if (srcset) {\n                        image.srcset = srcset;\n                    }\n                    if (src) {\n                        image.src = src;\n                    }\n                } else {\n                    onReady();\n                }\n        \n            } else {//image already loaded...\n                onReady();\n            }\n        };\n        s.preloadImages = function () {\n            s.imagesToLoad = s.container.find('img');\n            function _onReady() {\n                if (typeof s === 'undefined' || s === null || !s) return;\n                if (s.imagesLoaded !== undefined) s.imagesLoaded++;\n                if (s.imagesLoaded === s.imagesToLoad.length) {\n                    if (s.params.updateOnImagesReady) s.update();\n                    s.emit('onImagesReady', s);\n                }\n            }\n            for (var i = 0; i < s.imagesToLoad.length; i++) {\n                s.loadImage(s.imagesToLoad[i], (s.imagesToLoad[i].currentSrc || s.imagesToLoad[i].getAttribute('src')), (s.imagesToLoad[i].srcset || s.imagesToLoad[i].getAttribute('srcset')), s.imagesToLoad[i].sizes || s.imagesToLoad[i].getAttribute('sizes'), true, _onReady);\n            }\n        };\n        \n        /*=========================\n          Autoplay\n          ===========================*/\n        s.autoplayTimeoutId = undefined;\n        s.autoplaying = false;\n        s.autoplayPaused = false;\n        function autoplay() {\n            var autoplayDelay = s.params.autoplay;\n            var activeSlide = s.slides.eq(s.activeIndex);\n            if (activeSlide.attr('data-swiper-autoplay')) {\n                autoplayDelay = activeSlide.attr('data-swiper-autoplay') || s.params.autoplay;\n            }\n            s.autoplayTimeoutId = setTimeout(function () {\n                if (s.params.loop) {\n                    s.fixLoop();\n                    s._slideNext();\n                    s.emit('onAutoplay', s);\n                }\n                else {\n                    if (!s.isEnd) {\n                        s._slideNext();\n                        s.emit('onAutoplay', s);\n                    }\n                    else {\n                        if (!params.autoplayStopOnLast) {\n                            s._slideTo(0);\n                            s.emit('onAutoplay', s);\n                        }\n                        else {\n                            s.stopAutoplay();\n                        }\n                    }\n                }\n            }, autoplayDelay);\n        }\n        s.startAutoplay = function () {\n            if (typeof s.autoplayTimeoutId !== 'undefined') return false;\n            if (!s.params.autoplay) return false;\n            if (s.autoplaying) return false;\n            s.autoplaying = true;\n            s.emit('onAutoplayStart', s);\n            autoplay();\n        };\n        s.stopAutoplay = function (internal) {\n            if (!s.autoplayTimeoutId) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplaying = false;\n            s.autoplayTimeoutId = undefined;\n            s.emit('onAutoplayStop', s);\n        };\n        s.pauseAutoplay = function (speed) {\n            if (s.autoplayPaused) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplayPaused = true;\n            if (speed === 0) {\n                s.autoplayPaused = false;\n                autoplay();\n            }\n            else {\n                s.wrapper.transitionEnd(function () {\n                    if (!s) return;\n                    s.autoplayPaused = false;\n                    if (!s.autoplaying) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        autoplay();\n                    }\n                });\n            }\n        };\n        /*=========================\n          Min/Max Translate\n          ===========================*/\n        s.minTranslate = function () {\n            return (-s.snapGrid[0]);\n        };\n        s.maxTranslate = function () {\n            return (-s.snapGrid[s.snapGrid.length - 1]);\n        };\n        /*=========================\n          Slider/slides sizes\n          ===========================*/\n        s.updateAutoHeight = function () {\n            var activeSlides = [];\n            var newHeight = 0;\n            var i;\n        \n            // Find slides currently in view\n            if(s.params.slidesPerView !== 'auto' && s.params.slidesPerView > 1) {\n                for (i = 0; i < Math.ceil(s.params.slidesPerView); i++) {\n                    var index = s.activeIndex + i;\n                    if(index > s.slides.length) break;\n                    activeSlides.push(s.slides.eq(index)[0]);\n                }\n            } else {\n                activeSlides.push(s.slides.eq(s.activeIndex)[0]);\n            }\n        \n            // Find new height from heighest slide in view\n            for (i = 0; i < activeSlides.length; i++) {\n                if (typeof activeSlides[i] !== 'undefined') {\n                    var height = activeSlides[i].offsetHeight;\n                    newHeight = height > newHeight ? height : newHeight;\n                }\n            }\n        \n            // Update Height\n            if (newHeight) s.wrapper.css('height', newHeight + 'px');\n        };\n        s.updateContainerSize = function () {\n            var width, height;\n            if (typeof s.params.width !== 'undefined') {\n                width = s.params.width;\n            }\n            else {\n                width = s.container[0].clientWidth;\n            }\n            if (typeof s.params.height !== 'undefined') {\n                height = s.params.height;\n            }\n            else {\n                height = s.container[0].clientHeight;\n            }\n            if (width === 0 && s.isHorizontal() || height === 0 && !s.isHorizontal()) {\n                return;\n            }\n        \n            //Subtract paddings\n            width = width - parseInt(s.container.css('padding-left'), 10) - parseInt(s.container.css('padding-right'), 10);\n            height = height - parseInt(s.container.css('padding-top'), 10) - parseInt(s.container.css('padding-bottom'), 10);\n        \n            // Store values\n            s.width = width;\n            s.height = height;\n            s.size = s.isHorizontal() ? s.width : s.height;\n        };\n        \n        s.updateSlidesSize = function () {\n            s.slides = s.wrapper.children('.' + s.params.slideClass);\n            s.snapGrid = [];\n            s.slidesGrid = [];\n            s.slidesSizesGrid = [];\n        \n            var spaceBetween = s.params.spaceBetween,\n                slidePosition = -s.params.slidesOffsetBefore,\n                i,\n                prevSlideSize = 0,\n                index = 0;\n            if (typeof s.size === 'undefined') return;\n            if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n                spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * s.size;\n            }\n        \n            s.virtualSize = -spaceBetween;\n            // reset margins\n            if (s.rtl) s.slides.css({marginLeft: '', marginTop: ''});\n            else s.slides.css({marginRight: '', marginBottom: ''});\n        \n            var slidesNumberEvenToRows;\n            if (s.params.slidesPerColumn > 1) {\n                if (Math.floor(s.slides.length / s.params.slidesPerColumn) === s.slides.length / s.params.slidesPerColumn) {\n                    slidesNumberEvenToRows = s.slides.length;\n                }\n                else {\n                    slidesNumberEvenToRows = Math.ceil(s.slides.length / s.params.slidesPerColumn) * s.params.slidesPerColumn;\n                }\n                if (s.params.slidesPerView !== 'auto' && s.params.slidesPerColumnFill === 'row') {\n                    slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, s.params.slidesPerView * s.params.slidesPerColumn);\n                }\n            }\n        \n            // Calc slides\n            var slideSize;\n            var slidesPerColumn = s.params.slidesPerColumn;\n            var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n            var numFullColumns = slidesPerRow - (s.params.slidesPerColumn * slidesPerRow - s.slides.length);\n            for (i = 0; i < s.slides.length; i++) {\n                slideSize = 0;\n                var slide = s.slides.eq(i);\n                if (s.params.slidesPerColumn > 1) {\n                    // Set slides order\n                    var newSlideOrderIndex;\n                    var column, row;\n                    if (s.params.slidesPerColumnFill === 'column') {\n                        column = Math.floor(i / slidesPerColumn);\n                        row = i - column * slidesPerColumn;\n                        if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn-1)) {\n                            if (++row >= slidesPerColumn) {\n                                row = 0;\n                                column++;\n                            }\n                        }\n                        newSlideOrderIndex = column + row * slidesNumberEvenToRows / slidesPerColumn;\n                        slide\n                            .css({\n                                '-webkit-box-ordinal-group': newSlideOrderIndex,\n                                '-moz-box-ordinal-group': newSlideOrderIndex,\n                                '-ms-flex-order': newSlideOrderIndex,\n                                '-webkit-order': newSlideOrderIndex,\n                                'order': newSlideOrderIndex\n                            });\n                    }\n                    else {\n                        row = Math.floor(i / slidesPerRow);\n                        column = i - row * slidesPerRow;\n                    }\n                    slide\n                        .css(\n                            'margin-' + (s.isHorizontal() ? 'top' : 'left'),\n                            (row !== 0 && s.params.spaceBetween) && (s.params.spaceBetween + 'px')\n                        )\n                        .attr('data-swiper-column', column)\n                        .attr('data-swiper-row', row);\n        \n                }\n                if (slide.css('display') === 'none') continue;\n                if (s.params.slidesPerView === 'auto') {\n                    slideSize = s.isHorizontal() ? slide.outerWidth(true) : slide.outerHeight(true);\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n                }\n                else {\n                    slideSize = (s.size - (s.params.slidesPerView - 1) * spaceBetween) / s.params.slidesPerView;\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n        \n                    if (s.isHorizontal()) {\n                        s.slides[i].style.width = slideSize + 'px';\n                    }\n                    else {\n                        s.slides[i].style.height = slideSize + 'px';\n                    }\n                }\n                s.slides[i].swiperSlideSize = slideSize;\n                s.slidesSizesGrid.push(slideSize);\n        \n        \n                if (s.params.centeredSlides) {\n                    slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n                    if (i === 0) slidePosition = slidePosition - s.size / 2 - spaceBetween;\n                    if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                }\n                else {\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                    slidePosition = slidePosition + slideSize + spaceBetween;\n                }\n        \n                s.virtualSize += slideSize + spaceBetween;\n        \n                prevSlideSize = slideSize;\n        \n                index ++;\n            }\n            s.virtualSize = Math.max(s.virtualSize, s.size) + s.params.slidesOffsetAfter;\n            var newSlidesGrid;\n        \n            if (\n                s.rtl && s.wrongRTL && (s.params.effect === 'slide' || s.params.effect === 'coverflow')) {\n                s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n            if (!s.support.flexbox || s.params.setWrapperSize) {\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n        \n            if (s.params.slidesPerColumn > 1) {\n                s.virtualSize = (slideSize + s.params.spaceBetween) * slidesNumberEvenToRows;\n                s.virtualSize = Math.ceil(s.virtualSize / s.params.slidesPerColumn) - s.params.spaceBetween;\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n                if (s.params.centeredSlides) {\n                    newSlidesGrid = [];\n                    for (i = 0; i < s.snapGrid.length; i++) {\n                        if (s.snapGrid[i] < s.virtualSize + s.snapGrid[0]) newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                    s.snapGrid = newSlidesGrid;\n                }\n            }\n        \n            // Remove last grid elements depending on width\n            if (!s.params.centeredSlides) {\n                newSlidesGrid = [];\n                for (i = 0; i < s.snapGrid.length; i++) {\n                    if (s.snapGrid[i] <= s.virtualSize - s.size) {\n                        newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                }\n                s.snapGrid = newSlidesGrid;\n                if (Math.floor(s.virtualSize - s.size) - Math.floor(s.snapGrid[s.snapGrid.length - 1]) > 1) {\n                    s.snapGrid.push(s.virtualSize - s.size);\n                }\n            }\n            if (s.snapGrid.length === 0) s.snapGrid = [0];\n        \n            if (s.params.spaceBetween !== 0) {\n                if (s.isHorizontal()) {\n                    if (s.rtl) s.slides.css({marginLeft: spaceBetween + 'px'});\n                    else s.slides.css({marginRight: spaceBetween + 'px'});\n                }\n                else s.slides.css({marginBottom: spaceBetween + 'px'});\n            }\n            if (s.params.watchSlidesProgress) {\n                s.updateSlidesOffset();\n            }\n        };\n        s.updateSlidesOffset = function () {\n            for (var i = 0; i < s.slides.length; i++) {\n                s.slides[i].swiperSlideOffset = s.isHorizontal() ? s.slides[i].offsetLeft : s.slides[i].offsetTop;\n            }\n        };\n        \n        /*=========================\n          Dynamic Slides Per View\n          ===========================*/\n        s.currentSlidesPerView = function () {\n            var spv = 1, i, j;\n            if (s.params.centeredSlides) {\n                var size = s.slides[s.activeIndex].swiperSlideSize;\n                var breakLoop;\n                for (i = s.activeIndex + 1; i < s.slides.length; i++) {\n                    if (s.slides[i] && !breakLoop) {\n                        size += s.slides[i].swiperSlideSize;\n                        spv ++;\n                        if (size > s.size) breakLoop = true;\n                    }\n                }\n                for (j = s.activeIndex - 1; j >= 0; j--) {\n                    if (s.slides[j] && !breakLoop) {\n                        size += s.slides[j].swiperSlideSize;\n                        spv ++;\n                        if (size > s.size) breakLoop = true;\n                    }\n                }\n            }\n            else {\n                for (i = s.activeIndex + 1; i < s.slides.length; i++) {\n                    if (s.slidesGrid[i] - s.slidesGrid[s.activeIndex] < s.size) {\n                        spv++;\n                    }\n                }\n            }\n            return spv;\n        };\n        /*=========================\n          Slider/slides progress\n          ===========================*/\n        s.updateSlidesProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            if (s.slides.length === 0) return;\n            if (typeof s.slides[0].swiperSlideOffset === 'undefined') s.updateSlidesOffset();\n        \n            var offsetCenter = -translate;\n            if (s.rtl) offsetCenter = translate;\n        \n            // Visible Slides\n            s.slides.removeClass(s.params.slideVisibleClass);\n            for (var i = 0; i < s.slides.length; i++) {\n                var slide = s.slides[i];\n                var slideProgress = (offsetCenter + (s.params.centeredSlides ? s.minTranslate() : 0) - slide.swiperSlideOffset) / (slide.swiperSlideSize + s.params.spaceBetween);\n                if (s.params.watchSlidesVisibility) {\n                    var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n                    var slideAfter = slideBefore + s.slidesSizesGrid[i];\n                    var isVisible =\n                        (slideBefore >= 0 && slideBefore < s.size) ||\n                        (slideAfter > 0 && slideAfter <= s.size) ||\n                        (slideBefore <= 0 && slideAfter >= s.size);\n                    if (isVisible) {\n                        s.slides.eq(i).addClass(s.params.slideVisibleClass);\n                    }\n                }\n                slide.progress = s.rtl ? -slideProgress : slideProgress;\n            }\n        };\n        s.updateProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            var wasBeginning = s.isBeginning;\n            var wasEnd = s.isEnd;\n            if (translatesDiff === 0) {\n                s.progress = 0;\n                s.isBeginning = s.isEnd = true;\n            }\n            else {\n                s.progress = (translate - s.minTranslate()) / (translatesDiff);\n                s.isBeginning = s.progress <= 0;\n                s.isEnd = s.progress >= 1;\n            }\n            if (s.isBeginning && !wasBeginning) s.emit('onReachBeginning', s);\n            if (s.isEnd && !wasEnd) s.emit('onReachEnd', s);\n        \n            if (s.params.watchSlidesProgress) s.updateSlidesProgress(translate);\n            s.emit('onProgress', s, s.progress);\n        };\n        s.updateActiveIndex = function () {\n            var translate = s.rtl ? s.translate : -s.translate;\n            var newActiveIndex, i, snapIndex;\n            for (i = 0; i < s.slidesGrid.length; i ++) {\n                if (typeof s.slidesGrid[i + 1] !== 'undefined') {\n                    if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1] - (s.slidesGrid[i + 1] - s.slidesGrid[i]) / 2) {\n                        newActiveIndex = i;\n                    }\n                    else if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1]) {\n                        newActiveIndex = i + 1;\n                    }\n                }\n                else {\n                    if (translate >= s.slidesGrid[i]) {\n                        newActiveIndex = i;\n                    }\n                }\n            }\n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                if (newActiveIndex < 0 || typeof newActiveIndex === 'undefined') newActiveIndex = 0;\n            }\n            // for (i = 0; i < s.slidesGrid.length; i++) {\n                // if (- translate >= s.slidesGrid[i]) {\n                    // newActiveIndex = i;\n                // }\n            // }\n            snapIndex = Math.floor(newActiveIndex / s.params.slidesPerGroup);\n            if (snapIndex >= s.snapGrid.length) snapIndex = s.snapGrid.length - 1;\n        \n            if (newActiveIndex === s.activeIndex) {\n                return;\n            }\n            s.snapIndex = snapIndex;\n            s.previousIndex = s.activeIndex;\n            s.activeIndex = newActiveIndex;\n            s.updateClasses();\n            s.updateRealIndex();\n        };\n        s.updateRealIndex = function(){\n            s.realIndex = parseInt(s.slides.eq(s.activeIndex).attr('data-swiper-slide-index') || s.activeIndex, 10);\n        };\n        \n        /*=========================\n          Classes\n          ===========================*/\n        s.updateClasses = function () {\n            s.slides.removeClass(s.params.slideActiveClass + ' ' + s.params.slideNextClass + ' ' + s.params.slidePrevClass + ' ' + s.params.slideDuplicateActiveClass + ' ' + s.params.slideDuplicateNextClass + ' ' + s.params.slideDuplicatePrevClass);\n            var activeSlide = s.slides.eq(s.activeIndex);\n            // Active classes\n            activeSlide.addClass(s.params.slideActiveClass);\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (activeSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n            }\n            // Next Slide\n            var nextSlide = activeSlide.next('.' + s.params.slideClass).addClass(s.params.slideNextClass);\n            if (s.params.loop && nextSlide.length === 0) {\n                nextSlide = s.slides.eq(0);\n                nextSlide.addClass(s.params.slideNextClass);\n            }\n            // Prev Slide\n            var prevSlide = activeSlide.prev('.' + s.params.slideClass).addClass(s.params.slidePrevClass);\n            if (s.params.loop && prevSlide.length === 0) {\n                prevSlide = s.slides.eq(-1);\n                prevSlide.addClass(s.params.slidePrevClass);\n            }\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (nextSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                if (prevSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n            }\n        \n            // Pagination\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                // Current/Total\n                var current,\n                    total = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                if (s.params.loop) {\n                    current = Math.ceil((s.activeIndex - s.loopedSlides)/s.params.slidesPerGroup);\n                    if (current > s.slides.length - 1 - s.loopedSlides * 2) {\n                        current = current - (s.slides.length - s.loopedSlides * 2);\n                    }\n                    if (current > total - 1) current = current - total;\n                    if (current < 0 && s.params.paginationType !== 'bullets') current = total + current;\n                }\n                else {\n                    if (typeof s.snapIndex !== 'undefined') {\n                        current = s.snapIndex;\n                    }\n                    else {\n                        current = s.activeIndex || 0;\n                    }\n                }\n                // Types\n                if (s.params.paginationType === 'bullets' && s.bullets && s.bullets.length > 0) {\n                    s.bullets.removeClass(s.params.bulletActiveClass);\n                    if (s.paginationContainer.length > 1) {\n                        s.bullets.each(function () {\n                            if ($(this).index() === current) $(this).addClass(s.params.bulletActiveClass);\n                        });\n                    }\n                    else {\n                        s.bullets.eq(current).addClass(s.params.bulletActiveClass);\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    s.paginationContainer.find('.' + s.params.paginationCurrentClass).text(current + 1);\n                    s.paginationContainer.find('.' + s.params.paginationTotalClass).text(total);\n                }\n                if (s.params.paginationType === 'progress') {\n                    var scale = (current + 1) / total,\n                        scaleX = scale,\n                        scaleY = 1;\n                    if (!s.isHorizontal()) {\n                        scaleY = scale;\n                        scaleX = 1;\n                    }\n                    s.paginationContainer.find('.' + s.params.paginationProgressbarClass).transform('translate3d(0,0,0) scaleX(' + scaleX + ') scaleY(' + scaleY + ')').transition(s.params.speed);\n                }\n                if (s.params.paginationType === 'custom' && s.params.paginationCustomRender) {\n                    s.paginationContainer.html(s.params.paginationCustomRender(s, current + 1, total));\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        \n            // Next/active buttons\n            if (!s.params.loop) {\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    if (s.isBeginning) {\n                        s.prevButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.prevButton);\n                    }\n                    else {\n                        s.prevButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.prevButton);\n                    }\n                }\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    if (s.isEnd) {\n                        s.nextButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.nextButton);\n                    }\n                    else {\n                        s.nextButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.nextButton);\n                    }\n                }\n            }\n        };\n        \n        /*=========================\n          Pagination\n          ===========================*/\n        s.updatePagination = function () {\n            if (!s.params.pagination) return;\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                var paginationHTML = '';\n                if (s.params.paginationType === 'bullets') {\n                    var numberOfBullets = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                    for (var i = 0; i < numberOfBullets; i++) {\n                        if (s.params.paginationBulletRender) {\n                            paginationHTML += s.params.paginationBulletRender(s, i, s.params.bulletClass);\n                        }\n                        else {\n                            paginationHTML += '<' + s.params.paginationElement+' class=\"' + s.params.bulletClass + '\"></' + s.params.paginationElement + '>';\n                        }\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                    s.bullets = s.paginationContainer.find('.' + s.params.bulletClass);\n                    if (s.params.paginationClickable && s.params.a11y && s.a11y) {\n                        s.a11y.initPagination();\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    if (s.params.paginationFractionRender) {\n                        paginationHTML = s.params.paginationFractionRender(s, s.params.paginationCurrentClass, s.params.paginationTotalClass);\n                    }\n                    else {\n                        paginationHTML =\n                            '<span class=\"' + s.params.paginationCurrentClass + '\"></span>' +\n                            ' / ' +\n                            '<span class=\"' + s.params.paginationTotalClass+'\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType === 'progress') {\n                    if (s.params.paginationProgressRender) {\n                        paginationHTML = s.params.paginationProgressRender(s, s.params.paginationProgressbarClass);\n                    }\n                    else {\n                        paginationHTML = '<span class=\"' + s.params.paginationProgressbarClass + '\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType !== 'custom') {\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        };\n        /*=========================\n          Common update method\n          ===========================*/\n        s.update = function (updateTranslate) {\n            if (!s) return;\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updateProgress();\n            s.updatePagination();\n            s.updateClasses();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            function forceSetTranslate() {\n                var translate = s.rtl ? -s.translate : s.translate;\n                newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n            }\n            if (updateTranslate) {\n                var translated, newTranslate;\n                if (s.controller && s.controller.spline) {\n                    s.controller.spline = undefined;\n                }\n                if (s.params.freeMode) {\n                    forceSetTranslate();\n                    if (s.params.autoHeight) {\n                        s.updateAutoHeight();\n                    }\n                }\n                else {\n                    if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                        translated = s.slideTo(s.slides.length - 1, 0, false, true);\n                    }\n                    else {\n                        translated = s.slideTo(s.activeIndex, 0, false, true);\n                    }\n                    if (!translated) {\n                        forceSetTranslate();\n                    }\n                }\n            }\n            else if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n        };\n        \n        /*=========================\n          Resize Handler\n          ===========================*/\n        s.onResize = function (forceUpdatePagination) {\n            //Breakpoints\n            if (s.params.breakpoints) {\n                s.setBreakpoint();\n            }\n        \n            // Disable locks on resize\n            var allowSwipeToPrev = s.params.allowSwipeToPrev;\n            var allowSwipeToNext = s.params.allowSwipeToNext;\n            s.params.allowSwipeToPrev = s.params.allowSwipeToNext = true;\n        \n            s.updateContainerSize();\n            s.updateSlidesSize();\n            if (s.params.slidesPerView === 'auto' || s.params.freeMode || forceUpdatePagination) s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            if (s.controller && s.controller.spline) {\n                s.controller.spline = undefined;\n            }\n            var slideChangedBySlideTo = false;\n            if (s.params.freeMode) {\n                var newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n        \n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n            }\n            else {\n                s.updateClasses();\n                if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                    slideChangedBySlideTo = s.slideTo(s.slides.length - 1, 0, false, true);\n                }\n                else {\n                    slideChangedBySlideTo = s.slideTo(s.activeIndex, 0, false, true);\n                }\n            }\n            if (s.params.lazyLoading && !slideChangedBySlideTo && s.lazy) {\n                s.lazy.load();\n            }\n            // Return locks after resize\n            s.params.allowSwipeToPrev = allowSwipeToPrev;\n            s.params.allowSwipeToNext = allowSwipeToNext;\n        };\n        \n        /*=========================\n          Events\n          ===========================*/\n        \n        //Define Touch Events\n        s.touchEventsDesktop = {start: 'mousedown', move: 'mousemove', end: 'mouseup'};\n        if (window.navigator.pointerEnabled) s.touchEventsDesktop = {start: 'pointerdown', move: 'pointermove', end: 'pointerup'};\n        else if (window.navigator.msPointerEnabled) s.touchEventsDesktop = {start: 'MSPointerDown', move: 'MSPointerMove', end: 'MSPointerUp'};\n        s.touchEvents = {\n            start : s.support.touch || !s.params.simulateTouch  ? 'touchstart' : s.touchEventsDesktop.start,\n            move : s.support.touch || !s.params.simulateTouch ? 'touchmove' : s.touchEventsDesktop.move,\n            end : s.support.touch || !s.params.simulateTouch ? 'touchend' : s.touchEventsDesktop.end\n        };\n        \n        \n        // WP8 Touch Events Fix\n        if (window.navigator.pointerEnabled || window.navigator.msPointerEnabled) {\n            (s.params.touchEventsTarget === 'container' ? s.container : s.wrapper).addClass('swiper-wp8-' + s.params.direction);\n        }\n        \n        // Attach/detach events\n        s.initEvents = function (detach) {\n            var actionDom = detach ? 'off' : 'on';\n            var action = detach ? 'removeEventListener' : 'addEventListener';\n            var touchEventsTarget = s.params.touchEventsTarget === 'container' ? s.container[0] : s.wrapper[0];\n            var target = s.support.touch ? touchEventsTarget : document;\n        \n            var moveCapture = s.params.nested ? true : false;\n        \n            //Touch Events\n            if (s.browser.ie) {\n                touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, false);\n                target[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                target[action](s.touchEvents.end, s.onTouchEnd, false);\n            }\n            else {\n                if (s.support.touch) {\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, passiveListener);\n                    touchEventsTarget[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                    touchEventsTarget[action](s.touchEvents.end, s.onTouchEnd, passiveListener);\n                }\n                if ((params.simulateTouch && !s.device.ios && !s.device.android) || (params.simulateTouch && !s.support.touch && s.device.ios)) {\n                    touchEventsTarget[action]('mousedown', s.onTouchStart, false);\n                    document[action]('mousemove', s.onTouchMove, moveCapture);\n                    document[action]('mouseup', s.onTouchEnd, false);\n                }\n            }\n            window[action]('resize', s.onResize);\n        \n            // Next, Prev, Index\n            if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                s.nextButton[actionDom]('click', s.onClickNext);\n                if (s.params.a11y && s.a11y) s.nextButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                s.prevButton[actionDom]('click', s.onClickPrev);\n                if (s.params.a11y && s.a11y) s.prevButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.pagination && s.params.paginationClickable) {\n                s.paginationContainer[actionDom]('click', '.' + s.params.bulletClass, s.onClickIndex);\n                if (s.params.a11y && s.a11y) s.paginationContainer[actionDom]('keydown', '.' + s.params.bulletClass, s.a11y.onEnterKey);\n            }\n        \n            // Prevent Links Clicks\n            if (s.params.preventClicks || s.params.preventClicksPropagation) touchEventsTarget[action]('click', s.preventClicks, true);\n        };\n        s.attachEvents = function () {\n            s.initEvents();\n        };\n        s.detachEvents = function () {\n            s.initEvents(true);\n        };\n        \n        /*=========================\n          Handle Clicks\n          ===========================*/\n        // Prevent Clicks\n        s.allowClick = true;\n        s.preventClicks = function (e) {\n            if (!s.allowClick) {\n                if (s.params.preventClicks) e.preventDefault();\n                if (s.params.preventClicksPropagation && s.animating) {\n                    e.stopPropagation();\n                    e.stopImmediatePropagation();\n                }\n            }\n        };\n        // Clicks\n        s.onClickNext = function (e) {\n            e.preventDefault();\n            if (s.isEnd && !s.params.loop) return;\n            s.slideNext();\n        };\n        s.onClickPrev = function (e) {\n            e.preventDefault();\n            if (s.isBeginning && !s.params.loop) return;\n            s.slidePrev();\n        };\n        s.onClickIndex = function (e) {\n            e.preventDefault();\n            var index = $(this).index() * s.params.slidesPerGroup;\n            if (s.params.loop) index = index + s.loopedSlides;\n            s.slideTo(index);\n        };\n        \n        /*=========================\n          Handle Touches\n          ===========================*/\n        function findElementInEvent(e, selector) {\n            var el = $(e.target);\n            if (!el.is(selector)) {\n                if (typeof selector === 'string') {\n                    el = el.parents(selector);\n                }\n                else if (selector.nodeType) {\n                    var found;\n                    el.parents().each(function (index, _el) {\n                        if (_el === selector) found = selector;\n                    });\n                    if (!found) return undefined;\n                    else return selector;\n                }\n            }\n            if (el.length === 0) {\n                return undefined;\n            }\n            return el[0];\n        }\n        s.updateClickedSlide = function (e) {\n            var slide = findElementInEvent(e, '.' + s.params.slideClass);\n            var slideFound = false;\n            if (slide) {\n                for (var i = 0; i < s.slides.length; i++) {\n                    if (s.slides[i] === slide) slideFound = true;\n                }\n            }\n        \n            if (slide && slideFound) {\n                s.clickedSlide = slide;\n                s.clickedIndex = $(slide).index();\n            }\n            else {\n                s.clickedSlide = undefined;\n                s.clickedIndex = undefined;\n                return;\n            }\n            if (s.params.slideToClickedSlide && s.clickedIndex !== undefined && s.clickedIndex !== s.activeIndex) {\n                var slideToIndex = s.clickedIndex,\n                    realIndex,\n                    duplicatedSlides,\n                    slidesPerView = s.params.slidesPerView === 'auto' ? s.currentSlidesPerView() : s.params.slidesPerView;\n                if (s.params.loop) {\n                    if (s.animating) return;\n                    realIndex = parseInt($(s.clickedSlide).attr('data-swiper-slide-index'), 10);\n                    if (s.params.centeredSlides) {\n                        if ((slideToIndex < s.loopedSlides - slidesPerView/2) || (slideToIndex > s.slides.length - s.loopedSlides + slidesPerView/2)) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                    else {\n                        if (slideToIndex > s.slides.length - slidesPerView) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                }\n                else {\n                    s.slideTo(slideToIndex);\n                }\n            }\n        };\n        \n        var isTouched,\n            isMoved,\n            allowTouchCallbacks,\n            touchStartTime,\n            isScrolling,\n            currentTranslate,\n            startTranslate,\n            allowThresholdMove,\n            // Form elements to match\n            formElements = 'input, select, textarea, button, video',\n            // Last click time\n            lastClickTime = Date.now(), clickTimeout,\n            //Velocities\n            velocities = [],\n            allowMomentumBounce;\n        \n        // Animating Flag\n        s.animating = false;\n        \n        // Touches information\n        s.touches = {\n            startX: 0,\n            startY: 0,\n            currentX: 0,\n            currentY: 0,\n            diff: 0\n        };\n        \n        // Touch handlers\n        var isTouchEvent, startMoving;\n        s.onTouchStart = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            isTouchEvent = e.type === 'touchstart';\n            if (!isTouchEvent && 'which' in e && e.which === 3) return;\n            if (s.params.noSwiping && findElementInEvent(e, '.' + s.params.noSwipingClass)) {\n                s.allowClick = true;\n                return;\n            }\n            if (s.params.swipeHandler) {\n                if (!findElementInEvent(e, s.params.swipeHandler)) return;\n            }\n        \n            var startX = s.touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n            var startY = s.touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n        \n            // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n            if(s.device.ios && s.params.iOSEdgeSwipeDetection && startX <= s.params.iOSEdgeSwipeThreshold) {\n                return;\n            }\n        \n            isTouched = true;\n            isMoved = false;\n            allowTouchCallbacks = true;\n            isScrolling = undefined;\n            startMoving = undefined;\n            s.touches.startX = startX;\n            s.touches.startY = startY;\n            touchStartTime = Date.now();\n            s.allowClick = true;\n            s.updateContainerSize();\n            s.swipeDirection = undefined;\n            if (s.params.threshold > 0) allowThresholdMove = false;\n            if (e.type !== 'touchstart') {\n                var preventDefault = true;\n                if ($(e.target).is(formElements)) preventDefault = false;\n                if (document.activeElement && $(document.activeElement).is(formElements)) {\n                    document.activeElement.blur();\n                }\n                if (preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            s.emit('onTouchStart', s, e);\n        };\n        \n        s.onTouchMove = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (isTouchEvent && e.type === 'mousemove') return;\n            if (e.preventedByNestedSwiper) {\n                s.touches.startX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                s.touches.startY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                return;\n            }\n            if (s.params.onlyExternal) {\n                // isMoved = true;\n                s.allowClick = false;\n                if (isTouched) {\n                    s.touches.startX = s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                    s.touches.startY = s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                    touchStartTime = Date.now();\n                }\n                return;\n            }\n            if (isTouchEvent && s.params.touchReleaseOnEdges && !s.params.loop) {\n                if (!s.isHorizontal()) {\n                    // Vertical\n                    if (\n                        (s.touches.currentY < s.touches.startY && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentY > s.touches.startY && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n                else {\n                    if (\n                        (s.touches.currentX < s.touches.startX && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentX > s.touches.startX && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n            }\n            if (isTouchEvent && document.activeElement) {\n                if (e.target === document.activeElement && $(e.target).is(formElements)) {\n                    isMoved = true;\n                    s.allowClick = false;\n                    return;\n                }\n            }\n            if (allowTouchCallbacks) {\n                s.emit('onTouchMove', s, e);\n            }\n            if (e.targetTouches && e.targetTouches.length > 1) return;\n        \n            s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n            s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n            if (typeof isScrolling === 'undefined') {\n                var touchAngle;\n                if (s.isHorizontal() && s.touches.currentY === s.touches.startY || !s.isHorizontal() && s.touches.currentX === s.touches.startX) {\n                    isScrolling = false;\n                }\n                else {\n                    touchAngle = Math.atan2(Math.abs(s.touches.currentY - s.touches.startY), Math.abs(s.touches.currentX - s.touches.startX)) * 180 / Math.PI;\n                    isScrolling = s.isHorizontal() ? touchAngle > s.params.touchAngle : (90 - touchAngle > s.params.touchAngle);\n                }\n            }\n            if (isScrolling) {\n                s.emit('onTouchMoveOpposite', s, e);\n            }\n            if (typeof startMoving === 'undefined' && s.browser.ieTouch) {\n                if (s.touches.currentX !== s.touches.startX || s.touches.currentY !== s.touches.startY) {\n                    startMoving = true;\n                }\n            }\n            if (!isTouched) return;\n            if (isScrolling)  {\n                isTouched = false;\n                return;\n            }\n            if (!startMoving && s.browser.ieTouch) {\n                return;\n            }\n            s.allowClick = false;\n            s.emit('onSliderMove', s, e);\n            e.preventDefault();\n            if (s.params.touchMoveStopPropagation && !s.params.nested) {\n                e.stopPropagation();\n            }\n        \n            if (!isMoved) {\n                if (params.loop) {\n                    s.fixLoop();\n                }\n                startTranslate = s.getWrapperTranslate();\n                s.setWrapperTransition(0);\n                if (s.animating) {\n                    s.wrapper.trigger('webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd');\n                }\n                if (s.params.autoplay && s.autoplaying) {\n                    if (s.params.autoplayDisableOnInteraction) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        s.pauseAutoplay();\n                    }\n                }\n                allowMomentumBounce = false;\n                //Grab Cursor\n                if (s.params.grabCursor && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                    s.setGrabCursor(true);\n                }\n            }\n            isMoved = true;\n        \n            var diff = s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n        \n            diff = diff * s.params.touchRatio;\n            if (s.rtl) diff = -diff;\n        \n            s.swipeDirection = diff > 0 ? 'prev' : 'next';\n            currentTranslate = diff + startTranslate;\n        \n            var disableParentSwiper = true;\n            if ((diff > 0 && currentTranslate > s.minTranslate())) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.minTranslate() - 1 + Math.pow(-s.minTranslate() + startTranslate + diff, s.params.resistanceRatio);\n            }\n            else if (diff < 0 && currentTranslate < s.maxTranslate()) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.maxTranslate() + 1 - Math.pow(s.maxTranslate() - startTranslate - diff, s.params.resistanceRatio);\n            }\n        \n            if (disableParentSwiper) {\n                e.preventedByNestedSwiper = true;\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && s.swipeDirection === 'next' && currentTranslate < startTranslate) {\n                currentTranslate = startTranslate;\n            }\n            if (!s.params.allowSwipeToPrev && s.swipeDirection === 'prev' && currentTranslate > startTranslate) {\n                currentTranslate = startTranslate;\n            }\n        \n        \n            // Threshold\n            if (s.params.threshold > 0) {\n                if (Math.abs(diff) > s.params.threshold || allowThresholdMove) {\n                    if (!allowThresholdMove) {\n                        allowThresholdMove = true;\n                        s.touches.startX = s.touches.currentX;\n                        s.touches.startY = s.touches.currentY;\n                        currentTranslate = startTranslate;\n                        s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n                        return;\n                    }\n                }\n                else {\n                    currentTranslate = startTranslate;\n                    return;\n                }\n            }\n        \n            if (!s.params.followFinger) return;\n        \n            // Update active index in free mode\n            if (s.params.freeMode || s.params.watchSlidesProgress) {\n                s.updateActiveIndex();\n            }\n            if (s.params.freeMode) {\n                //Velocity\n                if (velocities.length === 0) {\n                    velocities.push({\n                        position: s.touches[s.isHorizontal() ? 'startX' : 'startY'],\n                        time: touchStartTime\n                    });\n                }\n                velocities.push({\n                    position: s.touches[s.isHorizontal() ? 'currentX' : 'currentY'],\n                    time: (new window.Date()).getTime()\n                });\n            }\n            // Update progress\n            s.updateProgress(currentTranslate);\n            // Update translate\n            s.setWrapperTranslate(currentTranslate);\n        };\n        s.onTouchEnd = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (allowTouchCallbacks) {\n                s.emit('onTouchEnd', s, e);\n            }\n            allowTouchCallbacks = false;\n            if (!isTouched) return;\n            //Return Grab Cursor\n            if (s.params.grabCursor && isMoved && isTouched  && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                s.setGrabCursor(false);\n            }\n        \n            // Time diff\n            var touchEndTime = Date.now();\n            var timeDiff = touchEndTime - touchStartTime;\n        \n            // Tap, doubleTap, Click\n            if (s.allowClick) {\n                s.updateClickedSlide(e);\n                s.emit('onTap', s, e);\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) > 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    clickTimeout = setTimeout(function () {\n                        if (!s) return;\n                        if (s.params.paginationHide && s.paginationContainer.length > 0 && !$(e.target).hasClass(s.params.bulletClass)) {\n                            s.paginationContainer.toggleClass(s.params.paginationHiddenClass);\n                        }\n                        s.emit('onClick', s, e);\n                    }, 300);\n        \n                }\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) < 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    s.emit('onDoubleTap', s, e);\n                }\n            }\n        \n            lastClickTime = Date.now();\n            setTimeout(function () {\n                if (s) s.allowClick = true;\n            }, 0);\n        \n            if (!isTouched || !isMoved || !s.swipeDirection || s.touches.diff === 0 || currentTranslate === startTranslate) {\n                isTouched = isMoved = false;\n                return;\n            }\n            isTouched = isMoved = false;\n        \n            var currentPos;\n            if (s.params.followFinger) {\n                currentPos = s.rtl ? s.translate : -s.translate;\n            }\n            else {\n                currentPos = -currentTranslate;\n            }\n            if (s.params.freeMode) {\n                if (currentPos < -s.minTranslate()) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                else if (currentPos > -s.maxTranslate()) {\n                    if (s.slides.length < s.snapGrid.length) {\n                        s.slideTo(s.snapGrid.length - 1);\n                    }\n                    else {\n                        s.slideTo(s.slides.length - 1);\n                    }\n                    return;\n                }\n        \n                if (s.params.freeModeMomentum) {\n                    if (velocities.length > 1) {\n                        var lastMoveEvent = velocities.pop(), velocityEvent = velocities.pop();\n        \n                        var distance = lastMoveEvent.position - velocityEvent.position;\n                        var time = lastMoveEvent.time - velocityEvent.time;\n                        s.velocity = distance / time;\n                        s.velocity = s.velocity / 2;\n                        if (Math.abs(s.velocity) < s.params.freeModeMinimumVelocity) {\n                            s.velocity = 0;\n                        }\n                        // this implies that the user stopped moving a finger then released.\n                        // There would be no events with distance zero, so the last event is stale.\n                        if (time > 150 || (new window.Date().getTime() - lastMoveEvent.time) > 300) {\n                            s.velocity = 0;\n                        }\n                    } else {\n                        s.velocity = 0;\n                    }\n                    s.velocity = s.velocity * s.params.freeModeMomentumVelocityRatio;\n        \n                    velocities.length = 0;\n                    var momentumDuration = 1000 * s.params.freeModeMomentumRatio;\n                    var momentumDistance = s.velocity * momentumDuration;\n        \n                    var newPosition = s.translate + momentumDistance;\n                    if (s.rtl) newPosition = - newPosition;\n                    var doBounce = false;\n                    var afterBouncePosition;\n                    var bounceAmount = Math.abs(s.velocity) * 20 * s.params.freeModeMomentumBounceRatio;\n                    if (newPosition < s.maxTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition + s.maxTranslate() < -bounceAmount) {\n                                newPosition = s.maxTranslate() - bounceAmount;\n                            }\n                            afterBouncePosition = s.maxTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.maxTranslate();\n                        }\n                    }\n                    else if (newPosition > s.minTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition - s.minTranslate() > bounceAmount) {\n                                newPosition = s.minTranslate() + bounceAmount;\n                            }\n                            afterBouncePosition = s.minTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.minTranslate();\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        var j = 0,\n                            nextSlide;\n                        for (j = 0; j < s.snapGrid.length; j += 1) {\n                            if (s.snapGrid[j] > -newPosition) {\n                                nextSlide = j;\n                                break;\n                            }\n        \n                        }\n                        if (Math.abs(s.snapGrid[nextSlide] - newPosition) < Math.abs(s.snapGrid[nextSlide - 1] - newPosition) || s.swipeDirection === 'next') {\n                            newPosition = s.snapGrid[nextSlide];\n                        } else {\n                            newPosition = s.snapGrid[nextSlide - 1];\n                        }\n                        if (!s.rtl) newPosition = - newPosition;\n                    }\n                    //Fix duration\n                    if (s.velocity !== 0) {\n                        if (s.rtl) {\n                            momentumDuration = Math.abs((-newPosition - s.translate) / s.velocity);\n                        }\n                        else {\n                            momentumDuration = Math.abs((newPosition - s.translate) / s.velocity);\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        s.slideReset();\n                        return;\n                    }\n        \n                    if (s.params.freeModeMomentumBounce && doBounce) {\n                        s.updateProgress(afterBouncePosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        s.animating = true;\n                        s.wrapper.transitionEnd(function () {\n                            if (!s || !allowMomentumBounce) return;\n                            s.emit('onMomentumBounce', s);\n        \n                            s.setWrapperTransition(s.params.speed);\n                            s.setWrapperTranslate(afterBouncePosition);\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        });\n                    } else if (s.velocity) {\n                        s.updateProgress(newPosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        if (!s.animating) {\n                            s.animating = true;\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        }\n        \n                    } else {\n                        s.updateProgress(newPosition);\n                    }\n        \n                    s.updateActiveIndex();\n                }\n                if (!s.params.freeModeMomentum || timeDiff >= s.params.longSwipesMs) {\n                    s.updateProgress();\n                    s.updateActiveIndex();\n                }\n                return;\n            }\n        \n            // Find current slide\n            var i, stopIndex = 0, groupSize = s.slidesSizesGrid[0];\n            for (i = 0; i < s.slidesGrid.length; i += s.params.slidesPerGroup) {\n                if (typeof s.slidesGrid[i + s.params.slidesPerGroup] !== 'undefined') {\n                    if (currentPos >= s.slidesGrid[i] && currentPos < s.slidesGrid[i + s.params.slidesPerGroup]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[i + s.params.slidesPerGroup] - s.slidesGrid[i];\n                    }\n                }\n                else {\n                    if (currentPos >= s.slidesGrid[i]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[s.slidesGrid.length - 1] - s.slidesGrid[s.slidesGrid.length - 2];\n                    }\n                }\n            }\n        \n            // Find current slide size\n            var ratio = (currentPos - s.slidesGrid[stopIndex]) / groupSize;\n        \n            if (timeDiff > s.params.longSwipesMs) {\n                // Long touches\n                if (!s.params.longSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    if (ratio >= s.params.longSwipesRatio) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    if (ratio > (1 - s.params.longSwipesRatio)) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n                }\n            }\n            else {\n                // Short swipes\n                if (!s.params.shortSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    s.slideTo(stopIndex + s.params.slidesPerGroup);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    s.slideTo(stopIndex);\n                }\n            }\n        };\n        /*=========================\n          Transitions\n          ===========================*/\n        s._slideTo = function (slideIndex, speed) {\n            return s.slideTo(slideIndex, speed, true, true);\n        };\n        s.slideTo = function (slideIndex, speed, runCallbacks, internal) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (typeof slideIndex === 'undefined') slideIndex = 0;\n            if (slideIndex < 0) slideIndex = 0;\n            s.snapIndex = Math.floor(slideIndex / s.params.slidesPerGroup);\n            if (s.snapIndex >= s.snapGrid.length) s.snapIndex = s.snapGrid.length - 1;\n        \n            var translate = - s.snapGrid[s.snapIndex];\n            // Stop autoplay\n            if (s.params.autoplay && s.autoplaying) {\n                if (internal || !s.params.autoplayDisableOnInteraction) {\n                    s.pauseAutoplay(speed);\n                }\n                else {\n                    s.stopAutoplay();\n                }\n            }\n            // Update progress\n            s.updateProgress(translate);\n        \n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                for (var i = 0; i < s.slidesGrid.length; i++) {\n                    if (- Math.floor(translate * 100) >= Math.floor(s.slidesGrid[i] * 100)) {\n                        slideIndex = i;\n                    }\n                }\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && translate < s.translate && translate < s.minTranslate()) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && translate > s.translate && translate > s.maxTranslate()) {\n                if ((s.activeIndex || 0) !== slideIndex ) return false;\n            }\n        \n            // Update Index\n            if (typeof speed === 'undefined') speed = s.params.speed;\n            s.previousIndex = s.activeIndex || 0;\n            s.activeIndex = slideIndex;\n            s.updateRealIndex();\n            if ((s.rtl && -translate === s.translate) || (!s.rtl && translate === s.translate)) {\n                // Update Height\n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n                s.updateClasses();\n                if (s.params.effect !== 'slide') {\n                    s.setWrapperTranslate(translate);\n                }\n                return false;\n            }\n            s.updateClasses();\n            s.onTransitionStart(runCallbacks);\n        \n            if (speed === 0 || s.browser.lteIE9) {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(0);\n                s.onTransitionEnd(runCallbacks);\n            }\n            else {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(speed);\n                if (!s.animating) {\n                    s.animating = true;\n                    s.wrapper.transitionEnd(function () {\n                        if (!s) return;\n                        s.onTransitionEnd(runCallbacks);\n                    });\n                }\n        \n            }\n        \n            return true;\n        };\n        \n        s.onTransitionStart = function (runCallbacks) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n            if (s.lazy) s.lazy.onTransitionStart();\n            if (runCallbacks) {\n                s.emit('onTransitionStart', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeStart', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextStart', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevStart', s);\n                    }\n                }\n        \n            }\n        };\n        s.onTransitionEnd = function (runCallbacks) {\n            s.animating = false;\n            s.setWrapperTransition(0);\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.lazy) s.lazy.onTransitionEnd();\n            if (runCallbacks) {\n                s.emit('onTransitionEnd', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeEnd', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextEnd', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevEnd', s);\n                    }\n                }\n            }\n            if (s.params.history && s.history) {\n                s.history.setHistory(s.params.history, s.activeIndex);\n            }\n            if (s.params.hashnav && s.hashnav) {\n                s.hashnav.setHash();\n            }\n        \n        };\n        s.slideNext = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n        };\n        s._slideNext = function (speed) {\n            return s.slideNext(true, speed, true);\n        };\n        s.slidePrev = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n        };\n        s._slidePrev = function (speed) {\n            return s.slidePrev(true, speed, true);\n        };\n        s.slideReset = function (runCallbacks, speed, internal) {\n            return s.slideTo(s.activeIndex, speed, runCallbacks);\n        };\n        \n        s.disableTouchControl = function () {\n            s.params.onlyExternal = true;\n            return true;\n        };\n        s.enableTouchControl = function () {\n            s.params.onlyExternal = false;\n            return true;\n        };\n        \n        /*=========================\n          Translate/transition helpers\n          ===========================*/\n        s.setWrapperTransition = function (duration, byController) {\n            s.wrapper.transition(duration);\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTransition(duration);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTransition(duration);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTransition(duration);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTransition(duration, byController);\n            }\n            s.emit('onSetTransition', s, duration);\n        };\n        s.setWrapperTranslate = function (translate, updateActiveIndex, byController) {\n            var x = 0, y = 0, z = 0;\n            if (s.isHorizontal()) {\n                x = s.rtl ? -translate : translate;\n            }\n            else {\n                y = translate;\n            }\n        \n            if (s.params.roundLengths) {\n                x = round(x);\n                y = round(y);\n            }\n        \n            if (!s.params.virtualTranslate) {\n                if (s.support.transforms3d) s.wrapper.transform('translate3d(' + x + 'px, ' + y + 'px, ' + z + 'px)');\n                else s.wrapper.transform('translate(' + x + 'px, ' + y + 'px)');\n            }\n        \n            s.translate = s.isHorizontal() ? x : y;\n        \n            // Check if we need to update progress\n            var progress;\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            if (translatesDiff === 0) {\n                progress = 0;\n            }\n            else {\n                progress = (translate - s.minTranslate()) / (translatesDiff);\n            }\n            if (progress !== s.progress) {\n                s.updateProgress(translate);\n            }\n        \n            if (updateActiveIndex) s.updateActiveIndex();\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTranslate(s.translate);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTranslate(s.translate);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTranslate(s.translate);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTranslate(s.translate, byController);\n            }\n            s.emit('onSetTranslate', s, s.translate);\n        };\n        \n        s.getTranslate = function (el, axis) {\n            var matrix, curTransform, curStyle, transformMatrix;\n        \n            // automatic axis detection\n            if (typeof axis === 'undefined') {\n                axis = 'x';\n            }\n        \n            if (s.params.virtualTranslate) {\n                return s.rtl ? -s.translate : s.translate;\n            }\n        \n            curStyle = window.getComputedStyle(el, null);\n            if (window.WebKitCSSMatrix) {\n                curTransform = curStyle.transform || curStyle.webkitTransform;\n                if (curTransform.split(',').length > 6) {\n                    curTransform = curTransform.split(', ').map(function(a){\n                        return a.replace(',','.');\n                    }).join(', ');\n                }\n                // Some old versions of Webkit choke when 'none' is passed; pass\n                // empty string instead in this case\n                transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n            }\n            else {\n                transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform  || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n                matrix = transformMatrix.toString().split(',');\n            }\n        \n            if (axis === 'x') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m41;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[12]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[4]);\n            }\n            if (axis === 'y') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m42;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[13]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[5]);\n            }\n            if (s.rtl && curTransform) curTransform = -curTransform;\n            return curTransform || 0;\n        };\n        s.getWrapperTranslate = function (axis) {\n            if (typeof axis === 'undefined') {\n                axis = s.isHorizontal() ? 'x' : 'y';\n            }\n            return s.getTranslate(s.wrapper[0], axis);\n        };\n        \n        /*=========================\n          Observer\n          ===========================*/\n        s.observers = [];\n        function initObserver(target, options) {\n            options = options || {};\n            // create an observer instance\n            var ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n            var observer = new ObserverFunc(function (mutations) {\n                mutations.forEach(function (mutation) {\n                    s.onResize(true);\n                    s.emit('onObserverUpdate', s, mutation);\n                });\n            });\n        \n            observer.observe(target, {\n                attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n                childList: typeof options.childList === 'undefined' ? true : options.childList,\n                characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n            });\n        \n            s.observers.push(observer);\n        }\n        s.initObservers = function () {\n            if (s.params.observeParents) {\n                var containerParents = s.container.parents();\n                for (var i = 0; i < containerParents.length; i++) {\n                    initObserver(containerParents[i]);\n                }\n            }\n        \n            // Observe container\n            initObserver(s.container[0], {childList: false});\n        \n            // Observe wrapper\n            initObserver(s.wrapper[0], {attributes: false});\n        };\n        s.disconnectObservers = function () {\n            for (var i = 0; i < s.observers.length; i++) {\n                s.observers[i].disconnect();\n            }\n            s.observers = [];\n        };\n        /*=========================\n          Loop\n          ===========================*/\n        // Create looped slides\n        s.createLoop = function () {\n            // Remove duplicated slides\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n        \n            var slides = s.wrapper.children('.' + s.params.slideClass);\n        \n            if(s.params.slidesPerView === 'auto' && !s.params.loopedSlides) s.params.loopedSlides = slides.length;\n        \n            s.loopedSlides = parseInt(s.params.loopedSlides || s.params.slidesPerView, 10);\n            s.loopedSlides = s.loopedSlides + s.params.loopAdditionalSlides;\n            if (s.loopedSlides > slides.length) {\n                s.loopedSlides = slides.length;\n            }\n        \n            var prependSlides = [], appendSlides = [], i;\n            slides.each(function (index, el) {\n                var slide = $(this);\n                if (index < s.loopedSlides) appendSlides.push(el);\n                if (index < slides.length && index >= slides.length - s.loopedSlides) prependSlides.push(el);\n                slide.attr('data-swiper-slide-index', index);\n            });\n            for (i = 0; i < appendSlides.length; i++) {\n                s.wrapper.append($(appendSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n            for (i = prependSlides.length - 1; i >= 0; i--) {\n                s.wrapper.prepend($(prependSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n        };\n        s.destroyLoop = function () {\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n            s.slides.removeAttr('data-swiper-slide-index');\n        };\n        s.reLoop = function (updatePosition) {\n            var oldIndex = s.activeIndex - s.loopedSlides;\n            s.destroyLoop();\n            s.createLoop();\n            s.updateSlidesSize();\n            if (updatePosition) {\n                s.slideTo(oldIndex + s.loopedSlides, 0, false);\n            }\n        \n        };\n        s.fixLoop = function () {\n            var newIndex;\n            //Fix For Negative Oversliding\n            if (s.activeIndex < s.loopedSlides) {\n                newIndex = s.slides.length - s.loopedSlides * 3 + s.activeIndex;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n            //Fix For Positive Oversliding\n            else if ((s.params.slidesPerView === 'auto' && s.activeIndex >= s.loopedSlides * 2) || (s.activeIndex > s.slides.length - s.params.slidesPerView * 2)) {\n                newIndex = -s.slides.length + s.activeIndex + s.loopedSlides;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n        };\n        /*=========================\n          Append/Prepend/Remove Slides\n          ===========================*/\n        s.appendSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.append(slides[i]);\n                }\n            }\n            else {\n                s.wrapper.append(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n        };\n        s.prependSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            var newActiveIndex = s.activeIndex + 1;\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.prepend(slides[i]);\n                }\n                newActiveIndex = s.activeIndex + slides.length;\n            }\n            else {\n                s.wrapper.prepend(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            s.slideTo(newActiveIndex, 0, false);\n        };\n        s.removeSlide = function (slidesIndexes) {\n            if (s.params.loop) {\n                s.destroyLoop();\n                s.slides = s.wrapper.children('.' + s.params.slideClass);\n            }\n            var newActiveIndex = s.activeIndex,\n                indexToRemove;\n            if (typeof slidesIndexes === 'object' && slidesIndexes.length) {\n                for (var i = 0; i < slidesIndexes.length; i++) {\n                    indexToRemove = slidesIndexes[i];\n                    if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                    if (indexToRemove < newActiveIndex) newActiveIndex--;\n                }\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n            else {\n                indexToRemove = slidesIndexes;\n                if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                if (indexToRemove < newActiveIndex) newActiveIndex--;\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n        \n            if (s.params.loop) {\n                s.createLoop();\n            }\n        \n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            if (s.params.loop) {\n                s.slideTo(newActiveIndex + s.loopedSlides, 0, false);\n            }\n            else {\n                s.slideTo(newActiveIndex, 0, false);\n            }\n        \n        };\n        s.removeAllSlides = function () {\n            var slidesIndexes = [];\n            for (var i = 0; i < s.slides.length; i++) {\n                slidesIndexes.push(i);\n            }\n            s.removeSlide(slidesIndexes);\n        };\n        \n\n        /*=========================\n          Effects\n          ===========================*/\n        s.effects = {\n            fade: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var offset = slide[0].swiperSlideOffset;\n                        var tx = -offset;\n                        if (!s.params.virtualTranslate) tx = tx - s.translate;\n                        var ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n                        var slideOpacity = s.params.fade.crossFade ?\n                                Math.max(1 - Math.abs(slide[0].progress), 0) :\n                                1 + Math.min(Math.max(slide[0].progress, -1), 0);\n                        slide\n                            .css({\n                                opacity: slideOpacity\n                            })\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px)');\n        \n                    }\n        \n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            flip: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var progress = slide[0].progress;\n                        if (s.params.flip.limitRotation) {\n                            progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        }\n                        var offset = slide[0].swiperSlideOffset;\n                        var rotate = -180 * progress,\n                            rotateY = rotate,\n                            rotateX = 0,\n                            tx = -offset,\n                            ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                            rotateX = -rotateY;\n                            rotateY = 0;\n                        }\n                        else if (s.rtl) {\n                            rotateY = -rotateY;\n                        }\n        \n                        slide[0].style.zIndex = -Math.abs(Math.round(progress)) + s.slides.length;\n        \n                        if (s.params.flip.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n        \n                        slide\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)');\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.eq(s.activeIndex).transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            if (!$(this).hasClass(s.params.slideActiveClass)) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            cube: {\n                setTranslate: function () {\n                    var wrapperRotate = 0, cubeShadow;\n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow = s.wrapper.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.wrapper.append(cubeShadow);\n                            }\n                            cubeShadow.css({height: s.width + 'px'});\n                        }\n                        else {\n                            cubeShadow = s.container.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.container.append(cubeShadow);\n                            }\n                        }\n                    }\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideAngle = i * 90;\n                        var round = Math.floor(slideAngle / 360);\n                        if (s.rtl) {\n                            slideAngle = -slideAngle;\n                            round = Math.floor(-slideAngle / 360);\n                        }\n                        var progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        var tx = 0, ty = 0, tz = 0;\n                        if (i % 4 === 0) {\n                            tx = - round * 4 * s.size;\n                            tz = 0;\n                        }\n                        else if ((i - 1) % 4 === 0) {\n                            tx = 0;\n                            tz = - round * 4 * s.size;\n                        }\n                        else if ((i - 2) % 4 === 0) {\n                            tx = s.size + round * 4 * s.size;\n                            tz = s.size;\n                        }\n                        else if ((i - 3) % 4 === 0) {\n                            tx = - s.size;\n                            tz = 3 * s.size + s.size * 4 * round;\n                        }\n                        if (s.rtl) {\n                            tx = -tx;\n                        }\n        \n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n        \n                        var transform = 'rotateX(' + (s.isHorizontal() ? 0 : -slideAngle) + 'deg) rotateY(' + (s.isHorizontal() ? slideAngle : 0) + 'deg) translate3d(' + tx + 'px, ' + ty + 'px, ' + tz + 'px)';\n                        if (progress <= 1 && progress > -1) {\n                            wrapperRotate = i * 90 + progress * 90;\n                            if (s.rtl) wrapperRotate = -i * 90 - progress * 90;\n                        }\n                        slide.transform(transform);\n                        if (s.params.cube.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n                    }\n                    s.wrapper.css({\n                        '-webkit-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-moz-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-ms-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        'transform-origin': '50% 50% -' + (s.size / 2) + 'px'\n                    });\n        \n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow.transform('translate3d(0px, ' + (s.width / 2 + s.params.cube.shadowOffset) + 'px, ' + (-s.width / 2) + 'px) rotateX(90deg) rotateZ(0deg) scale(' + (s.params.cube.shadowScale) + ')');\n                        }\n                        else {\n                            var shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n                            var multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n                            var scale1 = s.params.cube.shadowScale,\n                                scale2 = s.params.cube.shadowScale / multiplier,\n                                offset = s.params.cube.shadowOffset;\n                            cubeShadow.transform('scale3d(' + scale1 + ', 1, ' + scale2 + ') translate3d(0px, ' + (s.height / 2 + offset) + 'px, ' + (-s.height / 2 / scale2) + 'px) rotateX(-90deg)');\n                        }\n                    }\n                    var zFactor = (s.isSafari || s.isUiWebView) ? (-s.size / 2) : 0;\n                    s.wrapper.transform('translate3d(0px,0,' + zFactor + 'px) rotateX(' + (s.isHorizontal() ? 0 : wrapperRotate) + 'deg) rotateY(' + (s.isHorizontal() ? -wrapperRotate : 0) + 'deg)');\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.cube.shadow && !s.isHorizontal()) {\n                        s.container.find('.swiper-cube-shadow').transition(duration);\n                    }\n                }\n            },\n            coverflow: {\n                setTranslate: function () {\n                    var transform = s.translate;\n                    var center = s.isHorizontal() ? -transform + s.width / 2 : -transform + s.height / 2;\n                    var rotate = s.isHorizontal() ? s.params.coverflow.rotate: -s.params.coverflow.rotate;\n                    var translate = s.params.coverflow.depth;\n                    //Each slide offset from center\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideSize = s.slidesSizesGrid[i];\n                        var slideOffset = slide[0].swiperSlideOffset;\n                        var offsetMultiplier = (center - slideOffset - slideSize / 2) / slideSize * s.params.coverflow.modifier;\n        \n                        var rotateY = s.isHorizontal() ? rotate * offsetMultiplier : 0;\n                        var rotateX = s.isHorizontal() ? 0 : rotate * offsetMultiplier;\n                        // var rotateZ = 0\n                        var translateZ = -translate * Math.abs(offsetMultiplier);\n        \n                        var translateY = s.isHorizontal() ? 0 : s.params.coverflow.stretch * (offsetMultiplier);\n                        var translateX = s.isHorizontal() ? s.params.coverflow.stretch * (offsetMultiplier) : 0;\n        \n                        //Fix for ultra small values\n                        if (Math.abs(translateX) < 0.001) translateX = 0;\n                        if (Math.abs(translateY) < 0.001) translateY = 0;\n                        if (Math.abs(translateZ) < 0.001) translateZ = 0;\n                        if (Math.abs(rotateY) < 0.001) rotateY = 0;\n                        if (Math.abs(rotateX) < 0.001) rotateX = 0;\n        \n                        var slideTransform = 'translate3d(' + translateX + 'px,' + translateY + 'px,' + translateZ + 'px)  rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';\n        \n                        slide.transform(slideTransform);\n                        slide[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n                        if (s.params.coverflow.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0;\n                        }\n                    }\n        \n                    //Set correct perspective for IE10\n                    if (s.browser.ie) {\n                        var ws = s.wrapper[0].style;\n                        ws.perspectiveOrigin = center + 'px 50%';\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                }\n            }\n        };\n\n        /*=========================\n          Images Lazy Loading\n          ===========================*/\n        s.lazy = {\n            initialImageLoaded: false,\n            loadImageInSlide: function (index, loadInDuplicate) {\n                if (typeof index === 'undefined') return;\n                if (typeof loadInDuplicate === 'undefined') loadInDuplicate = true;\n                if (s.slides.length === 0) return;\n        \n                var slide = s.slides.eq(index);\n                var img = slide.find('.' + s.params.lazyLoadingClass + ':not(.' + s.params.lazyStatusLoadedClass + '):not(.' + s.params.lazyStatusLoadingClass + ')');\n                if (slide.hasClass(s.params.lazyLoadingClass) && !slide.hasClass(s.params.lazyStatusLoadedClass) && !slide.hasClass(s.params.lazyStatusLoadingClass)) {\n                    img = img.add(slide[0]);\n                }\n                if (img.length === 0) return;\n        \n                img.each(function () {\n                    var _img = $(this);\n                    _img.addClass(s.params.lazyStatusLoadingClass);\n                    var background = _img.attr('data-background');\n                    var src = _img.attr('data-src'),\n                        srcset = _img.attr('data-srcset'),\n                        sizes = _img.attr('data-sizes');\n                    s.loadImage(_img[0], (src || background), srcset, sizes, false, function () {\n                        if (background) {\n                            _img.css('background-image', 'url(\"' + background + '\")');\n                            _img.removeAttr('data-background');\n                        }\n                        else {\n                            if (srcset) {\n                                _img.attr('srcset', srcset);\n                                _img.removeAttr('data-srcset');\n                            }\n                            if (sizes) {\n                                _img.attr('sizes', sizes);\n                                _img.removeAttr('data-sizes');\n                            }\n                            if (src) {\n                                _img.attr('src', src);\n                                _img.removeAttr('data-src');\n                            }\n        \n                        }\n        \n                        _img.addClass(s.params.lazyStatusLoadedClass).removeClass(s.params.lazyStatusLoadingClass);\n                        slide.find('.' + s.params.lazyPreloaderClass + ', .' + s.params.preloaderClass).remove();\n                        if (s.params.loop && loadInDuplicate) {\n                            var slideOriginalIndex = slide.attr('data-swiper-slide-index');\n                            if (slide.hasClass(s.params.slideDuplicateClass)) {\n                                var originalSlide = s.wrapper.children('[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')');\n                                s.lazy.loadImageInSlide(originalSlide.index(), false);\n                            }\n                            else {\n                                var duplicatedSlide = s.wrapper.children('.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]');\n                                s.lazy.loadImageInSlide(duplicatedSlide.index(), false);\n                            }\n                        }\n                        s.emit('onLazyImageReady', s, slide[0], _img[0]);\n                    });\n        \n                    s.emit('onLazyImageLoad', s, slide[0], _img[0]);\n                });\n        \n            },\n            load: function () {\n                var i;\n                var slidesPerView = s.params.slidesPerView;\n                if (slidesPerView === 'auto') {\n                    slidesPerView = 0;\n                }\n                if (!s.lazy.initialImageLoaded) s.lazy.initialImageLoaded = true;\n                if (s.params.watchSlidesVisibility) {\n                    s.wrapper.children('.' + s.params.slideVisibleClass).each(function () {\n                        s.lazy.loadImageInSlide($(this).index());\n                    });\n                }\n                else {\n                    if (slidesPerView > 1) {\n                        for (i = s.activeIndex; i < s.activeIndex + slidesPerView ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        s.lazy.loadImageInSlide(s.activeIndex);\n                    }\n                }\n                if (s.params.lazyLoadingInPrevNext) {\n                    if (slidesPerView > 1 || (s.params.lazyLoadingInPrevNextAmount && s.params.lazyLoadingInPrevNextAmount > 1)) {\n                        var amount = s.params.lazyLoadingInPrevNextAmount;\n                        var spv = slidesPerView;\n                        var maxIndex = Math.min(s.activeIndex + spv + Math.max(amount, spv), s.slides.length);\n                        var minIndex = Math.max(s.activeIndex - Math.max(spv, amount), 0);\n                        // Next Slides\n                        for (i = s.activeIndex + slidesPerView; i < maxIndex; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                        // Prev Slides\n                        for (i = minIndex; i < s.activeIndex ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        var nextSlide = s.wrapper.children('.' + s.params.slideNextClass);\n                        if (nextSlide.length > 0) s.lazy.loadImageInSlide(nextSlide.index());\n        \n                        var prevSlide = s.wrapper.children('.' + s.params.slidePrevClass);\n                        if (prevSlide.length > 0) s.lazy.loadImageInSlide(prevSlide.index());\n                    }\n                }\n            },\n            onTransitionStart: function () {\n                if (s.params.lazyLoading) {\n                    if (s.params.lazyLoadingOnTransitionStart || (!s.params.lazyLoadingOnTransitionStart && !s.lazy.initialImageLoaded)) {\n                        s.lazy.load();\n                    }\n                }\n            },\n            onTransitionEnd: function () {\n                if (s.params.lazyLoading && !s.params.lazyLoadingOnTransitionStart) {\n                    s.lazy.load();\n                }\n            }\n        };\n        \n\n        /*=========================\n          Scrollbar\n          ===========================*/\n        s.scrollbar = {\n            isTouched: false,\n            setDragPosition: function (e) {\n                var sb = s.scrollbar;\n                var x = 0, y = 0;\n                var translate;\n                var pointerPosition = s.isHorizontal() ?\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX) :\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY) ;\n                var position = (pointerPosition) - sb.track.offset()[s.isHorizontal() ? 'left' : 'top'] - sb.dragSize / 2;\n                var positionMin = -s.minTranslate() * sb.moveDivider;\n                var positionMax = -s.maxTranslate() * sb.moveDivider;\n                if (position < positionMin) {\n                    position = positionMin;\n                }\n                else if (position > positionMax) {\n                    position = positionMax;\n                }\n                position = -position / sb.moveDivider;\n                s.updateProgress(position);\n                s.setWrapperTranslate(position, true);\n            },\n            dragStart: function (e) {\n                var sb = s.scrollbar;\n                sb.isTouched = true;\n                e.preventDefault();\n                e.stopPropagation();\n        \n                sb.setDragPosition(e);\n                clearTimeout(sb.dragTimeout);\n        \n                sb.track.transition(0);\n                if (s.params.scrollbarHide) {\n                    sb.track.css('opacity', 1);\n                }\n                s.wrapper.transition(100);\n                sb.drag.transition(100);\n                s.emit('onScrollbarDragStart', s);\n            },\n            dragMove: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                if (e.preventDefault) e.preventDefault();\n                else e.returnValue = false;\n                sb.setDragPosition(e);\n                s.wrapper.transition(0);\n                sb.track.transition(0);\n                sb.drag.transition(0);\n                s.emit('onScrollbarDragMove', s);\n            },\n            dragEnd: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                sb.isTouched = false;\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.dragTimeout);\n                    sb.dragTimeout = setTimeout(function () {\n                        sb.track.css('opacity', 0);\n                        sb.track.transition(400);\n                    }, 1000);\n        \n                }\n                s.emit('onScrollbarDragEnd', s);\n                if (s.params.scrollbarSnapOnRelease) {\n                    s.slideReset();\n                }\n            },\n            draggableEvents: (function () {\n                if ((s.params.simulateTouch === false && !s.support.touch)) return s.touchEventsDesktop;\n                else return s.touchEvents;\n            })(),\n            enableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).on(sb.draggableEvents.start, sb.dragStart);\n                $(target).on(sb.draggableEvents.move, sb.dragMove);\n                $(target).on(sb.draggableEvents.end, sb.dragEnd);\n            },\n            disableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).off(sb.draggableEvents.start, sb.dragStart);\n                $(target).off(sb.draggableEvents.move, sb.dragMove);\n                $(target).off(sb.draggableEvents.end, sb.dragEnd);\n            },\n            set: function () {\n                if (!s.params.scrollbar) return;\n                var sb = s.scrollbar;\n                sb.track = $(s.params.scrollbar);\n                if (s.params.uniqueNavElements && typeof s.params.scrollbar === 'string' && sb.track.length > 1 && s.container.find(s.params.scrollbar).length === 1) {\n                    sb.track = s.container.find(s.params.scrollbar);\n                }\n                sb.drag = sb.track.find('.swiper-scrollbar-drag');\n                if (sb.drag.length === 0) {\n                    sb.drag = $('<div class=\"swiper-scrollbar-drag\"></div>');\n                    sb.track.append(sb.drag);\n                }\n                sb.drag[0].style.width = '';\n                sb.drag[0].style.height = '';\n                sb.trackSize = s.isHorizontal() ? sb.track[0].offsetWidth : sb.track[0].offsetHeight;\n        \n                sb.divider = s.size / s.virtualSize;\n                sb.moveDivider = sb.divider * (sb.trackSize / s.size);\n                sb.dragSize = sb.trackSize * sb.divider;\n        \n                if (s.isHorizontal()) {\n                    sb.drag[0].style.width = sb.dragSize + 'px';\n                }\n                else {\n                    sb.drag[0].style.height = sb.dragSize + 'px';\n                }\n        \n                if (sb.divider >= 1) {\n                    sb.track[0].style.display = 'none';\n                }\n                else {\n                    sb.track[0].style.display = '';\n                }\n                if (s.params.scrollbarHide) {\n                    sb.track[0].style.opacity = 0;\n                }\n            },\n            setTranslate: function () {\n                if (!s.params.scrollbar) return;\n                var diff;\n                var sb = s.scrollbar;\n                var translate = s.translate || 0;\n                var newPos;\n        \n                var newSize = sb.dragSize;\n                newPos = (sb.trackSize - sb.dragSize) * s.progress;\n                if (s.rtl && s.isHorizontal()) {\n                    newPos = -newPos;\n                    if (newPos > 0) {\n                        newSize = sb.dragSize - newPos;\n                        newPos = 0;\n                    }\n                    else if (-newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize + newPos;\n                    }\n                }\n                else {\n                    if (newPos < 0) {\n                        newSize = sb.dragSize + newPos;\n                        newPos = 0;\n                    }\n                    else if (newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize - newPos;\n                    }\n                }\n                if (s.isHorizontal()) {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(' + (newPos) + 'px, 0, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateX(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.width = newSize + 'px';\n                }\n                else {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(0px, ' + (newPos) + 'px, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateY(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.height = newSize + 'px';\n                }\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.timeout);\n                    sb.track[0].style.opacity = 1;\n                    sb.timeout = setTimeout(function () {\n                        sb.track[0].style.opacity = 0;\n                        sb.track.transition(400);\n                    }, 1000);\n                }\n            },\n            setTransition: function (duration) {\n                if (!s.params.scrollbar) return;\n                s.scrollbar.drag.transition(duration);\n            }\n        };\n\n        /*=========================\n          Controller\n          ===========================*/\n        s.controller = {\n            LinearSpline: function (x, y) {\n                this.x = x;\n                this.y = y;\n                this.lastIndex = x.length - 1;\n                // Given an x value (x2), return the expected y2 value:\n                // (x1,y1) is the known point before given value,\n                // (x3,y3) is the known point after given value.\n                var i1, i3;\n                var l = this.x.length;\n        \n                this.interpolate = function (x2) {\n                    if (!x2) return 0;\n        \n                    // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n                    i3 = binarySearch(this.x, x2);\n                    i1 = i3 - 1;\n        \n                    // We have our indexes i1 & i3, so we can calculate already:\n                    // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n                    return ((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1]) + this.y[i1];\n                };\n        \n                var binarySearch = (function() {\n                    var maxIndex, minIndex, guess;\n                    return function(array, val) {\n                        minIndex = -1;\n                        maxIndex = array.length;\n                        while (maxIndex - minIndex > 1)\n                            if (array[guess = maxIndex + minIndex >> 1] <= val) {\n                                minIndex = guess;\n                            } else {\n                                maxIndex = guess;\n                            }\n                        return maxIndex;\n                    };\n                })();\n            },\n            //xxx: for now i will just save one spline function to to\n            getInterpolateFunction: function(c){\n                if(!s.controller.spline) s.controller.spline = s.params.loop ?\n                    new s.controller.LinearSpline(s.slidesGrid, c.slidesGrid) :\n                    new s.controller.LinearSpline(s.snapGrid, c.snapGrid);\n            },\n            setTranslate: function (translate, byController) {\n               var controlled = s.params.control;\n               var multiplier, controlledTranslate;\n               function setControlledTranslate(c) {\n                    // this will create an Interpolate function based on the snapGrids\n                    // x is the Grid of the scrolled scroller and y will be the controlled scroller\n                    // it makes sense to create this only once and recall it for the interpolation\n                    // the function does a lot of value caching for performance\n                    translate = c.rtl && c.params.direction === 'horizontal' ? -s.translate : s.translate;\n                    if (s.params.controlBy === 'slide') {\n                        s.controller.getInterpolateFunction(c);\n                        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n                        // but it did not work out\n                        controlledTranslate = -s.controller.spline.interpolate(-translate);\n                    }\n        \n                    if(!controlledTranslate || s.params.controlBy === 'container'){\n                        multiplier = (c.maxTranslate() - c.minTranslate()) / (s.maxTranslate() - s.minTranslate());\n                        controlledTranslate = (translate - s.minTranslate()) * multiplier + c.minTranslate();\n                    }\n        \n                    if (s.params.controlInverse) {\n                        controlledTranslate = c.maxTranslate() - controlledTranslate;\n                    }\n                    c.updateProgress(controlledTranslate);\n                    c.setWrapperTranslate(controlledTranslate, false, s);\n                    c.updateActiveIndex();\n               }\n               if (s.isArray(controlled)) {\n                   for (var i = 0; i < controlled.length; i++) {\n                       if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                           setControlledTranslate(controlled[i]);\n                       }\n                   }\n               }\n               else if (controlled instanceof Swiper && byController !== controlled) {\n        \n                   setControlledTranslate(controlled);\n               }\n            },\n            setTransition: function (duration, byController) {\n                var controlled = s.params.control;\n                var i;\n                function setControlledTransition(c) {\n                    c.setWrapperTransition(duration, s);\n                    if (duration !== 0) {\n                        c.onTransitionStart();\n                        c.wrapper.transitionEnd(function(){\n                            if (!controlled) return;\n                            if (c.params.loop && s.params.controlBy === 'slide') {\n                                c.fixLoop();\n                            }\n                            c.onTransitionEnd();\n        \n                        });\n                    }\n                }\n                if (s.isArray(controlled)) {\n                    for (i = 0; i < controlled.length; i++) {\n                        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                            setControlledTransition(controlled[i]);\n                        }\n                    }\n                }\n                else if (controlled instanceof Swiper && byController !== controlled) {\n                    setControlledTransition(controlled);\n                }\n            }\n        };\n\n        /*=========================\n          Hash Navigation\n          ===========================*/\n        s.hashnav = {\n            onHashCange: function (e, a) {\n                var newHash = document.location.hash.replace('#', '');\n                var activeSlideHash = s.slides.eq(s.activeIndex).attr('data-hash');\n                if (newHash !== activeSlideHash) {\n                    s.slideTo(s.wrapper.children('.' + s.params.slideClass + '[data-hash=\"' + (newHash) + '\"]').index());\n                }\n            },\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n                $(window)[action]('hashchange', s.hashnav.onHashCange);\n            },\n            setHash: function () {\n                if (!s.hashnav.initialized || !s.params.hashnav) return;\n                if (s.params.replaceState && window.history && window.history.replaceState) {\n                    window.history.replaceState(null, null, ('#' + s.slides.eq(s.activeIndex).attr('data-hash') || ''));\n                } else {\n                    var slide = s.slides.eq(s.activeIndex);\n                    var hash = slide.attr('data-hash') || slide.attr('data-history');\n                    document.location.hash = hash || '';\n                }\n            },\n            init: function () {\n                if (!s.params.hashnav || s.params.history) return;\n                s.hashnav.initialized = true;\n                var hash = document.location.hash.replace('#', '');\n                if (hash) {\n                    var speed = 0;\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n                        if (slideHash === hash && !slide.hasClass(s.params.slideDuplicateClass)) {\n                            var index = slide.index();\n                            s.slideTo(index, speed, s.params.runCallbacksOnInit, true);\n                        }\n                    }\n                }\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents();\n            },\n            destroy: function () {\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          History Api with fallback to Hashnav\n          ===========================*/\n        s.history = {\n            init: function () {\n                if (!s.params.history) return;\n                if (!window.history || !window.history.pushState) {\n                    s.params.history = false;\n                    s.params.hashnav = true;\n                    return;\n                }\n                s.history.initialized = true;\n                this.paths = this.getPathValues();\n                if (!this.paths.key && !this.paths.value) return;\n                this.scrollToSlide(0, this.paths.value, s.params.runCallbacksOnInit);\n                if (!s.params.replaceState) {\n                    window.addEventListener('popstate', this.setHistoryPopState);\n                }\n            },\n            setHistoryPopState: function() {\n                s.history.paths = s.history.getPathValues();\n                s.history.scrollToSlide(s.params.speed, s.history.paths.value, false);\n            },\n            getPathValues: function() {\n                var pathArray = window.location.pathname.slice(1).split('/');\n                var total = pathArray.length;\n                var key = pathArray[total - 2];\n                var value = pathArray[total - 1];\n                return { key: key, value: value };\n            },\n            setHistory: function (key, index) {\n                if (!s.history.initialized || !s.params.history) return;\n                var slide = s.slides.eq(index);\n                var value = this.slugify(slide.attr('data-history'));\n                if (!window.location.pathname.includes(key)) {\n                    value = key + '/' + value;\n                }\n                if (s.params.replaceState) {\n                    window.history.replaceState(null, null, value);\n                } else {\n                    window.history.pushState(null, null, value);\n                }\n            },\n            slugify: function(text) {\n                return text.toString().toLowerCase()\n                    .replace(/\\s+/g, '-')\n                    .replace(/[^\\w\\-]+/g, '')\n                    .replace(/\\-\\-+/g, '-')\n                    .replace(/^-+/, '')\n                    .replace(/-+$/, '');\n            },\n            scrollToSlide: function(speed, value, runCallbacks) {\n                if (value) {\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideHistory = this.slugify(slide.attr('data-history'));\n                        if (slideHistory === value && !slide.hasClass(s.params.slideDuplicateClass)) {\n                            var index = slide.index();\n                            s.slideTo(index, speed, runCallbacks);\n                        }\n                    }\n                } else {\n                    s.slideTo(0, speed, runCallbacks);\n                }\n            }\n        };\n\n        /*=========================\n          Keyboard Control\n          ===========================*/\n        function handleKeyboard(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var kc = e.keyCode || e.charCode;\n            // Directions locks\n            if (!s.params.allowSwipeToNext && (s.isHorizontal() && kc === 39 || !s.isHorizontal() && kc === 40)) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && (s.isHorizontal() && kc === 37 || !s.isHorizontal() && kc === 38)) {\n                return false;\n            }\n            if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n                return;\n            }\n            if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n                return;\n            }\n            if (kc === 37 || kc === 39 || kc === 38 || kc === 40) {\n                var inView = false;\n                //Check that swiper should be inside of visible area of window\n                if (s.container.parents('.' + s.params.slideClass).length > 0 && s.container.parents('.' + s.params.slideActiveClass).length === 0) {\n                    return;\n                }\n                var windowScroll = {\n                    left: window.pageXOffset,\n                    top: window.pageYOffset\n                };\n                var windowWidth = window.innerWidth;\n                var windowHeight = window.innerHeight;\n                var swiperOffset = s.container.offset();\n                if (s.rtl) swiperOffset.left = swiperOffset.left - s.container[0].scrollLeft;\n                var swiperCoord = [\n                    [swiperOffset.left, swiperOffset.top],\n                    [swiperOffset.left + s.width, swiperOffset.top],\n                    [swiperOffset.left, swiperOffset.top + s.height],\n                    [swiperOffset.left + s.width, swiperOffset.top + s.height]\n                ];\n                for (var i = 0; i < swiperCoord.length; i++) {\n                    var point = swiperCoord[i];\n                    if (\n                        point[0] >= windowScroll.left && point[0] <= windowScroll.left + windowWidth &&\n                        point[1] >= windowScroll.top && point[1] <= windowScroll.top + windowHeight\n                    ) {\n                        inView = true;\n                    }\n        \n                }\n                if (!inView) return;\n            }\n            if (s.isHorizontal()) {\n                if (kc === 37 || kc === 39) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if ((kc === 39 && !s.rtl) || (kc === 37 && s.rtl)) s.slideNext();\n                if ((kc === 37 && !s.rtl) || (kc === 39 && s.rtl)) s.slidePrev();\n            }\n            else {\n                if (kc === 38 || kc === 40) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if (kc === 40) s.slideNext();\n                if (kc === 38) s.slidePrev();\n            }\n        }\n        s.disableKeyboardControl = function () {\n            s.params.keyboardControl = false;\n            $(document).off('keydown', handleKeyboard);\n        };\n        s.enableKeyboardControl = function () {\n            s.params.keyboardControl = true;\n            $(document).on('keydown', handleKeyboard);\n        };\n        \n\n        /*=========================\n          Mousewheel Control\n          ===========================*/\n        s.mousewheel = {\n            event: false,\n            lastScrollTime: (new window.Date()).getTime()\n        };\n        if (s.params.mousewheelControl) {\n            /**\n             * The best combination if you prefer spinX + spinY normalization.  It favors\n             * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n             * 'wheel' event, making spin speed determination impossible.\n             */\n            s.mousewheel.event = (navigator.userAgent.indexOf('firefox') > -1) ?\n                'DOMMouseScroll' :\n                isEventSupported() ?\n                    'wheel' : 'mousewheel';\n        }\n        \n        function isEventSupported() {\n            var eventName = 'onwheel';\n            var isSupported = eventName in document;\n        \n            if (!isSupported) {\n                var element = document.createElement('div');\n                element.setAttribute(eventName, 'return;');\n                isSupported = typeof element[eventName] === 'function';\n            }\n        \n            if (!isSupported &&\n                document.implementation &&\n                document.implementation.hasFeature &&\n                    // always returns true in newer browsers as per the standard.\n                    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n                document.implementation.hasFeature('', '') !== true ) {\n                // This is the only way to test support for the `wheel` event in IE9+.\n                isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n            }\n        \n            return isSupported;\n        }\n        \n        function handleMousewheel(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var delta = 0;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            var data = normalizeWheel( e );\n        \n            if (s.params.mousewheelForceToAxis) {\n                if (s.isHorizontal()) {\n                    if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = data.pixelX * rtlFactor;\n                    else return;\n                }\n                else {\n                    if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = data.pixelY;\n                    else return;\n                }\n            }\n            else {\n                delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? - data.pixelX * rtlFactor : - data.pixelY;\n            }\n        \n            if (delta === 0) return;\n        \n            if (s.params.mousewheelInvert) delta = -delta;\n        \n            if (!s.params.freeMode) {\n                if ((new window.Date()).getTime() - s.mousewheel.lastScrollTime > 60) {\n                    if (delta < 0) {\n                        if ((!s.isEnd || s.params.loop) && !s.animating) {\n                            s.slideNext();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                    else {\n                        if ((!s.isBeginning || s.params.loop) && !s.animating) {\n                            s.slidePrev();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                }\n                s.mousewheel.lastScrollTime = (new window.Date()).getTime();\n        \n            }\n            else {\n                //Freemode or scrollContainer:\n                var position = s.getWrapperTranslate() + delta * s.params.mousewheelSensitivity;\n                var wasBeginning = s.isBeginning,\n                    wasEnd = s.isEnd;\n        \n                if (position >= s.minTranslate()) position = s.minTranslate();\n                if (position <= s.maxTranslate()) position = s.maxTranslate();\n        \n                s.setWrapperTransition(0);\n                s.setWrapperTranslate(position);\n                s.updateProgress();\n                s.updateActiveIndex();\n        \n                if (!wasBeginning && s.isBeginning || !wasEnd && s.isEnd) {\n                    s.updateClasses();\n                }\n        \n                if (s.params.freeModeSticky) {\n                    clearTimeout(s.mousewheel.timeout);\n                    s.mousewheel.timeout = setTimeout(function () {\n                        s.slideReset();\n                    }, 300);\n                }\n                else {\n                    if (s.params.lazyLoading && s.lazy) {\n                        s.lazy.load();\n                    }\n                }\n                // Emit event\n                s.emit('onScroll', s, e);\n        \n                // Stop autoplay\n                if (s.params.autoplay && s.params.autoplayDisableOnInteraction) s.stopAutoplay();\n        \n                // Return page scroll on edge positions\n                if (position === 0 || position === s.maxTranslate()) return;\n            }\n        \n            if (e.preventDefault) e.preventDefault();\n            else e.returnValue = false;\n            return false;\n        }\n        s.disableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.off(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        s.enableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.on(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        /**\n         * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n         * complicated, thus this doc is long and (hopefully) detailed enough to answer\n         * your questions.\n         *\n         * If you need to react to the mouse wheel in a predictable way, this code is\n         * like your bestest friend. * hugs *\n         *\n         * As of today, there are 4 DOM event types you can listen to:\n         *\n         *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n         *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n         *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n         *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n         *\n         * So what to do?  The is the best:\n         *\n         *   normalizeWheel.getEventType();\n         *\n         * In your event callback, use this code to get sane interpretation of the\n         * deltas.  This code will return an object with properties:\n         *\n         *   spinX   -- normalized spin speed (use for zoom) - x plane\n         *   spinY   -- \" - y plane\n         *   pixelX  -- normalized distance (to pixels) - x plane\n         *   pixelY  -- \" - y plane\n         *\n         * Wheel values are provided by the browser assuming you are using the wheel to\n         * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n         * significantly on different platforms and browsers, forgetting that you can\n         * scroll at different speeds.  Some devices (like trackpads) emit more events\n         * at smaller increments with fine granularity, and some emit massive jumps with\n         * linear speed or acceleration.\n         *\n         * This code does its best to normalize the deltas for you:\n         *\n         *   - spin is trying to normalize how far the wheel was spun (or trackpad\n         *     dragged).  This is super useful for zoom support where you want to\n         *     throw away the chunky scroll steps on the PC and make those equal to\n         *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n         *     resolve a single slow step on a wheel to 1.\n         *\n         *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n         *     get the crazy differences between browsers, but at least it'll be in\n         *     pixels!\n         *\n         *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n         *     should translate to positive value zooming IN, negative zooming OUT.\n         *     This matches the newer 'wheel' event.\n         *\n         * Why are there spinX, spinY (or pixels)?\n         *\n         *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n         *     with a mouse.  It results in side-scrolling in the browser by default.\n         *\n         *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n         *\n         *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n         *     probably is by browsers in conjunction with fancy 3D controllers .. but\n         *     you know.\n         *\n         * Implementation info:\n         *\n         * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n         * average mouse:\n         *\n         *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n         *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n         *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n         *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n         *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n         *\n         * On the trackpad:\n         *\n         *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n         *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n         *\n         * On other/older browsers.. it's more complicated as there can be multiple and\n         * also missing delta values.\n         *\n         * The 'wheel' event is more standard:\n         *\n         * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n         *\n         * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n         * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n         * backward compatibility with older events.  Those other values help us\n         * better normalize spin speed.  Example of what the browsers provide:\n         *\n         *                          | event.wheelDelta | event.detail\n         *        ------------------+------------------+--------------\n         *          Safari v5/OS X  |       -120       |       0\n         *          Safari v5/Win7  |       -120       |       0\n         *         Chrome v17/OS X  |       -120       |       0\n         *         Chrome v17/Win7  |       -120       |       0\n         *                IE9/Win7  |       -120       |   undefined\n         *         Firefox v4/OS X  |     undefined    |       1\n         *         Firefox v4/Win7  |     undefined    |       3\n         *\n         */\n        function normalizeWheel( /*object*/ event ) /*object*/ {\n            // Reasonable defaults\n            var PIXEL_STEP = 10;\n            var LINE_HEIGHT = 40;\n            var PAGE_HEIGHT = 800;\n        \n            var sX = 0, sY = 0,       // spinX, spinY\n                pX = 0, pY = 0;       // pixelX, pixelY\n        \n            // Legacy\n            if( 'detail' in event ) {\n                sY = event.detail;\n            }\n            if( 'wheelDelta' in event ) {\n                sY = -event.wheelDelta / 120;\n            }\n            if( 'wheelDeltaY' in event ) {\n                sY = -event.wheelDeltaY / 120;\n            }\n            if( 'wheelDeltaX' in event ) {\n                sX = -event.wheelDeltaX / 120;\n            }\n        \n            // side scrolling on FF with DOMMouseScroll\n            if( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n                sX = sY;\n                sY = 0;\n            }\n        \n            pX = sX * PIXEL_STEP;\n            pY = sY * PIXEL_STEP;\n        \n            if( 'deltaY' in event ) {\n                pY = event.deltaY;\n            }\n            if( 'deltaX' in event ) {\n                pX = event.deltaX;\n            }\n        \n            if( (pX || pY) && event.deltaMode ) {\n                if( event.deltaMode === 1 ) {          // delta in LINE units\n                    pX *= LINE_HEIGHT;\n                    pY *= LINE_HEIGHT;\n                } else {                             // delta in PAGE units\n                    pX *= PAGE_HEIGHT;\n                    pY *= PAGE_HEIGHT;\n                }\n            }\n        \n            // Fall-back if spin cannot be determined\n            if( pX && !sX ) {\n                sX = (pX < 1) ? -1 : 1;\n            }\n            if( pY && !sY ) {\n                sY = (pY < 1) ? -1 : 1;\n            }\n        \n            return {\n                spinX: sX,\n                spinY: sY,\n                pixelX: pX,\n                pixelY: pY\n            };\n        }\n\n        /*=========================\n          Parallax\n          ===========================*/\n        function setParallaxTransform(el, progress) {\n            el = $(el);\n            var p, pX, pY;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            p = el.attr('data-swiper-parallax') || '0';\n            pX = el.attr('data-swiper-parallax-x');\n            pY = el.attr('data-swiper-parallax-y');\n            if (pX || pY) {\n                pX = pX || '0';\n                pY = pY || '0';\n            }\n            else {\n                if (s.isHorizontal()) {\n                    pX = p;\n                    pY = '0';\n                }\n                else {\n                    pY = p;\n                    pX = '0';\n                }\n            }\n        \n            if ((pX).indexOf('%') >= 0) {\n                pX = parseInt(pX, 10) * progress * rtlFactor + '%';\n            }\n            else {\n                pX = pX * progress * rtlFactor + 'px' ;\n            }\n            if ((pY).indexOf('%') >= 0) {\n                pY = parseInt(pY, 10) * progress + '%';\n            }\n            else {\n                pY = pY * progress + 'px' ;\n            }\n        \n            el.transform('translate3d(' + pX + ', ' + pY + ',0px)');\n        }\n        s.parallax = {\n            setTranslate: function () {\n                s.container.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    setParallaxTransform(this, s.progress);\n        \n                });\n                s.slides.each(function () {\n                    var slide = $(this);\n                    slide.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function () {\n                        var progress = Math.min(Math.max(slide[0].progress, -1), 1);\n                        setParallaxTransform(this, progress);\n                    });\n                });\n            },\n            setTransition: function (duration) {\n                if (typeof duration === 'undefined') duration = s.params.speed;\n                s.container.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    var el = $(this);\n                    var parallaxDuration = parseInt(el.attr('data-swiper-parallax-duration'), 10) || duration;\n                    if (duration === 0) parallaxDuration = 0;\n                    el.transition(parallaxDuration);\n                });\n            }\n        };\n        \n\n        /*=========================\n          Zoom\n          ===========================*/\n        s.zoom = {\n            // \"Global\" Props\n            scale: 1,\n            currentScale: 1,\n            isScaling: false,\n            gesture: {\n                slide: undefined,\n                slideWidth: undefined,\n                slideHeight: undefined,\n                image: undefined,\n                imageWrap: undefined,\n                zoomMax: s.params.zoomMax\n            },\n            image: {\n                isTouched: undefined,\n                isMoved: undefined,\n                currentX: undefined,\n                currentY: undefined,\n                minX: undefined,\n                minY: undefined,\n                maxX: undefined,\n                maxY: undefined,\n                width: undefined,\n                height: undefined,\n                startX: undefined,\n                startY: undefined,\n                touchesStart: {},\n                touchesCurrent: {}\n            },\n            velocity: {\n                x: undefined,\n                y: undefined,\n                prevPositionX: undefined,\n                prevPositionY: undefined,\n                prevTime: undefined\n            },\n            // Calc Scale From Multi-touches\n            getDistanceBetweenTouches: function (e) {\n                if (e.targetTouches.length < 2) return 1;\n                var x1 = e.targetTouches[0].pageX,\n                    y1 = e.targetTouches[0].pageY,\n                    x2 = e.targetTouches[1].pageX,\n                    y2 = e.targetTouches[1].pageY;\n                var distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));\n                return distance;\n            },\n            // Events\n            onGestureStart: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchstart' || e.type === 'touchstart' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleStart = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.slide || !z.gesture.slide.length) {\n                    z.gesture.slide = $(this);\n                    if (z.gesture.slide.length === 0) z.gesture.slide = s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                    z.gesture.zoomMax = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax ;\n                    if (z.gesture.imageWrap.length === 0) {\n                        z.gesture.image = undefined;\n                        return;\n                    }\n                }\n                z.gesture.image.transition(0);\n                z.isScaling = true;\n            },\n            onGestureChange: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchmove' || e.type === 'touchmove' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleMove = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (s.support.gestures) {\n                    z.scale = e.scale * z.currentScale;\n                }\n                else {\n                    z.scale = (z.gesture.scaleMove / z.gesture.scaleStart) * z.currentScale;\n                }\n                if (z.scale > z.gesture.zoomMax) {\n                    z.scale = z.gesture.zoomMax - 1 + Math.pow((z.scale - z.gesture.zoomMax + 1), 0.5);\n                }\n                if (z.scale < s.params.zoomMin) {\n                    z.scale =  s.params.zoomMin + 1 - Math.pow((s.params.zoomMin - z.scale + 1), 0.5);\n                }\n                z.gesture.image.transform('translate3d(0,0,0) scale(' + z.scale + ')');\n            },\n            onGestureEnd: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchend' || e.type === 'touchend' && e.changedTouches.length < 2) {\n                        return;\n                    }\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                z.scale = Math.max(Math.min(z.scale, z.gesture.zoomMax), s.params.zoomMin);\n                z.gesture.image.transition(s.params.speed).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                z.currentScale = z.scale;\n                z.isScaling = false;\n                if (z.scale === 1) z.gesture.slide = undefined;\n            },\n            onTouchStart: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (z.image.isTouched) return;\n                if (s.device.os === 'android') e.preventDefault();\n                z.image.isTouched = true;\n                z.image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n            },\n            onTouchMove: function (e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                s.allowClick = false;\n                if (!z.image.isTouched || !z.gesture.slide) return;\n        \n                if (!z.image.isMoved) {\n                    z.image.width = z.gesture.image[0].offsetWidth;\n                    z.image.height = z.gesture.image[0].offsetHeight;\n                    z.image.startX = s.getTranslate(z.gesture.imageWrap[0], 'x') || 0;\n                    z.image.startY = s.getTranslate(z.gesture.imageWrap[0], 'y') || 0;\n                    z.gesture.slideWidth = z.gesture.slide[0].offsetWidth;\n                    z.gesture.slideHeight = z.gesture.slide[0].offsetHeight;\n                    z.gesture.imageWrap.transition(0);\n                    if (s.rtl) z.image.startX = -z.image.startX;\n                    if (s.rtl) z.image.startY = -z.image.startY;\n                }\n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n        \n                if (scaledWidth < z.gesture.slideWidth && scaledHeight < z.gesture.slideHeight) return;\n        \n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n        \n                z.image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n                if (!z.image.isMoved && !z.isScaling) {\n                    if (s.isHorizontal() &&\n                        (Math.floor(z.image.minX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x < z.image.touchesStart.x) ||\n                        (Math.floor(z.image.maxX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x > z.image.touchesStart.x)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                    else if (!s.isHorizontal() &&\n                        (Math.floor(z.image.minY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y < z.image.touchesStart.y) ||\n                        (Math.floor(z.image.maxY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y > z.image.touchesStart.y)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                }\n                e.preventDefault();\n                e.stopPropagation();\n        \n                z.image.isMoved = true;\n                z.image.currentX = z.image.touchesCurrent.x - z.image.touchesStart.x + z.image.startX;\n                z.image.currentY = z.image.touchesCurrent.y - z.image.touchesStart.y + z.image.startY;\n        \n                if (z.image.currentX < z.image.minX) {\n                    z.image.currentX =  z.image.minX + 1 - Math.pow((z.image.minX - z.image.currentX + 1), 0.8);\n                }\n                if (z.image.currentX > z.image.maxX) {\n                    z.image.currentX = z.image.maxX - 1 + Math.pow((z.image.currentX - z.image.maxX + 1), 0.8);\n                }\n        \n                if (z.image.currentY < z.image.minY) {\n                    z.image.currentY =  z.image.minY + 1 - Math.pow((z.image.minY - z.image.currentY + 1), 0.8);\n                }\n                if (z.image.currentY > z.image.maxY) {\n                    z.image.currentY = z.image.maxY - 1 + Math.pow((z.image.currentY - z.image.maxY + 1), 0.8);\n                }\n        \n                //Velocity\n                if (!z.velocity.prevPositionX) z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                if (!z.velocity.prevPositionY) z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                if (!z.velocity.prevTime) z.velocity.prevTime = Date.now();\n                z.velocity.x = (z.image.touchesCurrent.x - z.velocity.prevPositionX) / (Date.now() - z.velocity.prevTime) / 2;\n                z.velocity.y = (z.image.touchesCurrent.y - z.velocity.prevPositionY) / (Date.now() - z.velocity.prevTime) / 2;\n                if (Math.abs(z.image.touchesCurrent.x - z.velocity.prevPositionX) < 2) z.velocity.x = 0;\n                if (Math.abs(z.image.touchesCurrent.y - z.velocity.prevPositionY) < 2) z.velocity.y = 0;\n                z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                z.velocity.prevTime = Date.now();\n        \n                z.gesture.imageWrap.transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTouchEnd: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (!z.image.isTouched || !z.image.isMoved) {\n                    z.image.isTouched = false;\n                    z.image.isMoved = false;\n                    return;\n                }\n                z.image.isTouched = false;\n                z.image.isMoved = false;\n                var momentumDurationX = 300;\n                var momentumDurationY = 300;\n                var momentumDistanceX = z.velocity.x * momentumDurationX;\n                var newPositionX = z.image.currentX + momentumDistanceX;\n                var momentumDistanceY = z.velocity.y * momentumDurationY;\n                var newPositionY = z.image.currentY + momentumDistanceY;\n        \n                //Fix duration\n                if (z.velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - z.image.currentX) / z.velocity.x);\n                if (z.velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - z.image.currentY) / z.velocity.y);\n                var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n        \n                z.image.currentX = newPositionX;\n                z.image.currentY = newPositionY;\n        \n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n                z.image.currentX = Math.max(Math.min(z.image.currentX, z.image.maxX), z.image.minX);\n                z.image.currentY = Math.max(Math.min(z.image.currentY, z.image.maxY), z.image.minY);\n        \n                z.gesture.imageWrap.transition(momentumDuration).transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTransitionEnd: function (s) {\n                var z = s.zoom;\n                if (z.gesture.slide && s.previousIndex !== s.activeIndex) {\n                    z.gesture.image.transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.imageWrap.transform('translate3d(0,0,0)');\n                    z.gesture.slide = z.gesture.image = z.gesture.imageWrap = undefined;\n                    z.scale = z.currentScale = 1;\n                }\n            },\n            // Toggle Zoom\n            toggleZoom: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.slide) {\n                    z.gesture.slide = s.clickedSlide ? $(s.clickedSlide) : s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n        \n                var touchX, touchY, offsetX, offsetY, diffX, diffY, translateX, translateY, imageWidth, imageHeight, scaledWidth, scaledHeight, translateMinX, translateMinY, translateMaxX, translateMaxY, slideWidth, slideHeight;\n        \n                if (typeof z.image.touchesStart.x === 'undefined' && e) {\n                    touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n                    touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n                }\n                else {\n                    touchX = z.image.touchesStart.x;\n                    touchY = z.image.touchesStart.y;\n                }\n        \n                if (z.scale && z.scale !== 1) {\n                    // Zoom Out\n                    z.scale = z.currentScale = 1;\n                    z.gesture.imageWrap.transition(300).transform('translate3d(0,0,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.slide = undefined;\n                }\n                else {\n                    // Zoom In\n                    z.scale = z.currentScale = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax;\n                    if (e) {\n                        slideWidth = z.gesture.slide[0].offsetWidth;\n                        slideHeight = z.gesture.slide[0].offsetHeight;\n                        offsetX = z.gesture.slide.offset().left;\n                        offsetY = z.gesture.slide.offset().top;\n                        diffX = offsetX + slideWidth/2 - touchX;\n                        diffY = offsetY + slideHeight/2 - touchY;\n        \n                        imageWidth = z.gesture.image[0].offsetWidth;\n                        imageHeight = z.gesture.image[0].offsetHeight;\n                        scaledWidth = imageWidth * z.scale;\n                        scaledHeight = imageHeight * z.scale;\n        \n                        translateMinX = Math.min((slideWidth / 2 - scaledWidth / 2), 0);\n                        translateMinY = Math.min((slideHeight / 2 - scaledHeight / 2), 0);\n                        translateMaxX = -translateMinX;\n                        translateMaxY = -translateMinY;\n        \n                        translateX = diffX * z.scale;\n                        translateY = diffY * z.scale;\n        \n                        if (translateX < translateMinX) {\n                            translateX =  translateMinX;\n                        }\n                        if (translateX > translateMaxX) {\n                            translateX = translateMaxX;\n                        }\n        \n                        if (translateY < translateMinY) {\n                            translateY =  translateMinY;\n                        }\n                        if (translateY > translateMaxY) {\n                            translateY = translateMaxY;\n                        }\n                    }\n                    else {\n                        translateX = 0;\n                        translateY = 0;\n                    }\n                    z.gesture.imageWrap.transition(300).transform('translate3d(' + translateX + 'px, ' + translateY + 'px,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                }\n            },\n            // Attach/Detach Events\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n        \n                if (s.params.zoom) {\n                    var target = s.slides;\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    // Scale image\n                    if (s.support.gestures) {\n                        s.slides[action]('gesturestart', s.zoom.onGestureStart, passiveListener);\n                        s.slides[action]('gesturechange', s.zoom.onGestureChange, passiveListener);\n                        s.slides[action]('gestureend', s.zoom.onGestureEnd, passiveListener);\n                    }\n                    else if (s.touchEvents.start === 'touchstart') {\n                        s.slides[action](s.touchEvents.start, s.zoom.onGestureStart, passiveListener);\n                        s.slides[action](s.touchEvents.move, s.zoom.onGestureChange, passiveListener);\n                        s.slides[action](s.touchEvents.end, s.zoom.onGestureEnd, passiveListener);\n                    }\n        \n                    // Move image\n                    s[action]('touchStart', s.zoom.onTouchStart);\n                    s.slides.each(function (index, slide){\n                        if ($(slide).find('.' + s.params.zoomContainerClass).length > 0) {\n                            $(slide)[action](s.touchEvents.move, s.zoom.onTouchMove);\n                        }\n                    });\n                    s[action]('touchEnd', s.zoom.onTouchEnd);\n        \n                    // Scale Out\n                    s[action]('transitionEnd', s.zoom.onTransitionEnd);\n                    if (s.params.zoomToggle) {\n                        s.on('doubleTap', s.zoom.toggleZoom);\n                    }\n                }\n            },\n            init: function () {\n                s.zoom.attachEvents();\n            },\n            destroy: function () {\n                s.zoom.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          Plugins API. Collect all and init all plugins\n          ===========================*/\n        s._plugins = [];\n        for (var plugin in s.plugins) {\n            var p = s.plugins[plugin](s, s.params[plugin]);\n            if (p) s._plugins.push(p);\n        }\n        // Method to call all plugins event/method\n        s.callPlugins = function (eventName) {\n            for (var i = 0; i < s._plugins.length; i++) {\n                if (eventName in s._plugins[i]) {\n                    s._plugins[i][eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n        };\n\n        /*=========================\n          Events/Callbacks/Plugins Emitter\n          ===========================*/\n        function normalizeEventName (eventName) {\n            if (eventName.indexOf('on') !== 0) {\n                if (eventName[0] !== eventName[0].toUpperCase()) {\n                    eventName = 'on' + eventName[0].toUpperCase() + eventName.substring(1);\n                }\n                else {\n                    eventName = 'on' + eventName;\n                }\n            }\n            return eventName;\n        }\n        s.emitterEventListeners = {\n        \n        };\n        s.emit = function (eventName) {\n            // Trigger callbacks\n            if (s.params[eventName]) {\n                s.params[eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n            }\n            var i;\n            // Trigger events\n            if (s.emitterEventListeners[eventName]) {\n                for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                    s.emitterEventListeners[eventName][i](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n            // Trigger plugins\n            if (s.callPlugins) s.callPlugins(eventName, arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n        };\n        s.on = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            if (!s.emitterEventListeners[eventName]) s.emitterEventListeners[eventName] = [];\n            s.emitterEventListeners[eventName].push(handler);\n            return s;\n        };\n        s.off = function (eventName, handler) {\n            var i;\n            eventName = normalizeEventName(eventName);\n            if (typeof handler === 'undefined') {\n                // Remove all handlers for such event\n                s.emitterEventListeners[eventName] = [];\n                return s;\n            }\n            if (!s.emitterEventListeners[eventName] || s.emitterEventListeners[eventName].length === 0) return;\n            for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                if(s.emitterEventListeners[eventName][i] === handler) s.emitterEventListeners[eventName].splice(i, 1);\n            }\n            return s;\n        };\n        s.once = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            var _handler = function () {\n                handler(arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);\n                s.off(eventName, _handler);\n            };\n            s.on(eventName, _handler);\n            return s;\n        };\n\n        // Accessibility tools\n        s.a11y = {\n            makeFocusable: function ($el) {\n                $el.attr('tabIndex', '0');\n                return $el;\n            },\n            addRole: function ($el, role) {\n                $el.attr('role', role);\n                return $el;\n            },\n        \n            addLabel: function ($el, label) {\n                $el.attr('aria-label', label);\n                return $el;\n            },\n        \n            disable: function ($el) {\n                $el.attr('aria-disabled', true);\n                return $el;\n            },\n        \n            enable: function ($el) {\n                $el.attr('aria-disabled', false);\n                return $el;\n            },\n        \n            onEnterKey: function (event) {\n                if (event.keyCode !== 13) return;\n                if ($(event.target).is(s.params.nextButton)) {\n                    s.onClickNext(event);\n                    if (s.isEnd) {\n                        s.a11y.notify(s.params.lastSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.nextSlideMessage);\n                    }\n                }\n                else if ($(event.target).is(s.params.prevButton)) {\n                    s.onClickPrev(event);\n                    if (s.isBeginning) {\n                        s.a11y.notify(s.params.firstSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.prevSlideMessage);\n                    }\n                }\n                if ($(event.target).is('.' + s.params.bulletClass)) {\n                    $(event.target)[0].click();\n                }\n            },\n        \n            liveRegion: $('<span class=\"' + s.params.notificationClass + '\" aria-live=\"assertive\" aria-atomic=\"true\"></span>'),\n        \n            notify: function (message) {\n                var notification = s.a11y.liveRegion;\n                if (notification.length === 0) return;\n                notification.html('');\n                notification.html(message);\n            },\n            init: function () {\n                // Setup accessibility\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    s.a11y.makeFocusable(s.nextButton);\n                    s.a11y.addRole(s.nextButton, 'button');\n                    s.a11y.addLabel(s.nextButton, s.params.nextSlideMessage);\n                }\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    s.a11y.makeFocusable(s.prevButton);\n                    s.a11y.addRole(s.prevButton, 'button');\n                    s.a11y.addLabel(s.prevButton, s.params.prevSlideMessage);\n                }\n        \n                $(s.container).append(s.a11y.liveRegion);\n            },\n            initPagination: function () {\n                if (s.params.pagination && s.params.paginationClickable && s.bullets && s.bullets.length) {\n                    s.bullets.each(function () {\n                        var bullet = $(this);\n                        s.a11y.makeFocusable(bullet);\n                        s.a11y.addRole(bullet, 'button');\n                        s.a11y.addLabel(bullet, s.params.paginationBulletMessage.replace(/{{index}}/, bullet.index() + 1));\n                    });\n                }\n            },\n            destroy: function () {\n                if (s.a11y.liveRegion && s.a11y.liveRegion.length > 0) s.a11y.liveRegion.remove();\n            }\n        };\n        \n\n        /*=========================\n          Init/Destroy\n          ===========================*/\n        s.init = function () {\n            if (s.params.loop) s.createLoop();\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.enableDraggable();\n                }\n            }\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                if (!s.params.loop) s.updateProgress();\n                s.effects[s.params.effect].setTranslate();\n            }\n            if (s.params.loop) {\n                s.slideTo(s.params.initialSlide + s.loopedSlides, 0, s.params.runCallbacksOnInit);\n            }\n            else {\n                s.slideTo(s.params.initialSlide, 0, s.params.runCallbacksOnInit);\n                if (s.params.initialSlide === 0) {\n                    if (s.parallax && s.params.parallax) s.parallax.setTranslate();\n                    if (s.lazy && s.params.lazyLoading) {\n                        s.lazy.load();\n                        s.lazy.initialImageLoaded = true;\n                    }\n                }\n            }\n            s.attachEvents();\n            if (s.params.observer && s.support.observer) {\n                s.initObservers();\n            }\n            if (s.params.preloadImages && !s.params.lazyLoading) {\n                s.preloadImages();\n            }\n            if (s.params.zoom && s.zoom) {\n                s.zoom.init();\n            }\n            if (s.params.autoplay) {\n                s.startAutoplay();\n            }\n            if (s.params.keyboardControl) {\n                if (s.enableKeyboardControl) s.enableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.enableMousewheelControl) s.enableMousewheelControl();\n            }\n            // Deprecated hashnavReplaceState changed to replaceState for use in hashnav and history\n            if (s.params.hashnavReplaceState) {\n                s.params.replaceState = s.params.hashnavReplaceState;\n            }\n            if (s.params.history) {\n                if (s.history) s.history.init();\n            }\n            if (s.params.hashnav) {\n                if (s.hashnav) s.hashnav.init();\n            }\n            if (s.params.a11y && s.a11y) s.a11y.init();\n            s.emit('onInit', s);\n        };\n        \n        // Cleanup dynamic styles\n        s.cleanupStyles = function () {\n            // Container\n            s.container.removeClass(s.classNames.join(' ')).removeAttr('style');\n        \n            // Wrapper\n            s.wrapper.removeAttr('style');\n        \n            // Slides\n            if (s.slides && s.slides.length) {\n                s.slides\n                    .removeClass([\n                      s.params.slideVisibleClass,\n                      s.params.slideActiveClass,\n                      s.params.slideNextClass,\n                      s.params.slidePrevClass\n                    ].join(' '))\n                    .removeAttr('style')\n                    .removeAttr('data-swiper-column')\n                    .removeAttr('data-swiper-row');\n            }\n        \n            // Pagination/Bullets\n            if (s.paginationContainer && s.paginationContainer.length) {\n                s.paginationContainer.removeClass(s.params.paginationHiddenClass);\n            }\n            if (s.bullets && s.bullets.length) {\n                s.bullets.removeClass(s.params.bulletActiveClass);\n            }\n        \n            // Buttons\n            if (s.params.prevButton) $(s.params.prevButton).removeClass(s.params.buttonDisabledClass);\n            if (s.params.nextButton) $(s.params.nextButton).removeClass(s.params.buttonDisabledClass);\n        \n            // Scrollbar\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.scrollbar.track && s.scrollbar.track.length) s.scrollbar.track.removeAttr('style');\n                if (s.scrollbar.drag && s.scrollbar.drag.length) s.scrollbar.drag.removeAttr('style');\n            }\n        };\n        \n        // Destroy\n        s.destroy = function (deleteInstance, cleanupStyles) {\n            // Detach evebts\n            s.detachEvents();\n            // Stop autoplay\n            s.stopAutoplay();\n            // Disable draggable\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.disableDraggable();\n                }\n            }\n            // Destroy loop\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            // Cleanup styles\n            if (cleanupStyles) {\n                s.cleanupStyles();\n            }\n            // Disconnect observer\n            s.disconnectObservers();\n        \n            // Destroy zoom\n            if (s.params.zoom && s.zoom) {\n                s.zoom.destroy();\n            }\n            // Disable keyboard/mousewheel\n            if (s.params.keyboardControl) {\n                if (s.disableKeyboardControl) s.disableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.disableMousewheelControl) s.disableMousewheelControl();\n            }\n            // Disable a11y\n            if (s.params.a11y && s.a11y) s.a11y.destroy();\n            // Delete history popstate\n            if (s.params.history && !s.params.replaceState) {\n                window.removeEventListener('popstate', s.history.setHistoryPopState);\n            }\n            if (s.params.hashnav && s.hashnav)  {\n                s.hashnav.destroy();\n            }\n            // Destroy callback\n            s.emit('onDestroy');\n            // Delete instance\n            if (deleteInstance !== false) s = null;\n        };\n        \n        s.init();\n        \n\n    \n        // Return swiper instance\n        return s;\n    };\n    \n\n    /*==================================================\n        Prototype\n    ====================================================*/\n    Swiper.prototype = {\n        isSafari: (function () {\n            var ua = window.navigator.userAgent.toLowerCase();\n            return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n        })(),\n        isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent),\n        isArray: function (arr) {\n            return Object.prototype.toString.apply(arr) === '[object Array]';\n        },\n        /*==================================================\n        Browser\n        ====================================================*/\n        browser: {\n            ie: window.navigator.pointerEnabled || window.navigator.msPointerEnabled,\n            ieTouch: (window.navigator.msPointerEnabled && window.navigator.msMaxTouchPoints > 1) || (window.navigator.pointerEnabled && window.navigator.maxTouchPoints > 1),\n            lteIE9: (function() {\n                // create temporary DIV\n                var div = document.createElement('div');\n                // add content to tmp DIV which is wrapped into the IE HTML conditional statement\n                div.innerHTML = '<!--[if lte IE 9]><i></i><![endif]-->';\n                // return true / false value based on what will browser render\n                return div.getElementsByTagName('i').length === 1;\n            })()\n        },\n        /*==================================================\n        Devices\n        ====================================================*/\n        device: (function () {\n            var ua = window.navigator.userAgent;\n            var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/);\n            var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n            var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n            var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n            return {\n                ios: ipad || iphone || ipod,\n                android: android\n            };\n        })(),\n        /*==================================================\n        Feature Detection\n        ====================================================*/\n        support: {\n            touch : (window.Modernizr && Modernizr.touch === true) || (function () {\n                return !!(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch);\n            })(),\n    \n            transforms3d : (window.Modernizr && Modernizr.csstransforms3d === true) || (function () {\n                var div = document.createElement('div').style;\n                return ('webkitPerspective' in div || 'MozPerspective' in div || 'OPerspective' in div || 'MsPerspective' in div || 'perspective' in div);\n            })(),\n    \n            flexbox: (function () {\n                var div = document.createElement('div').style;\n                var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n                for (var i = 0; i < styles.length; i++) {\n                    if (styles[i] in div) return true;\n                }\n            })(),\n    \n            observer: (function () {\n                return ('MutationObserver' in window || 'WebkitMutationObserver' in window);\n            })(),\n    \n            passiveListener: (function () {\n                var supportsPassive = false;\n                try {\n                    var opts = Object.defineProperty({}, 'passive', {\n                        get: function() {\n                            supportsPassive = true;\n                        }\n                    });\n                    window.addEventListener('testPassiveListener', null, opts);\n                } catch (e) {}\n                return supportsPassive;\n            })(),\n    \n            gestures: (function () {\n                return 'ongesturestart' in window;\n            })()\n        },\n        /*==================================================\n        Plugins\n        ====================================================*/\n        plugins: {}\n    };\n    \n\n    /*===========================\n    Dom7 Library\n    ===========================*/\n    var Dom7 = (function () {\n        var Dom7 = function (arr) {\n            var _this = this, i = 0;\n            // Create array-like object\n            for (i = 0; i < arr.length; i++) {\n                _this[i] = arr[i];\n            }\n            _this.length = arr.length;\n            // Return collection with methods\n            return this;\n        };\n        var $ = function (selector, context) {\n            var arr = [], i = 0;\n            if (selector && !context) {\n                if (selector instanceof Dom7) {\n                    return selector;\n                }\n            }\n            if (selector) {\n                // String\n                if (typeof selector === 'string') {\n                    var els, tempParent, html = selector.trim();\n                    if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n                        var toCreate = 'div';\n                        if (html.indexOf('<li') === 0) toCreate = 'ul';\n                        if (html.indexOf('<tr') === 0) toCreate = 'tbody';\n                        if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) toCreate = 'tr';\n                        if (html.indexOf('<tbody') === 0) toCreate = 'table';\n                        if (html.indexOf('<option') === 0) toCreate = 'select';\n                        tempParent = document.createElement(toCreate);\n                        tempParent.innerHTML = selector;\n                        for (i = 0; i < tempParent.childNodes.length; i++) {\n                            arr.push(tempParent.childNodes[i]);\n                        }\n                    }\n                    else {\n                        if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n                            // Pure ID selector\n                            els = [document.getElementById(selector.split('#')[1])];\n                        }\n                        else {\n                            // Other selectors\n                            els = (context || document).querySelectorAll(selector);\n                        }\n                        for (i = 0; i < els.length; i++) {\n                            if (els[i]) arr.push(els[i]);\n                        }\n                    }\n                }\n                // Node/element\n                else if (selector.nodeType || selector === window || selector === document) {\n                    arr.push(selector);\n                }\n                //Array of elements or instance of Dom\n                else if (selector.length > 0 && selector[0].nodeType) {\n                    for (i = 0; i < selector.length; i++) {\n                        arr.push(selector[i]);\n                    }\n                }\n            }\n            return new Dom7(arr);\n        };\n        Dom7.prototype = {\n            // Classes and attriutes\n            addClass: function (className) {\n                if (typeof className === 'undefined') {\n                    return this;\n                }\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.add(classes[i]);\n                    }\n                }\n                return this;\n            },\n            removeClass: function (className) {\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.remove(classes[i]);\n                    }\n                }\n                return this;\n            },\n            hasClass: function (className) {\n                if (!this[0]) return false;\n                else return this[0].classList.contains(className);\n            },\n            toggleClass: function (className) {\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.toggle(classes[i]);\n                    }\n                }\n                return this;\n            },\n            attr: function (attrs, value) {\n                if (arguments.length === 1 && typeof attrs === 'string') {\n                    // Get attr\n                    if (this[0]) return this[0].getAttribute(attrs);\n                    else return undefined;\n                }\n                else {\n                    // Set attrs\n                    for (var i = 0; i < this.length; i++) {\n                        if (arguments.length === 2) {\n                            // String\n                            this[i].setAttribute(attrs, value);\n                        }\n                        else {\n                            // Object\n                            for (var attrName in attrs) {\n                                this[i][attrName] = attrs[attrName];\n                                this[i].setAttribute(attrName, attrs[attrName]);\n                            }\n                        }\n                    }\n                    return this;\n                }\n            },\n            removeAttr: function (attr) {\n                for (var i = 0; i < this.length; i++) {\n                    this[i].removeAttribute(attr);\n                }\n                return this;\n            },\n            data: function (key, value) {\n                if (typeof value === 'undefined') {\n                    // Get value\n                    if (this[0]) {\n                        var dataKey = this[0].getAttribute('data-' + key);\n                        if (dataKey) return dataKey;\n                        else if (this[0].dom7ElementDataStorage && (key in this[0].dom7ElementDataStorage)) return this[0].dom7ElementDataStorage[key];\n                        else return undefined;\n                    }\n                    else return undefined;\n                }\n                else {\n                    // Set value\n                    for (var i = 0; i < this.length; i++) {\n                        var el = this[i];\n                        if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};\n                        el.dom7ElementDataStorage[key] = value;\n                    }\n                    return this;\n                }\n            },\n            // Transforms\n            transform : function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            },\n            transition: function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            },\n            //Events\n            on: function (eventName, targetSelector, listener, capture) {\n                function handleLiveEvent(e) {\n                    var target = e.target;\n                    if ($(target).is(targetSelector)) listener.call(target, e);\n                    else {\n                        var parents = $(target).parents();\n                        for (var k = 0; k < parents.length; k++) {\n                            if ($(parents[k]).is(targetSelector)) listener.call(parents[k], e);\n                        }\n                    }\n                }\n                var events = eventName.split(' ');\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof targetSelector === 'function' || targetSelector === false) {\n                        // Usual events\n                        if (typeof targetSelector === 'function') {\n                            listener = arguments[1];\n                            capture = arguments[2] || false;\n                        }\n                        for (j = 0; j < events.length; j++) {\n                            this[i].addEventListener(events[j], listener, capture);\n                        }\n                    }\n                    else {\n                        //Live events\n                        for (j = 0; j < events.length; j++) {\n                            if (!this[i].dom7LiveListeners) this[i].dom7LiveListeners = [];\n                            this[i].dom7LiveListeners.push({listener: listener, liveListener: handleLiveEvent});\n                            this[i].addEventListener(events[j], handleLiveEvent, capture);\n                        }\n                    }\n                }\n    \n                return this;\n            },\n            off: function (eventName, targetSelector, listener, capture) {\n                var events = eventName.split(' ');\n                for (var i = 0; i < events.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        if (typeof targetSelector === 'function' || targetSelector === false) {\n                            // Usual events\n                            if (typeof targetSelector === 'function') {\n                                listener = arguments[1];\n                                capture = arguments[2] || false;\n                            }\n                            this[j].removeEventListener(events[i], listener, capture);\n                        }\n                        else {\n                            // Live event\n                            if (this[j].dom7LiveListeners) {\n                                for (var k = 0; k < this[j].dom7LiveListeners.length; k++) {\n                                    if (this[j].dom7LiveListeners[k].listener === listener) {\n                                        this[j].removeEventListener(events[i], this[j].dom7LiveListeners[k].liveListener, capture);\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                return this;\n            },\n            once: function (eventName, targetSelector, listener, capture) {\n                var dom = this;\n                if (typeof targetSelector === 'function') {\n                    targetSelector = false;\n                    listener = arguments[1];\n                    capture = arguments[2];\n                }\n                function proxy(e) {\n                    listener(e);\n                    dom.off(eventName, targetSelector, proxy, capture);\n                }\n                dom.on(eventName, targetSelector, proxy, capture);\n            },\n            trigger: function (eventName, eventData) {\n                for (var i = 0; i < this.length; i++) {\n                    var evt;\n                    try {\n                        evt = new window.CustomEvent(eventName, {detail: eventData, bubbles: true, cancelable: true});\n                    }\n                    catch (e) {\n                        evt = document.createEvent('Event');\n                        evt.initEvent(eventName, true, true);\n                        evt.detail = eventData;\n                    }\n                    this[i].dispatchEvent(evt);\n                }\n                return this;\n            },\n            transitionEnd: function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            },\n            // Sizing/Styles\n            width: function () {\n                if (this[0] === window) {\n                    return window.innerWidth;\n                }\n                else {\n                    if (this.length > 0) {\n                        return parseFloat(this.css('width'));\n                    }\n                    else {\n                        return null;\n                    }\n                }\n            },\n            outerWidth: function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'));\n                    else\n                        return this[0].offsetWidth;\n                }\n                else return null;\n            },\n            height: function () {\n                if (this[0] === window) {\n                    return window.innerHeight;\n                }\n                else {\n                    if (this.length > 0) {\n                        return parseFloat(this.css('height'));\n                    }\n                    else {\n                        return null;\n                    }\n                }\n            },\n            outerHeight: function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetHeight + parseFloat(this.css('margin-top')) + parseFloat(this.css('margin-bottom'));\n                    else\n                        return this[0].offsetHeight;\n                }\n                else return null;\n            },\n            offset: function () {\n                if (this.length > 0) {\n                    var el = this[0];\n                    var box = el.getBoundingClientRect();\n                    var body = document.body;\n                    var clientTop  = el.clientTop  || body.clientTop  || 0;\n                    var clientLeft = el.clientLeft || body.clientLeft || 0;\n                    var scrollTop  = window.pageYOffset || el.scrollTop;\n                    var scrollLeft = window.pageXOffset || el.scrollLeft;\n                    return {\n                        top: box.top  + scrollTop  - clientTop,\n                        left: box.left + scrollLeft - clientLeft\n                    };\n                }\n                else {\n                    return null;\n                }\n            },\n            css: function (props, value) {\n                var i;\n                if (arguments.length === 1) {\n                    if (typeof props === 'string') {\n                        if (this[0]) return window.getComputedStyle(this[0], null).getPropertyValue(props);\n                    }\n                    else {\n                        for (i = 0; i < this.length; i++) {\n                            for (var prop in props) {\n                                this[i].style[prop] = props[prop];\n                            }\n                        }\n                        return this;\n                    }\n                }\n                if (arguments.length === 2 && typeof props === 'string') {\n                    for (i = 0; i < this.length; i++) {\n                        this[i].style[props] = value;\n                    }\n                    return this;\n                }\n                return this;\n            },\n    \n            //Dom manipulation\n            each: function (callback) {\n                for (var i = 0; i < this.length; i++) {\n                    callback.call(this[i], i, this[i]);\n                }\n                return this;\n            },\n            html: function (html) {\n                if (typeof html === 'undefined') {\n                    return this[0] ? this[0].innerHTML : undefined;\n                }\n                else {\n                    for (var i = 0; i < this.length; i++) {\n                        this[i].innerHTML = html;\n                    }\n                    return this;\n                }\n            },\n            text: function (text) {\n                if (typeof text === 'undefined') {\n                    if (this[0]) {\n                        return this[0].textContent.trim();\n                    }\n                    else return null;\n                }\n                else {\n                    for (var i = 0; i < this.length; i++) {\n                        this[i].textContent = text;\n                    }\n                    return this;\n                }\n            },\n            is: function (selector) {\n                if (!this[0]) return false;\n                var compareWith, i;\n                if (typeof selector === 'string') {\n                    var el = this[0];\n                    if (el === document) return selector === document;\n                    if (el === window) return selector === window;\n    \n                    if (el.matches) return el.matches(selector);\n                    else if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);\n                    else if (el.mozMatchesSelector) return el.mozMatchesSelector(selector);\n                    else if (el.msMatchesSelector) return el.msMatchesSelector(selector);\n                    else {\n                        compareWith = $(selector);\n                        for (i = 0; i < compareWith.length; i++) {\n                            if (compareWith[i] === this[0]) return true;\n                        }\n                        return false;\n                    }\n                }\n                else if (selector === document) return this[0] === document;\n                else if (selector === window) return this[0] === window;\n                else {\n                    if (selector.nodeType || selector instanceof Dom7) {\n                        compareWith = selector.nodeType ? [selector] : selector;\n                        for (i = 0; i < compareWith.length; i++) {\n                            if (compareWith[i] === this[0]) return true;\n                        }\n                        return false;\n                    }\n                    return false;\n                }\n    \n            },\n            index: function () {\n                if (this[0]) {\n                    var child = this[0];\n                    var i = 0;\n                    while ((child = child.previousSibling) !== null) {\n                        if (child.nodeType === 1) i++;\n                    }\n                    return i;\n                }\n                else return undefined;\n            },\n            eq: function (index) {\n                if (typeof index === 'undefined') return this;\n                var length = this.length;\n                var returnIndex;\n                if (index > length - 1) {\n                    return new Dom7([]);\n                }\n                if (index < 0) {\n                    returnIndex = length + index;\n                    if (returnIndex < 0) return new Dom7([]);\n                    else return new Dom7([this[returnIndex]]);\n                }\n                return new Dom7([this[index]]);\n            },\n            append: function (newChild) {\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof newChild === 'string') {\n                        var tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = newChild;\n                        while (tempDiv.firstChild) {\n                            this[i].appendChild(tempDiv.firstChild);\n                        }\n                    }\n                    else if (newChild instanceof Dom7) {\n                        for (j = 0; j < newChild.length; j++) {\n                            this[i].appendChild(newChild[j]);\n                        }\n                    }\n                    else {\n                        this[i].appendChild(newChild);\n                    }\n                }\n                return this;\n            },\n            prepend: function (newChild) {\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof newChild === 'string') {\n                        var tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = newChild;\n                        for (j = tempDiv.childNodes.length - 1; j >= 0; j--) {\n                            this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n                        }\n                        // this[i].insertAdjacentHTML('afterbegin', newChild);\n                    }\n                    else if (newChild instanceof Dom7) {\n                        for (j = 0; j < newChild.length; j++) {\n                            this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n                        }\n                    }\n                    else {\n                        this[i].insertBefore(newChild, this[i].childNodes[0]);\n                    }\n                }\n                return this;\n            },\n            insertBefore: function (selector) {\n                var before = $(selector);\n                for (var i = 0; i < this.length; i++) {\n                    if (before.length === 1) {\n                        before[0].parentNode.insertBefore(this[i], before[0]);\n                    }\n                    else if (before.length > 1) {\n                        for (var j = 0; j < before.length; j++) {\n                            before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);\n                        }\n                    }\n                }\n            },\n            insertAfter: function (selector) {\n                var after = $(selector);\n                for (var i = 0; i < this.length; i++) {\n                    if (after.length === 1) {\n                        after[0].parentNode.insertBefore(this[i], after[0].nextSibling);\n                    }\n                    else if (after.length > 1) {\n                        for (var j = 0; j < after.length; j++) {\n                            after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);\n                        }\n                    }\n                }\n            },\n            next: function (selector) {\n                if (this.length > 0) {\n                    if (selector) {\n                        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) return new Dom7([this[0].nextElementSibling]);\n                        else return new Dom7([]);\n                    }\n                    else {\n                        if (this[0].nextElementSibling) return new Dom7([this[0].nextElementSibling]);\n                        else return new Dom7([]);\n                    }\n                }\n                else return new Dom7([]);\n            },\n            nextAll: function (selector) {\n                var nextEls = [];\n                var el = this[0];\n                if (!el) return new Dom7([]);\n                while (el.nextElementSibling) {\n                    var next = el.nextElementSibling;\n                    if (selector) {\n                        if($(next).is(selector)) nextEls.push(next);\n                    }\n                    else nextEls.push(next);\n                    el = next;\n                }\n                return new Dom7(nextEls);\n            },\n            prev: function (selector) {\n                if (this.length > 0) {\n                    if (selector) {\n                        if (this[0].previousElementSibling && $(this[0].previousElementSibling).is(selector)) return new Dom7([this[0].previousElementSibling]);\n                        else return new Dom7([]);\n                    }\n                    else {\n                        if (this[0].previousElementSibling) return new Dom7([this[0].previousElementSibling]);\n                        else return new Dom7([]);\n                    }\n                }\n                else return new Dom7([]);\n            },\n            prevAll: function (selector) {\n                var prevEls = [];\n                var el = this[0];\n                if (!el) return new Dom7([]);\n                while (el.previousElementSibling) {\n                    var prev = el.previousElementSibling;\n                    if (selector) {\n                        if($(prev).is(selector)) prevEls.push(prev);\n                    }\n                    else prevEls.push(prev);\n                    el = prev;\n                }\n                return new Dom7(prevEls);\n            },\n            parent: function (selector) {\n                var parents = [];\n                for (var i = 0; i < this.length; i++) {\n                    if (selector) {\n                        if ($(this[i].parentNode).is(selector)) parents.push(this[i].parentNode);\n                    }\n                    else {\n                        parents.push(this[i].parentNode);\n                    }\n                }\n                return $($.unique(parents));\n            },\n            parents: function (selector) {\n                var parents = [];\n                for (var i = 0; i < this.length; i++) {\n                    var parent = this[i].parentNode;\n                    while (parent) {\n                        if (selector) {\n                            if ($(parent).is(selector)) parents.push(parent);\n                        }\n                        else {\n                            parents.push(parent);\n                        }\n                        parent = parent.parentNode;\n                    }\n                }\n                return $($.unique(parents));\n            },\n            find : function (selector) {\n                var foundElements = [];\n                for (var i = 0; i < this.length; i++) {\n                    var found = this[i].querySelectorAll(selector);\n                    for (var j = 0; j < found.length; j++) {\n                        foundElements.push(found[j]);\n                    }\n                }\n                return new Dom7(foundElements);\n            },\n            children: function (selector) {\n                var children = [];\n                for (var i = 0; i < this.length; i++) {\n                    var childNodes = this[i].childNodes;\n    \n                    for (var j = 0; j < childNodes.length; j++) {\n                        if (!selector) {\n                            if (childNodes[j].nodeType === 1) children.push(childNodes[j]);\n                        }\n                        else {\n                            if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) children.push(childNodes[j]);\n                        }\n                    }\n                }\n                return new Dom7($.unique(children));\n            },\n            remove: function () {\n                for (var i = 0; i < this.length; i++) {\n                    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);\n                }\n                return this;\n            },\n            add: function () {\n                var dom = this;\n                var i, j;\n                for (i = 0; i < arguments.length; i++) {\n                    var toAdd = $(arguments[i]);\n                    for (j = 0; j < toAdd.length; j++) {\n                        dom[dom.length] = toAdd[j];\n                        dom.length++;\n                    }\n                }\n                return dom;\n            }\n        };\n        $.fn = Dom7.prototype;\n        $.unique = function (arr) {\n            var unique = [];\n            for (var i = 0; i < arr.length; i++) {\n                if (unique.indexOf(arr[i]) === -1) unique.push(arr[i]);\n            }\n            return unique;\n        };\n    \n        return $;\n    })();\n    \n\n    /*===========================\n     Get Dom libraries\n     ===========================*/\n    var swiperDomPlugins = ['jQuery', 'Zepto', 'Dom7'];\n    for (var i = 0; i < swiperDomPlugins.length; i++) {\n    \tif (window[swiperDomPlugins[i]]) {\n    \t\taddLibraryPlugin(window[swiperDomPlugins[i]]);\n    \t}\n    }\n    // Required DOM Plugins\n    var domLib;\n    if (typeof Dom7 === 'undefined') {\n    \tdomLib = window.Dom7 || window.Zepto || window.jQuery;\n    }\n    else {\n    \tdomLib = Dom7;\n    }\n\n    /*===========================\n    Add .swiper plugin from Dom libraries\n    ===========================*/\n    function addLibraryPlugin(lib) {\n        lib.fn.swiper = function (params) {\n            var firstInstance;\n            lib(this).each(function () {\n                var s = new Swiper(this, params);\n                if (!firstInstance) firstInstance = s;\n            });\n            return firstInstance;\n        };\n    }\n    \n    if (domLib) {\n        if (!('transitionEnd' in domLib.fn)) {\n            domLib.fn.transitionEnd = function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            };\n        }\n        if (!('transform' in domLib.fn)) {\n            domLib.fn.transform = function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            };\n        }\n        if (!('transition' in domLib.fn)) {\n            domLib.fn.transition = function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            };\n        }\n        if (!('outerWidth' in domLib.fn)) {\n            domLib.fn.outerWidth = function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'));\n                    else\n                        return this[0].offsetWidth;\n                }\n                else return null;\n            };\n        }\n    }\n\n    window.Swiper = Swiper;\n})();\n/*===========================\nSwiper AMD Export\n===========================*/\nif (typeof(module) !== 'undefined')\n{\n    module.exports = window.Swiper;\n}\nelse if (typeof define === 'function' && define.amd) {\n    define([], function () {\n        'use strict';\n        return window.Swiper;\n    });\n}\n//# sourceMappingURL=maps/swiper.js.map\n"]}