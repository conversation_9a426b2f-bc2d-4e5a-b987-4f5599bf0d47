/* Farsi (Persian) locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.fa = {};

flatpickr.l10ns.fa.weekdays = {
	shorthand: ["یک", "دو", "سه", "چهار", "پنج", "آدینه", "شنبه"],
	longhand: ["یک‌شنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنچ‌شنبه", "آدینه", "شنبه"]
};

flatpickr.l10ns.fa.months = {
	shorthand: ["ژانویه", "فوریه", "مارس", "آوریل", "مه", "ژوئن", "ژوئیه", "اوت", "سپتامبر", "اکتبر", "نوامبر", "دسامبر"],
	longhand: ["ژانویه", "فوریه", "مارس", "آوریل", "مه", "ژوئن", "ژوئیه", "اوت", "سپتامبر", "اکتبر", "نوامبر", "دسامبر"]
};

flatpickr.l10ns.fa.ordinal = function () {
	return "";
};
if (typeof module !== "undefined") module.exports = flatpickr.l10ns;