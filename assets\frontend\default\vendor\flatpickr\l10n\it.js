/* Italian locals for flatpickr */
var flatpickr = flatpickr || { l10ns: {} };
flatpickr.l10ns.it = {};

flatpickr.l10ns.it.weekdays = {
	shorthand: ["<PERSON>", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>b"],
	longhand: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>edì", "Me<PERSON>oledì", "<PERSON>iovedì", "Venerdì", "Sabato"]
};

flatpickr.l10ns.it.months = {
	shorthand: ["Gen", "Feb", "Mar", "Apr", "Mag", "Giu", "Lug", "Ago", "Set", "Ott", "Nov", "Dic"],
	longhand: ["Gen<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>e", "Maggio", "Giugno", "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"]
};

flatpickr.l10ns.it.firstDayOfWeek = 1;

flatpickr.l10ns.it.ordinal = "°";

flatpickr.l10ns.it.weekAbbreviation = "Se";

flatpickr.l10ns.it.scrollTitle = "Scrolla per aumentare";

flatpickr.l10ns.it.toggleTitle = "Clicca per cambiare";

if (typeof module !== "undefined") module.exports = flatpickr.l10ns;